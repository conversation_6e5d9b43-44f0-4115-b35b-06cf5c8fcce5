package cn.loveapp.uac.request;

import cn.loveapp.common.constant.CommonAppConstants;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * @Author: zhong<PERSON><PERSON>e
 * @Date: 2023/8/25 10:17
 * @Description: 用户设置复制接口-请求实体类
 */
@Data
public class UserSettingCopyRequest {

    /**
     * 来源用户
     */
    @NotNull
    private User originUser;

    /**
     * 目标用户列表
     */
    @NotEmpty
    private List<User> targetUsers;

    /**
     * 是否保留目标用户设置（已存在的设置不覆盖）
     */
    private Boolean keepTargetSettings = Boolean.FALSE;

    /**
     * 复制类型
     */
    private CopyType copyType = CopyType.ALL;

    /**
     * 是否存在平台类型设置
     */
    private Boolean isExitPlatformTypeSetting;


    @Data
    public static class User {
        /**
         * 用户id
         */
        @NotBlank
        private String userId;

        /**
         * 平台
         */
        @NotBlank
        private String platformId;

        /**
         * 应用
         */
        private String app = CommonAppConstants.APP_TRADE;
    }

    public enum CopyType {

        /**
         * 全复制
         */
        ALL,

        /**
         * 只复制设置项
         */
        ONLY_SETTINGS,

        /**
         * 只复制用户tag
         */
        ONLY_TAGS


    }
}
