package cn.loveapp.uac.request;

import cn.loveapp.uac.request.MethodInterface.MethodPost;
import cn.loveapp.uac.request.MethodInterface.MethodPut;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @program: uac-service-group
 * @description: RegisterSellerUserInfo
 * @author: Jason
 * @create: 2020-03-10 17:03
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class LoginInfoRequest extends BaseHttpRequest {

	@NotEmpty(message = "创建时间不能为空", groups = {MethodPost.class, MethodPut.class})
	private String createDate;
	@NotEmpty(message = "lastActivedt不能为空", groups = {MethodPost.class, MethodPut.class})
	private String lastActivedt;
	@NotEmpty(message = "vipflag不能为空", groups = {MethodPost.class, MethodPut.class})
	private String vipflag;
	@NotEmpty(message = "createIpAddress不能为空", groups = {MethodPost.class, MethodPut.class})
	private String createIpAddress;
	@NotEmpty(message = "lastIpAddress不能为空", groups = {MethodPost.class, MethodPut.class})
	private String lastIpAddress;
	@NotEmpty(message = "roleId不能为空", groups = {MethodPost.class, MethodPut.class})
	private String roleId;
	@NotEmpty(message = "lastUpdateTime不能为空", groups = {MethodPost.class, MethodPut.class})
	private String lastUpdateTime;
	@NotEmpty(message = "subDateTime不能为空", groups = {MethodPost.class, MethodPut.class})
	private String subDateTime;
	@NotEmpty(message = "isSilent不能为空", groups = {MethodPost.class, MethodPut.class})
	private String isSilent;
	@NotEmpty(message = "lastActivePoint不能为空", groups = {MethodPost.class, MethodPut.class})
	private String lastActivePoint;


}
