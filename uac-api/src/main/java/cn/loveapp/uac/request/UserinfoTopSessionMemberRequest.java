package cn.loveapp.uac.request;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @program: uac-service-group
 * @description: SellerUserInfoHttpRequest
 * @author: <PERSON>
 * @create: 2020-03-04 14:43
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class UserinfoTopSessionMemberRequest extends BaseHttpRequest {
	@NotEmpty
	private String memberId;

}
