package cn.loveapp.uac.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-04-18 18:04
 * @description: 获取店铺映射关系请求体
 */
@Data
public class UserShopInfoGetRequest {

    /**
     * 用户id
     */
    private String sellerId;

    /**
     * 用户nick
     */
    private String sellerNick;

    /**
     * 平台
     */
    @JSONField(alternateNames = "platformId")
    private String storeId;

    /**
     * 应用
     */
    @JSONField(alternateNames = "app")
    private String appName;
}
