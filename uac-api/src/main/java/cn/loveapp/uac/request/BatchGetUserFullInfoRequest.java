package cn.loveapp.uac.request;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/11/27 18:38
 * @Description: 批量获取userFullInfo
 */
@Data
public class BatchGetUserFullInfoRequest {

    /**
     * 批量请求
     */
    @NotEmpty
    private List<UserFullInfoRequest> userFullInfoRequests;

    /**
     * 是否需要刷新token，默认false
     */
    private boolean needCheckAndRefreshToken;

}
