package cn.loveapp.uac.request;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-18 16:05
 * @description: 获取店铺映射关系响应体
 */
@Data
public class UserShopInfoGetResponse {
    /**
     * 卖家id
     */
    private String sellerId;

    /**
     * 用户昵称
     */
    private String sellerNick;

    /**
     * 应用
     */
    private String appName;

    /**
     * 平台id
     */
    private String storeId;

    /**
     * 店铺信息
     */
    private List<ShopInfo> shopInfoList;

    @Data
    public static class ShopInfo {

        /**
         * 店铺id
         */
        private String shopId;
        /**
         * 店铺密钥
         */
        private String shopCipher;
        /**
         * 店铺名称
         */
        private String shopName;
        /**
         * 地区
         */
        private String region;
        /**
         * 用户类型
         */
        private String sellerType;
        /**
         * 店铺code
         */
        private String code;
        /**
         * 店铺类型
         */
        private Integer shopsType;
    }
}
