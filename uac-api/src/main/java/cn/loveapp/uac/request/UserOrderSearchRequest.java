package cn.loveapp.uac.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户订购记录请求
 *
 * <AUTHOR>
 * @Date 2024/8/15 3:53 PM
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserOrderSearchRequest extends BaseHttpRequest{

    /**
     * 排序方向 asc / desc
     */
    private String sortDirection;

    /**
     * 开始时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 订购项目
     */
    private List<String> itemCodes;


    public UserOrderSearchRequest(){
        this.sortDirection = "desc";
    }

}
