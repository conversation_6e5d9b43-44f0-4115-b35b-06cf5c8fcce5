package cn.loveapp.uac.request;

import cn.loveapp.uac.annotation.CheckAppHasExist;
import cn.loveapp.uac.annotation.CheckPlatformHasExist;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @program: uac-service-group
 * @description: CallbackRequest-授权回调请求类
 * @author: <PERSON>
 * @create: 2020-03-13 16:05
 **/
@Data
public class CallbackRequest {
	@NotBlank
	private String code;
	@CheckPlatformHasExist(message = "platform平台名称不能为空")
	private String platformId;
	@CheckAppHasExist(message = "app应用名称不能为空")
	private String app;

}
