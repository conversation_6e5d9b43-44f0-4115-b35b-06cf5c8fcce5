package cn.loveapp.uac.request;

import org.springframework.util.StringUtils;

import com.alibaba.fastjson.annotation.JSONField;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.uac.annotation.CheckIdOrNick;
import cn.loveapp.uac.annotation.CheckPlatformHasExist;
import lombok.Data;

/**
 * @program: uac-service-group
 * @description: BaseHttpRequest
 * @author: <PERSON>
 * @create: 2020-03-04 14:33
 **/
@Data
@CheckIdOrNick
public class BaseHttpRequest implements java.io.Serializable{

	/**
	 * 用户昵称
	 */
	private String sellerNick;

	/**
	 * 用户Id
	 */
	private String sellerId;

	/**
	 * 平台标识
	 */
	@CheckPlatformHasExist(message = "platform平台名称不能为空")
	private String platformId;

	/**
	 * 应用标识
	 */
	@JSONField(alternateNames = {"app", "appName"})
	private String app = CommonAppConstants.APP_TRADE;

	/**
	 * 子账号昵称
	 */
	private String subSellerNick;

	/**
	 * 子账号Id
	 */
	private String subSellerId;

    /**
     * 必要 用户appId
     */
    private String sellerAppId;

    /**
     * 1688 memberId
     */
    private String memberId;

    /**
     * 店铺id列表（tiktok）
     */
    private String shopId;

	/**
	 * 店铺名
	 */
	private String mallName;

	public String getApp() {
		if (StringUtils.isEmpty(app)) {
			return CommonAppConstants.APP_TRADE;
		}
		return app;
	}

}
