package cn.loveapp.uac.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-03-06 11:41
 * @description: 通过vipFlag获取用户列表请求体
 */
@Data
public class ListUserByVipInfoRequest {

    /**
     * vipFlag列表 （与professionalOrderCycleEnd二选一查询）
     */
    private List<Integer> vipFlagList;

    /**
     * 专业版到期时间（与vipFlagList二选一查询）
     */
    private LocalDateTime professionalOrderCycleEnd;

    /**
     * 平台id
     */
    @NotBlank
    private String storeId;

    /**
     * 应用名
     */
    @NotBlank
    private String appName;

    /**
     * 页码大小
     */
    @Max(1000)
    @Min(100)
    private Integer pageSize;

    /**
     * 起始查询id
     */
    @Min(0)
    private Integer lastId;
}
