package cn.loveapp.uac.request;

import cn.loveapp.common.utils.DateUtil;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

/**
 * @program: uac-service-group
 * @description: RefreshUserInfoRequest
 * @author: Jason
 * @create: 2020-03-12 10:38
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class RefreshUserInfoRequest extends BaseHttpRequest {
	private String vipflag;
	private String roleId;
	private String lastIpAddress;
	private String lastactivePoint;
	private String lastActiveDateTime;
	private String orderCycleEndTime;
	private String lastPaidTime;
	private String isSilent;


	public LocalDateTime convertLastActiveDateTime() {
		return DateUtil.parseString(lastActiveDateTime);
	}

	public LocalDateTime convertOrderCycleEndTime() {
		return DateUtil.parseString(orderCycleEndTime);
	}

	public LocalDateTime convertLastPaidTime() {
		return DateUtil.parseString(lastPaidTime);
	}

	public Boolean convertIsSilent() {
		if (StringUtils.isBlank(isSilent)) {
			return null;
		}
		if ("0".equals(isSilent)) {
			return Boolean.FALSE;
		} else{
			return Boolean.TRUE;
		}
	}

	public Boolean isEmpty() {
		if (StringUtils.isBlank(vipflag)) {
			return Boolean.FALSE;
		}
		return Boolean.TRUE;
	}

	public Integer getVipflag() {
		return StringUtils.isBlank(vipflag) ? null : Integer.valueOf(vipflag);
	}

}
