package cn.loveapp.uac.request;

import cn.loveapp.uac.contant.AyMultiTagType;
import lombok.Data;

import java.util.Set;

/**
 * 批量打标变更请求request
 *
 * <AUTHOR>
 * @Date 2023/10/16 3:04 PM
 */
@Data
public class BatchMultiUserTagUpdateRequest extends BatchRequest<UserInfoRequest> {

    /**
     * 新增tag
     */
    private Set<String> ayMultiTagsForAdd;

    /**
     * 删除tag
     */
    private Set<String> ayMultiTagsForDel;

    /**
     * 店铺类型
     */
    private AyMultiTagType tagType = AyMultiTagType.SHOPS;

}
