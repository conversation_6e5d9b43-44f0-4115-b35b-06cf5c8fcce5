package cn.loveapp.uac.request;

import lombok.Data;

import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/7/12 10:23
 * @Description: 批量读取用户配置 - 请求实体类
 */
@Data
public class BatchSettingGetRequest {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 平台
     */
    private String platformId;

    /**
     * 应用
     */
    private String app;

    /**
     * 设置名列表
     */
    private List<String> settings;

    /**
     * 是否读取默认设置
     */
    private Boolean loadDefaultSetting = Boolean.TRUE;
}
