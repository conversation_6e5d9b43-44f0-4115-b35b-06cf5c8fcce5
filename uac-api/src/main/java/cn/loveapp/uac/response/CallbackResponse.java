package cn.loveapp.uac.response;

import java.io.Serializable;
import lombok.Data;

/**
 * @program: uac-service-group
 * @description: CallbackResponse
 * @author: Jason
 * @create: 2020-07-20 13:44
 **/
@Data
public class CallbackResponse implements Serializable {
	private String sellerNick;
	private String accessToken;

	/**
	 * of
	 * @param sellerNick
	 * @param accessToken 未加密前的accesstoken
	 * @return
	 */
	public static CallbackResponse of(String sellerNick, String accessToken) {
		CallbackResponse callbackResponse = new CallbackResponse();
		callbackResponse.setSellerNick(sellerNick);
		callbackResponse.setAccessToken(accessToken);
		return callbackResponse;
	}

	public Boolean isSuccess() {
		return !"".equals(sellerNick) && null != sellerNick && !"".equals(accessToken) && null != accessToken;
	}

}
