package cn.loveapp.uac.response;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import cn.loveapp.uac.entity.LevelCycleEndTime;
import lombok.Data;

/**
 * @program: uac-service-group
 * @description: UserInfo
 * @author: Jason
 * @create: 2020-03-04 16:14
 **/
@Data
public class UserInfoResponse implements Serializable {
	private String platformId;
	private String appName;
	private String sellerId;
	private String corpId;
	private String sellerNick;
	private String topSession;
	private String sellerAppId;
	private String sellerAppSecret;
    private String memberId;
	private String roleId;
	private Integer vipflag;
	private String hVersion;
	private Boolean hasNeedAuth;
	private String tag;
	private LocalDateTime createDate;
	private LocalDateTime revivalDate;
	private LocalDateTime orderCycleEnd;
	private LocalDateTime authDeadLine;
	private LocalDateTime w2DeadLine;
	private String articleCode;
	private List<String> itemCode;
	private Boolean hasExist;

	private LocalDateTime fuwuMarketOrderCycleEnd;
	private Integer fuwuMarketVipflag;

	/**
	 * pdd用户设置的店铺名称  nick存储的为pdd生成的，mallName为用户自定义的名称
	 */
	private String mallName;

	/**
	 * 多店tag
	 */
	private List<String> ayMultiTags;


    /**
     * 比当前版本低的赠送版本集合
     */
    private List<LevelCycleEndTime> lowerSentLevelList;

    /**
     * 店铺id（TikTok）
     */
    private String  shopId;

    /**
     * 店铺密码（TiKTok）
     */
    private String  shopCipher;

    /**
     * 授权是否异常(false 代表正常 true 代表授权异常)
     */
    private Boolean isAuthExcept;

}
