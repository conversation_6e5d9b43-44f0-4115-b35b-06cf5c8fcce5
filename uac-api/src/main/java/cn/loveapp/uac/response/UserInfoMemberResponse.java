package cn.loveapp.uac.response;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * @program: uac-service-group
 * @description: UserInfo
 * @author: <PERSON>
 * @create: 2020-03-04 16:14
 **/
@Data
public class UserInfoMemberResponse implements Serializable {
	private String sellerId;
	private String sellerNick;
	private String topSession;
	private Integer vipflag;
	private Boolean hasNeedAuth;
}
