package cn.loveapp.uac.response;

import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-03-06 14:35
 * @description: 通过vipFlag获取用户列表请求体
 */
@Data
public class ListUserByVipInfoResponse {

    /**
     * 用户信息列表
     */
    private List<UserInfo> userInfoDTOList;

    /**
     * 下一次查询id
     */
    private Integer lastId;

    /**
     * 是否存在下一页
     */
    private Boolean next;

    @Data
    public static class UserInfo {
        /**
         * 用户id
         */
        private String sellerId;

        /**
         * 用户nick
         */
        private String sellerNick;

        /**
         * 专业版到期时间
         */
        private LocalDateTime professionalOrderCycleEnd;
    }
}
