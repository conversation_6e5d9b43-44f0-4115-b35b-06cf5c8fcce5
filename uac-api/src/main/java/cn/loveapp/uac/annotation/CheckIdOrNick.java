package cn.loveapp.uac.annotation;

import cn.loveapp.uac.annotation.processor.IdOrNickValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * <AUTHOR>
 * @Since 2020/6/17 21:11
 */
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = IdOrNickValidator.class)
public @interface CheckIdOrNick {

    String message() default "id和nick参数都缺少";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
