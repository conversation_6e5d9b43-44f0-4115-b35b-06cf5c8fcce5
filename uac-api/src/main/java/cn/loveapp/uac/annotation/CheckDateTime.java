package cn.loveapp.uac.annotation;

import cn.loveapp.uac.annotation.processor.DateTimeValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * @program: uac-service-group
 * @description: CheckDateTime 校验时间格式是否正确
 * @author: Jason
 * @create: 2020-03-07 14:28
 **/
@Documented
@Target({ElementType.PARAMETER, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = DateTimeValidator.class)
public @interface CheckDateTime {

	String message() default "时间格式错误 不存在";

	Class<?>[] groups() default {};

	Class<? extends Payload>[] payload() default {};
}
