package cn.loveapp.uac.annotation.processor;

import cn.loveapp.uac.annotation.CheckIdOrNick;
import cn.loveapp.uac.request.BaseHttpRequest;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

/**
 * sellerId 和 sellerNick 必须要有一个
 * <AUTHOR>
 * @Since 2020/6/17 21:10
 */
public class IdOrNickValidator implements ConstraintValidator<CheckIdOrNick, BaseHttpRequest> {

    @Override
    public void initialize(CheckIdOrNick constraintAnnotation) {

    }

    @Override
    public boolean isValid(BaseHttpRequest value, ConstraintValidatorContext context) {
        //如果id和nick都是空的 那么验证失败
		if (StringUtils.isAllEmpty(value.getSellerId(), value.getSellerNick(), value.getSellerAppId(), value.getMemberId())) {
            return false;
        }
        return true;
    }
}
