package cn.loveapp.uac.annotation;

/**
 * @program: uac-service-group
 * @description: PlatformId 校验平台名称是否存在
 * @author: <PERSON>
 * @create: 2020-03-04 15:53
 **/

import cn.loveapp.uac.annotation.processor.PlatformIdValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import jakarta.validation.constraints.NotEmpty;

import java.lang.annotation.*;

@Documented
@Target({ElementType.PARAMETER, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@NotEmpty
@Constraint(validatedBy = PlatformIdValidator.class)
public @interface CheckPlatformHasExist {

	String message() default "platform id 不存在";

	Class<?>[] groups() default {};

	Class<? extends Payload>[] payload() default {};
}
