package cn.loveapp.uac.annotation;

import cn.loveapp.uac.annotation.processor.AppValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import jakarta.validation.constraints.NotEmpty;

import java.lang.annotation.*;

/**
 * @program: uac-service-group
 * @description: AppType 校验入参的app应用名称是否存在
 * @author: Jason
 * @create: 2020-03-04 16:02
 **/
@Documented
@Target({ElementType.PARAMETER, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@NotEmpty
@Constraint(validatedBy = AppValidator.class)
public @interface CheckAppHasExist {
	String message() default "app name 不存在";

	Class<?>[] groups() default {};

	Class<? extends Payload>[] payload() default {};
}
