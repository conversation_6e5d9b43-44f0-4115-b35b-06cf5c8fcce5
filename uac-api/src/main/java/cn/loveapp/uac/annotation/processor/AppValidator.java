package cn.loveapp.uac.annotation.processor;

import cn.loveapp.uac.annotation.CheckAppHasExist;
import cn.loveapp.uac.utils.ValidatorUtils;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.Objects;

/**
 * @program: uac-service-group
 * @description: AppTypeValidator应用名称校验器
 * @author: Jason
 * @create: 2020-03-04 16:02
 **/
public class AppValidator implements ConstraintValidator<CheckAppHasExist, Object> {

	/**
	 * Initializes the validator in preparation for {@link #isValid(Object,
	 * ConstraintValidatorContext)} calls. The constraint annotation for a given constraint
	 * declaration is passed.
	 * <p>
	 * This method is guaranteed to be called before any use of this instance for validation.
	 * <p>
	 * The default implementation is a no-op.
	 *
	 * @param constraintAnnotation annotation instance for a given constraint declaration
	 */
	@Override
	public void initialize(CheckAppHasExist constraintAnnotation) {

	}

	/**
	 * Implements the validation logic. The state of {@code value} must not be altered.
	 * <p>
	 * This method can be accessed concurrently, thread-safety must be ensured by the implementation.
	 *
	 * @param o object to validate
	 * @param context context in which the constraint is evaluated
	 * @return {@code false} if {@code value} does not pass the constraint
	 */
	@Override
	public boolean isValid(Object o, ConstraintValidatorContext context) {
		if (Objects.isNull(o)) {
			return false;
		}
		return ValidatorUtils.checkApp(o.toString());
	}
}
