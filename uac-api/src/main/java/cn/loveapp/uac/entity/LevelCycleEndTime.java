package cn.loveapp.uac.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/***
 * <AUTHOR>
 * @Description 版本和剩余天数实体（版本赠送）
 * @Date 14:55 2024/1/5
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LevelCycleEndTime {
    /**
     * 版本等级
     */
    private int level;

    /**
     * 赠送剩余天数（经过拆分后的实际剩余天数）
     */
    private Integer cycleDays;
}
