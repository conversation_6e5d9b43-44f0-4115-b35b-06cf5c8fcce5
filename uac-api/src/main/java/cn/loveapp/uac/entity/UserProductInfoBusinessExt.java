package cn.loveapp.uac.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 数据落库状态记录表(user_productinfo_xxx_ext)实体类
 *
 * <AUTHOR>
 * @since 2018-12-24 15:57:33
 */
@Data
public class UserProductInfoBusinessExt implements Serializable {
    private static final long serialVersionUID = -13290625055810639L;

    private Integer id;
	/**
	 * 商家id
	 */
    private String sellerId;
	/**
	 * 用户nick
	 */
    private String sellerNick;
	/**
	 * 店铺id
	 */
    private String storeId;

    private String corpId;

	/**
	 * 存数据状态
	 */
    private Integer dbStatus;

	/**
	 * top状态
	 */
    private String topStatus;

	/**
	 * 用户要存的数据量
	 */
	private Integer topTradeCount;

	/**
	 * topsession状态
	 */
	private Integer sessionStatus;

	/**
	 * 降级标
	 */
    private String downgradeTag;

	/**
	 * 数据库ID
	 */
	private Integer dbId;

	/**
	 * 创建时间
	 */
    private LocalDateTime gmtCreate;

	/**
	 * 最后修改时间
	 */
    private LocalDateTime gmtModified;

	/**
	 * 拉数据状态
	 */
	private Integer pullStatus;

	private Integer apiStatus;

	/**
	 * 拉数据开始时间
	 */
	private LocalDateTime pullStartDateTime;

	/**
	 * 拉数据结束时间
	 */
	private LocalDateTime pullEndDateTime;

	/**
	 * 拉取数据末尾标识
	 */
	private Boolean pullEndPoint;

	/**
	 * 开通的应用
	 */
	private String appName;

	/**
	 * 1688 memberId 或 tradeSupplier的supplierId
	 */
	private String memberId;

	/**
	 * 产品业务（distribute,item,...）
	 */
	private String products;

    public static final Integer DB_WAIT = 101;
    public static final Integer DB_DOING = 102;
    public static final Integer DB_FAILED = -101;
	public static final Integer DB_FAILED_BY_WEB = -102;
	public static final Integer DB_FAILED_BY_SESSION = -103;
    public static final Integer DB_DONE = 10;

	public static final Integer API_STATUS_FAILED_BY_SESSION = -201;
	public static final Integer API_STATUS_SUCCESS = 10;

	/**
	 * check api status信息
	 * @param apiStatus
	 * @return
	 */
	public static Boolean checkApiStatus(Integer apiStatus) {
		return Objects.equals(API_STATUS_FAILED_BY_SESSION, apiStatus);
	}

	public Boolean isSuccess() {
		return topStatus.equals(DB_DONE.toString()) && dbStatus.equals(DB_DONE) && pullStatus.equals(DB_DONE);
	}

}
