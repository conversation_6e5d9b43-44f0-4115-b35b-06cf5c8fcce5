package cn.loveapp.uac.exception;

/**
 * BaseException
 * <AUTHOR>
 * @date 2019-01-29
 */
public abstract class BaseException extends Exception {
	private int code;

	public BaseException(int code, String msg){
		super(msg);
		this.code = code;
	}

	public BaseException(int code, String msg, Throwable ex){
		super(msg, ex);
		this.code = code;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

}
