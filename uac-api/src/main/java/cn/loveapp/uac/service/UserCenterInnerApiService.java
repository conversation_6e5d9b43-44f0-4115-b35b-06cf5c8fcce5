package cn.loveapp.uac.service;

import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.uac.domain.UserSettingDTO;
import cn.loveapp.uac.request.*;
import cn.loveapp.uac.response.*;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * @program: uac-service-group
 * @description: UserCenterInnerApiService
 * @author: Jason
 * @create: 2020-06-02 15:41
 **/
@FeignClient(name = "usercenter", url = "${loveapp.service.rpc.usercenter.host:http://127.0.0.1:8080}", path = UserCenterInnerApiService.PATH)
public interface UserCenterInnerApiService {
	String PATH = "export/uac/user";

	/**
	 * 获取用户的accessToken
	 * @param userInfoRequest
	 * @return
	 */
	@RequestMapping(value = "getTopSession", method = {RequestMethod.POST})
	CommonApiResponse<UserInfoResponse> getTopSession(@RequestBody @Valid UserInfoRequest userInfoRequest) throws Exception;

    /**
     * 获取指定vip等级的用户列表
     * @param listUserByVipInfoRequest
     * @return
     */
    @RequestMapping(value = "listUserByVipInfo", method = {RequestMethod.POST})
    CommonApiResponse<ListUserByVipInfoResponse> listUserByVipInfo(@RequestBody @Valid ListUserByVipInfoRequest listUserByVipInfoRequest) throws Exception;

	/**
	 * 批量获取用户的accessToken
	 * @param userInfoRequestList
	 * @return
	 */
	@RequestMapping(value = "batchGetTopSession", method = {RequestMethod.POST})
	CommonApiResponse<List<UserInfoResponse>> batchGetTopSession(@RequestBody @Valid BatchRequest<UserInfoRequest> userInfoRequestList) throws Exception;

	/**
	 * 重建用户信息
	 * @param userInfoRequest
	 * @return
	 */
	@RequestMapping(value = "rebuildUserInfo", method = {RequestMethod.POST})
	CommonApiResponse<UserInfoResponse> rebuildUserInfo(@RequestBody @Valid UserInfoRequest userInfoRequest);

	/**
	 * 更新用户缓存指定key的值
	 * @param request
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "batchUpdateUserCacheInfo", method = {RequestMethod.POST})
	CommonApiResponse<BatchUpdateUserCacheInfoResponse> batchUpdateUserCacheInfo(@RequestBody @Valid BatchUpdateUserCacheInfoRequest request) throws Exception;


	@RequestMapping(value = "batchGetUserCacheInfo", method = {RequestMethod.POST})
	CommonApiResponse<List<UserCacheInfoResponse>> batchGetUserCacheInfo(@RequestBody @Valid BatchGetUserCacheInfoRequest request) throws Exception;

	/**
	 * 根据memberid获取用户授权信息
	 * @param userInfoRequest
	 * @return
	 */
	@RequestMapping(value = "getTopSessionByMemberId", method = {RequestMethod.POST})
	CommonApiResponse<UserInfoTopSessionMemberResponse> getTopSessionByMemberId(
        @RequestBody @Valid UserinfoTopSessionMemberRequest userInfoRequest) throws Exception;

	/**
	 * 获取用户基本信息
	 * @param userInfoRequest
	 * @return
	 */
	@RequestMapping(value = "getUserInfo", method = {RequestMethod.POST})
	CommonApiResponse<UserInfoResponse> getUserInfo(@RequestBody @Valid UserInfoRequest userInfoRequest);

	/**
	 * 获取用户全部信息
	 * @param userFullInfoRequest
	 * @return
	 */
	@RequestMapping(value = "getUserFullInfo", method = {RequestMethod.POST})
	CommonApiResponse<UserFullInfoResponse> getUserFullInfo(@RequestBody @Valid UserFullInfoRequest userFullInfoRequest) throws Exception;

	/**
	 * 批量获取用户全部信息
	 *
	 * @param batchGetUserFullInfoRequest
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "batchGetUserFullInfo", method = {RequestMethod.POST})
	CommonApiResponse<List<UserFullInfoResponse>> batchGetUserFullInfo(@RequestBody @Valid BatchGetUserFullInfoRequest batchGetUserFullInfoRequest) throws Exception;

	/**
	 * 根据memberid获取用户信息
	 * @param userInfoRequest
	 * @return
	 */
	@RequestMapping(value = "getUserInfoByMemberId", method = {RequestMethod.POST})
	CommonApiResponse<UserInfoMemberResponse> getUserInfoByMemberId(@RequestBody @Valid UserInfoMemberRequest userInfoRequest);

	/**
	 * 批量获取用户基本信息
	 * @param userInfoRequestList
	 * @return
	 */
	@RequestMapping(value = "batchGetUserInfo", method = {RequestMethod.POST})
	CommonApiResponse<List<UserInfoResponse>> batchGetUserInfo(@RequestBody List<UserInfoRequest> userInfoRequestList);

	/**
	 * 批量更新用户配置
	 *
	 * @param batchSettingUpdateRequest
	 * @return
	 */
	@RequestMapping(value = "batch.setting.update", method = {RequestMethod.POST})
	CommonApiResponse<Void> batchSettingUpdate(@RequestBody BatchSettingUpdateRequest batchSettingUpdateRequest);

	/**
	 * 批量读取用户配置
	 *
	 * @param batchSettingGetRequest
	 * @return
	 */
	@RequestMapping(value = "batch.setting.get", method = {RequestMethod.POST})
	CommonApiResponse<List<UserSettingDTO>> batchSettingGet(@RequestBody BatchSettingGetRequest batchSettingGetRequest);

    /**
     * 批量读取用户配置
     *
     * @param batchUsersSettingGetRequest
     * @return
     */
    @RequestMapping(value = "batch.users.setting.get", method = {RequestMethod.POST})
    CommonApiResponse<List<BatchUsersSettingGetResponse>>
        batchUsersSettingGet(@RequestBody BatchUsersSettingGetRequest batchUsersSettingGetRequest);

	/**
	 * 复制用户设置和用户打标
	 *
	 * @param userSettingCopyRequest
	 * @return
	 */
	@RequestMapping(value = "user.setting.copy", method = {RequestMethod.POST})
	CommonApiResponse<Void> userSettingCopy(@RequestBody @Valid UserSettingCopyRequest userSettingCopyRequest);

	/**
	 * 批量用户打标
	 *
	 * @param batchMultiUserTagUpdateRequest
	 * @return
	 */
	@RequestMapping(value = "batch.multiuserTag.update", method = {RequestMethod.POST})
	CommonApiResponse<Void> batchMultiuserTagUpdate(@RequestBody BatchMultiUserTagUpdateRequest batchMultiUserTagUpdateRequest);

    /**
     * 获取店铺信息
     * @param request
     * @return
     */
    @RequestMapping(value = "getUserShopInfo", method = {RequestMethod.POST})
    CommonApiResponse<UserShopInfoGetResponse> getUserShopInfo(@RequestBody UserShopInfoGetRequest request);


	/**
	 * 根据店铺名获取店铺
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getUserByMallName", method = {RequestMethod.POST})
	CommonApiResponse<UserFullInfoResponse> getUserByMallName(@RequestBody UserInfoRequest request);

	/**
	 * 批量获取用户登陆信息
	 * @param userInfoRequestList
	 * @return
	 */
	@RequestMapping(value = "batchGetUserLoginInfo", method = {RequestMethod.POST})
	CommonApiResponse<List<UserInfoResponse>> batchGetUserLoginInfo(@RequestBody List<UserInfoRequest> userInfoRequestList);

}
