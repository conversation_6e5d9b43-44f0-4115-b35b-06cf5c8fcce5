package cn.loveapp.uac.service;

import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.uac.request.UserOrderSearchRequest;
import cn.loveapp.uac.response.UserOrderSearchResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;


/**
 * <AUTHOR>
 * @Date 2024/8/15 4:06 PM
 */
@FeignClient(name = "userOrderSearchService", url = "${loveapp.service.rpc.userOrderSearchService.host:http://127.0.0.1:8080}", path = UserOrderSearchApiService.PATH)
public interface UserOrderSearchApiService {

    String PATH = "export/uac/ordersearch";

    /**
     * 获取用户订购记录
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "getUserOrderSearch", method = {RequestMethod.POST})
    CommonApiResponse<UserOrderSearchResponse> getUserOrderSearch(@RequestBody UserOrderSearchRequest request);

}
