package cn.loveapp.uac.service;

import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.uac.request.GetUserInfoExtRequest;
import cn.loveapp.uac.request.UpdateUserInfoExtRequest;
import cn.loveapp.uac.response.GetUserInfoExtResponse;
import cn.loveapp.uac.response.UpdateUserInfoExtResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * @Author: zhongzijie
 * @Date: 2023/2/15 18:36
 * @Description: userExt
 */
@FeignClient(name = "userExtService", url = "${loveapp.service.rpc.userExtService.host:http://127.0.0.1:8080}", path = UserProductInfoExtApiService.PATH)
public interface UserProductInfoExtApiService {

    String PATH = "export/uac/ext";

    /**
     * 获取单个用户ext信息
     * @param request
     * @return
     */
    @RequestMapping(value = "getUserInfoExtBySellerId", method = {RequestMethod.POST})
    CommonApiResponse<GetUserInfoExtResponse> getUserInfoExtBySellerId(@RequestBody GetUserInfoExtRequest request);

    /**
     * 更新单个用户ext信息
     * @param request
     * @return
     */
    @RequestMapping(value = "updateUserExtBySellerId", method = {RequestMethod.POST})
    CommonApiResponse<UpdateUserInfoExtResponse> updateUserInfoExtBySellerId(@RequestBody UpdateUserInfoExtRequest request);
}
