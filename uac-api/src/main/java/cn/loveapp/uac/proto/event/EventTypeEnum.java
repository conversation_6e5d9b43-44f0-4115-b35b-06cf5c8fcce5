package cn.loveapp.uac.proto.event;


/**
 * 事件类型枚举
 */
public enum EventTypeEnum {

    /**
     * 用户等级变更事件
     */
    USER_LEVEL_CHANGED;

    public static EventTypeEnum getTypeByValues(String Name) {
        if (null == Name) {
            return null;
        }
        for (EventTypeEnum enums : EventTypeEnum.values()) {
            if (enums.name().equals(Name)) {
                return enums;
            }
        }
        return null;
    }

}
