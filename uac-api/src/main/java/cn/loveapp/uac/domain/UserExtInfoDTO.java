package cn.loveapp.uac.domain;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/2/15 18:49
 * @Description: 用户ext信息体
 */
@Data
public class UserExtInfoDTO {

    /**
     * 商家id
     */
    private String sellerId;
    /**
     * 用户nick
     */
    private String sellerNick;
    /**
     * 店铺id
     */
    private String storeId;

    private String corpId;

    /**
     * top状态
     */
    private String topStatus;
    /**
     * 用户要存的数据量
     */
    private Integer topTradeCount;

    /**
     * 数据库ID
     */
    private Integer dbId;

    /**
     * db状态
     */
    private Integer dbStatus;

    /**
     * 拉数据状态
     */
    private Integer pullStatus;

    private Integer apiStatus;

    /**
     * 拉数据开始时间
     */
    private LocalDateTime pullStartDateTime;

    /**
     * 拉数据结束时间
     */
    private LocalDateTime pullEndDateTime;

    /**
     * 拉数据末尾标识
     */
    private Boolean pullEndPoint;

    /**
     * 开通的应用
     */
    private String appName;

    /**
     * 1688 memberId 或 tradeSupplier的supplierId
     */
    private String memberId;

    /**
     * 产品业务（distribute,item,...）
     */
    private List<String> products;
}
