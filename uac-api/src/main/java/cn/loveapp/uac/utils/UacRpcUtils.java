package cn.loveapp.uac.utils;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.uac.domain.UserExtInfoDTO;
import cn.loveapp.uac.entity.UserProductInfoBusinessExt;
import cn.loveapp.uac.request.BatchGetUserCacheInfoRequest;
import cn.loveapp.uac.request.GetUserInfoExtRequest;
import cn.loveapp.uac.request.UserInfoRequest;
import cn.loveapp.uac.response.GetUserInfoExtResponse;
import cn.loveapp.uac.response.UserCacheInfoResponse;
import cn.loveapp.uac.service.UserCenterInnerApiService;
import cn.loveapp.uac.service.UserProductInfoExtApiService;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户中心rpc调用工具
 *
 * <AUTHOR>
 * @date 2023/7/27
 */
public class UacRpcUtils {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(UacRpcUtils.class);

    /**
     * redis string类型empty标记
     */
    private static final String REDIS_EMPTY_STRING = "_REDIS_EMPTY_";

    /**
     * 批量获取用户缓存, 如果缓存key无法生成, 直接调用rpc接口获取
     *
     * <AUTHOR>
     * @date 2023/7/27
     */
    public static CommonApiResponse<List<UserCacheInfoResponse>> batchGetUserCacheInfo(BatchGetUserCacheInfoRequest request, UserCenterInnerApiService apiService, StringRedisTemplate stringRedisTemplate) throws Exception {
        List<Pair<UserInfoRequest, String>> userCacheKeyList = null;
        boolean userRpc = false;
        for (UserInfoRequest userInfoRequest : request.getRequestList()) {
            String userCacheKey = UserCacheUtils.userCacheKey(userInfoRequest.getSellerNick(), userInfoRequest.getSellerId(), userInfoRequest.getPlatformId(), userInfoRequest.getApp());
            if (userCacheKey == null) {
                userRpc = true;
                break;
            }
            if (userCacheKeyList == null) {
                userCacheKeyList = Lists.newArrayList();
            }
            userCacheKeyList.add(Pair.of(userInfoRequest, userCacheKey));
        }
        if (userRpc) {
            return apiService.batchGetUserCacheInfo(request);
        }
        if (CollectionUtils.isEmpty(userCacheKeyList)) {
            return CommonApiResponse.success(ImmutableList.of());
        }
        List<UserCacheInfoResponse> responseList = Lists.newArrayListWithExpectedSize(userCacheKeyList.size());
        HashOperations<String, String, String> operations = stringRedisTemplate.opsForHash();
        for (Pair<UserInfoRequest, String> user : userCacheKeyList) {
            UserCacheInfoResponse userCacheInfoResponse = new UserCacheInfoResponse();
            String cacheValue;
            // TODO: 暂时兼容获取 shops_tag 时追加 ayMultiTags 的内容, 后面逐步废弃 shops_tag
            // {@link cn.loveapp.uac.service.service.impl.SellerServiceImpl#batchGetUserCacheInfo}
            if(UserCacheUtils.HKEY_SHOPS_TAG.equals(request.getCacheHkey())) {
                List<String> values = operations.multiGet(user.getValue(), Lists.newArrayList(request.getCacheHkey(), UserCacheUtils.HKEY_AY_MULTI_TAGS));
                cacheValue = StringUtils.defaultIfEmpty(values.stream().filter(Objects::nonNull).collect(Collectors.joining(",")), null);
            } else {
                cacheValue = operations.get(user.getValue(), request.getCacheHkey());
            }
            UserInfoRequest userInfoRequest = user.getKey();
            userCacheInfoResponse.setCacheValue(cacheValue);
            userCacheInfoResponse.setSellerNick(userInfoRequest.getSellerNick());
            userCacheInfoResponse.setSellerId(userInfoRequest.getSellerId());
            userCacheInfoResponse.setPlatformId(userInfoRequest.getPlatformId());
            userCacheInfoResponse.setAppName(userInfoRequest.getApp());
            responseList.add(userCacheInfoResponse);
        }
        return CommonApiResponse.success(responseList);
    }

    /**
     * 获取用户ext信息缓存，如果缓存取不到，则调uac接口获取
     *
     * @param request
     * @param userProductInfoExtApiService
     * @param stringRedisTemplate
     * @return
     */
    public static CommonApiResponse<GetUserInfoExtResponse> getUserInfoExtCacheBySellerId(GetUserInfoExtRequest request, UserProductInfoExtApiService userProductInfoExtApiService, StringRedisTemplate stringRedisTemplate) {
        String sellerId = request.getSellerId();
        String storeId = request.getStoreId();
        String appName = request.getAppName();
        if (StringUtils.isEmpty(appName) && CommonPlatformConstants.PLATFORM_TAO.equals(storeId)) {
            appName = CommonAppConstants.APP_TRADE;
            request.setAppName(appName);
        }
        String businessId = request.getBusinessId();
        String memberId = request.getMemberId();
        boolean userRpc = false;
        String cacheValue = null;
        GetUserInfoExtResponse getUserInfoExtResponse = new GetUserInfoExtResponse();
        if (CommonPlatformConstants.PLATFORM_1688.equals(storeId) && StringUtils.isEmpty(sellerId)
                && !StringUtils.isEmpty(memberId)) {
            userRpc = true;
        } else {
            String userExtCacheKey = UserCacheUtils.userExtCacheKey(sellerId, storeId, appName, businessId);
            ValueOperations<String, String> opsForValue = stringRedisTemplate.opsForValue();
            cacheValue = opsForValue.get(userExtCacheKey);
            if (StringUtils.isEmpty(cacheValue)) {
                userRpc = true;
            } else if (REDIS_EMPTY_STRING.equals(cacheValue)) {
                return CommonApiResponse.success(getUserInfoExtResponse);
            }
        }
        if (userRpc) {
            LOGGER.logInfo("远程调用 getUserInfoExtBySellerId");
            return userProductInfoExtApiService.getUserInfoExtBySellerId(request);
        } else {
            UserProductInfoBusinessExt userProductInfoBusinessExt = JSON.parseObject(cacheValue, UserProductInfoBusinessExt.class);
            UserExtInfoDTO userExtInfoDTO = new UserExtInfoDTO();
            String products = userProductInfoBusinessExt.getProducts();
            if (StringUtils.isNotEmpty(products)) {
                userExtInfoDTO.setProducts(Arrays.stream(products.split(",")).collect(Collectors.toList()));
            }
            BeanUtils.copyProperties(userProductInfoBusinessExt, userExtInfoDTO);
            getUserInfoExtResponse.setUserInfoExt(userExtInfoDTO);
            return CommonApiResponse.success(getUserInfoExtResponse);
        }
    }

}
