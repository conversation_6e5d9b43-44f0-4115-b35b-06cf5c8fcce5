package cn.loveapp.uac.utils;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import org.apache.commons.lang3.StringUtils;

/**
 * @program: uac-service-group
 * @description: ValidatorUtils
 * @author: <PERSON>
 * @create: 2020-03-04 15:59
 **/
public class ValidatorUtils {

	public static boolean checkPlatform(String platformId) {
		if (StringUtils.isBlank(platformId)) {
			return false;
		}
		return CommonPlatformConstants.getPlatform().contains(platformId);
	}

	public static boolean checkApp(String app) {
		if (StringUtils.isBlank(app)) {
			return false;
		}
		return CommonAppConstants.getApp().contains(app);
	}

}
