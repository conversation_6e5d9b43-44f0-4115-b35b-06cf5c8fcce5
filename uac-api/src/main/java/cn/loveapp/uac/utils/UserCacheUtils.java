package cn.loveapp.uac.utils;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import org.apache.commons.lang3.StringUtils;

import java.net.URLEncoder;

/**
 * 用户缓存工具
 *
 * <AUTHOR>
 * @date 2023/7/27
 */
public class UserCacheUtils {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(UserCacheUtils.class);

    public static final String HKEY_SHOPS_TAG = "shops_tag";
    public static final String HKEY_AY_MULTI_TAGS = "ayMultiTags";

    /**
     * 获取用户缓存key
     * @param sellerNick
     * @param sellerId
     * @param platformId
     * @param appName
     * @return
     */
    public static String userCacheKey(String sellerNick, String sellerId, String platformId, String appName) {
        boolean isTaoOldApp = CommonPlatformConstants.PLATFORM_TAO.equals(platformId) &&
                ((CommonAppConstants.APP_TRADE.equals(appName))
                        || CommonAppConstants.APP_ITEM.equals(appName)
                        || CommonAppConstants.APP_SHOP_HELPER.equals(appName));
        boolean isPddOldApp = CommonPlatformConstants.PLATFORM_PDD.equals(platformId) &&
                ((CommonAppConstants.APP_TRADE.equals(appName)) || CommonAppConstants.APP_ITEM.equals(appName)
                        || CommonAppConstants.APP_GUANDIAN.equals(appName)
                        || CommonAppConstants.APP_DISTRIBUTE.equals(appName));
        String key = null;
        try {
            if (isTaoOldApp || isPddOldApp) {
                if (StringUtils.isEmpty(sellerNick)) {
                    LOGGER.logError("缺少sellerNick, 无法初始化Key");
                    return null;
                }
                // 旧的应用 使用的 sellerNick 做redis key
                key = URLEncoder.encode(sellerNick, "utf-8");
            } else if (CommonAppConstants.APP_DISTRIBUTE.equals(appName)) {
                // 代发应用 redis 不统一, 不能直接拼接
                return null;
            } else {
                if (CommonPlatformConstants.PLATFORM_TIKTOK.equals(platformId)) {
                    if (StringUtils.isEmpty(sellerId)) {
                        LOGGER.logError("缺少sellerId, 无法初始化Key");
                        return null;
                    }
                    key = platformId + ":" + appName + ":" + sellerId;
                } else {
                    if (StringUtils.isEmpty(sellerNick)) {
                        LOGGER.logError("缺少sellerNick, 无法初始化Key");
                        return null;
                    }
                    // 新的应用 统一使用 platformId:appName:sellerNick 做redis key
                    key = platformId + ":" + appName + ":" + sellerNick;
                }

            }
            LOGGER.logInfo(platformId, appName, "redis初始化Key为: " + key);
            return key;
        } catch (Exception e) {
            LOGGER.logError(sellerNick, sellerId, "初始化Key失败: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取用户ext缓存key
     *
     * @param sellerId
     * @param platformId
     * @param appName
     * @param businessId
     * @return
     */
    public static String userExtCacheKey(String sellerId, String platformId, String appName, String businessId) {
        return "userext:" + businessId + ":" + platformId + ':' + appName + ':' + sellerId;
    }

}
