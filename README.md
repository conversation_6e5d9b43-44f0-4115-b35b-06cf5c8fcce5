# 爱用UAC用户中心服务组

## 项目概述

爱用UAC（User Access Control）用户中心服务组是爱用科技的核心用户管理系统，提供用户认证、授权、账户管理等功能。该项目采用微服务架构，基于Spring Boot框架构建，支持多平台用户统一管理。

## 项目架构

本项目采用Maven多模块架构，包含以下核心模块：

```
uac-service-group/
├── uac-api/                   # 二方接口定义模块
├── uac-newuser-api/           # 新用户API接口模块 (uac 内部使用)
├── uac-common/                # 通用组件模块
├── uac-service/               # 主服务模块
├── uac-job/                   # 定时任务模块
│   └── uac-authorization-job/ # 授权相关定时任务
├── uac-db-common/             # 数据库通用模块
├── uac-service-common/        # 服务通用模块
├── uac-newusers/              # 新用户管理模块
│   ├── uac-newuser-common/    # 新用户通用组件
│   ├── uac-newuser-scheduler/ # 新用户定时任务
│   └── uac-newuser-service/   # 新用户服务
└── uac-domain/                # 领域模型模块
```

## 技术栈

- **Java**: JDK 24+
- **框架**: Spring Boot 3.x
- **配置中心**: Apollo
- **缓存**: Redis, Caffeine
- **消息队列**: RocketMQ
- **数据库**: MySQL, MyBatis
- **其他**: Lombok, FastJSON2, Guava

## JDK 24 支持说明

### 环境要求
- **JDK版本**: JDK 24 (向下兼容JDK 8+)
- **Maven版本**: 3.8.0+ (推荐3.9.0+)

### JDK 24 配置说明

#### 3. JVM启动参数优化（JDK 24）
```bash
# 推荐的JVM参数配置
--add-opens=java.base/java.nio=ALL-UNNAMED
--add-exports=java.base/sun.security.action=ALL-UNNAMED
--add-exports=java.base/sun.reflect.misc=ALL-UNNAMED
--add-opens=java.base/java.lang=ALL-UNNAMED
--add-opens=java.base/java.math=ALL-UNNAMED
--add-opens=java.base/java.util=ALL-UNNAMED
--add-opens=java.base/sun.util.calendar=ALL-UNNAMED
--add-opens=java.base/jdk.internal.misc=ALL-UNNAMED
--enable-native-access=ALL-UNNAMED
--sun-misc-unsafe-memory-access=allow
```

## 日志文件
- **应用日志**: `logs/info.log`

## 模块详细说明

### uac-service (主服务模块)
- **功能**: 提供用户认证、授权、账户管理等核心API
- **启动类**: `cn.loveapp.uac.service.UacServiceApplication`
- **主要功能**:
  - 用户登录认证
  - 权限验证
  - 账户信息管理
  - 平台集成接口

### uac-newuser-service (新用户服务)
- **功能**: 处理新用户注册、开通等业务
- **启动类**: `cn.loveapp.uac.newuser.service.UacNewUserServiceApplication`

### uac-newuser-scheduler (新用户定时任务)
- **功能**: 处理新用户相关的定时任务
- **启动类**: `cn.loveapp.uac.newuser.scheduler.UacNewUserSchedulerApplication`

### uac-authorization-job (授权定时任务)
- **功能**: 处理用户授权相关的定时任务
- **启动类**: `cn.loveapp.uac.authorization.UacAuthorizationJobApplication`
