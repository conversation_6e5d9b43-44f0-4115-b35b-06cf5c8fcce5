package cn.loveapp.uac.authorization.task;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.properties.*;
import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.authorization.config.RefreshAccessTokenTaskConfig;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.service.service.SellerService;
import cn.loveapp.uac.db.common.repository.UserRepository;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: uac-service-group
 * @description: RefreshAccessTokenTask
 * @author: Jason
 * @create: 2020-09-21 15:51
 **/
@Component
public class RefreshAccessTokenTask {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(RefreshAccessTokenTask.class);


    @Autowired
    private UserRepository userRepository;

    @Autowired
    private SellerService sellerService;

    private final RefreshAccessTokenTaskConfig refreshAccessTokenTaskConfig;
    
    @Autowired
    private PlatformDoudianProperties doudianProperties;

    @Autowired
    private Platform1688Properties ali1688Properties;

    @Autowired
    private PlatformKwaishopProperties kwaishopProperties;

    @Autowired
    private PlatformWxshopProperties wxshopProperties;

    @Autowired
    private PlatformWxvideoshopProperties wxvideoshopProperties;

    @Autowired
    private PlatformYouzanProperties youzanProperties;

    @Autowired
    private PlatformXhsProperties xhsProperties;


    private final Integer offset = 0;

    private final Integer limit = 1000;

    public RefreshAccessTokenTask(RefreshAccessTokenTaskConfig refreshAccessTokenTaskConfig) {
        this.refreshAccessTokenTaskConfig = refreshAccessTokenTaskConfig;
    }

    @Scheduled(cron = "${uac.refresh.access.token.taobao.task.cron: 0 0 0 * * *}")
    public void scanTaoBaoNeedAuthAction() {
        String jobName = "TaoBaoNeedAuthJob";
        if (!refreshAccessTokenTaskConfig.getRefreshAccessTokenSwitch()) {
            LOGGER.logInfo(jobName + " 忽略job");
            return;
        }
        String platformId = CommonPlatformConstants.PLATFORM_TAO;
        String appName = CommonAppConstants.APP_TRADE;
        refreshUserToken((int maxId) -> userRepository.queryByLevel(UserProductInfo.LEVEL_ONE,
                BooleanUtils.toBoolean(UserProductInfo.NEED_AUTH), maxId, offset, limit, platformId, appName), jobName,
            platformId, appName);
    }

    @Scheduled(cron = "${uac.refresh.expired.access.token.taobao.task.cron: 0 0 2 * * *}")
    public void scanTaoBaoAuthorizationExpiredAction() {
        String jobName = "TaoBaoAuthorizationExpiredJob";
        if (!refreshAccessTokenTaskConfig.getRefreshAccessTokenSwitch()) {
            LOGGER.logInfo(jobName + " 忽略job");
            return;
        }
        String platformId = CommonPlatformConstants.PLATFORM_TAO;
        String appName = CommonAppConstants.APP_TRADE;
        LocalDateTime expireDeadLine = DateUtil.getNowMin();
        refreshUserToken(
            (int maxId) -> userRepository.queryByLevelAndW1DeadLine(UserProductInfo.LEVEL_ONE, expireDeadLine, maxId,
                offset, limit, platformId, appName), jobName, platformId, appName);
    }

    /**
     * 淘宝供货商 token 刷新任务
     */
    @Scheduled(cron = "${uac.refresh.expired.access.token.taobao-supplier.task.cron: 0 0 0/1 * * *}")
    public void scanTaoBaoSupplierAuthorizationExpiredAction() {
        String platformId = CommonPlatformConstants.PLATFORM_TAO;
        String jobName = "TaoBaoSupplierAuthorizationExpiredJob";
        LOGGER.logInfo("执行扫描" + platformId + " 供货商-token即将到期的用户");
        long startTime = System.currentTimeMillis();
        if (!refreshAccessTokenTaskConfig.getTaobaoSupplierRefreshAccessTokenSwitch()) {
            LOGGER.logInfo(jobName + " 忽略job");
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime w1DeadLineStart = now.minusHours(1);
        LocalDateTime w1DeadLineEnd = now.plusHours(2);
        LocalDateTime orderCycleEnd = now.minusMinutes(10).minusSeconds(0);
        String appName = CommonAppConstants.APP_TRADE_SUPPLIER;
        String finalName = jobName + appName;
        refreshUserToken(
                (int maxId) -> userRepository.queryByW1DeadLineAndOrderCycleEnd(w1DeadLineStart, w1DeadLineEnd, orderCycleEnd, maxId,
                        offset, limit, platformId, appName), finalName, platformId, appName);
        long end = System.currentTimeMillis();
        LOGGER.logInfo(platformId + "-刷新token结束，耗时：" + ((end - startTime) / 1000) + "秒");
    }

    @Scheduled(cron = "${uac.refresh.expired.access.token.doudian.task.cron: 0 0 0/1 * * *}")
    public void scanDoudianAuthorizationExpiredAction() {
        doRefreshTokenByApps(CommonPlatformConstants.PLATFORM_DOUDIAN, doudianProperties,
                "douDianAuthorizationExpiredJob-",refreshAccessTokenTaskConfig.getDoudianRefreshAccessTokenSwitch());
    }

    /**
     * 1688 token 刷新 任务
     */
    @Scheduled(cron = "${uac.refresh.expired.access.token.ali1688.task.cron: 0 0 0/1 * * *}")
    public void scan1688AuthorizationExpiredAction() {
        doRefreshTokenByApps(CommonPlatformConstants.PLATFORM_1688, ali1688Properties,
                "ali1688AuthorizationExpiredJob-",refreshAccessTokenTaskConfig.getAli1688RefreshAccessTokenSwitch());
    }

    /**
     * kwaishop token 刷新 任务
     */
    @Scheduled(cron = "${uac.refresh.expired.access.token.kwaishop.task.cron: 0 0 0/1 * * *}")
    public void scanKwaishopAuthorizationExpiredAction() {
        //当access_token失效之前或者之后，都可以使用有效的refresh_token去获取一个新的access_token：
        doRefreshTokenByApps(CommonPlatformConstants.PLATFORM_KWAISHOP, kwaishopProperties,
                "kwaishopAuthorizationExpiredJob-",refreshAccessTokenTaskConfig.getKwaishopRefreshAccessTokenSwitch());

    }

    /**
     * wxshop token 刷新 任务
     */
    @Scheduled(cron = "${uac.refresh.expired.access.token.wxshop.task.cron: 0 0 0/1 * * *}")
    public void scanWxshopAuthorizationExpiredAction() {
        //当access_token失效之前或者之后，都可以使用有效的refresh_token去获取一个新的access_token：
        doRefreshTokenByApps(CommonPlatformConstants.PLATFORM_WXSHOP, wxshopProperties,
                "wxshopAuthorizationExpiredJob-",refreshAccessTokenTaskConfig.getWxshopRefreshAccessTokenSwitch());

    }

    /**
     * wxvideoshop token 刷新 任务
     */
    @Scheduled(cron = "${uac.refresh.expired.access.token.wxvideoshop.task.cron: 0 0 0/1 * * *}")
    public void scanWxvideoshopAuthorizationExpiredAction() {
        //当access_token失效之前或者之后，都可以使用有效的refresh_token去获取一个新的access_token：
        doRefreshTokenByApps(CommonPlatformConstants.PLATFORM_WXVIDEOSHOP, wxvideoshopProperties,
                "wxvideoshopAuthorizationExpiredJob-",refreshAccessTokenTaskConfig.getWxvideoshopRefreshAccessTokenSwitch());
    }

    /**
     * youzan token 刷新 任务
     */
    @Scheduled(cron = "${uac.refresh.expired.access.token.youzan.task.cron: 0 0 0/1 * * *}")
    public void scanYouzanAuthorizationExpiredAction() {
        //当access_token失效之前或者之后，都可以使用有效的refresh_token去获取一个新的access_token：
        doRefreshTokenByApps(CommonPlatformConstants.PLATFORM_YOUZAN, youzanProperties,
                "youzanAuthorizationExpiredJob-",refreshAccessTokenTaskConfig.getYouzanRefreshAccessTokenSwitch());

    }

    /**
     * xhs token 刷新 任务
     */
    @Scheduled(cron = "${uac.refresh.expired.access.token.xhs.task.cron: 0 */15 * * * *}")
    public void scanXhsAuthorizationExpiredAction() {
        //当access_token失效之前或者之后，都可以使用有效的refresh_token去获取一个新的access_token：
        doRefreshTokenByApps(CommonPlatformConstants.PLATFORM_XHS, xhsProperties,
                "xhsAuthorizationExpiredJob-",refreshAccessTokenTaskConfig.getXhsRefreshAccessTokenSwitch(),
                refreshAccessTokenTaskConfig.getXhsRefreshAccessTokenIntervalStart(), refreshAccessTokenTaskConfig.getXhsRefreshAccessTokenIntervalEnd());
    }

    private void doRefreshTokenByApps(String platformId,
                                      AbstractPlatformProperties properties,
                                      String jobName,
                                      Boolean switchConf) {
        doRefreshTokenByApps(platformId, properties, jobName, switchConf, 60, 120);
    }

    private void doRefreshTokenByApps(String platformId,
                                      AbstractPlatformProperties properties,
                                      String jobName, Boolean switchConf,
                                      long intervalStart,
                                      long intervalEnd) {
        LOGGER.logInfo("执行扫描" + platformId + "-token即将到期的用户");
        long startTime = System.currentTimeMillis();
        if (!switchConf) {
            LOGGER.logInfo(jobName + " 忽略job");
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime w1DeadLineStart = now.minusMinutes(intervalStart);
        LocalDateTime w1DeadLineEnd = now.plusMinutes(intervalEnd);
        LocalDateTime orderCycleEnd = now.minusMinutes(10).minusSeconds(0);
        for (String appName : properties.getApps().keySet()) {
            String finalName = jobName + appName;
            refreshUserToken(
                    (int maxId) -> userRepository.queryByW1DeadLineAndOrderCycleEnd(w1DeadLineStart, w1DeadLineEnd, orderCycleEnd, maxId,
                            offset, limit, platformId, appName), finalName, platformId, appName);
        }
        long end = System.currentTimeMillis();
        LOGGER.logInfo(platformId + "-刷新token结束，耗时：" + ((end - startTime) / 1000) + "秒");
    }


    private void refreshUserToken(QueryUserService queryUserService, String jobName, String platformId,
        String appName) {
        MDC.put("topic", jobName);
        try {
            LOGGER.logInfo(jobName + " 开始");
            int maxId = 0;
            while (true) {
                List<UserProductInfo> userProductInfos = queryUserService.query(maxId);
                if (CollectionUtils.isEmpty(userProductInfos)) {
                    break;
                }
                maxId = userProductInfos.stream().map(UserProductInfo::getId).max(Integer::compareTo).orElse(0);
                batchProcess(userProductInfos, jobName, platformId, appName);
                LOGGER.logInfo(jobName + " scan");
            }
            LOGGER.logInfo(jobName + " 生产处理完毕");
        } catch (Exception e) {
            LOGGER.logError(jobName + " 任务执行异常: " + e.getMessage(), e);
        }finally {
            LOGGER.logInfo(jobName + " 全部处理完毕");
            MDC.clear();
        }
    }

    private void batchProcess(List<UserProductInfo> userProductInfos, String jobName, String platformId, String appName) {
        MDC.put("topic", jobName);
        try {
            if(CollectionUtils.isEmpty(userProductInfos)){
                return;
            }
            LOGGER.logInfo(jobName + " 开始刷新token: " + userProductInfos.size());
            for (UserProductInfo userProductInfo : userProductInfos) {
                if (StringUtils.isBlank(userProductInfo.getToprefreshkey())) {
                    continue;
                }
                UserInfoBo userInfoBo =
                    new UserInfoBo(platformId, appName, userProductInfo.getNick(),
                            userProductInfo.getUserId() == null ? userProductInfo.getUserIdStr() : userProductInfo.getUserId().toString());
                userInfoBo.setLevel(userProductInfo.getVipflag());
                try {
                    String accessToken =
                        sellerService.refreshAccessToken(userInfoBo, userProductInfo.getToprefreshkey(), platformId,
                            appName);
                    LOGGER.logInfo(userInfoBo.getSellerNick(), "-", jobName + " token刷新完成, 新的token为<" + accessToken + ">");
                } catch (Exception e) {
                    LOGGER.logError(userInfoBo.getSellerNick(), userInfoBo.getSellerId(), e.getMessage(), e);
                }
            }
            LOGGER.logInfo(jobName + " token刷新完毕: " + userProductInfos.size());
        } catch (Exception e) {
            LOGGER.logError(jobName + " 刷新异常: " + e.getMessage(), e);
        }finally {
            MDC.clear();
        }
    }

    @FunctionalInterface
    public interface QueryUserService {
        List<UserProductInfo> query(int maxId);
    }
}
