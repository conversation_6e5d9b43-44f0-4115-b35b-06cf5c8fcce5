package cn.loveapp.uac.authorization;

import org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisReactiveAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * UacAuthorizationJobServiceApplication
 *
 * <AUTHOR>
 * @date 2021/8/13
 */
@EnableScheduling
@SpringBootApplication(exclude = {RedisAutoConfiguration.class, RedisRepositoriesAutoConfiguration.class,
	DataSourceAutoConfiguration.class, MybatisAutoConfiguration.class, RedisReactiveAutoConfiguration.class,
	DataSourceHealthContributorAutoConfiguration.class}, scanBasePackages = {"cn.loveapp.uac"})
public class UacAuthorizationJobApplication {

	public static void main(String[] args) {
		SpringApplication.run(UacAuthorizationJobApplication.class, args);
	}
}
