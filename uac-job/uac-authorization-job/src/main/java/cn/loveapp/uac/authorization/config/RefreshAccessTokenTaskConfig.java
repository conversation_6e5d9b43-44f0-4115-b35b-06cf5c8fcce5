package cn.loveapp.uac.authorization.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @program: uac-service-group
 * @description:
 * @author: Jason
 * @create: 2020-09-21 15:55
 **/
@Configuration
@Data
public class RefreshAccessTokenTaskConfig {

	@Value("${uac.refresh.access.token.task.pool.size.config:1}")
	private Integer poolSize;

	@Value("${uac.refresh.access.token.task.core.size.config:1}")
	private Integer coreSize;

	@Value("${uac.refresh.access.token.taobao.switch.config:false}")
	private Boolean refreshAccessTokenSwitch;

	@Value("${uac.refresh.access.token.taobao-supplier.switch.config:false}")
	private Boolean taobaoSupplierRefreshAccessTokenSwitch;

	@Value("${uac.refresh.access.token.doudian.switch.config:false}")
	private Boolean doudianRefreshAccessTokenSwitch;

	@Value("${uac.refresh.access.token.ali1688.switch.config:false}")
	private Boolean ali1688RefreshAccessTokenSwitch;

    @Value("${uac.refresh.access.token.kwaishop.switch.config:false}")
    private Boolean kwaishopRefreshAccessTokenSwitch;

	@Value("${uac.refresh.access.token.wxshop.switch.config:false}")
	private Boolean wxshopRefreshAccessTokenSwitch;

	@Value("${uac.refresh.access.token.wxvideoshop.switch.config:false}")
	private Boolean wxvideoshopRefreshAccessTokenSwitch;

    @Value("${uac.refresh.access.token.youzan.switch.config:false}")
    private Boolean youzanRefreshAccessTokenSwitch;

	/**
	 * 小红书定时刷新session开关
	 */
	@Value("${uac.refresh.access.token.xhs.switch.config:false}")
	private Boolean xhsRefreshAccessTokenSwitch;

	/**
	 * 扫描-提前时间（分钟）
	 */
	@Value("${uac.refresh.access.token.xhs.xhsRefreshAccessTokenIntervalStart:30}")
	private long xhsRefreshAccessTokenIntervalStart;

	/**
	 * 扫描-后推时间（分钟）
	 */
	@Value("${uac.refresh.access.token.xhs.xhsRefreshAccessTokenIntervalEnd:30}")
	private long xhsRefreshAccessTokenIntervalEnd;


    @Value("${uac.redis.clean.switch.config:false}")
    private Boolean redisCleanSwitch;

    @Value("${uac.redis.clean.threadPoolSize:1}")
    private int redisCleanThreadPoolSize;
}
