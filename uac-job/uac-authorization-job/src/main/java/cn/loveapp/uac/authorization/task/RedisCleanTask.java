package cn.loveapp.uac.authorization.task;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import cn.loveapp.uac.authorization.config.RefreshAccessTokenTaskConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSON;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonLogisticsConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.properties.AbstractPlatformProperties;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.dao.redis.repository.UserManageRedisRepositoryHashRedisRepository;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.db.common.repository.UserRepository;
import jakarta.annotation.PreDestroy;

/**
 * @description: 清理Redis数据的任务
 * @author: hl1221hl
 * @date: 2025/09/17 17:08
 */
@Component
public class RedisCleanTask implements ApplicationRunner {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(RedisCleanTask.class);

    // Redis Key前缀常量
    private static final String REDIS_TASK_PREFIX = "redis_clean_task:";

    // 任务状态常量
    private static final String TASK_STATUS_RUNNING = "RUNNING";
    private static final String TASK_STATUS_COMPLETED = "COMPLETED";
    private static final String TASK_STATUS_FAILED = "FAILED";

    // 时间格式
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    @Qualifier("stringTradeRedisTemplate")
    private StringRedisTemplate tradeRedisTemplate;

    @Autowired
    @Qualifier("stringItemRedisTemplate")
    private StringRedisTemplate itemRedisTemplate;

    @Autowired
    @Qualifier("stringDistributeRedisTemplate")
    private StringRedisTemplate distributeRedisTemplate;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserManageRedisRepositoryHashRedisRepository userManageRedisRepository;

    @Autowired
    private RefreshAccessTokenTaskConfig refreshAccessTokenTaskConfig;

    /**
     * 线程池，用于并行处理不同平台的清理任务
     */
    private ExecutorService executorService;

    /**
     * platformID(storeId) -> appNames
     */
    private final LinkedHashMap<String, List<String>> platformAppNamesMap = new LinkedHashMap<>();

    public RedisCleanTask() {
        // 平台和对应的应用
        platformAppNamesMap.put(CommonPlatformConstants.PLATFORM_1688, List.of(CommonAppConstants.APP_DISTRIBUTE, CommonAppConstants.APP_TRADE, CommonAppConstants.APP_GUANDIAN, CommonAppConstants.APP_TRADE_ERP, CommonAppConstants.APP_ITEM));
        platformAppNamesMap.put(CommonPlatformConstants.PLATFORM_PDD, List.of(CommonAppConstants.APP_DISTRIBUTE, CommonAppConstants.APP_TRADE, CommonAppConstants.APP_GUANDIAN, CommonAppConstants.APP_TRADE_ERP, CommonAppConstants.APP_ITEM));
        platformAppNamesMap.put(CommonPlatformConstants.PLATFORM_DOUDIAN, List.of(CommonAppConstants.APP_DISTRIBUTE, CommonAppConstants.APP_TRADE, CommonAppConstants.APP_TRADE_ERP, CommonAppConstants.APP_ITEM));
        platformAppNamesMap.put(CommonPlatformConstants.PLATFORM_KWAISHOP, List.of(CommonAppConstants.APP_DISTRIBUTE, CommonAppConstants.APP_TRADE, CommonAppConstants.APP_TRADE_ERP, CommonAppConstants.APP_ITEM));
        platformAppNamesMap.put(CommonPlatformConstants.PLATFORM_XHS, List.of(CommonAppConstants.APP_DISTRIBUTE, CommonAppConstants.APP_TRADE, CommonAppConstants.APP_TRADE_ERP));
        platformAppNamesMap.put(CommonPlatformConstants.PLATFORM_WXVIDEOSHOP, List.of(CommonAppConstants.APP_DISTRIBUTE, CommonAppConstants.APP_TRADE, CommonAppConstants.APP_GUANDIAN, CommonAppConstants.APP_TRADE_ERP, CommonAppConstants.APP_ITEM));
        platformAppNamesMap.put(CommonPlatformConstants.PLATFORM_WXSHOP, List.of(CommonAppConstants.APP_TRADE, CommonAppConstants.APP_GUANDIAN, CommonAppConstants.APP_ITEM));
        platformAppNamesMap.put(CommonPlatformConstants.PLATFORM_JD, List.of(CommonAppConstants.APP_TRADE_ERP));
        platformAppNamesMap.put(CommonPlatformConstants.PLATFORM_TAO, List.of(CommonAppConstants.APP_TRADE, CommonAppConstants.APP_TRADE_ERP, CommonAppConstants.APP_ITEM));
    }

    /**
     * 初始化线程池
     */
    private void initializeThreadPool() {
        if (executorService != null && !executorService.isShutdown()) {
            return;
        }
        int threadPoolSize = refreshAccessTokenTaskConfig.getRedisCleanThreadPoolSize();
        this.executorService = new ThreadPoolExecutor(
                threadPoolSize,
                threadPoolSize,
                0L,
                TimeUnit.MILLISECONDS,new SynchronousQueue<>(), new ThreadFactoryBuilder().setNameFormat("redis-clean-task-pool-%d").build(),
                (Runnable r, ThreadPoolExecutor executor) -> {
                    if (!executor.isShutdown()) {
                        try {
                            executor.getQueue().put(r);
                        } catch (InterruptedException e) {
                            LOGGER.logError(e.toString(), e);
                            Thread.currentThread().interrupt();
                        }
                    }
                });
        LOGGER.logInfo("", "", "Redis清理任务线程池已初始化，线程数: " + threadPoolSize);
    }

    /**
     * 应用关闭时清理线程池
     */
    @PreDestroy
    public void destroy() {
        if (executorService != null && !executorService.isShutdown()) {
            LOGGER.logInfo("", "", "正在关闭Redis清理任务线程池...");
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(1, TimeUnit.MINUTES)) {
                    executorService.shutdownNow();
                    LOGGER.logWarn("", "", "强制关闭Redis清理任务线程池");
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
                LOGGER.logError("", "", "关闭Redis清理任务线程池时发生中断: " + e.getMessage(), e);
            }
        }
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        clean();
    }


    @Scheduled(cron = "${uac.newuser.task.redisClean.cron:0 0 1 * * ?}")
    public void clean() {
        if (!refreshAccessTokenTaskConfig.getRedisCleanSwitch()) {
            LOGGER.logInfo("", "", "Redis清理任务未开启，跳过执行");
            return;
        }

        LOGGER.logInfo("", "", "开始执行Redis清理任务");
        // 初始化线程池
        initializeThreadPool();
        // 启动时打印当前的进度
        printCurrentProgress();

        // 计算2个月之前的时间作为默认边界
        LocalDateTime defaultBoundary = LocalDate.now().atStartOfDay().minusMonths(2);
        LOGGER.logInfo("", "", "默认清理时间界限: " + defaultBoundary);

        // 使用线程池并行处理不同平台
        List<Future<PlatformCleanResult>> futures = new ArrayList<>();

        for (String platformId : platformAppNamesMap.keySet()) {
            Future<PlatformCleanResult> future = executorService.submit(new PlatformCleanTask(platformId, defaultBoundary));
            futures.add(future);
        }

        // 等待所有平台清理完成并统计结果
        int totalCleaned = 0;
        int successPlatforms = 0;
        int failedPlatforms = 0;

        for (Future<PlatformCleanResult> future : futures) {
            try {
                PlatformCleanResult result = future.get();
                totalCleaned += result.getTotalCleaned();
                if (result.isSuccess()) {
                    successPlatforms++;
                } else {
                    failedPlatforms++;
                }
            } catch (Exception e) {
                failedPlatforms++;
                LOGGER.logError("", "", "平台清理任务执行失败: " + e.getMessage(), e);
            }
        }

        LOGGER.logInfo("", "", String.format("Redis清理任务执行完成 - 总共清理: %d个缓存，成功平台: %d个，失败平台: %d个",
                totalCleaned, successPlatforms, failedPlatforms));
    }

    /**
     * 平台清理任务
     */
    private class PlatformCleanTask implements Callable<PlatformCleanResult> {
        private final String platformId;
        private final LocalDateTime defaultBoundary;

        public PlatformCleanTask(String platformId, LocalDateTime defaultBoundary) {
            this.platformId = platformId;
            this.defaultBoundary = defaultBoundary;
        }

        @Override
        public PlatformCleanResult call() {
            try {
                List<String> appNames = platformAppNamesMap.get(platformId);
                int platformTotalCleaned = 0;

                LOGGER.logInfo("", platformId, "开始清理平台Redis缓存");

                for (String appName : appNames) {
                    LOGGER.logInfo("", platformId, "开始清理应用Redis缓存: " + appName);
                    try {
                        int cleaned = cleanRedisForPlatformAndApp(platformId, appName, defaultBoundary);
                        platformTotalCleaned += cleaned;
                        LOGGER.logInfo("", platformId, "应用Redis缓存清理完成，共清理: " + cleaned + "个缓存");
                    } catch (Exception e) {
                        LOGGER.logError("", platformId + ":" + appName, "清理Redis缓存失败: " + e.getMessage(), e);
                    }
                }

                LOGGER.logInfo("", platformId, "平台Redis缓存清理完成，共清理: " + platformTotalCleaned + "个缓存");
                return new PlatformCleanResult(platformId, platformTotalCleaned, true);

            } catch (Exception e) {
                LOGGER.logError("", platformId, "平台Redis缓存清理失败: " + e.getMessage(), e);
                return new PlatformCleanResult(platformId, 0, false);
            }
        }
    }

    /**
     * 平台清理结果
     */
    private static class PlatformCleanResult {
        private final String platformId;
        private final int totalCleaned;
        private final boolean success;

        public PlatformCleanResult(String platformId, int totalCleaned, boolean success) {
            this.platformId = platformId;
            this.totalCleaned = totalCleaned;
            this.success = success;
        }

        public String getPlatformId() {
            return platformId;
        }

        public int getTotalCleaned() {
            return totalCleaned;
        }

        public boolean isSuccess() {
            return success;
        }
    }

    /**
     * 为指定平台和应用清理Redis缓存
     */
    private int cleanRedisForPlatformAndApp(String platformId, String appName, LocalDateTime defaultBoundary) {
        // 检查是否有正在运行的任务
        String currentStatus = getTaskStatus(platformId, appName);
        if (TASK_STATUS_RUNNING.equals(currentStatus)) {
            LOGGER.logWarn("", platformId + ":" + appName, "任务正在运行中，跳过本次执行");
            return 0;
        }

        // 保存任务开始状态
        saveTaskStatus(platformId, appName, TASK_STATUS_RUNNING);

        try {
            // 获取上次扫描时间和任务进度
            LocalDateTime lastScanTime = getLastScanTime(platformId, appName);
            String existingLastW1DeadlineStr = getLastW1Deadline(platformId, appName);

            // 计算扫描边界时间
            LocalDateTime scanBoundary;
            LocalDateTime lastW1Deadline = null;

            if (lastScanTime != null) {
                // 有上次扫描记录，仅扫描上次之后的数据
                scanBoundary = lastScanTime;
                LOGGER.logInfo("", platformId + ":" + appName, "检测到上次扫描时间: " + lastScanTime.format(DATETIME_FORMATTER) + ", 仅扫描新数据");

                // 如果有未完成的任务进度，使用进度中的lastW1Deadline作为起始点
                if (StringUtils.isNotEmpty(existingLastW1DeadlineStr)) {
                    lastW1Deadline = LocalDateTime.parse(existingLastW1DeadlineStr, DATETIME_FORMATTER);
                    LOGGER.logInfo("", platformId + ":" + appName, "检测到未完成的任务进度，从 " + lastW1Deadline.format(DATETIME_FORMATTER) + " 开始继续");
                }
            } else {
                // 没有上次扫描记录，使用默认边界
                scanBoundary = defaultBoundary;
                LOGGER.logInfo("", platformId + ":" + appName, "首次执行清理任务，扫描时间界限: " + scanBoundary.format(DATETIME_FORMATTER));
            }

            // 初始化进度记录
            int cleanedCount = getCleanedCount(platformId, appName);
            int totalProcessed = getTotalProcessed(platformId, appName);

            LOGGER.logInfo("", platformId + ":" + appName, "开始清理Redis缓存");

            int totalCleaned = 0;
            int pageSize = 1000;

            while (true) {
                try {
                    saveTaskStatus(platformId, appName, TASK_STATUS_RUNNING);
                    
                    // 分页查询w1_deadline在扫描边界之前的用户数据
                    List<UserProductInfo> users = userRepository.queryByW1DeadlineBeforeWithPage(
                            scanBoundary, lastW1Deadline, pageSize, platformId, appName);

                    if (users == null || users.isEmpty()) {
                        LOGGER.logInfo("", platformId + ":" + appName, "没有更多数据需要清理");
                        break;
                    }

                    LOGGER.logInfo("", platformId + ":" + appName, "本批次查询到 " + users.size() + " 条记录");

                    int cleanedInThisBatch = 0;
                    for (UserProductInfo user : users) {
                        totalProcessed++;
                        // 检查是否符合清理条件
                        if (shouldCleanUserCache(user, scanBoundary)) {
                            if (cleanUserRedisCache(user, platformId, appName)) {
                                cleanedInThisBatch++;
                                totalCleaned++;
                            }
                        }
                    }

                    // 更新进度
                    lastW1Deadline = users.get(users.size() - 1).getW1Deadline();
                    cleanedCount += cleanedInThisBatch;

                    // 保存进度
                    saveTaskProgress(platformId, appName, lastW1Deadline.format(DATETIME_FORMATTER), cleanedCount, totalProcessed);

                    LOGGER.logInfo("", platformId + ":" + appName, "本批次清理了 " + cleanedInThisBatch + " 个用户缓存，总计: " + totalCleaned);

                    // 如果返回的数据少于页面大小，说明已经是最后一页
                    if (users.size() < pageSize) {
                        break;
                    }

                } catch (Exception e) {
                    LOGGER.logError("", platformId + ":" + appName, "分页查询或清理过程中发生错误: " + e.getMessage(), e);
                    // 保存当前进度后抛出异常
                    saveTaskProgress(platformId, appName, lastW1Deadline != null ? lastW1Deadline.format(DATETIME_FORMATTER) : null, cleanedCount, totalProcessed);
                    saveTaskStatus(platformId, appName, TASK_STATUS_FAILED);
                    throw e;
                }
            }

            // 任务完成，保存最终状态
            saveTaskStatus(platformId, appName, TASK_STATUS_COMPLETED);
            saveLastScanTime(platformId, appName, scanBoundary);
            clearTaskProgressData(platformId, appName); // 清理进度数据

            LOGGER.logInfo("", platformId + ":" + appName, "完成清理Redis缓存，共清理 " + totalCleaned + " 个用户缓存");
            return totalCleaned;

        } catch (Exception e) {
            // 任务失败，保存失败状态
            saveTaskStatus(platformId, appName, TASK_STATUS_FAILED);
            LOGGER.logError("", platformId + ":" + appName, "清理Redis缓存失败: " + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 判断用户是否应该被清理缓存
     * 条件：order_cycle_end、lastupdatetime、w1_deadline 不等于null时并且都在2个月之前的数据
     * 如果这3个字段都为null，则跳过
     */
    private boolean shouldCleanUserCache(UserProductInfo user, LocalDateTime twoMonthsAgo) {
        LocalDateTime orderCycleEnd = user.getOrderCycleEnd();
        LocalDateTime lastUpdateTime = user.getLastupdatetime();
        LocalDateTime w1Deadline = user.getW1Deadline();

        // 如果3个字段都为null，则跳过
        if (orderCycleEnd == null && lastUpdateTime == null && w1Deadline == null) {
            return false;
        }

        // 检查不为null的字段是否都在2个月之前
        boolean shouldClean = true;

        if (orderCycleEnd != null && orderCycleEnd.isAfter(twoMonthsAgo)) {
            shouldClean = false;
        }

        if (lastUpdateTime != null && lastUpdateTime.isAfter(twoMonthsAgo)) {
            shouldClean = false;
        }

        if (w1Deadline != null && w1Deadline.isAfter(twoMonthsAgo)) {
            shouldClean = false;
        }

        return shouldClean;
    }

    /**
     * 清理用户的Redis缓存
     */
    private boolean cleanUserRedisCache(UserProductInfo user, String platformId, String appName) {
        try {
            String sellerNick = user.getNick();
            String sellerId = StringUtils.isEmpty(user.getUserIdStr()) ? user.getUserId() : user.getUserIdStr();

            if (StringUtils.isEmpty(sellerNick) && StringUtils.isEmpty(sellerId)) {
                return false;
            }

            // 生成Redis缓存key
            String redisKey = userManageRedisRepository.initCollection(sellerNick, sellerId, platformId, appName);

            if (StringUtils.isEmpty(redisKey)) {
                LOGGER.logWarn(sellerNick, sellerId, "无法生成Redis key，跳过清理");
                return false;
            }
            if (isInvalidKey(redisKey, sellerNick, sellerId)) {
                return false;
            }

            // 选择对应的Redis模板
            StringRedisTemplate redisTemplate = getRedisTemplate(appName);

            // 删除Redis缓存
            Boolean deleted = redisTemplate.delete(redisKey);
            // 代发应用需要删除两个key
            if (CommonAppConstants.APP_DISTRIBUTE.equals(appName) && StringUtils.isNotEmpty(sellerId) && !isInvalidKey(sellerId, sellerNick, sellerId)) {
                redisTemplate.delete(sellerId);
            }

            if (deleted) {
                LOGGER.logInfo(sellerNick, sellerId, "成功删除Redis缓存，key: " + redisKey);
                return true;
            } else {
                LOGGER.logWarn(sellerNick, sellerId, "Redis缓存不存在或删除失败，key: " + redisKey);
                return false;
            }

        } catch (Exception e) {
            LOGGER.logError(user.getNick(), user.getUserId(), "清理用户Redis缓存失败: " + e.getMessage(), e);
            return false;
        }
    }

    private static boolean isInvalidKey(String redisKey, String sellerNick, String sellerId) {
        if (redisKey.equalsIgnoreCase("aiyong") || redisKey.startsWith("vcs")) {
            LOGGER.logWarn(sellerNick, sellerId, "疑似关键 key，跳过清理: " + redisKey);
            return true;
        }
        return false;
    }

    /**
     * 根据应用名获取对应的Redis模板
     */
    private StringRedisTemplate getRedisTemplate(String appName) {
        if (CommonAppConstants.APP_ITEM.equals(appName)) {
            return itemRedisTemplate;
        } else if (CommonAppConstants.APP_DISTRIBUTE.equals(appName)) {
            return distributeRedisTemplate;
        } else {
            return tradeRedisTemplate;
        }
    }

    /**
     * 获取任务信息Redis Key
     */
    private String getTaskKey(String platformId, String appName) {
        return REDIS_TASK_PREFIX + platformId + ":" + appName;
    }

    private void printCurrentProgress() {
        //打印所有平台和应用的进度
        for (String platformId : platformAppNamesMap.keySet()) {
            List<String> appNames = platformAppNamesMap.get(platformId);
            for (String appName : appNames) {
                LOGGER.logInfo(getTaskKey(platformId, appName), null, "TaskStatus=" + getTaskStatus(platformId, appName) + ", cleanedCount=" + getCleanedCount(platformId, appName) + ", totalProcessed=" + getTotalProcessed(platformId, appName) + ", lastW1Deadline=" + getLastW1Deadline(platformId, appName));
            }
        }
    }

    /**
     * 保存任务状态
     */
    private void saveTaskStatus(String platformId, String appName, String status) {
        try {
            String key = getTaskKey(platformId, appName);
            String currentTime = LocalDateTime.now().format(DATETIME_FORMATTER);

            tradeRedisTemplate.opsForHash().put(key, "status", status);
            tradeRedisTemplate.opsForHash().put(key, "updateTime", currentTime);

            // 如果是开始状态，记录开始时间
            if (TASK_STATUS_RUNNING.equals(status)) {
                tradeRedisTemplate.opsForHash().put(key, "startTime", currentTime);
            }

            // 设置过期时间
            tradeRedisTemplate.expire(key, 7, TimeUnit.DAYS);

            LOGGER.logInfo("", platformId + ":" + appName, "保存任务状态: " + status);
        } catch (Exception e) {
            LOGGER.logError("", platformId + ":" + appName, "保存任务状态失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取任务状态
     */
    private String getTaskStatus(String platformId, String appName) {
        try {
            String key = getTaskKey(platformId, appName);
            Object status = tradeRedisTemplate.opsForHash().get(key, "status");
            return status != null ? status.toString() : null;
        } catch (Exception e) {
            LOGGER.logError("", platformId + ":" + appName, "获取任务状态失败: " + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 保存任务进度
     */
    private void saveTaskProgress(String platformId, String appName, String lastW1Deadline, int cleanedCount, int totalProcessed) {
        try {
            String key = getTaskKey(platformId, appName);
            String currentTime = LocalDateTime.now().format(DATETIME_FORMATTER);

            tradeRedisTemplate.opsForHash().put(key, "lastW1Deadline", lastW1Deadline);
            tradeRedisTemplate.opsForHash().put(key, "cleanedCount", String.valueOf(cleanedCount));
            tradeRedisTemplate.opsForHash().put(key, "totalProcessed", String.valueOf(totalProcessed));
            tradeRedisTemplate.opsForHash().put(key, "updateTime", currentTime);

            // 设置过期时间
            tradeRedisTemplate.expire(key, 7, TimeUnit.DAYS);

            LOGGER.logDebug("", platformId + ":" + appName, "保存任务进度: lastW1Deadline=" + lastW1Deadline + ", cleanedCount=" + cleanedCount);
        } catch (Exception e) {
            LOGGER.logError("", platformId + ":" + appName, "保存任务进度失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取上次w1Deadline进度
     */
    private String getLastW1Deadline(String platformId, String appName) {
        try {
            String key = getTaskKey(platformId, appName);
            Object lastW1Deadline = tradeRedisTemplate.opsForHash().get(key, "lastW1Deadline");
            return lastW1Deadline != null ? lastW1Deadline.toString() : null;
        } catch (Exception e) {
            LOGGER.logError("", platformId + ":" + appName, "获取任务进度失败: " + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取已清理数量
     */
    private int getCleanedCount(String platformId, String appName) {
        try {
            String key = getTaskKey(platformId, appName);
            Object cleanedCount = tradeRedisTemplate.opsForHash().get(key, "cleanedCount");
            return cleanedCount != null ? Integer.parseInt(cleanedCount.toString()) : 0;
        } catch (Exception e) {
            LOGGER.logError("", platformId + ":" + appName, "获取已清理数量失败: " + e.getMessage(), e);
        }
        return 0;
    }

    /**
     * 获取已处理总数
     */
    private int getTotalProcessed(String platformId, String appName) {
        try {
            String key = getTaskKey(platformId, appName);
            Object totalProcessed = tradeRedisTemplate.opsForHash().get(key, "totalProcessed");
            return totalProcessed != null ? Integer.parseInt(totalProcessed.toString()) : 0;
        } catch (Exception e) {
            LOGGER.logError("", platformId + ":" + appName, "获取已处理总数失败: " + e.getMessage(), e);
        }
        return 0;
    }

    /**
     * 保存上次扫描时间
     */
    private void saveLastScanTime(String platformId, String appName, LocalDateTime scanTime) {
        try {
            String key = getTaskKey(platformId, appName);
            String timeStr = scanTime.format(DATETIME_FORMATTER);

            tradeRedisTemplate.opsForHash().put(key, "lastScanTime", timeStr);
            tradeRedisTemplate.expire(key, 30, TimeUnit.DAYS);

            LOGGER.logInfo("", platformId + ":" + appName, "保存上次扫描时间: " + timeStr);
        } catch (Exception e) {
            LOGGER.logError("", platformId + ":" + appName, "保存上次扫描时间失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取上次扫描时间
     */
    private LocalDateTime getLastScanTime(String platformId, String appName) {
        try {
            String key = getTaskKey(platformId, appName);
            Object timeStr = tradeRedisTemplate.opsForHash().get(key, "lastScanTime");
            if (timeStr != null && StringUtils.isNotEmpty(timeStr.toString())) {
                return LocalDateTime.parse(timeStr.toString(), DATETIME_FORMATTER);
            }
        } catch (Exception e) {
            LOGGER.logError("", platformId + ":" + appName, "获取上次扫描时间失败: " + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 清理任务进度数据
     */
    private void clearTaskProgressData(String platformId, String appName) {
        try {
            String key = getTaskKey(platformId, appName);

            // 只清理进度相关字段，保留状态和扫描时间
            tradeRedisTemplate.opsForHash().delete(key, "lastW1Deadline", "cleanedCount", "totalProcessed");

            LOGGER.logInfo("", platformId + ":" + appName, "清理任务进度数据");
        } catch (Exception e) {
            LOGGER.logError("", platformId + ":" + appName, "清理任务进度数据失败: " + e.getMessage(), e);
        }
    }
}
