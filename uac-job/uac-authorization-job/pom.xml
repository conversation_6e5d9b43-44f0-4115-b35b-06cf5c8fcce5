<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>uac-job</artifactId>
        <groupId>cn.loveapp.uac</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>uac-authorization-job</artifactId>
    <name>爱用-uac-authorization-job</name>
    <description>爱用-uac-job 授权任务</description>
    <version>1.0-SNAPSHOT</version>

    <organization>
        <name>Loveapp Inc.</name>
        <url>http://www.aiyongbao.com</url>
    </organization>

    <dependencies>
        <dependency>
            <groupId>cn.loveapp.uac</groupId>
            <artifactId>uac-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.loveapp.uac</groupId>
            <artifactId>uac-db-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.loveapp.uac</groupId>
            <artifactId>uac-service-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
