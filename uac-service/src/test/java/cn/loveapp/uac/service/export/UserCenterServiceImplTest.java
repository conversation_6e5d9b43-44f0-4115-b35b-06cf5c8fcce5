package cn.loveapp.uac.service.export;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.uac.code.ApiCode;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.exception.UserException;
import cn.loveapp.uac.request.UserInfoRequest;
import cn.loveapp.uac.response.UserInfoResponse;
import cn.loveapp.uac.service.platform.biz.UserPlatformHandleService;
import com.alibaba.fastjson2.JSON;
import org.junit.jupiter.api.Assertions;
import org.mockito.BDDMockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.eq;

/**
 * <AUTHOR>
 * @Since 2020/6/22 14:14
 */
@WebMvcTest({UserCenterServiceImpl.class})
public class UserCenterServiceImplTest {

    private UserInfoRequest userInfoRequest = new UserInfoRequest();

    @MockitoBean
    private UserPlatformHandleService userProxyService;

    @Autowired
    private MockMvc mockMvc;

    /**
     * 批量获取用户信息
     */
    @org.junit.jupiter.api.Test
    public void batchGetUserInfoTest1() throws Exception {
        String sellerId = "3936370796";
        String sellerNick = "赵东昊的测试店铺";
        String platformId = CommonPlatformConstants.PLATFORM_TAO;
        Integer vipFlag = 1;
        String appName = CommonAppConstants.APP_TRADE;

        UserInfoBo userInfoBo = new UserInfoBo();
        userInfoBo.setSellerNick(sellerNick);
        userInfoBo.setPlatformId(platformId);
        userInfoBo.setAppType(appName);

        UserInfoResponse userInfoResponse = new UserInfoResponse();
        userInfoResponse.setSellerId(sellerId);
        userInfoResponse.setSellerNick(sellerNick);
        userInfoResponse.setVipflag(vipFlag);

        BDDMockito.given(userProxyService.getUserInfo(eq(userInfoBo), eq(platformId), eq(appName))).willReturn(userInfoResponse);

        List<UserInfoResponse> userInfoList = Collections.singletonList(userInfoResponse);

        userInfoRequest = new UserInfoRequest();
        userInfoRequest.setSellerNick(sellerNick);
        userInfoRequest.setPlatformId(platformId);
        userInfoRequest.setApp(appName);

        List<UserInfoRequest> requestList = Collections.singletonList(userInfoRequest);

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/export/uac/user/batchGetUserInfo")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(requestList))
        ).andReturn();

        Assertions.assertEquals(200, mvcResult.getResponse().getStatus());
        Assertions.assertEquals(CommonApiStatus.Success.code(), JSON.parseObject(mvcResult.getResponse().getContentAsString(StandardCharsets.UTF_8)).get("code"));
        List<UserInfoResponse> responseValues = JSON.parseArray(JSON.parseObject(mvcResult.getResponse().getContentAsString(StandardCharsets.UTF_8)).getString("body"), UserInfoResponse.class);
        Assertions.assertEquals(requestList.size(), responseValues.size());
        Assertions.assertEquals(userInfoList, responseValues);
    }


    /**
     * 批量获取用户信息, 某个用户不存在
     */
    @org.junit.jupiter.api.Test
    public void batchGetUserInfoTest2() throws Exception {
        String sellerId = "3936370796";
        String sellerNick = "赵东昊的测试店铺";
        String platformId = CommonPlatformConstants.PLATFORM_TAO;
        Integer vipFlag = 1;
        String appName = CommonAppConstants.APP_TRADE;

        UserInfoBo userInfoBo = new UserInfoBo();
        userInfoBo.setSellerNick(sellerNick);
        userInfoBo.setPlatformId(platformId);
        userInfoBo.setAppType(appName);

        UserInfoResponse userInfoResponse = new UserInfoResponse();
        userInfoResponse.setSellerId(sellerId);
        userInfoResponse.setSellerNick(sellerNick);
        userInfoResponse.setVipflag(vipFlag);

        BDDMockito.given(userProxyService.getUserInfo(eq(userInfoBo), eq(platformId), eq(appName))).willReturn(userInfoResponse);

        List<UserInfoResponse> userInfoList = Collections.singletonList(userInfoResponse);

        userInfoRequest = new UserInfoRequest();
        userInfoRequest.setSellerNick(sellerNick);
        userInfoRequest.setPlatformId(platformId);
        userInfoRequest.setApp(appName);

        String sellerNick2 = "该用户不存在";
        UserInfoRequest userInfoRequest2 = new UserInfoRequest();
        userInfoRequest2.setSellerNick(sellerNick2);
        userInfoRequest2.setPlatformId(platformId);
        userInfoRequest2.setApp(appName);

        UserInfoBo userInfoBo2 = new UserInfoBo();
        userInfoBo2.setAppType(appName);
        userInfoBo2.setPlatformId(platformId);
        userInfoBo2.setSellerNick(sellerNick2);

        BDDMockito.given(userProxyService.getUserInfo(eq(userInfoBo2), eq(platformId), eq(appName)))
                .willThrow(new UserException(ApiCode.NO_EXIST_USER.code(), ApiCode.NO_EXIST_USER.message()));

        List<UserInfoRequest> requestList = Arrays.asList(userInfoRequest, userInfoRequest2);

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/export/uac/user/batchGetUserInfo")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(requestList))
        ).andReturn();

        Assertions.assertEquals(200, mvcResult.getResponse().getStatus());
        Assertions.assertEquals(CommonApiStatus.Success.code(), JSON.parseObject(mvcResult.getResponse().getContentAsString(StandardCharsets.UTF_8)).get("code"));
        List<UserInfoResponse> responseValues = JSON.parseArray(JSON.parseObject(mvcResult.getResponse().getContentAsString(StandardCharsets.UTF_8)).getString("body"), UserInfoResponse.class);
        Assertions.assertNotEquals(requestList.size(), responseValues.size());
        Assertions.assertEquals(userInfoList, responseValues);
    }

    /**
     * pdd
     * @return
     */
    private UserInfoResponse getUserInfo() {
        UserInfoResponse userInfo = new UserInfoResponse();
        userInfo.setSellerId("682119758");
        userInfo.setSellerNick("pdd68211975897");
        return userInfo;
    }


}
