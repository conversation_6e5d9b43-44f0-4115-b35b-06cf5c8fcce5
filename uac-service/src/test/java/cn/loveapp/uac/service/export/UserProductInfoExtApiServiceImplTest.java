package cn.loveapp.uac.service.export;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonBusinessConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.uac.common.config.DistributeConfig;
import cn.loveapp.uac.common.config.redis.RedisConfiguration;
import cn.loveapp.uac.common.dao.redis.repository.UserManageRedisRepositoryHashRedisRepository;
import cn.loveapp.uac.db.common.dao.dream.CommonUserProductionInfoExtDao;
import cn.loveapp.uac.db.common.repository.UserProductionInfoExtRepository;
import cn.loveapp.uac.db.common.repository.impl.UserProductionInfoExtRepositoryImpl;
import cn.loveapp.uac.entity.UserProductInfoBusinessExt;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;

/**
 * @Author: zhongzijie
 * @Date: 2023/2/20 12:07
 * @Description:
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE,
        classes = {RedisConfiguration.class, UserProductionInfoExtRepository.class, StringRedisTemplate.class, UserManageRedisRepositoryHashRedisRepository.class,
                DistributeConfig.class, CommonUserProductionInfoExtDao.class, RedisConnectionFactory.class})
public class UserProductInfoExtApiServiceImplTest {

    @MockitoSpyBean
    private UserProductionInfoExtRepositoryImpl userProductionInfoExtRepository;

    @MockitoSpyBean
    private CommonUserProductionInfoExtDao commonUserProductionInfoExtDao;

//    @MockitoSpyBean
//    @Qualifier("stringTradeRedisTemplate")
//    private StringRedisTemplate stringTradeRedisTemplate;
//
//    @MockitoSpyBean
//    @Qualifier("stringItemRedisTemplate")
//    private StringRedisTemplate stringItemRedisTemplate;

    @MockitoSpyBean
    private UserManageRedisRepositoryHashRedisRepository userManageRedisRepositoryHashRedisRepository;

    @MockitoSpyBean
    private DistributeConfig distributeConfig;

    @MockitoSpyBean
    @Qualifier("redisTradeConnectionFactory")
    private RedisConnectionFactory cf1;

    @MockitoSpyBean
    @Qualifier("redisItemConnectionFactory")
    private RedisConnectionFactory cf2;

    /**
     * 获取单个用户ext信息
     */
    @org.junit.jupiter.api.Test
    public void getSingleUserExtBySellerIdTest1() throws Exception {
        String sellerId = "1959203576";
        String sellerNick = "dcd222ding";
        String businessId = CommonBusinessConstants.BUSINESS_ITEM;
        String storeId = CommonPlatformConstants.PLATFORM_TAO;
        String appName = CommonAppConstants.APP_TRADE;

        UserProductInfoBusinessExt userProductInfoBusinessExt = userProductionInfoExtRepository.querySingleBySellerId(sellerId, storeId, appName, businessId);

        Assertions.assertEquals(userProductInfoBusinessExt.getSellerNick(), sellerNick);

    }
}
