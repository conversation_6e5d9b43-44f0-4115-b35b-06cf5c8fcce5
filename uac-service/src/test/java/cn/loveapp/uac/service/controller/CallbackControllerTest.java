package cn.loveapp.uac.service.controller;

import cn.loveapp.uac.request.CallbackRequest;
import cn.loveapp.uac.response.CallbackResponse;
import cn.loveapp.uac.service.platform.biz.CallbackPlatformHandleService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.BDDMockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * @program: uac-service-group
 * @description: CallbackControllerTest
 * @author: Jason
 * @create: 2020-03-23 11:49
 **/
@WebMvcTest({CallbackController.class})
public class CallbackControllerTest {

	@Autowired
	private MockMvc mockMvc;

	@MockitoBean
	private CallbackPlatformHandleService callbackProxyService;

	private CallbackRequest callbackRequest = new CallbackRequest();
	private MultiValueMap<String, String> callbackRequestMap = new LinkedMultiValueMap<>();


	@BeforeEach
	public void setUp() {
		callbackRequest.setApp("trade");
		callbackRequest.setPlatformId("TAO");
		callbackRequest.setCode("xxx");
	}

	@org.junit.jupiter.api.Test
	public void authCallbackTest1() throws Exception {
		callbackRequest.setCode(null);
		callbackRequestMap = this.setMultiValueMap(callbackRequest);
		MvcResult mvcResult = mockMvc.perform(post("/uac/callback/authCallback")
			.params(callbackRequestMap))
			.andExpect(status().is4xxClientError())
			.andReturn();
		Assertions.assertEquals(400, mvcResult.getResponse().getStatus());
	}

	@org.junit.jupiter.api.Test
	public void authCallbackTest2() throws Exception {
		callbackRequestMap = this.setMultiValueMap(callbackRequest);
		CallbackResponse callbackResponse = new CallbackResponse();
		callbackResponse.setSellerNick("xxx");
		callbackResponse.setAccessToken("xxxx");
		BDDMockito.given(callbackProxyService.authCallback(any(),any(),any())).willReturn(callbackResponse);
		MvcResult mvcResult = mockMvc.perform(post("/uac/callback/authCallback")
			.params(callbackRequestMap))
			.andExpect(status().isOk())
			.andReturn();
		Assertions.assertEquals(200, mvcResult.getResponse().getStatus());
	}

	@org.junit.jupiter.api.Test
	public void authCallbackTest3() throws Exception {
		callbackRequestMap = this.setMultiValueMap(callbackRequest);
		CallbackResponse callbackResponse = new CallbackResponse();
		callbackResponse.setSellerNick("xxx");
		callbackResponse.setAccessToken("");
		BDDMockito.given(callbackProxyService.authCallback(any(),any(),any())).willReturn(callbackResponse);
		MvcResult mvcResult = mockMvc.perform(post("/uac/callback/authCallback")
			.params(callbackRequestMap))
			.andExpect(status().isOk())
			.andReturn();
		Assertions.assertEquals(200, mvcResult.getResponse().getStatus());
		Assertions.assertEquals(500, JSON.parseObject(mvcResult.getResponse().getContentAsString()).get("code"));
	}

	private <T> MultiValueMap<String, String> setMultiValueMap(T classExample){
		MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
		for (Map.Entry<String, Object> entry :((JSONObject) JSON.toJSON(classExample)).entrySet()) {
			if(null != entry.getValue() && StringUtils.isNotEmpty(entry.getValue().toString())){
				multiValueMap.add(entry.getKey(),entry.getValue().toString());
			}
		}
		return multiValueMap;
	}

}
