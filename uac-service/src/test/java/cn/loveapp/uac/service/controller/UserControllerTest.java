package cn.loveapp.uac.service.controller;

import cn.loveapp.uac.code.ApiCode;
import cn.loveapp.uac.exception.UserException;
import cn.loveapp.uac.request.LoginInfoRequest;
import cn.loveapp.uac.request.RefreshUserInfoRequest;
import cn.loveapp.uac.request.UserInfoRequest;
import cn.loveapp.uac.response.UserInfoResponse;
import cn.loveapp.uac.service.platform.biz.UserPlatformHandleService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.BDDMockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.time.LocalDateTime;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * @Created by: IntelliJ IDEA.
 * @description:
 * @authr: jason
 * @date: 2020/3/23
 * @time: 9:40 AM
 */
@WebMvcTest({UserController.class})
public class UserControllerTest {

	private UserInfoRequest userInfoRequest = new UserInfoRequest();
	private MultiValueMap<String, String> userInfoRequestMap = new LinkedMultiValueMap<>();

	private LoginInfoRequest loginInfoRequest = new LoginInfoRequest();
	private MultiValueMap<String, String> loginInfoRequestMap = new LinkedMultiValueMap<>();

	private RefreshUserInfoRequest refreshUserInfoRequest = new RefreshUserInfoRequest();
	private MultiValueMap<String, String> refreshUserInfoRequestMap = new LinkedMultiValueMap<>();

	@Autowired
	private MockMvc mockMvc;

	@MockitoBean
	private UserPlatformHandleService userProxyService;

	@BeforeEach
	public void setUp() {
		userInfoRequest.setApp("trade");
		userInfoRequest.setPlatformId("TAO");
		userInfoRequest.setSellerNick("赵东昊的测试店铺");
		userInfoRequest.setSellerId("123123");
		userInfoRequest.setSubSellerNick(null);

		loginInfoRequest.setSellerId("123123");
		loginInfoRequest.setCreateDate("2020-03-23 10:23:55");
		loginInfoRequest.setCreateIpAddress("127.0.0.1");
		loginInfoRequest.setIsSilent("1");
		loginInfoRequest.setLastActivedt("2020-03-23 10:23:55");
		loginInfoRequest.setLastIpAddress("127.0.0.1");
		loginInfoRequest.setLastUpdateTime("2020-03-23 10:23:55");
		loginInfoRequest.setRoleId("B");
		loginInfoRequest.setSubDateTime("2020-03-23 10:23:55");
		loginInfoRequest.setVipflag("1");
		loginInfoRequest.setApp("trade");
		loginInfoRequest.setPlatformId("TAO");
		loginInfoRequest.setSellerNick("赵东昊的测试店铺");
		loginInfoRequest.setSubSellerNick(null);
		loginInfoRequest.setLastActivePoint("ww");

		refreshUserInfoRequest.setSellerNick("赵东昊的测试店铺");
		refreshUserInfoRequest.setSellerId("123123");
		refreshUserInfoRequest.setApp("trade");
		refreshUserInfoRequest.setPlatformId("TAO");
		refreshUserInfoRequest.setVipflag("3");
	}

	@org.junit.jupiter.api.Test
	public void loginTest1() throws Exception {
		userInfoRequest.setSellerNick(null);
		userInfoRequestMap = this.setMultiValueMap(userInfoRequest);
		MvcResult mvcResult = mockMvc.perform(post("/uac/user/login")
			.params(userInfoRequestMap))
			.andExpect(status().is4xxClientError())
			.andReturn();
		Assertions.assertEquals(400, mvcResult.getResponse().getStatus());
	}

	@org.junit.jupiter.api.Test
	public void loginTest2() throws Exception {
		userInfoRequestMap = this.setMultiValueMap(userInfoRequest);
		BDDMockito.given(userProxyService.login(any(),any(),any())).willThrow(new UserException(ApiCode.NO_EXIST_USER.code(), ApiCode.NO_EXIST_USER.message()));
		MvcResult mvcResult = mockMvc.perform(post("/uac/user/login")
			.params(userInfoRequestMap))
			.andExpect(status().isOk())
			.andReturn();

		Assertions.assertEquals(200, mvcResult.getResponse().getStatus());
		Assertions.assertEquals(ApiCode.NO_EXIST_USER.code(), JSON.parseObject(mvcResult.getResponse().getContentAsString()).get("sub_code"));
	}

	@org.junit.jupiter.api.Test
	public void loginTest3() throws Exception {
		userInfoRequestMap = this.setMultiValueMap(userInfoRequest);
		BDDMockito.given(userProxyService.login(any(),any(),any())).willReturn(getUserInfo());
		MvcResult mvcResult = mockMvc.perform(post("/uac/user/login")
			.params(userInfoRequestMap))
			.andExpect(status().isOk())
			.andReturn();

		Assertions.assertEquals(200, mvcResult.getResponse().getStatus());
	}

	@org.junit.jupiter.api.Test
	public void quickLoginTest1() throws Exception {
		loginInfoRequest.setSellerNick(null);
		loginInfoRequest.setVipflag(null);
		loginInfoRequestMap = this.setMultiValueMap(loginInfoRequest);
		MvcResult mvcResult = mockMvc.perform(post("/uac/user/quickLogin")
			.params(loginInfoRequestMap))
			.andExpect(status().is4xxClientError())
			.andReturn();

		Assertions.assertEquals(400, mvcResult.getResponse().getStatus());
	}

	@org.junit.jupiter.api.Test
	public void quickLoginTest2() throws Exception {
		loginInfoRequestMap = this.setMultiValueMap(loginInfoRequest);
		BDDMockito.given(userProxyService.quickLogin(any(),any(),any())).willThrow(new UserException(ApiCode.NO_EXIST_USER.code(), ApiCode.NO_EXIST_USER.message()));
		MvcResult mvcResult = mockMvc.perform(post("/uac/user/quickLogin")
			.params(loginInfoRequestMap))
			.andExpect(status().isOk())
			.andReturn();


		Assertions.assertEquals(200, mvcResult.getResponse().getStatus());
		Assertions.assertEquals(ApiCode.NO_EXIST_USER.code(), JSON.parseObject(mvcResult.getResponse().getContentAsString()).get("sub_code"));
	}

	@org.junit.jupiter.api.Test
	public void quickLoginTest3() throws Exception {
		loginInfoRequestMap = this.setMultiValueMap(loginInfoRequest);
		BDDMockito.given(userProxyService.quickLogin(any(),any(),any())).willReturn(getUserInfo());
		MvcResult mvcResult = mockMvc.perform(post("/uac/user/quickLogin")
			.params(loginInfoRequestMap))
			.andExpect(status().isOk())
			.andReturn();

		Assertions.assertEquals(200, mvcResult.getResponse().getStatus());
	}
	/*
	@org.junit.jupiter.api.Test
	public void getUserInfoTest1() throws Exception {
		userInfoRequest.setSellerNick(null);
		userInfoRequestMap = this.setMultiValueMap(userInfoRequest);
		MvcResult mvcResult = mockMvc.perform(post("/uac/user/getUserInfo")
			.params(userInfoRequestMap))
			.andExpect(status().is4xxClientError())
			.andReturn();

		Assertions.assertEquals(400, mvcResult.getResponse().getStatus());
	}

	@org.junit.jupiter.api.Test
	public void getUserInfoTest2() throws Exception {
		userInfoRequestMap = this.setMultiValueMap(userInfoRequest);
		BDDMockito.given(userProxyService.getUserInfo(any(),any(),any())).willThrow(new UserException(ApiCode.NO_EXIST_USER.code(), ApiCode.NO_EXIST_USER.message()));
		MvcResult mvcResult = mockMvc.perform(post("/uac/user/getUserInfo")
			.params(userInfoRequestMap))
			.andExpect(status().isOk())
			.andReturn();

		Assertions.assertEquals(200, mvcResult.getResponse().getStatus());
		Assertions.assertEquals(ApiCode.NO_EXIST_USER.code(), JSON.parseObject(mvcResult.getResponse().getContentAsString()).get("sub_code"));
	}

	@org.junit.jupiter.api.Test
	public void getUserInfoTest3() throws Exception {
		userInfoRequestMap = this.setMultiValueMap(userInfoRequest);
		BDDMockito.given(userProxyService.getUserInfo(any(),any(),any())).willReturn(getUserInfo());
		MvcResult mvcResult = mockMvc.perform(post("/uac/user/getUserInfo")
			.params(userInfoRequestMap))
			.andExpect(status().isOk())
			.andReturn();

		Assertions.assertEquals(200, mvcResult.getResponse().getStatus());
	}*/

	@org.junit.jupiter.api.Test
	public void refreshUserInfoTest1() throws Exception {
		refreshUserInfoRequest.setSellerNick(null);
		refreshUserInfoRequestMap = this.setMultiValueMap(refreshUserInfoRequest);
		MvcResult mvcResult = mockMvc.perform(post("/uac/user/updatingUserInfo")
			.params(refreshUserInfoRequestMap))
			.andExpect(status().is4xxClientError())
			.andReturn();

		Assertions.assertEquals(400, mvcResult.getResponse().getStatus());
	}

	@org.junit.jupiter.api.Test
	public void refreshUserInfoTest2() throws Exception {
		refreshUserInfoRequestMap = this.setMultiValueMap(refreshUserInfoRequest);
		BDDMockito.given(userProxyService.refreshUserInfo(any(),any(),any())).willReturn(Boolean.FALSE);
		MvcResult mvcResult = mockMvc.perform(post("/uac/user/updatingUserInfo")
			.params(refreshUserInfoRequestMap))
			.andExpect(status().isOk())
			.andReturn();
		Assertions.assertEquals(200, mvcResult.getResponse().getStatus());
		Assertions.assertEquals(500, JSON.parseObject(mvcResult.getResponse().getContentAsString()).get("code"));
	}

	@org.junit.jupiter.api.Test
	public void refreshUserInfoTest3() throws Exception {
		refreshUserInfoRequestMap = this.setMultiValueMap(refreshUserInfoRequest);
		BDDMockito.given(userProxyService.refreshUserInfo(any(),any(),any())).willReturn(Boolean.TRUE);
		MvcResult mvcResult = mockMvc.perform(post("/uac/user/updatingUserInfo")
			.params(refreshUserInfoRequestMap))
			.andExpect(status().isOk())
			.andReturn();
		Assertions.assertEquals(200, mvcResult.getResponse().getStatus());
	}

	@org.junit.jupiter.api.Test
	public void refreshUserInfoTest4() throws Exception {
		refreshUserInfoRequest.setVipflag(null);
		refreshUserInfoRequestMap = this.setMultiValueMap(refreshUserInfoRequest);
		BDDMockito.given(userProxyService.refreshUserInfo(any(),any(),any())).willReturn(Boolean.TRUE);
		MvcResult mvcResult = mockMvc.perform(post("/uac/user/updatingUserInfo")
			.params(refreshUserInfoRequestMap))
			.andExpect(status().isOk())
			.andReturn();
		Assertions.assertEquals(200, mvcResult.getResponse().getStatus());
		Assertions.assertEquals(400, JSON.parseObject(mvcResult.getResponse().getContentAsString()).get("code"));
	}

	@org.junit.jupiter.api.Test
	public void getTopSessionTest1() throws Exception {
		userInfoRequest.setSellerNick(null);
		userInfoRequestMap = this.setMultiValueMap(userInfoRequest);
		MvcResult mvcResult = mockMvc.perform(post("/uac/user/getTopSession")
			.params(userInfoRequestMap))
			.andExpect(status().is4xxClientError())
			.andReturn();

		Assertions.assertEquals(400, mvcResult.getResponse().getStatus());
	}

	@org.junit.jupiter.api.Test
	public void getTopSessionTest2() throws Exception {
		userInfoRequestMap = this.setMultiValueMap(userInfoRequest);
		BDDMockito.given(userProxyService.getAccessToken(any(),any(),any())).willThrow(new UserException(ApiCode.NO_EXIST_USER.code(), ApiCode.NO_EXIST_USER.message()));
		MvcResult mvcResult = mockMvc.perform(post("/uac/user/getTopSession")
			.params(userInfoRequestMap))
			.andExpect(status().isOk())
			.andReturn();
		Assertions.assertEquals(200, mvcResult.getResponse().getStatus());
		Assertions.assertEquals(ApiCode.NO_EXIST_USER.code(), JSON.parseObject(mvcResult.getResponse().getContentAsString()).get("sub_code"));
	}

	@org.junit.jupiter.api.Test
	public void getTopSessionTest3() throws Exception {
		userInfoRequestMap = this.setMultiValueMap(userInfoRequest);
		BDDMockito.given(userProxyService.getAccessToken(any(),any(),any())).willReturn(getUserInfo());
		MvcResult mvcResult = mockMvc.perform(post("/uac/user/getTopSession")
			.params(userInfoRequestMap))
			.andExpect(status().isOk())
			.andReturn();
		Assertions.assertEquals(200, mvcResult.getResponse().getStatus());
	}

	@org.junit.jupiter.api.Test
	public void getTopSessionTest4() throws Exception {
		userInfoRequestMap = this.setMultiValueMap(userInfoRequest);
		BDDMockito.given(userProxyService.getAccessToken(any(),any(),any())).willThrow(new Exception());
		MvcResult mvcResult = mockMvc.perform(post("/uac/user/getTopSession")
			.params(userInfoRequestMap))
			.andExpect(status().isOk())
			.andReturn();
		Assertions.assertEquals(200, mvcResult.getResponse().getStatus());
		Assertions.assertEquals(500, JSON.parseObject(mvcResult.getResponse().getContentAsString()).get("code"));
	}

	@org.junit.jupiter.api.Test
	public void rebuildUserInfoTest1() throws Exception {
		userInfoRequest.setSellerNick(null);
		userInfoRequestMap = this.setMultiValueMap(userInfoRequest);
		MvcResult mvcResult = mockMvc.perform(post("/uac/user/rebuildUserInfo")
			.params(userInfoRequestMap))
			.andExpect(status().is4xxClientError())
			.andReturn();
		Assertions.assertEquals(400, mvcResult.getResponse().getStatus());
	}

	@org.junit.jupiter.api.Test
	public void rebuildUserInfoTest2() throws Exception {
		userInfoRequestMap = this.setMultiValueMap(userInfoRequest);
		BDDMockito.given(userProxyService.rebuildUserInfo(any(),any(),any())).willThrow(new UserException(ApiCode.NO_EXIST_USER.code(), ApiCode.NO_EXIST_USER.message()));
		MvcResult mvcResult = mockMvc.perform(post("/uac/user/rebuildUserInfo")
			.params(userInfoRequestMap))
			.andExpect(status().isOk())
			.andReturn();
		Assertions.assertEquals(200, mvcResult.getResponse().getStatus());
		Assertions.assertEquals(ApiCode.NO_EXIST_USER.code(), JSON.parseObject(mvcResult.getResponse().getContentAsString()).get("sub_code"));
	}

	@org.junit.jupiter.api.Test
	public void rebuildUserInfoTest3() throws Exception {
		userInfoRequestMap = this.setMultiValueMap(userInfoRequest);
		BDDMockito.given(userProxyService.rebuildUserInfo(any(),any(),any())).willReturn(null);
		MvcResult mvcResult = mockMvc.perform(post("/uac/user/rebuildUserInfo")
			.params(userInfoRequestMap))
			.andExpect(status().isOk())
			.andReturn();
		Assertions.assertEquals(200, mvcResult.getResponse().getStatus());
		Assertions.assertEquals(500, JSON.parseObject(mvcResult.getResponse().getContentAsString()).get("code"));
	}

	@org.junit.jupiter.api.Test
	public void rebuildUserInfoTest4() throws Exception {
		userInfoRequestMap = this.setMultiValueMap(userInfoRequest);
		BDDMockito.given(userProxyService.rebuildUserInfo(any(),any(),any())).willReturn(getUserInfo());
		MvcResult mvcResult = mockMvc.perform(post("/uac/user/rebuildUserInfo")
			.params(userInfoRequestMap))
			.andExpect(status().isOk())
			.andReturn();
		Assertions.assertEquals(200, mvcResult.getResponse().getStatus());
	}

	private UserInfoResponse getUserInfo() {
		UserInfoResponse userInfo = new UserInfoResponse();
		userInfo.setAuthDeadLine(LocalDateTime.now());
		userInfo.setSellerId("123123");
		userInfo.setSellerNick("赵东昊的测试店铺");
		userInfo.setOrderCycleEnd(LocalDateTime.now().plusDays(7));
		userInfo.setTopSession("xxxxxx");
		return userInfo;
	}

	private <T> MultiValueMap<String, String> setMultiValueMap(T classExample){
		MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
		for (Map.Entry<String, Object> entry :((JSONObject) JSON.toJSON(classExample)).entrySet()) {
			if(null != entry.getValue() && StringUtils.isNotEmpty(entry.getValue().toString())){
				multiValueMap.add(entry.getKey(),entry.getValue().toString());
			}
		}
		return multiValueMap;
	}
}
