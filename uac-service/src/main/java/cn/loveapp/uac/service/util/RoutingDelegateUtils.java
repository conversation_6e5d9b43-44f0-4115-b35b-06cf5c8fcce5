package cn.loveapp.uac.service.util;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.Collections;
import java.util.Enumeration;
import java.util.List;

/**
 * 转发代理 工具类
 *
 * <AUTHOR>
 */
public class RoutingDelegateUtils {

    /**
     * request请求转发到指定的 redirectHost
     *
     * @param requestRedirectDTO
     *
     * @return
     * @throws Exception
     */
    public static <T> ResponseEntity<String> redirect(RequestRedirectDTO<T> requestRedirectDTO) throws Exception {
        String redirectUrl = createRedirectUrl(requestRedirectDTO.getRequestURI(),
                requestRedirectDTO.getQueryString(), requestRedirectDTO.getRedirectHost());
        HttpEntity requestEntity;
        String method = requestRedirectDTO.getMethod();
        HttpMethod httpMethod = HttpMethod.valueOf(method);
        MultiValueMap<String, String> headers = requestRedirectDTO.getHeaders();
        if (headers != null) {
            headers.remove("Host");
            headers.remove("Content-Length");
        }

        if (requestRedirectDTO.getBody() == null) {
            requestEntity = new HttpEntity(requestRedirectDTO.getParameters(), headers);
        } else {
            requestEntity = new HttpEntity(requestRedirectDTO.getBody(), headers);
        }

        RestTemplate restTemplate = new RestTemplate();
        try {
            return restTemplate.exchange(new URI(redirectUrl), httpMethod, requestEntity, String.class);
        } catch (HttpClientErrorException e) {
            return new ResponseEntity<>(e.getResponseBodyAsString(), e.getStatusCode());
        }
    }

    private static String createRedirectUrl(String requestURI ,String queryString, String redirectHost) {
        return redirectHost + requestURI + (queryString != null ? "?" + queryString : "");
    }

    public static MultiValueMap<String, String> parseRequestHeader(HttpServletRequest request) {
        HttpHeaders headers = new HttpHeaders();
        List<String> headerNames = Collections.list(request.getHeaderNames());
        for (String headerName : headerNames) {
            List<String> headerValues = Collections.list(request.getHeaders(headerName));
            for (String headerValue : headerValues) {
                headers.add(headerName, headerValue);
            }
        }
        return headers;
    }

    public static MultiValueMap<String, String> parseRequestParameter(HttpServletRequest request) {
        MultiValueMap<String, String> bodyMap = new LinkedMultiValueMap<>();
        Enumeration<String> parameterNames = request.getParameterNames();
        while (parameterNames.hasMoreElements()) {
            String key = parameterNames.nextElement();
            bodyMap.set(key, request.getParameter(key));
        }
        return bodyMap;
    }

    @Data
    public static class RequestRedirectDTO<T> {
        /**
         * HTTP request method
         */
        @NotNull
        private String method;

        /**
         * HTTP request Headers
         */
        @NotNull
        private MultiValueMap<String, String> headers;

        /**
         * HTTP request URI
         */
        @NotNull
        private String requestURI;

        /**
         * the query string that is contained in the request URL after the path
         */
        private String queryString;

        /**
         * HTTP request Parameters
         * <p>
         * 一般是form-data 请求的参数
         */
        private MultiValueMap<String, String> parameters;

        /**
         * HTTP request body
         * <p>
         * 一般是@RequestBody 传入的JSON字符串 对应的实体对象
         */
        private T body;

        /**
         * 需要转发到对应服务的host
         */
        @NotEmpty
        private String redirectHost;
    }
}
