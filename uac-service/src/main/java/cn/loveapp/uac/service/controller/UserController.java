package cn.loveapp.uac.service.controller;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.uac.code.ApiCode;
import cn.loveapp.uac.common.bo.LoginUserInfoBo;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.exception.DbWriteException;
import cn.loveapp.uac.exception.UserException;
import cn.loveapp.uac.request.LoginInfoRequest;
import cn.loveapp.uac.request.MethodInterface.MethodGet;
import cn.loveapp.uac.request.MethodInterface.MethodPost;
import cn.loveapp.uac.request.RefreshUserInfoRequest;
import cn.loveapp.uac.request.UserInfoRequest;
import cn.loveapp.uac.response.UserInfoResponse;
import cn.loveapp.uac.service.platform.biz.UserPlatformHandleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: uac-service-group
 * @description: SellerController
 * @author: Jason
 * @create: 2020-03-03 18:18
 **/
@RestController
@RequestMapping("uac/user")
public class UserController {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(UserController.class);

	@Autowired
	private UserPlatformHandleService userPlatformHandleService;

	/**
	 * 登录用户信息详情(aiyong.usercenter.login)
	 */
	@RequestMapping(value = "login", method = {RequestMethod.POST,RequestMethod.GET})
	public CommonApiResponse<UserInfoResponse> login(@Validated(MethodGet.class) UserInfoRequest userInfoRequest) {
		try {
			UserInfoResponse userInfo = userPlatformHandleService.login(UserInfoBo.of(userInfoRequest),
				userInfoRequest.getPlatformId(), userInfoRequest.getApp());
			return CommonApiResponse.success(userInfo);
		}catch (UserException e) {
			CommonApiResponse commonApiResponse = new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(),
				ApiCode.NO_EXIST_USER.code(), ApiCode.NO_EXIST_USER.message(), null);
			return commonApiResponse;
		}
	}

	/**
	 * 登录(aiyong.usercenter.quick.login)
	 */
	@RequestMapping(value = "quickLogin", method = {RequestMethod.POST})
	public CommonApiResponse quickLogin(@Validated(MethodPost.class) LoginInfoRequest loginInfoRequest)
		throws DbWriteException {
		try {
			UserInfoResponse userInfo = userPlatformHandleService.quickLogin(LoginUserInfoBo.of(loginInfoRequest), loginInfoRequest.getPlatformId(), loginInfoRequest.getApp());
			return CommonApiResponse.success(userInfo);
		} catch (UserException e) {
			CommonApiResponse commonApiResponse = new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(),
				ApiCode.NO_EXIST_USER.code(), ApiCode.NO_EXIST_USER.message(), null);
			return commonApiResponse;
		}
	}

	/**
	 * 刷新用户信息(aiyong.usercenter.information.updating)
	 * @param refreshUserInfoRequest
	 * @return
	 */
	@RequestMapping(value = "updatingUserInfo", method = {RequestMethod.POST,RequestMethod.GET})
	public CommonApiResponse refreshUserInfo(@Validated RefreshUserInfoRequest refreshUserInfoRequest) {
		if (!refreshUserInfoRequest.isEmpty()) {
			return CommonApiResponse.of(CommonApiStatus.RequestParamError.code(), CommonApiStatus.RequestParamError.message());
		}
		Boolean refreshUserInfoResult = userPlatformHandleService.refreshUserInfo(UserInfoBo.of(refreshUserInfoRequest),
			refreshUserInfoRequest.getPlatformId(), refreshUserInfoRequest.getApp());
		if (Boolean.FALSE.equals(refreshUserInfoResult)) {
			return CommonApiResponse.of(CommonApiStatus.ServerError.code(), CommonApiStatus.ServerError.message());
		}
		return CommonApiResponse.success();
	}


	/**
	 * 获取用户授权信息(aiyong.usercenter.token.get)
	 * @return CommonApiResponse<UserInfoResponse>
	 */
	@RequestMapping(value = "getTopSession", method = {RequestMethod.POST})
	public CommonApiResponse<UserInfoResponse> getTopSession(@Validated UserInfoRequest userInfoRequest) {
		try {
			UserInfoResponse userInfo = userPlatformHandleService.getAccessToken(UserInfoBo.of(userInfoRequest), userInfoRequest.getPlatformId(), userInfoRequest.getApp());
			return CommonApiResponse.success(userInfo);
		} catch (UserException e) {
			CommonApiResponse<UserInfoResponse> commonApiResponse = new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(),
				e.getCode(), e.getMessage(), null);
			return commonApiResponse;
		} catch (Exception e) {
			LOGGER.logError(userInfoRequest.getSellerNick(), "-", "系统异常, 原因为:" + e.getMessage(), e);
			return CommonApiResponse.of(CommonApiStatus.ServerError.code(), CommonApiStatus.ServerError.message(), null);
		}
	}

}
