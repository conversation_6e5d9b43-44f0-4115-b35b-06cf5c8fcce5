package cn.loveapp.uac.service.interceptor;

import cn.loveapp.uac.service.web.VersionGrayHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * @program: uac-service-group
 * @description: RequestInterceptorConfig
 * @author: Jason
 * @create: 2020-05-26 17:45
 **/
@Configuration
public class RequestInterceptorConfig implements WebMvcConfigurer {

	@Autowired(required = false)
	private VersionGrayHandler versionGrayHandler;

	@Bean
	public UserRequestInterceptor userRequestInterceptor(){
		return new UserRequestInterceptor();
	}

	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		//注册拦截器
		registry.addInterceptor(this.userRequestInterceptor())
			.order(Ordered.HIGHEST_PRECEDENCE)
			.addPathPatterns("/**");

		if (versionGrayHandler != null) {
			registry.addInterceptor(versionGrayHandler).addPathPatterns("/**");
		}
	}
}
