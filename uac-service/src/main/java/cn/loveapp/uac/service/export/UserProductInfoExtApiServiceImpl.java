package cn.loveapp.uac.service.export;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.uac.db.common.repository.UserProductionInfoExtRepository;
import cn.loveapp.uac.domain.UserExtInfoDTO;
import cn.loveapp.uac.entity.UserProductInfoBusinessExt;
import cn.loveapp.uac.request.GetUserInfoExtRequest;
import cn.loveapp.uac.request.UpdateUserInfoExtRequest;
import cn.loveapp.uac.response.GetUserInfoExtResponse;
import cn.loveapp.uac.response.UpdateUserInfoExtResponse;
import cn.loveapp.uac.service.UserProductInfoExtApiService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.stream.Collectors;

import static cn.loveapp.common.web.CommonApiStatus.RequestParamError;

/**
 * @Author: zhongzijie
 * @Date: 2023/2/16 9:29
 * @Description:
 */
@RestController
@RequestMapping(value = UserProductInfoExtApiService.PATH)
public class UserProductInfoExtApiServiceImpl implements UserProductInfoExtApiService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(UserProductInfoExtApiServiceImpl.class);

    @Autowired
    private UserProductionInfoExtRepository userProductionInfoExtRepository;

    @Override
    public CommonApiResponse<GetUserInfoExtResponse> getUserInfoExtBySellerId(GetUserInfoExtRequest request) {
        String businessId = request.getBusinessId();
        String sellerId = request.getSellerId();
        String storeId = request.getStoreId();
        String appName = request.getAppName();
        String memberId = request.getMemberId();
        boolean isIdEmpty = StringUtils.isEmpty(memberId) && StringUtils.isEmpty(sellerId);
        if (StringUtils.isEmpty(businessId) || StringUtils.isEmpty(storeId) || StringUtils.isEmpty(appName)
            || isIdEmpty) {
            return CommonApiResponse.failed(RequestParamError.code(), RequestParamError.message());
        }
        UserProductInfoBusinessExt userProductInfoBusinessExt = null;
        if (CommonPlatformConstants.PLATFORM_1688.equals(storeId) && StringUtils.isEmpty(sellerId)
            && !StringUtils.isEmpty(memberId)) {
            userProductInfoBusinessExt =
                userProductionInfoExtRepository.querySingleByMemberId(memberId, storeId, appName, businessId);
        } else {
            userProductInfoBusinessExt =
                userProductionInfoExtRepository.querySingleBySellerId(sellerId, storeId, appName, businessId);
        }

        GetUserInfoExtResponse getUserInfoExtResponse = new GetUserInfoExtResponse();
        if (userProductInfoBusinessExt == null) {
            return CommonApiResponse.success(getUserInfoExtResponse);
        }
        UserExtInfoDTO userExtInfoDTO = new UserExtInfoDTO();
        String products = userProductInfoBusinessExt.getProducts();
        if (StringUtils.isNotEmpty(products)) {
            userExtInfoDTO.setProducts(Arrays.stream(products.split(",")).collect(Collectors.toList()));
        }
        BeanUtils.copyProperties(userProductInfoBusinessExt, userExtInfoDTO);
        getUserInfoExtResponse.setUserInfoExt(userExtInfoDTO);
        return CommonApiResponse.success(getUserInfoExtResponse);
    }

    @Override
    public CommonApiResponse<UpdateUserInfoExtResponse> updateUserInfoExtBySellerId(UpdateUserInfoExtRequest request) {
        String businessId = request.getBusinessId();
        UserExtInfoDTO userExtInfoDTO = request.getUserExtInfoDTO();
        if (StringUtils.isEmpty(businessId)
                || userExtInfoDTO == null
                || StringUtils.isEmpty(userExtInfoDTO.getSellerId())
                || StringUtils.isEmpty(userExtInfoDTO.getStoreId())
                || StringUtils.isEmpty(userExtInfoDTO.getAppName())) {
            return CommonApiResponse.failed(RequestParamError.code(), RequestParamError.message());
        }
        UserProductInfoBusinessExt userProductInfoBusinessExt = new UserProductInfoBusinessExt();
        BeanUtils.copyProperties(userExtInfoDTO, userProductInfoBusinessExt);
        userProductInfoBusinessExt.setGmtCreate(null);
        userProductInfoBusinessExt.setGmtModified(LocalDateTime.now());
        int rows = userProductionInfoExtRepository.update(userProductInfoBusinessExt, businessId);
        UpdateUserInfoExtResponse updateUserInfoExtResponse = new UpdateUserInfoExtResponse();
        updateUserInfoExtResponse.setRows((long) rows);
        return CommonApiResponse.success(updateUserInfoExtResponse);
    }
}
