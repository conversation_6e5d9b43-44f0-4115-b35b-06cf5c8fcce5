package cn.loveapp.uac.service.interceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.MDC;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * @program: uac-service-group
 * @description: UserRequestInterceptor
 * @author: Jason
 * @create: 2020-05-26 17:44
 **/
public class UserRequestInterceptor implements HandlerInterceptor {

    @Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
		MDC.remove("sellerNick");
		MDC.remove("subSellerNick");
		MDC.remove("sellerId");
		MDC.remove("subSellerId");
		MDC.remove("platform");
		MDC.remove("appName");
		String sellerNick = request.getParameter("sellerNick");
		String subSellerNick = request.getParameter("subSellerNick");
		String sellerId = request.getParameter("sellerId");
		String subSellerId = request.getParameter("subSellerId");
		String platform = request.getParameter("platformId");
		String appName = request.getParameter("app");
		MDC.put("sellerNick", sellerNick);
		MDC.put("subSellerNick", subSellerNick);
		MDC.put("sellerId", sellerId);
		MDC.put("subSellerId", subSellerId);
		MDC.put("platform", platform);
		MDC.put("appName", appName);
		return true;
	}
}
