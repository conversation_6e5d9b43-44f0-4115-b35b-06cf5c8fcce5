package cn.loveapp.uac.service.controller;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.config.web.WarnUpRequestData;
import cn.loveapp.uac.common.controller.BaseCloudNativeController;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.sql.DataSource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 探活Controller
 *
 * <AUTHOR>
 * @date 2019-04-16
 */
@Controller
@RequestMapping("/")
public class CloudNativeController extends BaseCloudNativeController {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(CloudNativeController.class);

	public CloudNativeController(ObjectProvider<List<DataSource>> provider) {
		super(provider);
	}

	@Override
	protected HttpStatus checkReadNess() {
        return super.checkReadNess();
	}

}
