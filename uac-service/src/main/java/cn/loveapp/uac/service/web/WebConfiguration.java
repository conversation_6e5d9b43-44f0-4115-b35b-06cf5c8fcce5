package cn.loveapp.uac.service.web;

import cn.loveapp.common.autoconfigure.web.HttpTraceLogJsonFilter;
import com.alibaba.fastjson2.filter.ValueFilter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.stream.Collectors;

@Configuration
public class WebConfiguration {

    @Bean
    public HttpTraceLogJsonValueFilter httpTraceLogJsonValueFilter() {
        return new HttpTraceLogJsonValueFilter();
    }

    /**
     * 优化请求响应日志，忽略或缩减一些无用日志
     */
    public static class HttpTraceLogJsonValueFilter implements HttpTraceLogJsonFilter, ValueFilter {
        @Override
        public Object apply(Object object, String name, Object value) {
            if ("tag".equals(name) && value instanceof String) {
                // 过滤掉过长的 tag 信息
                return StringUtils.contains((String) value, "tos") ? "**,tos,**" : "***";
            } else if ("settings".equals(name) && value instanceof ArrayList && ((ArrayList<?>)value).getFirst() instanceof String) {
                // 替换缩写掉过长的 设置 信息
                return ((ArrayList<?>) value).stream()
                    .map(HttpTraceLogJsonValueFilter::abbreviatedSettingsName).collect(Collectors.toList());
            } else if ("key".equals(name) && value instanceof String) {
                return abbreviatedSettingsName(value);
            }
            return value;
        }

        private static String abbreviatedSettingsName(Object value) {
            if(value == null) {
                return null;
            }
            return value.toString().replace("abnormal.deliver", "a*.de*")
                .replace("abnormal.aftersale", "a*.af*")
                .replace("abnormal.logistics", "a*.lo*");
        }
    }
}

