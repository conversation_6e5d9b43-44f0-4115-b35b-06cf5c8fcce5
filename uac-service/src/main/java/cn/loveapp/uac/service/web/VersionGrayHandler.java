package cn.loveapp.uac.service.web;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.request.BaseHttpRequest;
import cn.loveapp.uac.service.config.VersionGrayConfig;
import cn.loveapp.uac.service.util.RoutingDelegateUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdvice;

import java.io.IOException;
import java.lang.reflect.Type;

/**
 * 版本灰度 处理器
 *
 * <AUTHOR>
 */
@ConditionalOnProperty(prefix = VersionGrayConfig.CONFIGURATION_PREFIX, name = "enable", havingValue = "true")
@ControllerAdvice
public class VersionGrayHandler implements HandlerInterceptor, RequestBodyAdvice {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(VersionGrayHandler.class);

    private static final String SELLER_NICK_REQUEST_PARAM = "sellerNick";

    private static final String PLATFORM_ID_REQUEST_PARAM = "platformId";

    private static final String APP_NAME_REQUEST_PARAM = "app";

    private static final String SPLIT = "_";

    @Autowired
    private VersionGrayConfig versionGrayConfig;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String sellerNick = request.getParameter(SELLER_NICK_REQUEST_PARAM);
        String platformId = request.getParameter(PLATFORM_ID_REQUEST_PARAM);
        String appName = request.getParameter(APP_NAME_REQUEST_PARAM);
        if (checkGrayConditions(sellerNick, platformId, appName)) {
            LOGGER.logInfo(request.getParameter(SELLER_NICK_REQUEST_PARAM), platformId, "灰度名单中用户, 准备进行灰度转发");
            throw new VersionGrayHitException(sellerNick);
        }
        return true;
    }

    @Override
    public boolean supports(MethodParameter methodParameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        return versionGrayConfig.isEnable();
    }

    @Override
    public HttpInputMessage beforeBodyRead(HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) throws IOException {
        return inputMessage;
    }

    @SneakyThrows
    @Override
    public Object afterBodyRead(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        if (body instanceof BaseHttpRequest) {
            String sellerNick = ((BaseHttpRequest) body).getSellerNick();
            String platformId = ((BaseHttpRequest) body).getPlatformId();
            String appName = ((BaseHttpRequest) body).getApp();
            if (checkGrayConditions(sellerNick, platformId, appName)) {
                LOGGER.logInfo(sellerNick, platformId, "灰度名单中用户, 准备进行灰度转发");
                throw new VersionGrayHitException((BaseHttpRequest) body);
            }
        }
        return body;
    }

    @Override
    public Object handleEmptyBody(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        return body;
    }


    /**
     * 处理VersionGrayHitException异常, 执行灰度策略
     *
     * @param request
     */
    @ExceptionHandler(value = VersionGrayHitException.class)
    public ResponseEntity<String> catVersionGrayHitException(HttpServletRequest request, VersionGrayHitException e) throws Exception {
        RoutingDelegateUtils.RequestRedirectDTO<BaseHttpRequest> requestRedirectDTO = new RoutingDelegateUtils.RequestRedirectDTO<>();
        requestRedirectDTO.setRedirectHost(versionGrayConfig.getServiceHost());
        requestRedirectDTO.setMethod(request.getMethod());
        requestRedirectDTO.setHeaders(RoutingDelegateUtils.parseRequestHeader(request));
        requestRedirectDTO.setRequestURI(request.getRequestURI());
        requestRedirectDTO.setQueryString(request.getQueryString());
        requestRedirectDTO.setParameters(RoutingDelegateUtils.parseRequestParameter(request));
        requestRedirectDTO.setBody(e.getRequestBody());
        return RoutingDelegateUtils.redirect(requestRedirectDTO);
    }

    /**
     * 灰度命中的异常
     * <p>
     * 判断request_body中信息满足灰度条件后抛出异常信息, 通过异常处理来执行灰度逻辑
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class VersionGrayHitException extends Exception {
        private BaseHttpRequest requestBody;

        public VersionGrayHitException() {
            super("灰度转发");
        }

        public VersionGrayHitException(String message) {
            super("灰度转发: " + message);
        }

        public VersionGrayHitException(BaseHttpRequest requestBody) {
            super("灰度转发: " + requestBody.getSellerNick());
            this.requestBody = requestBody;
        }
    }

    /**
     * 校验是否满足灰度条件
     *
     * @param sellerNick
     * @param platformId
     * @param appName
     * @return
     */
    private boolean checkGrayConditions(String sellerNick, String platformId, String appName){
        if (!versionGrayConfig.isEnable()){
            return false;
        }
        if (versionGrayConfig.getUsers().contains(sellerNick)){
            return true;
        }
        if (versionGrayConfig.getPlatforms().contains(platformId)){
            return true;
        }
        if (versionGrayConfig.getPlatforms().contains(platformId + SPLIT + appName)){
            return true;
        }
        return false;
    }
}
