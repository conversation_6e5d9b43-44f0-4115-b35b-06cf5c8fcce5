package cn.loveapp.uac.service;

import cn.loveapp.common.utils.LoggerHelper;
import org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisReactiveAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.util.StringUtils;

/**
 * ServiceApplication
 *
 * <AUTHOR>
 * @date 2020-03-03 16:56
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, RedisAutoConfiguration.class},
	scanBasePackages = {"cn.loveapp.uac.service","cn.loveapp.uac.common",
	"cn.loveapp.uac.db.common"})
public class UacServiceApplication {

	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(UacServiceApplication.class);
	private static final String APOLLO_ENV = "env";

	/**
	 * Description 程序主入口
	 *
	 * <AUTHOR> Jiang
	 * @date 2018-09-21 23:37
	 */
	public static void main(String[] args) {
        SpringApplication.run(UacServiceApplication.class, args);
	}}
