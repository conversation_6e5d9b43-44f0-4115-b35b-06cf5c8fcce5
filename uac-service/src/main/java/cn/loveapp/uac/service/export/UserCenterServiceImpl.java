package cn.loveapp.uac.service.export;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.uac.code.ApiCode;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.dto.QueryUserSettingsParam;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.contant.AyMultiTagType;
import cn.loveapp.uac.db.common.dao.dream.AyMultiUserTagDao;
import cn.loveapp.uac.db.common.entity.AyMultiUserTag;
import cn.loveapp.uac.db.common.entity.UserSettings;
import cn.loveapp.uac.db.common.entity.UserShopInfoMapping;
import cn.loveapp.uac.db.common.repository.UserRepository;
import cn.loveapp.uac.db.common.repository.UserSettingsRepository;
import cn.loveapp.uac.db.common.service.PlatformUserProductInfoService;
import cn.loveapp.uac.domain.UserSettingDTO;
import cn.loveapp.uac.exception.UserException;
import cn.loveapp.uac.request.*;
import cn.loveapp.uac.response.*;
import cn.loveapp.uac.service.UserCenterInnerApiService;
import cn.loveapp.uac.service.platform.biz.UserPlatformHandleService;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.loveapp.common.web.CommonApiStatus.RequestParamError;

/**
 * @program: uac-service-group
 * @description: UserCenterServiceImpl
 * @author: Jason
 * @create: 2020-06-05 11:01
 **/
@RestController
@RequestMapping(value = UserCenterInnerApiService.PATH)
public class UserCenterServiceImpl implements UserCenterInnerApiService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(UserCenterServiceImpl.class);

	@Autowired
	private UserPlatformHandleService userPlatformHandleService;

    @Autowired
    private UserRepository userRepository;

	/**
	 * 检验淘宝vipflag是否过期开关
	 */
	@Value("${uac.service.fullinfo.checkTaobaoVipflagIsExpired.enabled:false}")
	private Boolean checkTaobaoVipflagIsExpired;

	@Autowired
	private UserSettingsRepository userSettingsRepository;

	@Autowired
	private AyMultiUserTagDao ayMultiUserTagDao;

    @Autowired
    private PlatformUserProductInfoService platformUserProductInfoService;

	/**
	 * 获取用户信息详情(aiyong.usercenter.information.get)
	 * @return CommonApiResponse<UserInfoResponse>
	 */
	@Override
	public CommonApiResponse<UserInfoResponse> getUserInfo(@RequestBody @Valid UserInfoRequest userInfoRequest) {
		try {
            logRequestParams(userInfoRequest);
			UserInfoResponse userInfo = userPlatformHandleService.getUserInfo(UserInfoBo.of(userInfoRequest),
						userInfoRequest.getPlatformId(), userInfoRequest.getApp());
			userInfo.setVipflag(getVipflag(userInfo.getOrderCycleEnd(),userInfo.getVipflag(),userInfoRequest.getPlatformId()));
			return CommonApiResponse.success(userInfo);
		} catch (UserException e) {
			CommonApiResponse<UserInfoResponse> commonApiResponse = new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(),
				ApiCode.NO_EXIST_USER.code(), ApiCode.NO_EXIST_USER.message(), null);
			return commonApiResponse;
		}
	}

	@Override
	public CommonApiResponse<UserFullInfoResponse> getUserFullInfo(@RequestBody @Valid UserFullInfoRequest userFullInfoRequest) throws Exception {
		try {
            logRequestParams(userFullInfoRequest);
			UserProductInfo userProductInfo = userPlatformHandleService.getUserFullInfo(UserInfoBo.of(userFullInfoRequest), true, userFullInfoRequest.getPlatformId(), userFullInfoRequest.getApp());
			UserFullInfoResponse userFullInfoResponse = fillWithUserFullInfoInner(userProductInfo);
			userFullInfoResponse.setVipflag(getVipflag(userFullInfoResponse.getOrderCycleEnd(),userFullInfoResponse.getVipflag(),userFullInfoRequest.getPlatformId()));
			return CommonApiResponse.success(userFullInfoResponse);
		} catch (UserException e) {
			CommonApiResponse<UserFullInfoResponse> commonApiResponse = new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(),
				ApiCode.NO_EXIST_USER.code(), ApiCode.NO_EXIST_USER.message(), null);
			return commonApiResponse;
		}
	}

	@Override
	public CommonApiResponse<List<UserFullInfoResponse>> batchGetUserFullInfo(@RequestBody @Valid BatchGetUserFullInfoRequest batchGetUserFullInfoRequest) throws Exception {

		logRequestParams(batchGetUserFullInfoRequest);
		List<UserFullInfoRequest> userFullInfoRequests = batchGetUserFullInfoRequest.getUserFullInfoRequests();
		List<UserFullInfoResponse> result = Lists.newArrayList();
		for (UserFullInfoRequest userFullInfoRequest : userFullInfoRequests) {
			try {
				UserProductInfo userProductInfo = userPlatformHandleService.getUserFullInfo(UserInfoBo.of(userFullInfoRequest), true, userFullInfoRequest.getPlatformId(), userFullInfoRequest.getApp());
				UserFullInfoResponse userFullInfoResponse = fillWithUserFullInfoInner(userProductInfo);
				userFullInfoResponse.setVipflag(getVipflag(userFullInfoResponse.getOrderCycleEnd(),userFullInfoResponse.getVipflag(),userFullInfoRequest.getPlatformId()));
				result.add(userFullInfoResponse);
			} catch (UserException e) {
				LOGGER.logWarn(userFullInfoRequest.getSellerNick(), userFullInfoRequest.getPlatformId(), "该用户不存在");
				continue;
			}
		}
		return CommonApiResponse.success(result);

	}

	private UserFullInfoResponse fillWithUserFullInfoInner(UserProductInfo userProductInfo) {
		String sellerId = StringUtils.isBlank(userProductInfo.getUserIdStr()) ? userProductInfo.getUserId()+ "" : userProductInfo.getUserIdStr();
		UserFullInfoResponse userFullInfoInner = new UserFullInfoResponse();
		BeanUtils.copyProperties(userProductInfo, userFullInfoInner);
		userFullInfoInner.setSellerNick(userProductInfo.getNick());
		userFullInfoInner.setSellerId(sellerId);
		userFullInfoInner.setAccessToken(userProductInfo.getTopsessionkey());
		userFullInfoInner.setRefreshToken(userProductInfo.getToprefreshkey());
		userFullInfoInner.setIsNeedauth(userProductInfo.getIsNeedauth());
		return userFullInfoInner;
	}

	/**
	 * 根据memberid获取用户信息
	 */
	@Override
	public CommonApiResponse<UserInfoMemberResponse> getUserInfoByMemberId(@RequestBody @Valid UserInfoMemberRequest userInfoRequest) {
		try {
            logRequestParams(userInfoRequest);
			UserInfoResponse userInfo = userPlatformHandleService.getUserInfo(UserInfoBo.of(userInfoRequest), userInfoRequest.getMemberId(),
					userInfoRequest.getPlatformId(), userInfoRequest.getApp());
			UserInfoMemberResponse userInfoMemberResponse = new UserInfoMemberResponse();
			BeanUtils.copyProperties(userInfo, userInfoMemberResponse);
			userInfoMemberResponse.setVipflag(getVipflag(userInfo.getOrderCycleEnd(),userInfoMemberResponse.getVipflag(),userInfoRequest.getPlatformId()));
			return CommonApiResponse.success(userInfoMemberResponse);
		} catch (UserException e) {
			CommonApiResponse<UserInfoMemberResponse> commonApiResponse = new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(),
				ApiCode.NO_EXIST_USER.code(), ApiCode.NO_EXIST_USER.message(), null);
			return commonApiResponse;
		}
	}

	/**
	 * 批量获取用户基本信息
	 *
	 * @param userInfoRequestList
	 * @return
	 */
	@Override
	public CommonApiResponse<List<UserInfoResponse>> batchGetUserInfo(@RequestBody List<UserInfoRequest> userInfoRequestList) {
        logRequestParams(userInfoRequestList);
		List<UserInfoResponse> userInfoList = new ArrayList<>();
		for (UserInfoRequest userInfoRequest : userInfoRequestList) {
			try {
				UserInfoResponse userInfo = userPlatformHandleService.getUserInfo(UserInfoBo.of(userInfoRequest),
						userInfoRequest.getPlatformId(), userInfoRequest.getApp());
				userInfo.setVipflag(getVipflag(userInfo.getOrderCycleEnd(),userInfo.getVipflag(),userInfoRequest.getPlatformId()));
				userInfoList.add(userInfo);
			} catch (UserException e) {
				LOGGER.logInfo(userInfoRequest.getSellerNick(), "-",
						"账号<"+userInfoRequest.getSellerNick()+">,<"+userInfoRequest.getSubSellerNick()+">,不存在");
			}
		}
		return CommonApiResponse.success(userInfoList);
	}

	@Override
	public CommonApiResponse<Void> batchSettingUpdate(@RequestBody BatchSettingUpdateRequest batchSettingUpdateRequest) {
		logRequestParams(batchSettingUpdateRequest);
		List<UserInfoRequest> users = batchSettingUpdateRequest.getUsers();
		List<UserSettingDTO> settings = batchSettingUpdateRequest.getSettings();
		if (CollectionUtils.isEmpty(users) || CollectionUtils.isEmpty(settings)) {
			return CommonApiResponse.failed(CommonApiStatus.RequestParamError.code(), CommonApiStatus.RequestParamError.message());
		}
		try {
			List<UserSettings> userSettingsList = Lists.newArrayList();
			for (UserInfoRequest user : users) {
				String platformId = user.getPlatformId();
				String appName = user.getApp();
				String userId = user.getSellerId();
				if (StringUtils.isNotEmpty(user.getMemberId()) && CommonPlatformConstants.PLATFORM_1688.equals(platformId)) {
					userId = user.getMemberId();
				}

				for (UserSettingDTO settingDTO : settings) {
					String key = settingDTO.getKey();
					String value = settingDTO.getValue();
					String description = settingDTO.getDescription();
					UserSettings userSettings = new UserSettings();
					userSettings.setUserId(userId);
					userSettings.setPlatformId(platformId);
					userSettings.setAppName(appName);
					userSettings.setSettingKey(key);
					userSettings.setSettingValue(value);
					userSettings.setDescription(description);
					userSettingsList.add(userSettings);
				}
			}
			userSettingsRepository.batchUpsertUserSetting(userSettingsList);
		} catch (Exception e) {
			LOGGER.logError("批量更新用户设置异常: " + e.getMessage(), e);
			return CommonApiResponse.serverError();
		}
		return CommonApiResponse.success();
	}

	@Override
	public CommonApiResponse<List<UserSettingDTO>> batchSettingGet(@RequestBody BatchSettingGetRequest batchSettingGetRequest) {
		logRequestParams(batchSettingGetRequest);
		String userId = batchSettingGetRequest.getUserId();
		String platformId = batchSettingGetRequest.getPlatformId();
		String appName = batchSettingGetRequest.getApp();
		if (CommonPlatformConstants.PLATFORM_TAO.equals(platformId) && StringUtils.isEmpty(appName)) {
			appName = CommonAppConstants.APP_TRADE;
		}
		List<String> settings = batchSettingGetRequest.getSettings();
		if (StringUtils.isAnyEmpty(userId, platformId, appName) || CollectionUtils.isEmpty(settings)) {
			return CommonApiResponse.failed(CommonApiStatus.RequestParamError.code(), CommonApiStatus.RequestParamError.message());
		}
		try {
			QueryUserSettingsParam userSettingsParam = new QueryUserSettingsParam();
			userSettingsParam.setUserId(userId);
			userSettingsParam.setPlatformId(platformId);
			userSettingsParam.setAppName(appName);
			userSettingsParam.setSettingKeys(settings);
			userSettingsParam.setLoadDefaultSetting(batchSettingGetRequest.getLoadDefaultSetting());
			List<UserSettingDTO> userSettingDTOList = userSettingsRepository.batchQueryUserSetting(userSettingsParam);
			return CommonApiResponse.success(userSettingDTOList);
		} catch (Exception e) {
			LOGGER.logError("批量查询用户设置异常: " + e.getMessage(), e);
			return CommonApiResponse.serverError();
		}
	}

    @Override
    public CommonApiResponse<List<BatchUsersSettingGetResponse>>
        batchUsersSettingGet(@RequestBody BatchUsersSettingGetRequest batchUsersSettingGetRequest) {

        logRequestParams(batchUsersSettingGetRequest);
        List<BatchSettingGetRequest> batchSettingGetParams = batchUsersSettingGetRequest.getBatchSettingGetParams();
        if (CollectionUtils.isEmpty(batchSettingGetParams)) {
            return CommonApiResponse.failed(CommonApiStatus.RequestParamError.code(),
                CommonApiStatus.RequestParamError.message());
        }

        try {
            List<BatchUsersSettingGetResponse> batchUsersSettingGetRespons = new ArrayList<>();

            for (BatchSettingGetRequest batchSettingGetParam : batchSettingGetParams) {
                String userId = batchSettingGetParam.getUserId();
                String platformId = batchSettingGetParam.getPlatformId();
                String appName = batchSettingGetParam.getApp();
                List<String> settings = batchSettingGetParam.getSettings();
                if (CommonPlatformConstants.PLATFORM_TAO.equals(platformId) && StringUtils.isEmpty(appName)) {
                    appName = CommonAppConstants.APP_TRADE;
                }

                if (StringUtils.isAnyEmpty(platformId, appName, userId) || CollectionUtils.isEmpty(settings)) {
                    LOGGER.logInfo("用户获取配置参数缺失, 跳过获取用户配置");
                    continue;
                }

                BatchUsersSettingGetResponse batchUsersSettingGetResponse = new BatchUsersSettingGetResponse();
                QueryUserSettingsParam userSettingsParam = new QueryUserSettingsParam();
                userSettingsParam.setUserId(userId);
                userSettingsParam.setPlatformId(platformId);
                userSettingsParam.setAppName(appName);
                userSettingsParam.setSettingKeys(settings);
                userSettingsParam.setLoadDefaultSetting(batchSettingGetParam.getLoadDefaultSetting());
                List<UserSettingDTO> userSettingDTOList =
                    userSettingsRepository.batchQueryUserSetting(userSettingsParam);

                batchUsersSettingGetResponse.setSellerId(userId);
                batchUsersSettingGetResponse.setUserSettingDTOList(userSettingDTOList);
                batchUsersSettingGetRespons.add(batchUsersSettingGetResponse);
            }

            return CommonApiResponse.success(batchUsersSettingGetRespons);
        } catch (Exception e) {
            LOGGER.logError("批量查询用户设置异常: " + e.getMessage(), e);
            return CommonApiResponse.serverError();
        }
    }

	@Override
	public CommonApiResponse<Void> userSettingCopy(@RequestBody @Valid UserSettingCopyRequest userSettingCopyRequest) {
 		logRequestParams(userSettingCopyRequest);

		try {
			switch (userSettingCopyRequest.getCopyType()) {
				case ONLY_SETTINGS:
					// 设置复制
					settingCopy(userSettingCopyRequest);
					break;
				case ONLY_TAGS:
					// 打标复制
					shopsTagsCopy(userSettingCopyRequest);
					break;
				case ALL:
				default:
					// 设置复制
					settingCopy(userSettingCopyRequest);
					// 打标复制
					shopsTagsCopy(userSettingCopyRequest);
			}
		} catch (Exception e) {
			LOGGER.logError("复制用户设置异常: " + e.getMessage(), e);
			return CommonApiResponse.serverError();
		}

		return CommonApiResponse.success();
	}

	private void shopsTagsCopy(UserSettingCopyRequest userSettingCopyRequest) {

		UserSettingCopyRequest.User originUser = userSettingCopyRequest.getOriginUser();
		List<UserSettingCopyRequest.User> targetUsers = userSettingCopyRequest.getTargetUsers();
		if (originUser == null || CollectionUtils.isEmpty(targetUsers)) {
			return;
		}
		String originUserUserId = originUser.getUserId();
		String originUserApp = originUser.getApp();
		String originUserPlatformId = originUser.getPlatformId();

		AyMultiUserTag ayMultiUserTag = ayMultiUserTagDao.queryTagByType(originUserUserId, originUserPlatformId, originUserApp, AyMultiTagType.SHOPS.code);
		if (ayMultiUserTag == null || StringUtils.isEmpty(ayMultiUserTag.getTags())) {
			LOGGER.logInfo(originUserUserId, "-", "用户不存在可复制的tag信息");
			return;
		}

		Set<String> ayMultiTagsForAdd = Sets.newHashSet(StringUtils.split(ayMultiUserTag.getTags(), AyMultiUserTag.SEPARATOR));

		BatchMultiUserTagUpdateRequest update = new BatchMultiUserTagUpdateRequest();
		update.setAyMultiTagsForAdd(ayMultiTagsForAdd);
		List<UserInfoRequest> requests = new ArrayList<>();

		targetUsers.forEach(user -> {
			UserInfoRequest request = new UserInfoRequest();
			request.setApp(user.getApp());
			request.setSellerId(user.getUserId());
			request.setPlatformId(user.getPlatformId());
			requests.add(request);
		});

		update.setRequestList(requests);

		batchMultiuserTagUpdate(update);

	}

	private void settingCopy(UserSettingCopyRequest userSettingCopyRequest) {
		String originUserId = userSettingCopyRequest.getOriginUser().getUserId();
		String originPlatformId = userSettingCopyRequest.getOriginUser().getPlatformId();
		String originApp = userSettingCopyRequest.getOriginUser().getApp();
        List<UserSettingCopyRequest.User> targetUsers = userSettingCopyRequest.getTargetUsers();
		List<UserSettings> originUserSettingDTOList = userSettingsRepository.queryAllSettings(originUserId,
				originPlatformId, originApp);

        List<String> platformSettingKeywordList = CommonPlatformConstants.getPlatform().stream()
            .map(UserCenterServiceImpl::getPlatformSettingKeyword).collect(Collectors.toList());
        // 外层key是设置名，内层key是userId、platformId、appName组成的唯一标识，value是要修改的设置实体类对象
		Map<String, Map<String, UserSettings>> keyAndSettingsMap = Maps.newHashMap();
        List<UserSettings> platformUserSettings = Lists.newArrayList();
        originUserSettingDTOList.forEach(e -> {
			for (UserSettingCopyRequest.User targetUser : targetUsers) {
                String originUserSettingKey = e.getSettingKey();
                if (BooleanUtils.isTrue(userSettingCopyRequest.getIsExitPlatformTypeSetting())) {
                    // 找到相同key, 不同平台设置值不一样的特殊处理
                    boolean isPlatformSetting = false;
                    String originPlatformSettingKeyword = getPlatformSettingKeyword(targetUser.getPlatformId());
                    // 过滤一下平台设置，如果设置是按照平台设置的则过滤掉非当前平台的设置
                    for (String platformSettingKeyword : platformSettingKeywordList) {
                        if (originUserSettingKey.contains(platformSettingKeyword)
                            && !Objects.equals(originPlatformSettingKeyword, platformSettingKeyword)) {
                            isPlatformSetting = true;
                            break;
                        } else if (originUserSettingKey.contains(platformSettingKeyword)
                            && Objects.equals(originPlatformSettingKeyword, platformSettingKeyword)) {
                            // 说明匹配到了平台配置 处理key为通用key
                            originUserSettingKey = originUserSettingKey.replace(originPlatformSettingKeyword, ".");
                            UserSettings userSettings = new UserSettings();
                            userSettings.setUserId(targetUser.getUserId());
                            userSettings.setPlatformId(targetUser.getPlatformId());
                            userSettings.setAppName(targetUser.getApp());
                            userSettings.setSettingKey(originUserSettingKey);
                            userSettings.setSettingValue(e.getSettingValue());
                            userSettings.setDescription(e.getDescription());
                            platformUserSettings.add(userSettings);
                            isPlatformSetting = true;
                            break;
                        }
                    }

                    if (isPlatformSetting) {
                        // 店铺群配置直接跳过
                        continue;
                    }
                }

                // 正常通用配置
                UserSettings userSettings = new UserSettings();
				userSettings.setUserId(targetUser.getUserId());
				userSettings.setPlatformId(targetUser.getPlatformId());
				userSettings.setAppName(targetUser.getApp());
				userSettings.setSettingKey(originUserSettingKey);
				userSettings.setSettingValue(e.getSettingValue());
				userSettings.setDescription(e.getDescription());
				String nickAndPlatformAndAppName = concatUserIdAndPlatformAndApp(targetUser.getUserId(),
						targetUser.getPlatformId(), targetUser.getApp());
				Map<String, UserSettings> userSettingsMap = keyAndSettingsMap.get(originUserSettingKey);
				if (userSettingsMap == null) {
					userSettingsMap = Maps.newHashMap();
					keyAndSettingsMap.put(originUserSettingKey, userSettingsMap);
				}
				userSettingsMap.put(nickAndPlatformAndAppName, userSettings);
			}
		});

		Boolean keepTargetSettings = userSettingCopyRequest.getKeepTargetSettings();
		if (BooleanUtils.isTrue(keepTargetSettings)) {
			// 查询targetUser的设置数据，存到targetUserSettingDTOList
			List<UserSettings> targetUserSettingDTOList = Lists.newArrayList();
			for (UserSettingCopyRequest.User targetUser : targetUsers) {
				targetUserSettingDTOList.addAll(userSettingsRepository.queryAllSettings(targetUser.getUserId(),
						targetUser.getPlatformId(), targetUser.getApp()));
			}
			// 遍历存到targetUserSettingDTOList，把已经存在的设置实体对象移除
			targetUserSettingDTOList.forEach(e -> {
				Map<String, UserSettings> userSettingsMap = keyAndSettingsMap.get(e.getSettingKey());
				if (userSettingsMap != null) {
					String nickAndPlatformAndAppName = concatUserIdAndPlatformAndApp(e.getUserId(),
							e.getPlatformId(), e.getAppName());
					userSettingsMap.remove(nickAndPlatformAndAppName);
					if (userSettingsMap.isEmpty()) {
						keyAndSettingsMap.remove(e.getSettingKey());
					}
				}
			});
		}

		if (CollectionUtils.isNotEmpty(keyAndSettingsMap.values())) {
			// 汇总需要变更的实体对象，每批20个去做批量修改
			List<Map<String, UserSettings>> maps = new ArrayList<>(keyAndSettingsMap.values());
			List<UserSettings> settings = Lists.newArrayList();
			maps.forEach(e -> settings.addAll(new ArrayList<>(e.values())));
			List<List<UserSettings>> partitions = Lists.partition(settings, 20);
			for (List<UserSettings> partition : partitions) {
				userSettingsRepository.batchUpsertUserSetting(partition);
			}
		}

        if (CollectionUtils.isNotEmpty(platformUserSettings)) {
            List<List<UserSettings>> partitions = Lists.partition(platformUserSettings, 20);
            for (List<UserSettings> partition : partitions) {
                userSettingsRepository.batchUpsertUserSetting(partition);
            }
        }
	}

    /**
     * 获取平台配置的关键字
     *
     * @param platformId
     * @return
     */
    private static String getPlatformSettingKeyword(String platformId) {
        return "." + platformId + ".";
    }

    /**
	 * 拼接userId、platformId、appName
	 *
	 * @param userId
	 * @param platformId
	 * @param appName
	 * @return
	 */
	private String concatUserIdAndPlatformAndApp(String userId, String platformId, String appName) {
		return userId + ":" + platformId + ":" + appName;
	}

	/**
	 * 获取用户授权信息(aiyong.usercenter.token.get)
	 * @return CommonApiResponse<UserInfoResponse>
	 */
	@Override
	public CommonApiResponse<UserInfoResponse> getTopSession(@RequestBody @Valid UserInfoRequest userInfoRequest) throws Exception {
		try {
            logRequestParams(userInfoRequest);
			UserInfoResponse userInfo = userPlatformHandleService.getAccessToken(UserInfoBo.of(userInfoRequest), userInfoRequest.getPlatformId(), userInfoRequest.getApp());
			userInfo.setVipflag(getVipflag(userInfo.getOrderCycleEnd(),userInfo.getVipflag(),userInfoRequest.getPlatformId()));
			return CommonApiResponse.success(userInfo);
		} catch (UserException e) {
			CommonApiResponse<UserInfoResponse> commonApiResponse = new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(),
				e.getCode(), e.getMessage(), null);
			return commonApiResponse;
		}
	}

    @Override
    public CommonApiResponse<ListUserByVipInfoResponse>
    listUserByVipInfo(@RequestBody @Valid ListUserByVipInfoRequest listUserByVipInfoRequest) throws Exception {
        try {
            logRequestParams(listUserByVipInfoRequest);
            List<Integer> vipFlagList = listUserByVipInfoRequest.getVipFlagList();
            String storeId = listUserByVipInfoRequest.getStoreId();
            String appName = listUserByVipInfoRequest.getAppName();
            Integer pageSize = listUserByVipInfoRequest.getPageSize();
            Integer startId = listUserByVipInfoRequest.getLastId();
            LocalDateTime professionalOrderCycleEnd = listUserByVipInfoRequest.getProfessionalOrderCycleEnd();

            List<UserProductInfo> userProductInfos = null;
            if (CollectionUtils.isNotEmpty(vipFlagList)) {
                userProductInfos =
                    userRepository.queryNickListByvipFlagList(vipFlagList, startId, pageSize, storeId, appName);
            } else if (professionalOrderCycleEnd != null) {
                userProductInfos = userRepository.queryNickListByProfessionalOrderCycleEnd(professionalOrderCycleEnd,
                    startId, pageSize, storeId, appName);
            }


            List<ListUserByVipInfoResponse.UserInfo> UserInfoDTOList = Lists.newArrayList();
            Boolean isNext = false;
            Integer maxId = null;
            if (CollectionUtils.isNotEmpty(userProductInfos)) {
                if (userProductInfos.size() == pageSize) {
                    isNext = true;
                    maxId = userProductInfos.get(userProductInfos.size() - 1).getId();
                }

                for (UserProductInfo userProductInfo : userProductInfos) {
                    ListUserByVipInfoResponse.UserInfo userInfo = new ListUserByVipInfoResponse.UserInfo();
                    userInfo.setSellerNick(userProductInfo.getNick());
                    userInfo.setSellerId(userProductInfo.getUserId());
                    userInfo.setProfessionalOrderCycleEnd(userProductInfo.getProfessionalOrderCycleEnd());
                    UserInfoDTOList.add(userInfo);
                }
            }

            ListUserByVipInfoResponse listUserByVipInfoResponse = new ListUserByVipInfoResponse();
            listUserByVipInfoResponse.setUserInfoDTOList(UserInfoDTOList);
            listUserByVipInfoResponse.setNext(isNext);
            listUserByVipInfoResponse.setLastId(maxId);
            return CommonApiResponse.success(listUserByVipInfoResponse);
        } catch (Exception e) {
            LOGGER.logError("根据vipList查询用户名列表异常: " + e.getMessage(), e);
            return CommonApiResponse.serverError();
        }
    }

    /**
	 * 获取用户授权信息(aiyong.usercenter.token.get)  最大支持200个批量查询
	 * @return CommonApiResponse<UserInfoResponse>
	 */
	@Override
	public CommonApiResponse<List<UserInfoResponse>> batchGetTopSession(@RequestBody @Valid BatchRequest<UserInfoRequest> userInfoRequestList) throws Exception {
        logRequestParams(userInfoRequestList);
		List<UserInfoResponse> list = new LinkedList<>();
		List<UserInfoRequest> requestList = userInfoRequestList.getRequestList();
		String sellerNick = null;
		String subSellerNick = null;
		try {
			for (UserInfoRequest userInfoRequest : requestList) {
				sellerNick = userInfoRequest.getSellerNick();
				subSellerNick = userInfoRequest.getSubSellerNick();
				UserInfoResponse userInfo = userPlatformHandleService.getAccessToken(UserInfoBo.of(userInfoRequest), userInfoRequest.getPlatformId(), userInfoRequest.getApp());
				userInfo.setVipflag(getVipflag(userInfo.getOrderCycleEnd(),userInfo.getVipflag(),userInfoRequest.getPlatformId()));
				list.add(userInfo);
			}
			return CommonApiResponse.success(list);
		} catch (UserException e) {
			LOGGER.logError(sellerNick, "-",
					"账号<" + sellerNick + ">,<" + subSellerNick + ">,获取topsessio失败: " + e.getMessage());
			return new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(),
					e.getCode(), e.getMessage(), null);
		}
	}

	/**
	 * 获取用户授权信息(aiyong.usercenter.key.information.rebuild)
	 * @return CommonApiResponse<UserInfoResponse>
	 */
	@Override
	public CommonApiResponse<UserInfoResponse> rebuildUserInfo(@RequestBody @Valid UserInfoRequest userInfoRequest) {
		try {
            logRequestParams(userInfoRequest);
			UserInfoResponse userInfo = userPlatformHandleService.rebuildUserInfo(UserInfoBo.of(userInfoRequest), userInfoRequest.getPlatformId(), userInfoRequest.getApp());
			userInfo.setVipflag(getVipflag(userInfo.getOrderCycleEnd(),userInfo.getVipflag(),userInfoRequest.getPlatformId()));
			if (Objects.isNull(userInfo)) {
				LOGGER.logError(userInfoRequest.getSellerNick(), "-", "rebuilder失败, 返回结果为空");
				return CommonApiResponse.of(CommonApiStatus.ServerError.code(), CommonApiStatus.ServerError.message(), null);
			}
			return CommonApiResponse.success(userInfo);
		} catch (UserException e) {
			CommonApiResponse<UserInfoResponse> commonApiResponse = new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(),
				e.getCode(), e.getMessage(), null);
			return commonApiResponse;
		}
	}

	/**
	 * 根据memberid获取用户授权信息
	 */
	@Override
	public CommonApiResponse<UserInfoTopSessionMemberResponse> getTopSessionByMemberId(@RequestBody @Valid UserinfoTopSessionMemberRequest userInfoRequest) throws Exception {
		try {
            logRequestParams(userInfoRequest);
			UserInfoResponse userInfo = userPlatformHandleService.getAccessToken(UserInfoBo.of(userInfoRequest), userInfoRequest.getMemberId(), userInfoRequest.getPlatformId(), userInfoRequest.getApp());
			UserInfoTopSessionMemberResponse userInfoTopSessionMemberResponse = new UserInfoTopSessionMemberResponse();
			BeanUtils.copyProperties(userInfo, userInfoTopSessionMemberResponse);
			userInfoTopSessionMemberResponse.setVipflag(getVipflag(userInfo.getOrderCycleEnd(),userInfo.getVipflag(),userInfoRequest.getPlatformId()));
			return CommonApiResponse.success(userInfoTopSessionMemberResponse);
		} catch (UserException e) {
			CommonApiResponse<UserInfoTopSessionMemberResponse> commonApiResponse = new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(),
				e.getCode(), e.getMessage(), null);
			return commonApiResponse;
		}
	}

	@Override
	public CommonApiResponse<BatchUpdateUserCacheInfoResponse> batchUpdateUserCacheInfo(@RequestBody @Valid BatchUpdateUserCacheInfoRequest request) throws Exception {
		logRequestParams(request);
		List<UserInfoBo> userInfoBoList = request.getRequestList().stream().map(UserInfoBo::of).collect(Collectors.toList());
		int result = userPlatformHandleService.batchUpdateUserCacheInfo(userInfoBoList, request.getCacheHkey(), request.getCacheValue());
		BatchUpdateUserCacheInfoResponse response = new BatchUpdateUserCacheInfoResponse();
		response.setSize(result);
		return CommonApiResponse.success(response);
	}

	@Override
	public CommonApiResponse<List<UserCacheInfoResponse>> batchGetUserCacheInfo(@RequestBody @Valid BatchGetUserCacheInfoRequest request) throws Exception {
		logRequestParams(request);
		List<UserInfoBo> userInfoBoList = request.getRequestList().stream().map(UserInfoBo::of).collect(Collectors.toList());
		List<UserCacheInfoResponse> result = userPlatformHandleService.batchGetUserCacheInfo(userInfoBoList, request.getCacheHkey());
		return CommonApiResponse.success(result);
	}

	@Override
	public CommonApiResponse<Void> batchMultiuserTagUpdate(@RequestBody BatchMultiUserTagUpdateRequest batchMultiUserTagUpdateRequest) {
		List<UserInfoRequest> requestList = batchMultiUserTagUpdateRequest.getRequestList();
		Set<String> ayMultiTagsForAdd = batchMultiUserTagUpdateRequest.getAyMultiTagsForAdd();
		Set<String> ayMultiTagsForDel = batchMultiUserTagUpdateRequest.getAyMultiTagsForDel();

		if (CollectionUtils.isNotEmpty(ayMultiTagsForAdd) && CollectionUtils.isNotEmpty(ayMultiTagsForDel)) {
			// 同时存在新增和删除时，判断内容是否存在重复
			Set<String> commonElements = new HashSet<>(ayMultiTagsForAdd);
			commonElements.retainAll(ayMultiTagsForDel);
			if (CollectionUtils.isNotEmpty(commonElements)) {
				// 新增tag和删除tag存在相同元素，无法更新
				LOGGER.logError("新增tag和删除tag存在相同元素，无法更新");
				return CommonApiResponse.failed(RequestParamError.code(), RequestParamError.message());
			}
		}

		List<AyMultiUserTag> ayMultiUserTags = new ArrayList<>();
		List<UserInfoBo> reflashUserInfoBoList = new ArrayList<>();

		for (UserInfoRequest request : requestList) {
			String sellerId = request.getSellerId();
			String sellerNick = request.getSellerNick();
			String platformId = request.getPlatformId();
			String appName = request.getApp();
			AyMultiUserTag ayMultiUserTag = ayMultiUserTagDao.queryTag(sellerId, platformId, appName);
			if (ayMultiUserTag == null) {
				ayMultiUserTag = new AyMultiUserTag();
				ayMultiUserTag.setSellerId(sellerId);
				ayMultiUserTag.setStoreId(platformId);
				ayMultiUserTag.setAppName(appName);
			}
			ayMultiUserTag.addTags(ayMultiTagsForAdd);
			ayMultiUserTag.deleteTags(ayMultiTagsForDel);
			ayMultiUserTag.setType(batchMultiUserTagUpdateRequest.getTagType().code);
			ayMultiUserTags.add(ayMultiUserTag);
			UserInfoBo userInfoBo = new UserInfoBo(platformId, appName, sellerNick, sellerId);
			reflashUserInfoBoList.add(userInfoBo);
		}

		if (ayMultiUserTags.isEmpty()) {
			return CommonApiResponse.success();
		}

		ayMultiUserTagDao.batchInsertOrUpdate(ayMultiUserTags);

		reflashUserInfoBoList.forEach(userInfoBo -> {
			String storeId = userInfoBo.getPlatformId();
			String appName = userInfoBo.getAppType();
			try {
				userPlatformHandleService.rebuildUserInfo(userInfoBo, storeId, appName);
			} catch (UserException e) {
				LOGGER.logError("更新缓存异常：" + e.getMessage(), e);
			}
		});
		return CommonApiResponse.success();
	}

    @Override
    public CommonApiResponse<UserShopInfoGetResponse> getUserShopInfo(@RequestBody UserShopInfoGetRequest request) {
        logRequestParams(request);
        String sellerId = request.getSellerId();
        String sellerNick = request.getSellerNick();
        String storeId = request.getStoreId();
        String appName = request.getAppName();

        List<UserShopInfoMapping> userShopInfoList =
            platformUserProductInfoService.getUserShopInfo(sellerId, sellerNick, storeId, appName);

        List<UserShopInfoGetResponse.ShopInfo> shopInfoList = Lists.newArrayList();
        UserShopInfoGetResponse response = new UserShopInfoGetResponse();
        if (!CollectionUtils.isEmpty(userShopInfoList)) {
            for (UserShopInfoMapping userShopInfoMapping : userShopInfoList) {
                if (StringUtils.isEmpty(response.getSellerId())) {
                    response.setSellerId(userShopInfoMapping.getSellerId());
                }

                if (StringUtils.isEmpty(response.getSellerNick())) {
                    response.setSellerNick(userShopInfoMapping.getSellerNick());
                }

                if (StringUtils.isEmpty(response.getStoreId())) {
                    response.setStoreId(userShopInfoMapping.getStoreId());
                }

                if (StringUtils.isEmpty(response.getAppName())) {
                    response.setAppName(userShopInfoMapping.getAppName());
                }

                UserShopInfoGetResponse.ShopInfo shopInfo = new UserShopInfoGetResponse.ShopInfo();
                BeanUtils.copyProperties(userShopInfoMapping, shopInfo);
                shopInfoList.add(shopInfo);
            }
        }

        response.setShopInfoList(shopInfoList);
        return CommonApiResponse.success(response);
    }

	@Override
	public CommonApiResponse<UserFullInfoResponse> getUserByMallName(@RequestBody UserInfoRequest request) {
		try {
			logRequestParams(request);
			UserProductInfo userProductInfo = userRepository.queryByMallName(request.getMallName(), request.getPlatformId(), request.getApp());
			UserFullInfoResponse userFullInfoResponse = fillWithUserFullInfoInner(userProductInfo);
			userFullInfoResponse.setVipflag(getVipflag(userFullInfoResponse.getOrderCycleEnd(),userFullInfoResponse.getVipflag(),request.getPlatformId()));
			return CommonApiResponse.success(userFullInfoResponse);
		} catch (Exception e) {
			CommonApiResponse<UserFullInfoResponse> commonApiResponse = new CommonApiResponse(CommonApiStatus.Success.code(), CommonApiStatus.Success.message(),
					ApiCode.NO_EXIST_USER.code(), ApiCode.NO_EXIST_USER.message(), null);
			return commonApiResponse;
		}
	}

	@Override
	public CommonApiResponse<List<UserInfoResponse>> batchGetUserLoginInfo(@RequestBody List<UserInfoRequest> userInfoRequestList) {
		logRequestParams(userInfoRequestList);
		List<UserInfoResponse> userInfoList = new ArrayList<>();
		for (UserInfoRequest userInfoRequest : userInfoRequestList) {
			try {
				UserInfoResponse userInfo;
				if (CommonPlatformConstants.PLATFORM_TAO.equals(userInfoRequest.getPlatformId())) {
					userInfo = userPlatformHandleService.login(UserInfoBo.of(userInfoRequest),
							userInfoRequest.getPlatformId(), userInfoRequest.getApp());
				} else {
					userInfo = userPlatformHandleService.getUserInfo(UserInfoBo.of(userInfoRequest),
							userInfoRequest.getPlatformId(), userInfoRequest.getApp());
				}
				userInfo.setVipflag(getVipflag(userInfo.getOrderCycleEnd(),userInfo.getVipflag(),userInfoRequest.getPlatformId()));
				userInfoList.add(userInfo);
			} catch (UserException e) {
				LOGGER.logInfo(userInfoRequest.getSellerNick(), "-",
						"账号<"+userInfoRequest.getSellerNick()+">,<"+userInfoRequest.getSubSellerNick()+">,不存在");
			}
		}
		return CommonApiResponse.success(userInfoList);
	}

    /**
	 * 获取vipflag
	 * @param orderCycleEnd
	 * @param platformId
	 * @param vipflag
	 * @return
	 */
	protected Integer getVipflag(LocalDateTime orderCycleEnd, Integer vipflag, String platformId){
		//如果到期那么vipflag设置为0 淘宝根据开关判断是否需要检查vipflag
		if (orderCycleEnd != null) {
			if (checkTaobaoVipflagIsExpired || !CommonPlatformConstants.PLATFORM_TAO.equals(platformId)) {
				if(LocalDateTime.now().isAfter(orderCycleEnd)){
					vipflag = UserProductInfo.LEVEL_ZERO;
				}
			}
		}
		return vipflag;
	}

    private void logRequestParams(Object requestParams) {
    }
}
