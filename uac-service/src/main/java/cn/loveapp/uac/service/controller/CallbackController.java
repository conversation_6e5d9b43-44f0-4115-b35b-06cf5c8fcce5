package cn.loveapp.uac.service.controller;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.uac.request.CallbackRequest;
import cn.loveapp.uac.response.CallbackResponse;
import cn.loveapp.uac.common.bo.AuthBo;
import cn.loveapp.uac.service.platform.biz.CallbackPlatformHandleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: uac-service-group
 * @description: CallbackController
 * @author: Jason
 * @create: 2020-03-13 11:47
 **/
@RestController
@RequestMapping("uac/callback")
public class CallbackController {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(CallbackController.class);

	@Autowired
	private CallbackPlatformHandleService callbackPlatformHandleService;

	/**
	 * 用户授权回调(aiyong.usercenter.auth.callback)
	 * @param callbackRequest
	 * @return
	 */
	@RequestMapping(value = "authCallback", method = {RequestMethod.POST,RequestMethod.GET})
	public CommonApiResponse<CallbackResponse> authCallback(@Validated CallbackRequest callbackRequest) {
		CallbackResponse callbackResponse = callbackPlatformHandleService.authCallback(AuthBo.of(callbackRequest),
			callbackRequest.getPlatformId(), callbackRequest.getApp());
		if (callbackResponse.isSuccess()) {
			return CommonApiResponse.success(callbackResponse);
		}
		return CommonApiResponse.of(CommonApiStatus.ServerError.code(), CommonApiStatus.ServerError.message());
	}
}
