package cn.loveapp.uac.service.export;


import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.uac.common.dto.OrderSearchQueryDTO;
import cn.loveapp.uac.db.common.convert.CommonConvertMapper;
import cn.loveapp.uac.db.common.entity.OrderSearch;
import cn.loveapp.uac.db.common.repository.OrderSearchRepository;
import cn.loveapp.uac.request.UserOrderSearchRequest;
import cn.loveapp.uac.response.UserOrderSearchResponse;
import cn.loveapp.uac.service.UserOrderSearchApiService;
import cn.loveapp.uac.service.UserProductInfoExtApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/15 4:06 PM
 */
@RestController
@RequestMapping(value = UserOrderSearchApiService.PATH)
public class UserOrderSearchApiServiceImpl implements UserOrderSearchApiService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(UserOrderSearchApiServiceImpl.class);

    @Autowired
    private OrderSearchRepository orderSearchRepository;

    @Override
    public CommonApiResponse<UserOrderSearchResponse> getUserOrderSearch(UserOrderSearchRequest request) {
        String appName = request.getApp();
        String platformId = request.getPlatformId();

        OrderSearchQueryDTO queryDTO = CommonConvertMapper.INSTANCE.toOrderSearchQuery(request);

        List<OrderSearch> orderSearchList = orderSearchRepository.queryOrderSearchList(queryDTO, platformId, appName);
        UserOrderSearchResponse response = new UserOrderSearchResponse();
        response.setUserOrderSearchList(CommonConvertMapper.INSTANCE.toOrderSearchList(orderSearchList));
        return CommonApiResponse.success(response);
    }
}
