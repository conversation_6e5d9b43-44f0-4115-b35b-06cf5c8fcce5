## \u5E94\u7528\u540D\u79F0
spring.application.name=${APPLICATION_NAME:spring-application}
## springboot\u5F53\u524D\u8FD0\u884C\u73AF\u5883
spring.profiles.active=dev

## apollo\u529F\u80FD\u5F00\u5173
loveapp.apollo.enabled=true
apollo.bootstrap.enabled = ${loveapp.apollo.enabled}

## apollo \u5E94\u7528id\u8BBE\u7F6E
app.id=cn.loveapp.uac
## apollo namespace\u8BBE\u7F6E
apollo.bootstrap.namespaces=uac-service,application,service-registry
## apollo env\u8BBE\u7F6E
env=${spring.profiles.active}

spring.cache.type=caffeine
spring.cache.caffeine.spec=maximumSize=10000,expireAfterAccess=5m
