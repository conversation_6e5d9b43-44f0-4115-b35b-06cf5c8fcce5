<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.loveapp.uac</groupId>
        <artifactId>uac-service-group</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>uac-service</artifactId>

    <name>爱用-uac-service</name>
    <description>爱用-uac web接口模块</description>
    <version>1.0-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>cn.loveapp.uac</groupId>
            <artifactId>uac-api</artifactId>
			<version>${uac.api.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.loveapp.uac</groupId>
            <artifactId>uac-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
		<dependency>
			<groupId>cn.loveapp.uac</groupId>
			<artifactId>uac-db-common</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>cn.loveapp.uac</groupId>
			<artifactId>uac-service-common</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>net.spy</groupId>
			<artifactId>spymemcached</artifactId>
		</dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
