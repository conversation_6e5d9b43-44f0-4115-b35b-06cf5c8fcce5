package cn.loveapp.uac.newuser.service;

import cn.loveapp.common.utils.LoggerHelper;
import org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisReactiveAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/1/13 17:14
 * @Description: 新用户Service启动类
 */
@EnableFeignClients(basePackages = {"cn.loveapp.uac", "cn.loveapp.items.api"})
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, RedisAutoConfiguration.class},
    scanBasePackages = {"cn.loveapp.uac.common", "cn.loveapp.uac.db.common","cn.loveapp.uac.newuser.common","cn.loveapp.uac.newuser.service"})
public class UacNewUserServiceApplication {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(UacNewUserServiceApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(UacNewUserServiceApplication.class, args);
    }
}
