package cn.loveapp.uac.newuser.service.export;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.uac.newuser.common.business.UserSaveDataBusinessHandleService;
import cn.loveapp.uac.newuser.common.dto.ExportSaveDataCourseRequest;
import cn.loveapp.uac.newuser.common.dto.SaveDataCourseDTO;
import cn.loveapp.uac.newuser.common.dto.UserInfoDTO;
import cn.loveapp.uac.newuser.common.service.UserCenterService;
import cn.loveapp.uac.response.UserFullInfoResponse;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023-06-05 16:54
 * @Description:
 */
@RestController
@RequestMapping("/export/uac/newuser")
public class ExportNewUserController {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ExportNewUserController.class);

    @Autowired
    private UserSaveDataBusinessHandleService userSaveDataBusinessHandleService;

    @Autowired
    private UserCenterService userCenterService;

    /**
     * 获取用户存数据进度
     *
     * @param request
     * @return
     */
    @PostMapping("/saveDataCourse")
    public CommonApiResponse<SaveDataCourseDTO>
        saveDataCourse(@RequestBody @Validated ExportSaveDataCourseRequest request) {

        UserFullInfoResponse userFullInfo = userCenterService.getUserFullInfo(request.getSellerNick(),
            request.getSellerId(), request.getAppName(), request.getPlatformId());

        if (userFullInfo == null) {
            LOGGER.logInfo(request.getSellerNick(), "-", "获取用户信息失败");
            return CommonApiResponse.of(CommonApiStatus.ServerError.code(), "获取用户信息失败,请稍后再试");
        }

        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setNick(userFullInfo.getSellerNick());
        userInfoDTO.setSellerId(userFullInfo.getSellerId());
        userInfoDTO.setSession(userFullInfo.getAccessToken());
        userInfoDTO.setVipFlag(String.valueOf(userFullInfo.getVipflag()));
        userInfoDTO.setStoreId(request.getPlatformId());
        userInfoDTO.setAppName(request.getAppName());
        userInfoDTO.setBusinessId(request.getBusinessId());

        SaveDataCourseDTO saveDataCourseDTO =
            userSaveDataBusinessHandleService.getPullDataProgress(request.getBusinessId(), userInfoDTO);

        if (saveDataCourseDTO.getProgress() == null || saveDataCourseDTO.getTotal() == null) {
            LOGGER.logInfo(userInfoDTO.getNick(), "-", "获取进度失败");
            return CommonApiResponse.of(CommonApiStatus.ServerError.code(), "获取进度失败,请稍后再试");
        }

        if (saveDataCourseDTO.getTotal() < saveDataCourseDTO.getProgress()) {
            LOGGER.logInfo(userInfoDTO.getNick(), "-", "拉取的数量比总量还多");
            saveDataCourseDTO.setTotal(saveDataCourseDTO.getProgress());
        }
        return CommonApiResponse.success(saveDataCourseDTO);
    }
}
