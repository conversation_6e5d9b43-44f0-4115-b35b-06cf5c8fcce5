package cn.loveapp.uac.newuser.service.controller;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.uac.newuser.common.business.UserSaveDataBusinessHandleService;
import cn.loveapp.uac.newuser.common.dto.SaveDataCourseDTO;
import cn.loveapp.uac.newuser.common.dto.SaveDataDTO;
import cn.loveapp.uac.newuser.common.dto.UserInfoDTO;
import cn.loveapp.uac.newuser.common.utils.PlatformAndAppNameUtil;
import cn.loveapp.uac.newuser.common.utils.SessionValidateUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: zhongzijie
 * @Date: 2023/1/13 17:14
 * @Description: 新用户接口
 */
@RestController
@RequestMapping("/uac/newuser")
public class NewuserController {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(NewuserController.class);

	@Autowired
	private UserSaveDataBusinessHandleService userSaveDataBusinessHandleService;

	@Autowired
	private SessionValidateUtil sessionValidateUtil;

	/**
	 * 获取用户存数据进度
	 * @param request
	 * @return
	 */
	@RequestMapping("/saveDataCourse")
	public Object saveDataCourse(HttpServletRequest request){
		CommonApiResponse<UserInfoDTO> userInfoDTOCommonApiResponse = checkSessionInfo(request);
		if (!userInfoDTOCommonApiResponse.isSuccess()) {
			return userInfoDTOCommonApiResponse;
		}
		UserInfoDTO session = userInfoDTOCommonApiResponse.getBody();
		SaveDataCourseDTO saveDataCourseDTO = userSaveDataBusinessHandleService.getPullDataProgress(session.getBusinessId(), session);

		if (saveDataCourseDTO.getProgress() == null || saveDataCourseDTO.getTotal() == null){
			LOGGER.logInfo(session.getNick(),"-","获取进度失败");
			return CommonApiResponse.of(CommonApiStatus.ServerError.code(),"获取进度失败,请稍后再试");
		}

		if (saveDataCourseDTO.getTotal() < saveDataCourseDTO.getProgress()){
			LOGGER.logInfo(session.getNick(),"-","拉取的数量比总量还多");
			saveDataCourseDTO.setTotal(saveDataCourseDTO.getProgress());
		}
		return CommonApiResponse.success(saveDataCourseDTO);
	}

	/**
	 * 准备给用户开通存数据接口, 设置为待开户
	 * 如果closeSaveData = true, 则该用户关闭存数据并离网
	 * @return 成功与失败
	 */
	@RequestMapping("/prepare")
	public Object prepare(SaveDataDTO saveDataDTO) throws Exception {

		//调用预存单服务
		if (saveDataDTO == null || StringUtils.isAnyEmpty(saveDataDTO.getSellerId(), saveDataDTO.getBusinessId())){
			LOGGER.logInfo("-","-","参数异常");
			return CommonApiResponse.of(400,"参数异常");
		}
		String businessId = PlatformAndAppNameUtil.defaultBusinessId(saveDataDTO.getBusinessId());
		String platformId = PlatformAndAppNameUtil.defaultPlatformId(saveDataDTO.getPlatform());
		String appName = saveDataDTO.getServiceName();
		saveDataDTO.setBusinessId(businessId);
		saveDataDTO.setPlatform(platformId);
		boolean result;
		if (saveDataDTO.getCloseSaveData()) {
			result = userSaveDataBusinessHandleService.closeSaveData(saveDataDTO.getSellerNick(), saveDataDTO.getSellerId(), platformId, businessId, appName);
		} else {
			result = userSaveDataBusinessHandleService.prepareSaveData(saveDataDTO.getBusinessId(), saveDataDTO);
		}
		return CommonApiResponse.of(200, result ? "提交成功" : "提交失败", result);
	}


	/**
	 * 通用校验session
	 * @param request
	 * @return
	 */
	private CommonApiResponse<UserInfoDTO> checkSessionInfo(HttpServletRequest request) {
		UserInfoDTO session = sessionValidateUtil.checkSessionInfo(request,LOGGER);
		if(CommonApiStatus.ServerError.code() == session.getCode()) {
			LOGGER.logInfo(session.getNick(),"-","校验身份失败的原因:"+session.getMsg());
			return CommonApiResponse.failed(CommonApiStatus.ServerError.code(),session.getMsg());
		}
		LOGGER.logInfo("-","session中信息",session.toString());
		return CommonApiResponse.success(session);
	}
}
