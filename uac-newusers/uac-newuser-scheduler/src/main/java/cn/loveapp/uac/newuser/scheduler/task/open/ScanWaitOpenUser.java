package cn.loveapp.uac.newuser.scheduler.task.open;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.newuser.scheduler.config.NewUserTaskConfig;
import cn.loveapp.uac.newuser.scheduler.consumer.OpenUserConsumer;
import cn.loveapp.uac.newuser.scheduler.service.NewUserTaskService;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import jakarta.annotation.PostConstruct;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Author: zhong<PERSON>jie
 * @Date: 2022/12/29 15:02
 * @Description: 扫描待开户用户
 */
@Component
public class ScanWaitOpenUser implements ApplicationListener<ContextClosedEvent> {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ScanWaitOpenUser.class);

    @Autowired
    private NewUserTaskService newUserTaskService;

    @Autowired
    private NewUserTaskConfig newUserTaskConfig;

    private ThreadPoolExecutor businessExecutorService;

    @PostConstruct
    public void ScanWaitOpenUserConstruct() {
        int businessPoolSize = newUserTaskConfig.getBusinessPoolSize();
        businessExecutorService = new ThreadPoolExecutor(businessPoolSize, businessPoolSize, 0L, TimeUnit.SECONDS,
                new SynchronousQueue(), new ThreadFactoryBuilder().setNameFormat("ScanWaitOpenUser-task-pool-%d").build(),
                (Runnable r, ThreadPoolExecutor executor) -> {
                    if (!executor.isShutdown()) {
                        try {
                            executor.getQueue().put(r);
                        } catch (InterruptedException e) {
                            LOGGER.logError(e.toString(), e);
                            Thread.currentThread().interrupt();
                        }
                    }
                });
    }

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        LOGGER.logInfo("ScanWaitOpenUser exited");
        if (event.getApplicationContext() != null && event.getApplicationContext().getParent() != null) {
            return;
        }
        if (businessExecutorService != null) {
            LOGGER.logInfo("ScanWaitOpenUser-task-pool waiting...");
            try {
                businessExecutorService.shutdown();
            } catch (Exception e) {
                LOGGER.logError("ScanWaitOpenUser-task-pool exception is " + e.getMessage(), e);
            }
            LOGGER.logInfo("ScanWaitOpenUser-task-pool done");
        }
    }

    /**
     * 扫描待开通用户, 发送到开户队列
     * 下一步 {@link OpenUserConsumer}
     */
    @Scheduled(fixedDelayString = "${uac.newuser.task.scanWaitOpenUser.delay:#{1 * 60 * 1000}}")
    public void scanWaitOpenUser() {
        MDC.put("task", "open-scanWaitOpenUser");
        LOGGER.logInfo("开始执行任务 open-scanWaitOpenUser: " + newUserTaskConfig.getScanWaitOpenUserEnable());
        if (!newUserTaskConfig.getScanWaitOpenUserEnable()) {
            return;
        }
        List<String> businessIds = newUserTaskConfig.getBusinessIds();
        for (String businessId : businessIds) {
            businessExecutorService.execute(()->{
                MDC.put("task", "open-scanWaitOpenUser");
                newUserTaskService.scanWaitOpenUser(null, businessId, null);
            });
        }
    }

}
