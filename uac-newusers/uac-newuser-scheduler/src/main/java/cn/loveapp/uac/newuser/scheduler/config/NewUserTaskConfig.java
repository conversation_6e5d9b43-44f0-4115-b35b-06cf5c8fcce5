package cn.loveapp.uac.newuser.scheduler.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * @Author: z<PERSON><PERSON><PERSON>e
 * @Date: 2023/3/10 18:01
 * @Description: 定时任务相关配置
 */
@Data
@Configuration
public class NewUserTaskConfig {

    /**
     * uac-newser定时任务需要处理的业务
     */
    @Value("${uac.newuser.task.businessIds:item}")
    public List<String> businessIds;

    /**
     * 扫描授权关闭的用户-定时任务 分页查询每页最大返回条数
     */
    @Value("${uac.newuser.task.scanAuthCancelledUser.limit:50}")
    private Integer scanAuthCancelledUserLimit;

    /**
     * 扫描授权关闭的用户开关
     */
    @Value("${uac.newuser.task.scanAuthCancelledUser.enable:true}")
    private Boolean scanAuthCancelledUserEnable;

    /**
     * 老用户离网开关
     */
    @Value("${uac.newuser.task.scanOfflineUser.enable:false}")
    private Boolean scanOfflineUserEnable;

    /**
     * 扫描需要重试开通的新用户开关
     */
    @Value("${uac.newuser.task.scanOpenExceptionUser.enable:false}")
    private Boolean scanOpenExceptionUserEnable;

    /**
     * 扫描需要重试开通的新用户开关
     */
    @Value("${uac.newuser.task.scanRetryUser.enable:false}")
    private Boolean scanRetryUserEnable;

    /**
     * 扫描所有待开通用户开关
     */
    @Value("${uac.newuser.task.scanWaitOpenUser.enable:false}")
    private Boolean scanWaitOpenUserEnable;

    /**
     * 扫描长时间处于异常状态的用户置为待修复开关
     */
    @Value("${uac.newuser.task.fix.long.waiting.enable:false}")
    private Boolean userTaskFixLongWaitingEnable;

    /**
     * 线程池线程数
     */
    @Value("${uac.newuser.task.pool.size:50}")
    private Integer poolSize;

    /**
     * 业务线程池线程数
     */
    @Value("${uac.newuser.task.business.pool.size:10}")
    private Integer businessPoolSize;
}
