package cn.loveapp.uac.newuser.scheduler.config;

import cn.loveapp.common.mq.rocketmq.CommonDefaultMQPushConsumer;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.utils.RocketMqQueueHelper;
import cn.loveapp.uac.newuser.common.config.NewUserQueueConfig;
import cn.loveapp.uac.newuser.scheduler.consumer.OpenUserConsumer;
import jakarta.annotation.Resource;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextClosedEvent;

/**
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/1/9 9:52
 * @Description: 存数据用户开通消费者相关配置
 */
@Configuration
public class NewuserConsumerConfig {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(NewuserConsumerConfig.class);

	@Resource(type = NewUserQueueConfig.class)
	private NewUserQueueConfig newuserQueueConfig;

	private DefaultMQPushConsumer consumer = null;

	@Bean(destroyMethod = "shutdown", name = "commonNewuserConsumer")
	public DefaultMQPushConsumer commonNewuserConsumer() {
		//启动ONS消息队列
		try {
			consumer = new CommonDefaultMQPushConsumer(newuserQueueConfig.getConsumerid());
			consumer.setNamesrvAddr(newuserQueueConfig.getNamesrvAddr());
			consumer.setConsumeThreadMax(newuserQueueConfig.getMaxThreadNum());
			consumer.setConsumeThreadMin(newuserQueueConfig.getMaxThreadNum());
            consumer.setAwaitTerminationMillisWhenShutdown(60_000);
			LOGGER.logInfo("-","-","create uac newuser Consumer started");
		} catch (Exception e) {
			LOGGER.logError("create uac newuser Consumer failed", e);
		}
		return consumer;
	}

	@Bean
	public OnsLifeCycleManager onsLifeCycleManager() {
		return new OnsLifeCycleManager();
	}

	/**
	 * Ons 生命周期管理
	 */
	public static class OnsLifeCycleManager implements CommandLineRunner {
		private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OnsLifeCycleManager.class);

		@Autowired
		@Qualifier("commonNewuserConsumer")
		private DefaultMQPushConsumer commonNewuserConsumer;

		@Autowired
		private OpenUserConsumer openUserConsumer;

		@Autowired
		private NewUserQueueConfig queueConfig;

		@Override
		public void run(String... args) throws Exception {
			//启动新用户消费者
			if (commonNewuserConsumer != null) {
				commonNewuserConsumer.subscribe(queueConfig.getTopic(), queueConfig.getTag());
				commonNewuserConsumer.registerMessageListener(openUserConsumer);
				LOGGER.logInfo("uac newuser Consumer is startting");
				commonNewuserConsumer.start();
				LOGGER.logInfo("uac newuser Consumer is started, Topic: " + queueConfig.getTopic() + " Tag: " + queueConfig.getTag());
			}
		}
	}
}
