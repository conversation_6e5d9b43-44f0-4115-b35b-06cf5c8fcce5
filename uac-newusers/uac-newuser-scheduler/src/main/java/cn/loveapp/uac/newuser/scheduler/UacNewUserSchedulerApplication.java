package cn.loveapp.uac.newuser.scheduler;

import cn.loveapp.common.utils.LoggerHelper;
import org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisReactiveAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.util.StringUtils;

/**
 * @Author: zhong<PERSON><PERSON>e
 * @Date: 2023/1/12 18:05
 * @Description: 新用户定时任务启动类
 */
@EnableScheduling
@EnableFeignClients(basePackages = {"cn.loveapp.uac", "cn.loveapp.items.api"})
@SpringBootApplication(exclude = {RedisAutoConfiguration.class, DataSourceAutoConfiguration.class},
    scanBasePackages = {"cn.loveapp.uac.common", "cn.loveapp.uac.db.common","cn.loveapp.uac.newuser.common","cn.loveapp.uac.newuser.scheduler","cn.loveapp.uac.service.config"})
public class UacNewUserSchedulerApplication {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(UacNewUserSchedulerApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(UacNewUserSchedulerApplication.class, args);
    }
}
