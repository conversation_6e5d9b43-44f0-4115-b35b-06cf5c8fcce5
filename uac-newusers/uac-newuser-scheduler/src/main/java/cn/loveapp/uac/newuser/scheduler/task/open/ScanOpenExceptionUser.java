package cn.loveapp.uac.newuser.scheduler.task.open;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.newuser.scheduler.config.NewUserTaskConfig;
import cn.loveapp.uac.newuser.scheduler.service.NewUserTaskService;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import jakarta.annotation.PostConstruct;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Author: zhong<PERSON><PERSON>e
 * @Date: 2023/1/11 15:18
 * @Description: 扫描长时间处于开通中的异常用户, 重新设置为待开通
 */
@Component
public class ScanOpenExceptionUser implements ApplicationListener<ContextClosedEvent> {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ScanOpenExceptionUser.class);

    @Autowired
    private NewUserTaskService newUserTaskService;

    @Autowired
    private NewUserTaskConfig newUserTaskConfig;

    private ThreadPoolExecutor businessExecutorService;

    @PostConstruct
    public void ScanOpenExceptionUserConstruct() {
        int businessPoolSize = newUserTaskConfig.getBusinessPoolSize();
        businessExecutorService = new ThreadPoolExecutor(businessPoolSize, businessPoolSize, 0L, TimeUnit.SECONDS,
                new SynchronousQueue(), new ThreadFactoryBuilder().setNameFormat("ScanOpenExceptionUser-task-pool-%d").build(),
                (Runnable r, ThreadPoolExecutor executor) -> {
                    if (!executor.isShutdown()) {
                        try {
                            executor.getQueue().put(r);
                        } catch (InterruptedException e) {
                            LOGGER.logError(e.toString(), e);
                            Thread.currentThread().interrupt();
                        }
                    }
                });
    }

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        LOGGER.logInfo("ScanOpenExceptionUser exited");
        if (event.getApplicationContext() != null && event.getApplicationContext().getParent() != null) {
            return;
        }
        if (businessExecutorService != null) {
            LOGGER.logInfo("ScanOpenExceptionUser-task-pool waiting...");
            try {
                businessExecutorService.shutdown();
            } catch (Exception e) {
                LOGGER.logError("ScanOpenExceptionUser-task-pool exception is " + e.getMessage(), e);
            }
            LOGGER.logInfo("ScanOpenExceptionUser-task-pool done");
        }
    }

    @Scheduled(cron = "${uac.newuser.task.scanOpenExceptionUser.cron:0 5 * * * ?}")
    public void scanOpenExceptionUser() {
        MDC.put("task", "open-scanOpenExceptionUser");
        LOGGER.logInfo("开始执行任务 open-scanOpenExceptionUser: " + newUserTaskConfig.getScanOpenExceptionUserEnable());
        if (!newUserTaskConfig.getScanOpenExceptionUserEnable()) {
            return;
        }
        List<String> businessIds = newUserTaskConfig.getBusinessIds();
        for (String businessId : businessIds) {
            businessExecutorService.execute(()->{
                MDC.put("task", "open-scanOpenExceptionUser");
                newUserTaskService.scanOpenExceptionUser(null, businessId, null);
            });
        }
    }

}
