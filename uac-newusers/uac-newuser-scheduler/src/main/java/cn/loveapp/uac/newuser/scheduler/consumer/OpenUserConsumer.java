package cn.loveapp.uac.newuser.scheduler.consumer;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.constant.OnsRateLimitConstant;
import cn.loveapp.uac.common.consumer.AiyongMessageExt;
import cn.loveapp.uac.common.consumer.BaseOnsConsumer;
import cn.loveapp.uac.entity.UserProductInfoBusinessExt;
import cn.loveapp.uac.db.common.entity.AyBusinessOpenUser;
import cn.loveapp.uac.db.common.repository.AyBusinessOpenUserLogRepository;
import cn.loveapp.uac.db.common.repository.AyBusinessOpenUserRepository;
import cn.loveapp.uac.db.common.repository.UserProductionInfoExtRepository;
import cn.loveapp.uac.newuser.common.business.UserSaveDataBusinessHandleService;
import cn.loveapp.uac.utils.ValidatorUtils;
import cn.loveapp.uac.newuser.common.constant.OpenResult;
import cn.loveapp.uac.db.common.entity.AyBusinessOpenUserLog;
import cn.loveapp.uac.newuser.common.proto.OpenUserRequest;
import com.alibaba.fastjson2.JSON;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.validation.Validator;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * @Author: zhongzijie
 * @Date: 2023/2/7 16:59
 * @Description: 用户存数据开通consumer
 * @Description: 和存单开通共享同一个topic，但tag有所区分，如果后续还有其他存数据业务，也可以配置tag诸如"item || newtag"的形式，这个consumer是全业务通用的
 */
@Component
public class OpenUserConsumer extends BaseOnsConsumer {

	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OpenUserConsumer.class);

	@Autowired
	private UserSaveDataBusinessHandleService userSaveOrderService;

	@Autowired
	private AyBusinessOpenUserRepository ayBusinessOpenUserRepository;

	@Autowired
	private AyBusinessOpenUserLogRepository ayTradeOpenUserLogDao;

	@Autowired
	private UserProductionInfoExtRepository userProductionInfoExtRepository;

    @Autowired
    private Validator validator;

	public OpenUserConsumer(MeterRegistry registry, Environment environment) {
		super(registry, environment, "NewuserConsumer.QPS");
	}

	@Override
	protected String getRateLimitKey() {
		return OnsRateLimitConstant.ROCKETMQ_RATELIMIT_OPEN_USER;
	}

	@Override
	protected void execute(AiyongMessageExt aiyongMessageExt) {
		String content = aiyongMessageExt.getContent();
		OpenUserRequest openUserRequest = JSON.parseObject(content, OpenUserRequest.class);
		/**
		 * 校验消息结构是否符合规定要求
		 */
        validator.validate(openUserRequest);
		String businessId = openUserRequest.getBusinessId();
		AyBusinessOpenUser ayBusinessOpenUser = ayBusinessOpenUserRepository.queryById(openUserRequest.getId(), businessId);
		if (null == ayBusinessOpenUser || !ayBusinessOpenUser.isOpening()) {
			LOGGER.logInfo(openUserRequest.getSellerNick(), "-", "处理的用户状态异常: " + ayBusinessOpenUser);
			return;
		}
		String sellerId = ayBusinessOpenUser.getSellerId();
		String sellerNick = ayBusinessOpenUser.getSellerNick();
		String platformId = ayBusinessOpenUser.getPlatId();
		String appName = ayBusinessOpenUser.getAppName();
		MDC.put("sellerNick", sellerNick);
		MDC.put("platformId", platformId);
		MDC.put("appName", appName);
		AyBusinessOpenUserLog ayBusinessOpenUserLog = ayTradeOpenUserLogDao.queryById(ayBusinessOpenUser.getUserLogId(), businessId);
		try {
			if (null == platformId) {
				updateOpenuser(ayBusinessOpenUser, ayBusinessOpenUserLog, OpenResult.PLATFORM_ID_IS_NULL, businessId);
			} else {
				LOGGER.logInfo(sellerNick, "处理的新用户", JSON.toJSONString(ayBusinessOpenUser));
				OpenResult result = userSaveOrderService.openSaveData(ayBusinessOpenUserLog, sellerId, sellerNick, platformId, businessId, appName);
				updateOpenuser(ayBusinessOpenUser, ayBusinessOpenUserLog, result, businessId);
				LOGGER.logInfo(sellerNick, "处理的新用户-success", "" + ayBusinessOpenUser);
			}
		} finally {
			MDC.remove("sellerNick");
			MDC.remove("platformId");
			MDC.remove("appName");
		}
	}


	private void updateOpenuser(AyBusinessOpenUser ayBusinessOpenUser, AyBusinessOpenUserLog ayBusinessOpenUserLog, OpenResult item, String businessId) {
		//更新ayTradeOpenUser表状态
		if (AyBusinessOpenUser.DONE == item.code()){
			ayBusinessOpenUser.setStatus(AyBusinessOpenUser.DONE);
		}else if (item.code() <= -100 && item.code() > -200){
			//可以重试的失败
			ayBusinessOpenUser.setStatus(AyBusinessOpenUser.WAIT_RETRY);
		}else if (item.code() <= OpenResult.REPEATED_REQUESTS.code()){
			// 忽略此请求, 不记录
			return;
		}else {
			ayBusinessOpenUser.setStatus(AyBusinessOpenUser.FAILED);
		}
		ayBusinessOpenUser.setGmtModify(LocalDateTime.now());
		ayBusinessOpenUserRepository.updateByStatus(ayBusinessOpenUser, businessId);
		if (CommonPlatformConstants.PLATFORM_TAO.equals(ayBusinessOpenUser.getPlatId())) {
			if (checkAuthFailed(item)) {
				userProductionInfoExtRepository.updateApiStatus(ayBusinessOpenUser.getSellerId(), UserProductInfoBusinessExt.API_STATUS_FAILED_BY_SESSION, ayBusinessOpenUser.getPlatId(), ayBusinessOpenUser.getAppName(), businessId);
			}
			if (checkSuccess(item)) {
				userProductionInfoExtRepository.updateApiStatus(ayBusinessOpenUser.getSellerId(), UserProductInfoBusinessExt.API_STATUS_SUCCESS, ayBusinessOpenUser.getPlatId(), ayBusinessOpenUser.getAppName(), businessId);
			}
		}
		//更新log表
		if (ayBusinessOpenUserLog == null) {
			LOGGER.logInfo(ayBusinessOpenUser.getSellerNick(), "更新状态失败", "ayOpenUserLog");
			return;
		}
		ayBusinessOpenUserLog.setReason(item.message());
		ayBusinessOpenUserLog.setStatus(item.code());
		ayTradeOpenUserLogDao.update(ayBusinessOpenUserLog, businessId);
	}

	public boolean checkAuthFailed(OpenResult item) {
		return OpenResult.GET_AUTH_EXCEPTION == item || OpenResult.TOP_SESSION_IS_NULL == item
			|| OpenResult.SOLD_GET_EXCEPTION == item || OpenResult.MESSAGE_PUSH_OPEN_FAILURE == item
			|| OpenResult.OPEN_USER_SAVE_ORDER_FAILURE == item || OpenResult.USERID_IS_NULL == item;
	}

	public boolean checkSuccess(OpenResult item) {
		return OpenResult.OPEN_SUCCESS == item;
	}

}
