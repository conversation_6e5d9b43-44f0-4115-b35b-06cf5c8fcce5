package cn.loveapp.uac.newuser.scheduler.task.close;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonBusinessConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.doudian.DoudianSDKService;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.uac.entity.UserProductInfoBusinessExt;
import cn.loveapp.uac.db.common.repository.UserProductionInfoExtRepository;
import cn.loveapp.uac.newuser.scheduler.config.NewUserTaskConfig;
import cn.loveapp.uac.request.UserInfoRequest;
import cn.loveapp.uac.response.UserInfoResponse;
import cn.loveapp.uac.service.UserCenterInnerApiService;
import com.doudian.api.request.OpenCloudDdpGetShopListRequest;
import com.doudian.api.response.OpenCloudDdpGetShopListResponse;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.List;

import static cn.loveapp.uac.entity.UserProductInfoBusinessExt.DB_DONE;


/**
 * @Author: zhongzijie
 * @Date: 2022/6/30 11:47
 * @Description: 定时任务-扫描授权关闭的用户
 */
@Component
public class ScanAuthCancelledUserTask {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ScanAuthCancelledUserTask.class);

    private final static String AUTHORIZATION_FAILED = "30001";

    @Autowired
    private UserProductionInfoExtRepository userProductionInfoExtRepository;

    @Autowired
    private DoudianSDKService sdkService;

    @Autowired
    private UserCenterInnerApiService userCenterInnerApiService;

    @Autowired
    private NewUserTaskConfig newUserTaskConfig;

    /**
     * 定时任务-扫描授权关闭的用户，目前只扫描抖店用户，将pullStatus置为-101
     */
    @Scheduled(fixedDelayString = "${uac.newuser.task.scanAuthCancelledUser.delay:#{5 * 60 * 1000}}")
    public void scanAuthCancelledUser() {
        MDC.put("task", "close-scanAuthCancelledUserTask");
        LOGGER.logInfo("开始执行任务 close-scanAuthCancelledUserTask: " + newUserTaskConfig.getScanAuthCancelledUserEnable());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        int offset = 0;
        final int currentLimit = newUserTaskConfig.getScanAuthCancelledUserLimit();
        try {
            if (newUserTaskConfig.getScanAuthCancelledUserEnable()) {
                while (true) {
                    List<UserProductInfoBusinessExt> userProductInfoBusinessExts = userProductionInfoExtRepository.queryByPullStatusAndStoreIdAndTopStatusLimit(DB_DONE, CommonPlatformConstants.PLATFORM_DOUDIAN, DB_DONE.toString(), offset, currentLimit, CommonBusinessConstants.BUSINESS_ITEM);
                    if (userProductInfoBusinessExts == null) {
                        break;
                    }
                    for (UserProductInfoBusinessExt userProductInfoBusinessExt : userProductInfoBusinessExts) {
                        String sellerNick = userProductInfoBusinessExt.getSellerNick();
                        String sellerId = userProductInfoBusinessExt.getSellerId();
                        try {
                            UserInfoRequest userInfoRequest = new UserInfoRequest();
                            userInfoRequest.setSellerNick(sellerNick);
                            userInfoRequest.setApp(CommonAppConstants.APP_TRADE);
                            userInfoRequest.setPlatformId(CommonPlatformConstants.PLATFORM_DOUDIAN);
                            CommonApiResponse<UserInfoResponse> userInfoResponse = userCenterInnerApiService.getTopSession(userInfoRequest);
                            if (userInfoResponse != null && userInfoResponse.isSuccess()) {
                                String sessionKey = userInfoResponse.getBody().getTopSession();
                                OpenCloudDdpGetShopListRequest listRequest = new OpenCloudDdpGetShopListRequest();
                                listRequest.setShopId(Long.parseLong(sellerId));
                                OpenCloudDdpGetShopListResponse response = sdkService.execute(listRequest, sessionKey, CommonAppConstants.APP_TRADE);
                                if (response != null && AUTHORIZATION_FAILED.equals(response.getCode())) {
                                    userProductInfoBusinessExt.setPullStatus(UserProductInfoBusinessExt.DB_FAILED);
                                    int count = userProductionInfoExtRepository.update(userProductInfoBusinessExt, CommonBusinessConstants.BUSINESS_TRADE);
                                    LOGGER.logInfo(sellerNick, "-", "该用户取消了授权, pullStatus置为-101, 更新条数: " + count);
                                }
                            }


                        } catch (Exception e) {
                            LOGGER.logError(sellerNick, "-", "调用抖店api并修改pullstatus时出现异常: " + e.getMessage(), e);
                        }
                    }
                    if (userProductInfoBusinessExts.size() < currentLimit) {
                        break;
                    }
                    offset = offset + currentLimit;
                }
            }
        } catch (Exception ex) {
            LOGGER.logError("执行ScanAuthCancelledUserTask报错: " + ex.getMessage(), ex);
        } finally {
            stopWatch.stop();
            LOGGER.logInfo("结束执行ScanAuthCancelledUserTask, 任务耗时: " + stopWatch.getTotalTimeSeconds());
        }

    }

}
