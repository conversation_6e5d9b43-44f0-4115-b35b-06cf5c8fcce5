package cn.loveapp.uac.newuser.scheduler.task.open;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.newuser.scheduler.config.NewUserTaskConfig;
import cn.loveapp.uac.newuser.scheduler.service.NewUserTaskService;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import jakarta.annotation.PostConstruct;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Author: zhong<PERSON><PERSON>e
 * @Date: 2023/1/6 15:13
 * @Description: 扫描需要重试开通的新用户
 */
@Component
public class ScanRetryUser implements ApplicationListener<ContextClosedEvent> {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ScanRetryUser.class);

    @Autowired
    private NewUserTaskService newUserTaskService;

    @Autowired
    private NewUserTaskConfig newUserTaskConfig;

    private ThreadPoolExecutor businessExecutorService;

    @PostConstruct
    public void ScanRetryUserConstruct() {
        int businessPoolSize = newUserTaskConfig.getBusinessPoolSize();
        businessExecutorService = new ThreadPoolExecutor(businessPoolSize, businessPoolSize, 0L, TimeUnit.SECONDS,
                new SynchronousQueue(), new ThreadFactoryBuilder().setNameFormat("ScanRetryUser-task-pool-%d").build(),
                (Runnable r, ThreadPoolExecutor executor) -> {
                    if (!executor.isShutdown()) {
                        try {
                            executor.getQueue().put(r);
                        } catch (InterruptedException e) {
                            LOGGER.logError(e.toString(), e);
                            Thread.currentThread().interrupt();
                        }
                    }
                });
    }

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        LOGGER.logInfo("ScanRetryUser exited");
        if (event.getApplicationContext() != null && event.getApplicationContext().getParent() != null) {
            return;
        }
        if (businessExecutorService != null) {
            LOGGER.logInfo("ScanRetryUser-task-pool waiting...");
            try {
                businessExecutorService.shutdown();
            } catch (Exception e) {
                LOGGER.logError("ScanRetryUser-task-pool exception is " + e.getMessage(), e);
            }
            LOGGER.logInfo("ScanRetryUser-task-pool done");
        }
    }

    /**
     * 扫描需要重试开通的新用户
     */
    @Scheduled(fixedDelayString = "${uac.newuser.task.scanRetryUser.delay:#{1 * 60 * 1000}}")
    public void scanRetryUser() {
        MDC.put("task", "open-scanRetryUser");
        LOGGER.logInfo("开始执行任务 open-scanRetryUser: " + newUserTaskConfig.getScanRetryUserEnable());
        if (!newUserTaskConfig.getScanRetryUserEnable()) {
            return;
        }
        List<String> businessIds = newUserTaskConfig.getBusinessIds();
        for (String businessId : businessIds) {
            businessExecutorService.execute(()->{
                MDC.put("task", "open-scanRetryUser");
                newUserTaskService.scanRetryUser(null, businessId, null);
            });
        }
    }
}
