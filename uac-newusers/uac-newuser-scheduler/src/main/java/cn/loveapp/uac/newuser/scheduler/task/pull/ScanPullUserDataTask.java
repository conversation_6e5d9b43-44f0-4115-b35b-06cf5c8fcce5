package cn.loveapp.uac.newuser.scheduler.task.pull;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.newuser.scheduler.config.NewUserTaskConfig;
import cn.loveapp.uac.newuser.scheduler.service.NewUserTaskService;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/1/11 15:41
 * @Description: 扫描异常用户标记为待修补状态，并定时扫描待修补状态的用户去拉取业务数据
 */
@Component
public class ScanPullUserDataTask implements ApplicationListener<ContextClosedEvent> {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ScanPullUserDataTask.class);

    private ThreadPoolExecutor executorService;

    private ThreadPoolExecutor businessExecutorService;

    @Autowired
    private NewUserTaskService newUserTaskService;

    @Autowired
    private NewUserTaskConfig newUserTaskConfig;

    @PostConstruct
    public void UserTaskConstruct() {
        int poolSize = newUserTaskConfig.getPoolSize();
        executorService = new ThreadPoolExecutor(poolSize, poolSize, 0L, TimeUnit.SECONDS,
                new SynchronousQueue(), new ThreadFactoryBuilder().setNameFormat("user-task-pool-%d").build(),
                (Runnable r, ThreadPoolExecutor executor) -> {
                    if (!executor.isShutdown()) {
                        try {
                            executor.getQueue().put(r);
                        } catch (InterruptedException e) {
                            LOGGER.logError(e.toString(), e);
                            Thread.currentThread().interrupt();
                        }
                    }
                });
        int businessPoolSize = newUserTaskConfig.getBusinessPoolSize();
        businessExecutorService = new ThreadPoolExecutor(businessPoolSize, businessPoolSize, 0L, TimeUnit.SECONDS,
                new SynchronousQueue(), new ThreadFactoryBuilder().setNameFormat("business-user-task-pool-%d").build(),
                (Runnable r, ThreadPoolExecutor executor) -> {
                    if (!executor.isShutdown()) {
                        try {
                            executor.getQueue().put(r);
                        } catch (InterruptedException e) {
                            LOGGER.logError(e.toString(), e);
                            Thread.currentThread().interrupt();
                        }
                    }
                });
    }

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        LOGGER.logInfo("ScanPullUserDataTask exited");
        if (event.getApplicationContext() != null && event.getApplicationContext().getParent() != null) {
            return;
        }
        if (executorService != null) {
            LOGGER.logInfo("user-task-pool waiting...");
            try {
                executorService.shutdown();
            } catch (Exception e) {
                LOGGER.logError("user-task-pool exception is " + e.getMessage(), e);
            }
            LOGGER.logInfo("user-task-pool done");
        }
        if (businessExecutorService != null) {
            LOGGER.logInfo("business-user-task-pool waiting...");
            try {
                businessExecutorService.shutdown();
            } catch (Exception e) {
                LOGGER.logError("business-user-task-pool exception is " + e.getMessage(), e);
            }
            LOGGER.logInfo("business-user-task-pool done");
        }
    }

    /**
     * 扫描长时间处于拉数据状态的用户并标记为待修复状态
     */
    @Scheduled(fixedDelayString = "${uac.newuser.task.scanUserFixLongWaiting.delay:#{30 * 60 * 1000}}")
    public void scanUserFixLongWaiting() {
        MDC.put("task", "pull-scanUserFixLongWaiting");
        LOGGER.logInfo("开始执行任务 pull-scanUserFixLongWaiting: " + checkSwitchTurnOff());
        if (checkSwitchTurnOff()) {
            return;
        }
        List<String> businessIds = newUserTaskConfig.getBusinessIds();
        for (String businessId : businessIds) {
            businessExecutorService.execute(()->{
                MDC.put("task", "pull-scanUserFixLongWaiting");
                newUserTaskService.scanUserFixLongWaiting(null, businessId, null);
            });
        }
    }

    /**
     * 扫描长时间处于拉数据失败状态的用户并标记为待修复状态
     */
    @Scheduled(fixedDelayString = "${uac.newuser.task.scanUserFailedLongWaiting.delay:#{30 * 60 * 1000}}")
    public void scanUserFailedLongWaiting() {
        MDC.put("task", "pull-scanUserFailedLongWaiting");
        LOGGER.logInfo("开始执行任务 pull-scanUserFailedLongWaiting: " + checkSwitchTurnOff());
        if (checkSwitchTurnOff()) {
            return;
        }
        List<String> businessIds = newUserTaskConfig.getBusinessIds();
        for (String businessId : businessIds) {
            businessExecutorService.execute(()->{
                MDC.put("task", "pull-scanUserFailedLongWaiting");
                newUserTaskService.scanUserFailedLongWaiting(null, businessId, null);
            });
        }
    }

    /**
     * 扫描修复失败的用户并标记为待修复状态
     */
    @Scheduled(fixedDelayString = "${uac.newuser.task.scanUserWaitFixFailed.delay:#{30 * 60 * 1000}}")
    public void scanUserWaitFixFailed() {
        MDC.put("task", "pull-scanUserWaitFixFailed");
        LOGGER.logInfo("开始执行任务 pull-scanUserWaitFixFailed: " + checkSwitchTurnOff());
        if (checkSwitchTurnOff()) {
            return;
        }
        List<String> businessIds = newUserTaskConfig.getBusinessIds();
        for (String businessId : businessIds) {
            businessExecutorService.execute(()->{
                MDC.put("task", "pull-scanUserWaitFixFailed");
                newUserTaskService.scanUserWaitFixFailed(null, businessId, null);
            });
        }
    }

    /**
     * 扫描待修复用户
     */
    @Scheduled(fixedDelayString = "${uac.newuser.task.scanUserWaitFix.delay:#{5 * 60 * 1000}}")
    public void scanUserWaitFix() {
        MDC.put("task", "pull-scanUserWaitFix");
        LOGGER.logInfo("开始执行任务 pull-scanUserWaitFix: " + checkSwitchTurnOff());
        if (checkSwitchTurnOff()) {
            return;
        }
        List<String> businessIds = newUserTaskConfig.getBusinessIds();
        for (String businessId : businessIds) {
            businessExecutorService.execute(()->{
                MDC.put("task", "pull-scanUserWaitFix");
                newUserTaskService.scanUserWaitFix(executorService, null, businessId, null);
            });
        }
    }


    /**
     * 校验开关状态
     *
     * @return
     */
    protected boolean checkSwitchTurnOff() {
        return BooleanUtils.isNotTrue(newUserTaskConfig.getUserTaskFixLongWaitingEnable());
    }

}
