package cn.loveapp.uac.newuser.scheduler.service;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/3/10 18:28
 * @Description: 新用户定时任务Service
 */
public interface NewUserTaskService {

    /**
     * 扫描待开通用户
     * @param platformId
     * @param businessId
     * @param appName
     */
    void scanWaitOpenUser(String platformId, String businessId, String appName);

    /**
     * 扫描需要重试开通的新用户
     * @param platformId
     * @param businessId
     * @param appName
     */
    void scanRetryUser(String platformId, String businessId, String appName);

    /**
     * 扫描长时间处于开通中的异常用户, 重新设置为待开通
     * @param platformId
     * @param businessId
     * @param appName
     */
    void scanOpenExceptionUser(String platformId, String businessId, String appName);

    /**
     * 扫描离网用户
     * @param platformId
     * @param businessId
     * @param appName
     */
    void scanOfflineUser(String platformId, String businessId, String appName);

    /**
     * 扫描长时间处于拉数据状态的用户并标记为待修复状态
     * @param platformId
     * @param businessId
     * @param appName
     */
    void scanUserFixLongWaiting(String platformId, String businessId, String appName);

    /**
     * 扫描长时间处于拉数据失败状态的用户并标记为待修复状态
     * @param platformId
     * @param businessId
     * @param appName
     */
    void scanUserFailedLongWaiting(String platformId, String businessId, String appName);

    /**
     * 扫描修复失败的用户并标记为待修复状态
     * @param platformId
     * @param businessId
     * @param appName
     */
    void scanUserWaitFixFailed(String platformId, String businessId, String appName);

    /**
     * 扫描待修复用户
     * @param executorService
     * @param platformId
     * @param businessId
     * @param appName
     */
    void scanUserWaitFix(ThreadPoolExecutor executorService, String platformId, String businessId, String appName);
}
