package cn.loveapp.uac.newuser.scheduler.service.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.utils.RocketMqQueueHelper;
import cn.loveapp.uac.db.common.entity.AyBusinessOpenUser;
import cn.loveapp.uac.entity.UserProductInfoBusinessExt;
import cn.loveapp.uac.db.common.repository.AyBusinessOpenUserRepository;
import cn.loveapp.uac.db.common.repository.UserProductionInfoExtRepository;
import cn.loveapp.uac.domain.ComLoveRpcInnerprocessRequestHead;
import cn.loveapp.uac.domain.PullDataRequestBody;
import cn.loveapp.uac.domain.PullDataRequestProto;
import cn.loveapp.uac.newuser.common.config.BaseNewUserQueueConfig;
import cn.loveapp.uac.newuser.common.config.NewUserQueueConfigMap;
import cn.loveapp.uac.newuser.common.config.OpenUserCommonConfig;
import cn.loveapp.uac.newuser.common.config.OpenUserDispatcherConfig;
import cn.loveapp.uac.service.config.*;
import cn.loveapp.uac.newuser.common.constant.UserProductionInfoExtConst;
import cn.loveapp.uac.newuser.common.proto.OpenUserRequest;
import cn.loveapp.uac.newuser.common.service.UserService;
import cn.loveapp.uac.newuser.scheduler.config.NewUserTaskConfig;
import cn.loveapp.uac.newuser.scheduler.service.NewUserTaskService;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * @Author: zhongzijie
 * @Date: 2023/3/10 18:29
 * @Description: 新用户定时任务Service实现类
 */
@Service
public class NewUserTaskServiceImpl implements NewUserTaskService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(NewUserTaskServiceImpl.class);

    /**
     * 预发环境激活配置
     */
    private final static String PRETEST_ENV = "pretest";

    @Autowired
    private AyBusinessOpenUserRepository ayBusinessOpenUserRepository;

    @Autowired
    private UserProductionInfoExtRepository userProductionInfoExtRepository;

    @Autowired
    private RocketMqQueueHelper onsQueueHelper;

    @Value("${uac.open.user.scan.limit:200}")
    private Integer limit;

    @Value("${uac.open.user.scan.offset:0}")
    private Integer offset;

    @Value("${uac.open.user.retry.count:480}")
    private Integer defaultRetryCount;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private VersionGrayConfig versionGrayConfig;

    @Autowired
    private DefaultMQProducer newuserProducer;

    @Autowired
    private OpenUserDispatcherConfig openUserDispatcherConfig;

    @Autowired
    private RocketMqQueueHelper rocketMqQueueHelper;

    @Autowired
    private NewUserQueueConfigMap newUserQueueConfigMap;

    @Autowired
    private NewUserTaskConfig newUserTaskConfig;

    @Autowired
    private UserService userService;

    @Autowired
    private OpenUserCommonConfig openUserCommonConfig;

    @Override
    public void scanWaitOpenUser(String platformId, String businessId, String appName) {
        //1.找出所有待开通用户
        int maxId = 0;
        while (true) {
            Set<AyBusinessOpenUser> ayBusinessOpenUserSet = ayBusinessOpenUserRepository.queryByStatus(Collections.singletonList(AyBusinessOpenUser.WAIT_OPEN), maxId, offset, limit, businessId);
            if (org.springframework.util.CollectionUtils.isEmpty(ayBusinessOpenUserSet)) {
                break;
            }
            maxId = ayBusinessOpenUserSet.stream().map(AyBusinessOpenUser::getId).max(Integer::compareTo).get();
            //生产中排除预发配置用户开户扫描
            List<String> grayUsers = versionGrayConfig.getUsers();
            boolean isPretestEnv = applicationContext.getEnvironment().acceptsProfiles(PRETEST_ENV);
            if (!BooleanUtils.isTrue(isPretestEnv)) {
                if (BooleanUtils.isTrue(versionGrayConfig.isEnable()) && CollectionUtils.isNotEmpty(grayUsers)) {
                    Set<AyBusinessOpenUser> igroneGrayBusinessOpenUsersSet = ayBusinessOpenUserSet.stream().filter(b -> grayUsers.stream().noneMatch(grayUser -> Objects.equals(b.getSellerNick(), grayUser))).collect(Collectors.toSet());
                    ayBusinessOpenUserSet = igroneGrayBusinessOpenUsersSet;
                }
            }else{
                //预发环境（开关为false）下只消费预发用户
                LOGGER.logInfo("newuser-pretest预发环境，过滤预发用户ing...");
                if (CollectionUtils.isNotEmpty(grayUsers)) {
                    Set<AyBusinessOpenUser> needGrayBusinessOpenUsersSet = ayBusinessOpenUserSet.stream().filter(b -> grayUsers.stream().anyMatch(grayUser -> Objects.equals(b.getSellerNick(), grayUser))).collect(Collectors.toSet());
                    ayBusinessOpenUserSet = needGrayBusinessOpenUsersSet;
                }
            }


            LOGGER.logInfo("-","-", "即将开通的用户<" + ayBusinessOpenUserSet.size() + ">");
            for (AyBusinessOpenUser ayBusinessOpenUser : ayBusinessOpenUserSet) {
                try {
                    //2.修改状态为开通中
                    reset(ayBusinessOpenUser, AyBusinessOpenUser.OPENING, businessId);
                } catch (Exception e) {
                    LOGGER.logError(ayBusinessOpenUser.getSellerNick(), "", "修改开通状态失败: " + e.getMessage(), e);
                    continue;
                }
                try {
                    //3.发送到newuser队列
                    BaseNewUserQueueConfig newUserQueueConfig = newUserQueueConfigMap.getBaseNewUserQueueConfig(businessId);
                    OpenUserRequest openUserRequest = new OpenUserRequest();
                    openUserRequest.setBusinessId(businessId);
                    openUserRequest.setId(ayBusinessOpenUser.getId());
                    openUserRequest.setSellerId(ayBusinessOpenUser.getSellerId());
                    openUserRequest.setSellerNick(ayBusinessOpenUser.getSellerNick());
                    // 这里发送的tag约定是某一个业务id
                    String msgId = onsQueueHelper.push(newUserQueueConfig.getTopic(), businessId, openUserRequest, newuserProducer, newUserQueueConfig.getRetryCount(),
                            newUserQueueConfig.getDelayTimeLevel());
                    if(msgId == null){
                        reset(ayBusinessOpenUser, AyBusinessOpenUser.WAIT_OPEN, businessId);
                        LOGGER.logError(ayBusinessOpenUser.getSellerNick(), "", "发送" + newUserQueueConfig.getTopic() + "失败, 重新改为待开通");
                    }
                } catch (Exception e) {
                    reset(ayBusinessOpenUser, AyBusinessOpenUser.WAIT_OPEN, businessId);
                    LOGGER.logError(ayBusinessOpenUser.getSellerNick(), "", "发送soldget失败, 重新改为待开通: " + e.getMessage(), e);
                }
            }
        }
    }

    @Override
    public void scanRetryUser(String platformId, String businessId, String appName) {
        //1.找出所有待开通用户
        int maxId = 0;
        while (true) {
            Set<AyBusinessOpenUser> ayBusinessOpenUserSet = ayBusinessOpenUserRepository.queryByStatus(Collections.singletonList(AyBusinessOpenUser.WAIT_RETRY), maxId, offset, limit, businessId);
            if (org.springframework.util.CollectionUtils.isEmpty(ayBusinessOpenUserSet)) {
                break;
            }
            maxId = ayBusinessOpenUserSet.stream().map(AyBusinessOpenUser::getId).max(Integer::compareTo).get();
            LOGGER.logInfo("-", "-", "即将开通的用户数据为<" + ayBusinessOpenUserSet.size() + ">");
            for (AyBusinessOpenUser ayBusinessOpenUser : ayBusinessOpenUserSet) {
                Integer retryCount = ayBusinessOpenUser.getRetryCount();
                if (retryCount > defaultRetryCount) {
                    LOGGER.logInfo(ayBusinessOpenUser.getSellerNick(), "重试后失败", JSON.toJSONString(ayBusinessOpenUser));
                    ayBusinessOpenUser.setStatus(AyBusinessOpenUser.FAILED);
                    ayBusinessOpenUser.setGmtModify(LocalDateTime.now());
                    ayBusinessOpenUserRepository.updateByStatusAndRetryCount(ayBusinessOpenUser, businessId);
                    continue;
                }
                //2.修改状态为开通中
                retryCount = retryCount + 1;
                ayBusinessOpenUser.setRetryCount(retryCount);
                ayBusinessOpenUser.setStatus(AyBusinessOpenUser.OPENING);
                ayBusinessOpenUser.setGmtModify(LocalDateTime.now());
                ayBusinessOpenUserRepository.updateByStatusAndRetryCount(ayBusinessOpenUser, businessId);
                try {
                    BaseNewUserQueueConfig newUserQueueConfig = newUserQueueConfigMap.getBaseNewUserQueueConfig(businessId);
                    OpenUserRequest openUserRequest = new OpenUserRequest();
                    openUserRequest.setBusinessId(businessId);
                    openUserRequest.setId(ayBusinessOpenUser.getId());
                    openUserRequest.setSellerId(ayBusinessOpenUser.getSellerId());
                    openUserRequest.setSellerNick(ayBusinessOpenUser.getSellerNick());
                    onsQueueHelper.push(newUserQueueConfig.getTopic(), businessId, openUserRequest, newuserProducer, newUserQueueConfig.getRetryCount(), 0);

                    LOGGER.logInfo(ayBusinessOpenUser.getSellerNick(), ayBusinessOpenUser.getSellerNick(), "batch router 发送 topic=" + newUserQueueConfig.getTopic());
                } catch (Exception e) {
                    LOGGER.logError(ayBusinessOpenUser.getSellerNick(), ayBusinessOpenUser.getSellerNick(), "iynewuser 重新发回消息队列失败: " + e.getMessage(), e);
                }
            }
        }
    }

    @Override
    public void scanOpenExceptionUser(String platformId, String businessId, String appName) {
        //1.找出长时间处于开通中的异常用户
        LocalDateTime nowTime = LocalDateTime.now();
        LocalDateTime queryTime = nowTime.minusDays(1L);
        Set<AyBusinessOpenUser> ayBusinessOpenUserSet = ayBusinessOpenUserRepository.queryByStatusAndModify(AyBusinessOpenUser.OPENING, queryTime, businessId);

        LOGGER.logInfo("-","开通中的异常用户", JSON.toJSONString(ayBusinessOpenUserSet));
        for (AyBusinessOpenUser ayBusinessOpenUser : ayBusinessOpenUserSet) {
            final Integer retryCount = 0;
            ayBusinessOpenUser.setRetryCount(retryCount);
            ayBusinessOpenUser.setStatus(AyBusinessOpenUser.WAIT_OPEN);
            ayBusinessOpenUser.setGmtModify(LocalDateTime.now());
            ayBusinessOpenUserRepository.updateByStatusAndRetryCount(ayBusinessOpenUser, businessId);
        }
    }

    @Override
    public void scanOfflineUser(String platformId, String businessId, String appName) {
        if (!newUserTaskConfig.getScanOfflineUserEnable()) {
            return;
        }
        Long startTime = System.currentTimeMillis();
        LOGGER.logInfo("-",startTime.toString(),"老用户离网任务开始");
        int pageSize = 1000;
        List<String> oldUserNickList = Lists.newArrayList();
        Pageable pageRequest = PageRequest.of(0,pageSize);

        while (newUserTaskConfig.getScanOfflineUserEnable()){
            try {
                List<UserProductInfoBusinessExt> result = userProductionInfoExtRepository.
                        queryByTopStatusLimit("10", pageRequest.getOffset(), pageRequest.getPageSize(), businessId);
                for (UserProductInfoBusinessExt userExt : result) {
                    try {
                        LOGGER.logInfo(userExt.getSellerNick(), userExt.getSellerId(), "正在比较是否离网");
                        if (openUserCommonConfig.getPretestUsers().contains(userExt.getSellerNick())) {
                            LOGGER.logInfo(userExt.getSellerNick(), userExt.getSellerId(), "测试账户,跳过");
                            continue;
                        }
                        String sellerNick = userService.handleOldUserOffLine(userExt, businessId, userExt.getStoreId(), userExt.getAppName());
                        if (null == sellerNick) {
                            continue;
                        }
                        oldUserNickList.add(sellerNick);
                    }catch (Exception e){
                        LOGGER.logError(userExt.getSellerNick(),"-","用户离网异常",e);
                    }
                }
                if (CollectionUtils.isEmpty(result) || result.size() < pageRequest.getPageSize()){
                    break;
                }
            }finally {
                pageRequest = pageRequest.next();
            }
        }

        Long endTime = System.currentTimeMillis()-startTime;
        LOGGER.logInfo("老用户离网任务结束本次离网用户 " + oldUserNickList.size()+"#   #"+oldUserNickList + " costTime=" + endTime);
        MDC.clear();
    }

    /**
     * 扫描长时间处于拉数据状态的用户并标记为待修复状态
     *
     * @param platformId
     * @param businessId
     * @param appName
     */
    @Override
    public void scanUserFixLongWaiting(String platformId, String businessId, String appName) {
        int maxId = 0;
        try {
            LocalDateTime beforeThirtyMinute = LocalDateTime.now().minusMinutes(30L).withSecond(0).withNano(0);
            while (true) {
                List<UserProductInfoBusinessExt> userProductInfoBusinessExtList =
                        userProductionInfoExtRepository
                                .queryAllByLimit(UserProductInfoBusinessExt.DB_DOING, UserProductionInfoExtConst.ARRIVE_WITHOUT_ENDPOINT, CommonPlatformConstants.PLATFORM_TAO,
                                        beforeThirtyMinute, maxId, businessId);
                if (null == userProductInfoBusinessExtList) {
                    LOGGER.logInfo("scanUserFixLongWaiting-扫描结果为空");
                    break;
                }
                LOGGER.logInfo("scanUserFixLongWaiting-userProductionInfoExtList size" + userProductInfoBusinessExtList.size());
                maxId = userProductInfoBusinessExtList.stream().map(UserProductInfoBusinessExt::getId).max(Integer::compareTo).orElse(0);

                Map<String, List<String>> storeIdAppendAppNameAndSellerNicks = userProductInfoBusinessExtList.stream()
                        .filter(userProductionInfoExt ->userProductionInfoExt.getPullEndDateTime() != null)
                        .collect(Collectors.groupingBy(e -> e.getStoreId() + "&&" + e.getAppName(), Collectors.mapping(UserProductInfoBusinessExt::getSellerId, Collectors.toList())));
                for (Map.Entry<String, List<String>> entry : storeIdAppendAppNameAndSellerNicks.entrySet()) {
                    String storeIdAppendAppName = entry.getKey();
                    String[] storeIdAppendAppNames = storeIdAppendAppName.split("&&");
                    String storeId = storeIdAppendAppNames[0];
                    String app = storeIdAppendAppNames[1];
                    List<String> sellerIds = entry.getValue();
                    userProductionInfoExtRepository.batchUpdatePullStatusForSellerId(sellerIds, UserProductionInfoExtConst.FIX_WAIT, storeId, app, businessId);
                }
            }
        } catch (Exception e) {
            LOGGER.logError("scanUserFixLongWaiting throw exception, message is " + e.getMessage(), e);
        }
    }

    /**
     * 扫描长时间处于拉数据失败状态的用户并标记为待修复状态
     *
     * @param platformId
     * @param businessId
     * @param appName
     */
    @Override
    public void scanUserFailedLongWaiting(String platformId, String businessId, String appName) {
        int maxId = 0;
        try {
            LocalDateTime startDateTime = LocalDateTime.now().minusDays(2L).withHour(0).withMinute(0).withSecond(0).withNano(0);
            LocalDateTime endDateTime = LocalDateTime.now().minusMinutes(10L).withSecond(0).withNano(0);
            while (true) {
                List<UserProductInfoBusinessExt> userProductInfoBusinessExtList =
                        userProductionInfoExtRepository.queryAllByLimitBetweenPullEndTime(UserProductInfoBusinessExt.DB_FAILED,
                                UserProductionInfoExtConst.ARRIVE_WITHOUT_ENDPOINT, CommonPlatformConstants.PLATFORM_TAO,
                                startDateTime, endDateTime, maxId, businessId);
                if (null == userProductInfoBusinessExtList) {
                    LOGGER.logInfo("scanUserFailedLongWaiting-扫描结果为空");
                    break;
                }
                LOGGER.logInfo("scanUserFailedLongWaiting-userProductionInfoExtList size" + userProductInfoBusinessExtList.size());

                maxId = userProductInfoBusinessExtList.stream().map(UserProductInfoBusinessExt::getId).max(Integer::compareTo).orElse(0);

                Map<String, List<String>> storeIdAndSellerNicks = userProductInfoBusinessExtList.stream()
                        .filter(userProductionInfoExt ->userProductionInfoExt.getPullEndDateTime() != null)
                        .collect(Collectors.groupingBy(e -> e.getStoreId() + "&&" + e.getAppName(), Collectors.mapping(UserProductInfoBusinessExt::getSellerId, Collectors.toList())));
                for (Map.Entry<String, List<String>> entry : storeIdAndSellerNicks.entrySet()) {
                    String storeIdAppendAppName = entry.getKey();
                    String[] storeIdAppendAppNames = storeIdAppendAppName.split("&&");
                    String storeId = storeIdAppendAppNames[0];
                    String app = storeIdAppendAppNames[1];
                    List<String> sellerIds = entry.getValue();
                    userProductionInfoExtRepository.batchUpdatePullStatusForSellerId(sellerIds, UserProductionInfoExtConst.FIX_WAIT, storeId, app, businessId);
                }
            }
        } catch (Exception e) {
            LOGGER.logError("scanUserFailedLongWaiting throw exception, message is " + e.getMessage(), e);
        }
    }

    /**
     * 扫描修复失败的用户并标记为待修复状态
     *
     * @param platformId
     * @param businessId
     * @param appName
     */
    @Override
    public void scanUserWaitFixFailed(String platformId, String businessId, String appName) {
        int maxId = 0;
        try {
            while (true) {
                List<UserProductInfoBusinessExt> userProductInfoBusinessExtList = userProductionInfoExtRepository
                        .queryAllWithOutTimeByLimit(UserProductionInfoExtConst.FIX_FAILED, UserProductionInfoExtConst.ARRIVE_WITHOUT_ENDPOINT, CommonPlatformConstants.PLATFORM_TAO, maxId, businessId);
                if (null == userProductInfoBusinessExtList) {
                    LOGGER.logInfo("scanUserWaitFixFailed-扫描结果为空");
                    break;
                }
                LOGGER.logInfo("scanUserWaitFixFailed-userProductionInfoExtList size" + userProductInfoBusinessExtList.size());
                maxId = userProductInfoBusinessExtList.stream().map(UserProductInfoBusinessExt::getId).max(Integer::compareTo).orElse(0);

                Map<String, List<String>> storeIdAndSellerNicks = userProductInfoBusinessExtList.stream()
                        .filter(userProductionInfoExt ->userProductionInfoExt.getPullEndDateTime() != null)
                        .collect(Collectors.groupingBy(e -> e.getStoreId() + "&&" + e.getAppName(), Collectors.mapping(UserProductInfoBusinessExt::getSellerId, Collectors.toList())));
                for (Map.Entry<String, List<String>> entry : storeIdAndSellerNicks.entrySet()) {
                    String storeIdAppendAppName = entry.getKey();
                    String[] storeIdAppendAppNames = storeIdAppendAppName.split("&&");
                    String storeId = storeIdAppendAppNames[0];
                    String app = storeIdAppendAppNames[1];
                    List<String> sellerIds = entry.getValue();
                    userProductionInfoExtRepository.batchUpdatePullStatusForSellerId(sellerIds, UserProductionInfoExtConst.FIX_WAIT, storeId, app, businessId);
                }
            }
        } catch (Exception e) {
            LOGGER.logError("scanUserWaitFixFailed throw exception, message is " + e.getMessage(), e);
        }
    }

    /**
     * 扫描待修复用户
     *
     * @param executorService
     * @param platformId
     * @param businessId
     * @param appName
     */
    @Override
    public void scanUserWaitFix(ThreadPoolExecutor executorService, String platformId, String businessId, String appName) {
        int maxId = 0;
        try {
            while (true) {
                List<UserProductInfoBusinessExt> userProductInfoBusinessExtList = userProductionInfoExtRepository
                        .queryAllWithOutTimeByLimit(UserProductionInfoExtConst.FIX_WAIT, UserProductionInfoExtConst.ARRIVE_WITHOUT_ENDPOINT, CommonPlatformConstants.PLATFORM_TAO, maxId, businessId);
                if (null == userProductInfoBusinessExtList) {
                    LOGGER.logInfo("actionScanUserWaitFix-扫描结果为空");
                    break;
                }
                LOGGER.logInfo("actionScanUserWaitFix-userProductionInfoExtList size" + userProductInfoBusinessExtList.size());
                maxId = userProductInfoBusinessExtList.stream().map(UserProductInfoBusinessExt::getId).max(Integer::compareTo).orElse(0);

                userProductInfoBusinessExtList = userProductInfoBusinessExtList.stream().filter(userProductionInfoExt ->
                        userProductionInfoExt.getPullEndDateTime() != null).collect(Collectors.toList());
                doTask(userProductInfoBusinessExtList, businessId, executorService);

                Map<String, List<String>> storeIdAndSellerNicks = userProductInfoBusinessExtList.stream()
                        .collect(Collectors.groupingBy(e -> e.getStoreId() + "&&" + e.getAppName(), Collectors.mapping(UserProductInfoBusinessExt::getSellerId, Collectors.toList())));
                for (Map.Entry<String, List<String>> entry : storeIdAndSellerNicks.entrySet()) {
                    String storeIdAppendAppName = entry.getKey();
                    String[] storeIdAppendAppNames = storeIdAppendAppName.split("&&");
                    String storeId = storeIdAppendAppNames[0];
                    String app = storeIdAppendAppNames[1];
                    List<String> sellerIds = entry.getValue();
                    userProductionInfoExtRepository.batchUpdatePullStatusForSellerId(sellerIds, UserProductionInfoExtConst.FIX_ING, storeId, app, businessId);
                }
            }
        } catch (Exception e) {
            LOGGER.logError("actionScanOrdersFixLongWaitingUser throw exception, message is " + e.getMessage(), e);
        }
    }

    protected void doTask(List<UserProductInfoBusinessExt> userProductInfoBusinessExts, String businessId, ThreadPoolExecutor executorService) {
        List<List<UserProductInfoBusinessExt>> userInfoLists = Lists.partition(userProductInfoBusinessExts, 10);
        for (List<UserProductInfoBusinessExt> userInfoList : userInfoLists) {
            executorService.execute(() -> {
                actionUserFixLongWaitingProcess(userInfoList, businessId);
            });
        }
    }

    public void actionUserFixLongWaitingProcess(List<UserProductInfoBusinessExt> userProductInfoBusinessExts, String businessId) {
        LOGGER.logInfo("beginning task");
        for (UserProductInfoBusinessExt userProductInfoBusinessExt : userProductInfoBusinessExts) {
            String sellerNick = userProductInfoBusinessExt.getSellerNick();
            String sellerId = userProductInfoBusinessExt.getSellerId();
            String platformId = userProductInfoBusinessExt.getStoreId();
            String appName = userProductInfoBusinessExt.getAppName();
            LOGGER.logInfo(sellerNick, sellerId, "开始处理用户数据");
            try {
                String messageId = sendMessage2PullDataQueue(sellerNick, sellerId, platformId, appName, businessId);
                if (null == messageId) {
                    LOGGER.logInfo(sellerNick, sellerId, "没有发送成功");
                    userProductionInfoExtRepository.batchUpdatePullStatusForSellerId(Collections.singletonList(sellerId), UserProductionInfoExtConst.FIX_FAILED, platformId, appName, businessId);
                } else {
                    userProductionInfoExtRepository.batchUpdatePullStatusForSellerId(Collections.singletonList(sellerId), UserProductInfoBusinessExt.DB_WAIT, platformId, appName, businessId);
                }
            } catch (Exception e) {
                userProductionInfoExtRepository.batchUpdatePullStatusForSellerId(Collections.singletonList(sellerId), UserProductionInfoExtConst.FIX_FAILED, platformId, appName, businessId);
                LOGGER.logError(sellerNick, sellerId, "程序异常出错, 原因为:" + e.getMessage(), e);
            } finally {
                LOGGER.logInfo(sellerNick, sellerId, "结束处理用户数据");
            }
        }
        LOGGER.logInfo("ending task");
    }

    /**
     * 修改开户状态
     * @param ayBusinessOpenUser
     * @param waitOpen
     * @param businessId
     */
    private void reset(AyBusinessOpenUser ayBusinessOpenUser, int waitOpen, String businessId) {
        ayBusinessOpenUser.setStatus(waitOpen);
        ayBusinessOpenUser.setGmtModify(LocalDateTime.now());
        ayBusinessOpenUserRepository.updateByStatus(ayBusinessOpenUser, businessId);
    }

    /**
     * 发送消息到拉数据队列
     * @param sellerNick
     * @param sellerId
     * @param platformId
     * @param businessId
     * @return
     */
    protected String sendMessage2PullDataQueue(String sellerNick, String sellerId, String platformId, String appName, String businessId) {
        Pair<String, String> target = openUserDispatcherConfig.getTargetTopicAndTag(businessId, platformId);
        String topic = target.getLeft();
        String tag = target.getRight();
        ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead = new ComLoveRpcInnerprocessRequestHead();
        comLoveRpcInnerprocessRequestHead.setSellerNick(sellerNick);
        comLoveRpcInnerprocessRequestHead.setSellerId(sellerId);
        comLoveRpcInnerprocessRequestHead.setPlatformId(platformId);
        comLoveRpcInnerprocessRequestHead.setAppName(appName);
        PullDataRequestBody pullDataRequestBody = new PullDataRequestBody();
        PullDataRequestProto pullDataRequestProto = new PullDataRequestProto();
        pullDataRequestProto.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead);
        pullDataRequestProto.setPullDataRequestBody(pullDataRequestBody);
        String messageId = rocketMqQueueHelper.push(topic, tag, pullDataRequestProto, newuserProducer, 0);
        return messageId;
    }

}
