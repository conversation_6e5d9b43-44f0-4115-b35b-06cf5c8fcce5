package cn.loveapp.uac.newuser.scheduler.task.close;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.newuser.scheduler.config.NewUserTaskConfig;
import cn.loveapp.uac.newuser.scheduler.service.NewUserTaskService;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import jakarta.annotation.PostConstruct;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Author: zhong<PERSON><PERSON>e
 * @Date: 2023/1/9 9:52
 * @Description: 扫描离网用户
 */
@Component
public class ScanOfflineUser implements ApplicationListener<ContextClosedEvent> {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ScanOfflineUser.class);

    @Autowired
    private NewUserTaskService newUserTaskService;

    @Autowired
    private NewUserTaskConfig newUserTaskConfig;

    private ThreadPoolExecutor businessExecutorService;

    @PostConstruct
    public void ScanOfflineUserConstruct() {
        int businessPoolSize = newUserTaskConfig.getBusinessPoolSize();
        businessExecutorService = new ThreadPoolExecutor(businessPoolSize, businessPoolSize, 0L, TimeUnit.SECONDS,
                new SynchronousQueue(), new ThreadFactoryBuilder().setNameFormat("ScanOfflineUser-task-pool-%d").build(),
                (Runnable r, ThreadPoolExecutor executor) -> {
                    if (!executor.isShutdown()) {
                        try {
                            executor.getQueue().put(r);
                        } catch (InterruptedException e) {
                            LOGGER.logError(e.toString(), e);
                            Thread.currentThread().interrupt();
                        }
                    }
                });
    }

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        LOGGER.logInfo("ScanOfflineUser exited");
        if (event.getApplicationContext() != null && event.getApplicationContext().getParent() != null) {
            return;
        }
        if (businessExecutorService != null) {
            LOGGER.logInfo("ScanOfflineUser-task-pool waiting...");
            try {
                businessExecutorService.shutdown();
            } catch (Exception e) {
                LOGGER.logError("ScanOfflineUser-task-pool exception is " + e.getMessage(), e);
            }
            LOGGER.logInfo("ScanOfflineUser-task-pool done");
        }
    }

    @Scheduled(cron = "${uac.newuser.task.scanOfflineUser.cron:}")
    public void scanOfflineUser() {
        MDC.put("task", "close-scanOfflineUser");
        List<String> businessIds = newUserTaskConfig.getBusinessIds();
        LOGGER.logInfo("开始执行任务 close-scanOfflineUser: " + businessIds);
        for (String businessId : businessIds) {
            businessExecutorService.execute(()->{
                MDC.put("task", "close-scanOfflineUser");
                newUserTaskService.scanOfflineUser(null, businessId, null);
            });
        }
    }
}
