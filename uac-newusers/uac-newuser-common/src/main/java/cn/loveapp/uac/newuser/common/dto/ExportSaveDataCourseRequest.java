package cn.loveapp.uac.newuser.common.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-06-05 16:59
 * @Description: 获取存商品进度请求体（内部接口）
 */
@Data
public class ExportSaveDataCourseRequest {

    /**
     * 用户ID
     */
    @NotBlank
    private String sellerId;

    /**
     * 用户昵称
     */
    @NotBlank
    private String sellerNick;

    /**
     * 平台ID
     */
    @NotBlank
    private String platformId;

    /**
     * 应用昵称
     */
    @NotBlank
    private String appName;

    /**
     * 业务ID
     */
    @NotBlank
    private String businessId;
}
