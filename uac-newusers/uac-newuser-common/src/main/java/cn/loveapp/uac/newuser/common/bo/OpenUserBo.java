package cn.loveapp.uac.newuser.common.bo;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;

/**
 * @program: orders-services-group
 * @description: UserInfo
 * @author: Jason
 * @create: 2019-11-18 15:47
 **/
@Data
public class OpenUserBo {
	private String sellerId;
	private String sellerNick;
	private String corpId;
	private String memberId;
	private String storeId;

	private int vipFlag;

	/**
	 * 授权变更或订购变更时, 上次授权到期时间
	 */
	private LocalDateTime lastAuthDeadLine;

	/**
	 * 授权变更或订购变更时, 上次订购到期时间
	 */
	private LocalDateTime lastOrderCycleEnd;

	/**
	 * 供货商id
	 */
	private String supplierId;

	/**
	 * 供货商nick
	 */
	private String supplierNick;

	public OpenUserBo(String sellerId, String sellerNick, String mallName, String corpId, String memberId, String storeId,
					  int vipFlag, String accessToken, String refreshToken, LocalDateTime authDeadLine, LocalDateTime orderCycleEnd) {
		this.sellerId = sellerId;
		this.sellerNick = StringUtils.isEmpty(sellerNick) ? mallName : sellerNick;
		this.corpId = corpId;
		this.memberId = memberId;
		this.storeId = storeId;
		this.vipFlag = vipFlag;
		this.accessToken = accessToken;
		this.refreshToken = refreshToken;
		this.authDeadLine = authDeadLine;
		this.orderCycleEnd = orderCycleEnd == null ? authDeadLine : orderCycleEnd;
		this.mallName = mallName;
	}

	private String accessToken;
	private String refreshToken;
	private LocalDateTime authDeadLine;
	private LocalDateTime orderCycleEnd;

    /**
     * 店铺名
     */
    private String mallName;

	public OpenUserBo() {
	}

}
