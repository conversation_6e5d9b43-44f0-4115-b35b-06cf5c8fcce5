package cn.loveapp.uac.newuser.common.business;

import cn.loveapp.common.autoconfigure.platform.CommonDispatcherHandler;
import cn.loveapp.uac.db.common.entity.AyBusinessOpenUserLog;
import cn.loveapp.uac.newuser.common.constant.OpenResult;
import cn.loveapp.uac.newuser.common.dto.SaveDataDTO;
import cn.loveapp.uac.newuser.common.dto.SaveDataCourseDTO;
import cn.loveapp.uac.newuser.common.dto.UserInfoDTO;

import java.io.UnsupportedEncodingException;

/**
 * @Author: zhongzijie
 * @Date: 2023/1/7 16:09
 * @Description: 各业务用户存数据管理service
 */
public interface UserSaveDataBusinessHandleService extends CommonDispatcherHandler {

	/**
	 * 获取用户存数据进度
	 * @param session 用户信息
	 * @return 存数据进度
	 */
	SaveDataCourseDTO getPullDataProgress(String businessId, UserInfoDTO session);

	/**
	 * 通过nick开通用户的方法
	 *
     * @param ayBusinessOpenUserLog
     * @param sellerId
     * @param sellerNick 昵称
     * @param businessId
     * @return
	 */
	OpenResult openSaveData(AyBusinessOpenUserLog ayBusinessOpenUserLog, String sellerId, String sellerNick, String platformId, String businessId, String appName);

	/**
	 * 关闭用户存数据
	 *
	 * @param sellerNick 昵称
	 * @param sellerId
	 * @param platformId
	 * @param businessId
	 * @param appName
	 * @return
	 */
	boolean closeSaveData(String sellerNick, String sellerId, String platformId, String businessId, String appName);

	/**
	 * 准备开通存数据
	 *
	 * @return true或false
	 * @throws UnsupportedEncodingException
	 */
	boolean prepareSaveData(String businessId, SaveDataDTO saveDataDTO) throws Exception;

}
