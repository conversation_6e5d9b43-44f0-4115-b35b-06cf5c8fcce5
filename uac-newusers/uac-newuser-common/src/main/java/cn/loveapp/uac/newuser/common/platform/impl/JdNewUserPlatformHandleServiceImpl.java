package cn.loveapp.uac.newuser.common.platform.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import org.springframework.stereotype.Service;

/**
 * 京东 新用户开户平台处理service
 *
 * <AUTHOR>
 * @Date 2025/3/5 16:20
 */
@Service
public class JdNewUserPlatformHandleServiceImpl extends AbstractNewUserPlatformHandleServiceImpl {

    @Override
    public Boolean isNeedPullData(String businessId, String platformId, String appName) {
        return true;
    }

    @Override
    public String getDispatcherId() {
        return CommonPlatformConstants.PLATFORM_JD;
    }
}
