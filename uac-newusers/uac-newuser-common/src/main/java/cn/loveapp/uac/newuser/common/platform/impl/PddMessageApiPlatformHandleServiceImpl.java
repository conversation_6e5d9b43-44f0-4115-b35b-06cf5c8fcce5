package cn.loveapp.uac.newuser.common.platform.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.pdd.PddSDKService;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.request.MessageRequest;
import cn.loveapp.uac.common.response.MessageResponse;
import cn.loveapp.uac.newuser.common.platform.MessageApiPlatformHandleService;
import cn.loveapp.uac.common.code.ErrorCode;
import com.alibaba.fastjson2.JSON;
import com.pdd.pop.sdk.http.PopBaseHttpResponse;
import com.pdd.pop.sdk.http.api.pop.request.*;
import com.pdd.pop.sdk.http.api.pop.response.*;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * @program: orders-services-group
 * @description: PDDPlatformMessageServiceImpl
 * @author: Jason
 * @create: 2019-11-28 17:28
 **/
@Service
public class PddMessageApiPlatformHandleServiceImpl implements MessageApiPlatformHandleService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PddMessageApiPlatformHandleServiceImpl.class);

	@Autowired
	private PddSDKService pddAppsSDKService;

	@Override
	public boolean isSubscribeUserMessageService(MessageRequest request, String platformId, String appName) {
		PddPmcUserGetRequest userPermitRequest = new PddPmcUserGetRequest();
		userPermitRequest.setOwnerId(request.getSellerId());
		PddPmcUserGetResponse userGetResponse = pddAppsSDKService.execute(userPermitRequest, appName);
		if (null != userGetResponse) {
			return userGetResponse.getErrorResponse() == null
				&& userGetResponse.getPmcUserGetResponse() != null
				&& userGetResponse.getPmcUserGetResponse().getPmcUser()!=null
				&& Integer.valueOf(0).equals(userGetResponse.getPmcUserGetResponse().getPmcUser().getIsExpire());
		}
		return false;
	}

	@Override
	public MessageResponse subscribeUserMessageService(@NotNull MessageRequest request, String platformId, String appName) {
		PddPmcUserPermitRequest pddPmcUserPermitRequest = new PddPmcUserPermitRequest();
		if (!StringUtils.isEmpty(request.getTopics())) {
			pddPmcUserPermitRequest.setTopics(request.getTopics());
		}
		PddPmcUserPermitResponse userPermitResponse = pddAppsSDKService.execute(pddPmcUserPermitRequest,request.getTopSession(),appName);
		MessageResponse response = new MessageResponse();
		if (null != userPermitResponse) {
			if (!Objects.isNull(userPermitResponse.getErrorResponse())) {
				toResponse(response, userPermitResponse);
			} else {
				response.setIsSuccess(userPermitResponse.getPmcUserPermitResponse().getIsSuccess());
			}
			return response;
		}
		response.setErrorCode(ErrorCode.BaseCode.REQUEST_ERR.getCode().toString());
		return response;
	}

	private void toResponse(MessageResponse response, PopBaseHttpResponse popBaseHttpResponse) {
		response.setErrorCode(popBaseHttpResponse.getErrorResponse().getErrorCode().toString());
		response.setSubCode(popBaseHttpResponse.getErrorResponse().getSubCode());
		response.setSubMsg(popBaseHttpResponse.getErrorResponse().getSubMsg());
	}

	@Override
	public MessageResponse cancelUserMesageService(MessageRequest request, String platformId, String appName) {
		PddPmcUserCancelRequest pddPmcUserCancelRequest = new PddPmcUserCancelRequest();
		MessageResponse response = new MessageResponse();
		if (StringUtils.isEmpty(request.getSellerId())) {
			response.setErrorCode(ErrorCode.BaseCode.PARAMS_ERR.getCode().toString());
			return response;
		}
		pddPmcUserCancelRequest.setOwnerId(request.getSellerId());
		PddPmcUserCancelResponse pddPmcUserCancelResponse = pddAppsSDKService.execute(pddPmcUserCancelRequest, appName);
		if (null != pddPmcUserCancelResponse) {
			if (!Objects.isNull(pddPmcUserCancelResponse.getErrorResponse())) {
				toResponse(response, pddPmcUserCancelResponse);
			}
			response.setIsSuccess(true);
			if (pddPmcUserCancelResponse.getTmcUserCancelResponse() != null) {
				response.setIsSuccess(pddPmcUserCancelResponse.getTmcUserCancelResponse().getIsSuccess());
			}
			return response;
		}
		response.setErrorCode(ErrorCode.BaseCode.REQUEST_ERR.getCode().toString());
		return null;
	}

	@Override
	public boolean isSubscribeUserDbService(MessageRequest request, String platformId, String appName) {
		PddDdyPdpUsersGetRequest userPermitRequest = new PddDdyPdpUsersGetRequest();
		userPermitRequest.setOwnerId(Long.parseLong(request.getSellerId()));
		PddDdyPdpUsersGetResponse userGetResponse = pddAppsSDKService.execute(userPermitRequest, appName);
		if (null != userGetResponse) {
			return userGetResponse.getErrorResponse() == null
				&& userGetResponse.getDdyPdpUsersGetResponse() != null
				&& CollectionUtils.isNotEmpty(userGetResponse.getDdyPdpUsersGetResponse().getUsers())
				&& Integer.valueOf(1).equals(userGetResponse.getDdyPdpUsersGetResponse().getUsers().get(0).getStatus());
		}
		return false;
	}

	@Override
	public MessageResponse subscribeUserDbService(MessageRequest request, String platformId, String appName) {
		PddDdyPdpUserAddRequest pddDdyPdpUserAddRequest = new PddDdyPdpUserAddRequest();
		pddDdyPdpUserAddRequest.setRdsId(request.getRdsName());
		PddDdyPdpUserAddResponse pddAddResponse = pddAppsSDKService.execute(pddDdyPdpUserAddRequest, request.getTopSession(), appName);
		MessageResponse response = new MessageResponse();
		String errMessage = "商家已经存在，请勿重复添加";
		Integer errorCode = 50001;
		if (pddAddResponse != null){
			if (pddAddResponse.getDdyPdpUserAddResponse() != null){
				response.setIsSuccess(pddAddResponse.getDdyPdpUserAddResponse().getIsSuccess());
			}else if (errorCode.equals(pddAddResponse.getErrorResponse().getErrorCode()) && errMessage.equals(pddAddResponse.getErrorResponse().getSubMsg())){
				response.setIsSuccess(true);
				return response;
			}else if (pddAddResponse.getErrorResponse() != null){
				LOGGER.logInfo(request.getSellerNick(),"-", "pddDdyPdpUserAddRequest调用接口报错"+JSON.toJSONString(pddAddResponse.getErrorResponse()));
				toResponse(response,pddAddResponse);
				response.setIsSuccess(false);
			}else {
				response.setIsSuccess(false);
			}
		}else {
			response.setIsSuccess(false);
		}
		return response;
	}

	@Override
	public MessageResponse cancelUserDbService(MessageRequest request, String platformId, String appName) {
		 PddDdyPdpUserDeleteRequest pddDdyPdpUserDeleteRequest = new PddDdyPdpUserDeleteRequest();
		 pddDdyPdpUserDeleteRequest.setOwnerId(Long.parseLong(request.getSellerId()));
		 PddDdyPdpUserDeleteResponse pddDeleteResponse = pddAppsSDKService.execute(pddDdyPdpUserDeleteRequest, appName);
		 MessageResponse response = new MessageResponse();
		 if (pddDeleteResponse != null){
			 if (pddDeleteResponse.getDdyPdpUsersDeleteResponse() != null) {
				 response.setIsSuccess(pddDeleteResponse.getDdyPdpUsersDeleteResponse().getIsSuccess());
			 }else if (pddDeleteResponse.getErrorResponse() != null){
				 LOGGER.logInfo(request.getSellerNick(),"-", "pddDdyPdpUserDeleteRequest调用接口报错"+JSON.toJSONString(pddDeleteResponse.getErrorResponse()));
				 toResponse(response,pddDeleteResponse);
				 response.setIsSuccess(false);
			 }else {
				 response.setIsSuccess(false);
			 }
		 }else {
			 response.setIsSuccess(false);
		 }
		 return response;
	}

	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_PDD;
	}
}
