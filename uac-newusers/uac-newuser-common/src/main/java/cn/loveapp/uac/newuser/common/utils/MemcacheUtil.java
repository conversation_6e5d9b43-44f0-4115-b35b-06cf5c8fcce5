package cn.loveapp.uac.newuser.common.utils;

import cn.loveapp.common.utils.LoggerHelper;
import net.spy.memcached.AddrUtil;
import net.spy.memcached.ConnectionFactoryBuilder;
import net.spy.memcached.ConnectionFactoryBuilder.Protocol;
import net.spy.memcached.MemcachedClient;
import net.spy.memcached.auth.AuthDescriptor;
import net.spy.memcached.auth.PlainCallbackHandler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 *
 * JedisPoolUtils
 * <AUTHOR>
 * @since 2018-11-26 11:20:56
 */

@Component
public class MemcacheUtil {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(MemcacheUtil.class);

	private final String CACHE_PREFIX = "memc.sess.key.";
	private MemcachedClient mc = null;

	@Value("${ocs.memcache.host}")
	private String host;

	@Value("${ocs.memcache.username}")
	private String userName;

	@Value("${ocs.memcache.password}")
	private String password;

	public String getCache(String cacheKey) {
		try{
			MemcachedClient memcachedClient = getMemcachedClient(false);
			String key = CACHE_PREFIX + cacheKey;
			Object value = memcachedClient.get(key);
			if (null != value) {
				return value.toString();
			}else{
				return "";
			}
		}catch(IOException ex){
			LOGGER.logError("Couldn't create a connection,bailing out:" + ex.getMessage(), ex);
		}
		return null;
	}

	public MemcachedClient getMemcachedClient(boolean force) throws IOException {
		if(mc == null || force){
			AuthDescriptor ad = null;
			if(StringUtils.isNotEmpty(userName)){
				ad = new AuthDescriptor(new String[]{"PLAIN"}, new PlainCallbackHandler(userName, password));
			}
			//然后连接使用ConnectionFactoryBuilder，二进制是必须的
			mc = new MemcachedClient(new ConnectionFactoryBuilder()
				.setProtocol(Protocol.BINARY)
				.setAuthDescriptor(ad).build(),
				AddrUtil.getAddresses(host));
		}
		return mc;
	}

}
