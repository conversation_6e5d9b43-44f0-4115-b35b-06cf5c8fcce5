package cn.loveapp.uac.newuser.common.platform.impl;

import org.springframework.stereotype.Service;

import cn.loveapp.common.constant.CommonPlatformConstants;

/**
 * <AUTHOR>
 * @date 2025-04-18 15:47
 * @description: 淘工厂 新用户开户平台处理服务实现接口
 */
@Service
public class TgcNewUserPlatformHandleServiceImpl extends AbstractNewUserPlatformHandleServiceImpl {

    @Override
    public Boolean isNeedPullData(String businessId, String platformId, String appName) {
        return true;
    }

    @Override
    public String getDispatcherId() {
        return CommonPlatformConstants.PLATFORM_TGC;
    }
}
