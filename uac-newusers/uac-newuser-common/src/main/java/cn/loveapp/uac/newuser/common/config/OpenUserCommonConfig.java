package cn.loveapp.uac.newuser.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/21 18:41
 * @Description: 开户服务通用配置
 */
@Data
@Configuration
public class OpenUserCommonConfig {

	/**
	 * 	自动开通用户的规则中最大的dbNum
	 */
	@Value("${uac.newuser.autords_max_num:15}")
	private Integer maxAutoDbNum;

	@Value("${uac.newuser.exclude.db.id:1,2,3}")
	private List<Integer> excludeDbId;

	/**
	 * 	单个数据库中的最大的订单数量
	 */
	@Value("${uac.newuser.max_dborder_sum:80000000}")
	private Long maxDbOrderSum;

	@Value("${uac.newuser.randomSearchDbId.enable:false}")
	private Boolean randomSearchDb;

	@Value("${uac.newuser.default.downgrade:fullinfo,soldget}")
	private String defaultDowngradeTag;

	@Value("${uac.newuser.allow.open.level.list:}")
	private List<Integer> allowOpenLevelList;

	@Value("${uac.newuser.exclude.level.list:4}")
	private List<Integer> excludeLevelLists;

	@Value("${uac.newuser.ignore.vip.appNamePlatform.list:distribute:doudian,distribute:pdd}")
	private List<String> ignoreVipAppNamePlatform;

	/**
	 * 预发用户列表
	 */
	@Value("${uac.newuser.pretestUsers:}")
	private List<String> pretestUsers = new ArrayList<>();

}
