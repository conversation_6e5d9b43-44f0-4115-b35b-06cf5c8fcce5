package cn.loveapp.uac.newuser.common.platform;

import cn.loveapp.common.autoconfigure.platform.CommonDispatcherHandler;
import cn.loveapp.uac.entity.UserProductInfoBusinessExt;
import cn.loveapp.uac.newuser.common.dto.NewUserPlatformHandleDTO;
import cn.loveapp.uac.newuser.common.dto.SaveDataDTO;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/8/8 9:51
 * @Description: 新用户开户平台处理service
 */
public interface NewUserPlatformHandleService extends CommonDispatcherHandler {

    /**
     * 是否检验W1Deadline
     *
     * @param businessId
     * @param platformId
     * @param appName
     * @return
     */
    Boolean isOnlyCheckW1Deadline(String businessId, String platformId, String appName);

    /**
     * 是否需要拉数据
     *
     * @param businessId
     * @param platformId
     * @param appName
     * @return
     */
    Boolean isNeedPullData(String businessId, String platformId, String appName);

    /**
     * 是否需要订阅平台消息推送
     *
     * @param businessId
     * @param platformId
     * @param appName
     * @return
     */
    Boolean isNeedSubscribeMc(String businessId, String platformId, String appName);

    /**
     * 是否需要订阅平台RDS推送
     *
     * @param businessId
     * @param platformId
     * @param appName
     * @return
     */
    Boolean isNeedSubscribeRds(String businessId, String platformId, String appName);

    /**
     * 获取rds规则
     *
     * @param businessId
     * @param platformId
     * @param appName
     * @return
     */
    String getRdsRule(String businessId, String platformId, String appName);

    /**
     * 离网时是否比较authDeadLine
     *
     * @param businessId
     * @param platformId
     * @param appName
     * @return
     */
    Boolean isNeedCompareAuthDeadline(String businessId, String platformId, String appName);

    /**
     * 正常情况下（用户已开户且各状态正常）处理预开户各平台特殊逻辑
     *
     * @param saveDataDTO
     * @param userInfo
     * @param newUserPlatformHandleDTO
     * @param platformId
     * @param appName
     */
    void handlePrepareOpenOnNormalCondition(SaveDataDTO saveDataDTO, UserProductInfoBusinessExt userInfo,
        NewUserPlatformHandleDTO newUserPlatformHandleDTO, String platformId, String appName);

    /**
     * 先校验token后发送到拉数据队列
     *
     * @param saveDataDTO
     * @param userInfo
     * @param platformId
     * @param appName
     * @return
     */
    boolean checkTokenAndSend2PullDataQueue(SaveDataDTO saveDataDTO, UserProductInfoBusinessExt userInfo, String platformId, String appName);

    /**
     * 发送到拉数据队列
     *
     * @param sellerId
     * @param sellerNick
     * @param openType
     * @param businessId
     * @param platformId
     * @param appName
     * @return
     */
    boolean send2PullDataQueue(String sellerId, String sellerNick, Integer openType, String businessId, String platformId, String appName);
}
