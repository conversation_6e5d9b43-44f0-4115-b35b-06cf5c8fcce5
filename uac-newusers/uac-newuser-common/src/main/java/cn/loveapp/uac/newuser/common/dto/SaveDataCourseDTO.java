package cn.loveapp.uac.newuser.common.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: zhong<PERSON><PERSON><PERSON>
 * @Date: 2023/1/13 17:14
 * @Description: 存数据进度
 */
@Data
public class SaveDataCourseDTO {

	/**
	 * 当前进度
	 */
	private Integer progress;

	/**
	 * 总数
	 */
	private Integer total;

	/**
	 * 拉数据开始时间
	 */
	private LocalDateTime pullStartTime;

	/**
	 * 拉取状态
	 */
	private Integer pullStatus;

    /**
     * 开通状态
     */
    private Integer topStatus;

}
