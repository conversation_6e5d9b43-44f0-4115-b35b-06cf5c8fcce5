package cn.loveapp.uac.newuser.common.platform.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.entity.UserProductInfoBusinessExt;
import cn.loveapp.uac.newuser.common.dto.NewUserPlatformHandleDTO;
import cn.loveapp.uac.newuser.common.dto.SaveDataDTO;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * @Author: zhongzijie
 * @Date: 2023/8/8 11:47
 * @Description: 抖店-新用户开户平台处理service实现类
 */
@Service
public class DoudianNewUserPlatformHandleServiceImpl extends AbstractNewUserPlatformHandleServiceImpl {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(DoudianNewUserPlatformHandleServiceImpl.class);

    /**
     * 抖店周期拉单开关
     */
    @Value("${uac.newuser.doudian.cycle-pull.enabled:true}")
    private Boolean doudianCyclePullEnabled;

    /**
     * 抖店周期拉单时间间隔(天)
     */
    @Value("${uac.newuser.doudian.cycle-pull.interval:7}")
    private Integer doudianCyclePullInterval;

    @Override
    public Boolean isNeedPullData(String businessId, String platformId, String appName) {
        return true;
    }

    @Override
    public void handlePrepareOpenOnNormalCondition(SaveDataDTO saveDataDTO, UserProductInfoBusinessExt userInfo, NewUserPlatformHandleDTO newUserPlatformHandleDTO, String platformId, String appName) {
        String sellerNick = saveDataDTO.getSellerNick();
        String platformAndAppName = platformId + ":" + appName;
        if (doudianCyclePullEnabled && !UserProductInfoBusinessExt.DB_DOING.equals(userInfo.getPullStatus())) {
            if (BooleanUtils.isTrue(saveDataDTO.getTokenUpdated())) {
                if(doCheckTokenAndSend2PullDataQueue(saveDataDTO, userInfo, platformId, appName)){
                    LOGGER.logInfo(sellerNick, platformAndAppName, "抖店用户授权变更 , 需要补单");
                }
            } else {
                LocalDateTime deadline = LocalDateTime.now().minusDays(doudianCyclePullInterval);
                LocalDateTime pullTime = userInfo.getPullEndDateTime();
                if(pullTime != null && pullTime.isBefore(deadline)){
                    if(doCheckTokenAndSend2PullDataQueue(saveDataDTO, userInfo, platformId, appName)){
                        LOGGER.logInfo(sellerNick, platformAndAppName,
                                "抖店用户超" + doudianCyclePullInterval + "天未拉数据, 需要补数据: pullTime=" + pullTime + ", deadline=" + deadline);
                    }
                }
            }
        }
    }

    @Override
    public String getDispatcherId() {
        return CommonPlatformConstants.PLATFORM_DOUDIAN;
    }
}
