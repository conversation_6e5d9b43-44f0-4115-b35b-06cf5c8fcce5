package cn.loveapp.uac.newuser.common.dto;

import lombok.Data;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2023-08-14 11:55
 * @Description: 新用户平台处理DTO
 */

@Data
public class NewUserPlatformHandleDTO {

    /**
     * 是否是存在代发业务的用户
     */
    private Boolean isDistributeUser;

    /**
     * 淘宝老用户之前没有代发业务，现在存代发业务且第一次开通代发业务,重新开通
     */
    private Supplier taobaoDistributeUserFunction;

    /**
     * 是否需要拉取商品信息
     */
    private Boolean isPullItemInfo;
}
