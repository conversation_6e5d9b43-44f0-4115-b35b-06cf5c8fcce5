package cn.loveapp.uac.newuser.common.service;

import cn.loveapp.uac.entity.UserProductInfoBusinessExt;
import cn.loveapp.uac.newuser.common.bo.OpenUserBo;
import cn.loveapp.uac.newuser.common.entity.TargetSellerInfo;

import java.util.List;

/**
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/1/3 9:51
 * @Description: 用户service
 */
public interface UserService {

	/**
	 * 通过sellerNick获取用户信息
	 * @param platformSellerNick
	 * @param platformId
	 * @param appName
	 * @return
	 */
	OpenUserBo getSellerInfoBySellerNick(String platformSellerNick, String platformId, String appName);

	/**
	 * 通过userId获取用户信息
	 * @param platformSellerId
	 * @param platformId
	 * @param appName
	 * @return
	 */
	OpenUserBo getUserInfoBySellerId(String platformSellerId, String platformId, String appName);

	/**
	 * 获取授权token
	 * @param sellerNick
	 * @param sellerId
	 * @param platformId
	 * @param appName
	 * @return
	 */
	String getAuthorization(String sellerNick, String sellerId, String platformId, String appName);

	/**
	 * 批量获取授权token
	 * @param targetSellerInfoList
	 * @return
	 */
	List<TargetSellerInfo> batchGetAuthorization(List<TargetSellerInfo> targetSellerInfoList);

	/**
	 * 处理过期用户下线操作
	 * @param userExt
	 * @param businessId
	 * @param platformId
	 * @param appName
	 * @return
	 */
	String handleOldUserOffLine(UserProductInfoBusinessExt userExt, String businessId, String platformId, String appName);

}
