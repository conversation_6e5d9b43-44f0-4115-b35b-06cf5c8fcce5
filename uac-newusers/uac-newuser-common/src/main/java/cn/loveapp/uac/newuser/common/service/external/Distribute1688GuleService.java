package cn.loveapp.uac.newuser.common.service.external;

import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.uac.newuser.common.dto.request.CheckDistributeUserRequest;
import cn.loveapp.uac.newuser.common.dto.response.CheckDistributeUserResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * <AUTHOR>
 * @date 2023-07-19 17:13
 * @Description: 代发1688gule服务接口请求
 */
@FeignClient(name = "1688guleService", url = "${loveapp.service.rpc.1688guleService.host:http://127.0.0.1:8080}" , path = "/")
public interface Distribute1688GuleService {

    @PostMapping("/Distributeuser/getIsDistributeUser")
    CommonApiResponse<CheckDistributeUserResponse> checkDistributeUser(@RequestBody CheckDistributeUserRequest request);
}
