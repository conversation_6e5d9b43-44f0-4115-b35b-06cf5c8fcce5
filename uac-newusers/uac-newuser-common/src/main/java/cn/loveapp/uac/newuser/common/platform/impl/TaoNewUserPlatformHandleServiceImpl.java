package cn.loveapp.uac.newuser.common.platform.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonBusinessConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.dao.redis.repository.SearchActiveRedisRepository;
import cn.loveapp.uac.entity.UserProductInfoBusinessExt;
import cn.loveapp.uac.newuser.common.dto.NewUserPlatformHandleDTO;
import cn.loveapp.uac.newuser.common.dto.SaveDataDTO;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * @Author: zhong<PERSON><PERSON>e
 * @Date: 2023/8/8 14:21
 * @Description: 淘宝-新用户开户平台处理service实现类
 */
@Service
public class TaoNewUserPlatformHandleServiceImpl extends AbstractNewUserPlatformHandleServiceImpl {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(TaoNewUserPlatformHandleServiceImpl.class);

    @Value("${uac.newuser.taobao.item.searchActiveTimeout:7}")
    private Integer searchActiveTimeout;

    @Autowired
    private SearchActiveRedisRepository searchActiveRedisRepository;

    @Override
    public Boolean isNeedPullData(String businessId, String platformId, String appName) {
        return true;
    }

    @Override
    public Boolean isNeedSubscribeMc(String businessId, String platformId, String appName) {
        return true;
    }

    @Override
    public void handlePrepareOpenOnNormalCondition(SaveDataDTO saveDataDTO, UserProductInfoBusinessExt userInfo,
        NewUserPlatformHandleDTO newUserPlatformHandleDTO, String platformId, String appName) {
        String sellerId = saveDataDTO.getSellerId();
        String businessId = saveDataDTO.getBusinessId();
        if (CommonBusinessConstants.BUSINESS_ITEM.equals(businessId) && CommonAppConstants.APP_ITEM.equals(appName)) {
            // 淘宝爱用商品存商品用户，需要判断活跃标识
            String value = searchActiveRedisRepository.get(sellerId, platformId, appName);
            LocalDateTime now = LocalDateTime.now();
            boolean isNeedPullData = false;
            if (StringUtils.isNotEmpty(value)) {
                LocalDateTime lastDateTime = DateUtil.parseString(value);
                if (now.isAfter(lastDateTime.plusDays(searchActiveTimeout))) {
                    isNeedPullData = true;
                }
            } else {
                isNeedPullData = true;
            }
            if (isNeedPullData) {
                doCheckTokenAndSend2PullDataQueue(saveDataDTO, userInfo, platformId, appName);
                // 续期活跃标记
                searchActiveRedisRepository.set(sellerId, platformId, appName, DateUtil.convertLocalDateTimetoString(), searchActiveTimeout, TimeUnit.DAYS);
            }
        }

        if (BooleanUtils.isTrue(newUserPlatformHandleDTO.getIsDistributeUser()) && StringUtils.isEmpty(userInfo.getProducts())) {
            LOGGER.logInfo(sellerId, businessId, "淘宝老用户之前没有代发业务，现在存代发业务且第一次开通代发业务,重新开通");
            Supplier taobaoDistributeUserFunction = newUserPlatformHandleDTO.getTaobaoDistributeUserFunction();
            taobaoDistributeUserFunction.get();
        }
    }

    @Override
    public String getDispatcherId() {
        return CommonPlatformConstants.PLATFORM_TAO;
    }
}
