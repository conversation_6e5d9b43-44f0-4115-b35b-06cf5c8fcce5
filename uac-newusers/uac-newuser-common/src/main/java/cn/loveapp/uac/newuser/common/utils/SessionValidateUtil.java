package cn.loveapp.uac.newuser.common.utils;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.uac.common.utils.SerializedPhpParser;
import cn.loveapp.uac.newuser.common.dto.UserInfoDTO;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * 获取用户session的公共方法
 *
 * @author: yxm
 * @date: 2018/12/29 07:09
 */
@Component
public class SessionValidateUtil {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(SessionValidateUtil.class);

	public static final String PHPSESSIONID = "PHPSESSID";
	public static final String NICK = "nick";
	public static final String CORP_ID = "corpId";
	public static final String IS_VIP = "isVip";
	public static final String SELLER_ID = "sellerId";
	public static final String REQUEST_CORP_ID = "corp_id";
	public static final String UNKNOWN_IP = "unknown";
	public static final String SESSION_VIPFLAG = "vipflag";
	public static final String SEPARATOR_CHARS = ",";

	@Autowired
	private MemcacheUtil memcacheUtil;

	/**
	 * 从cookie中获取用户信息
	 * @param request
	 * @param logger
	 * @return
	 */
	public UserInfoDTO checkSessionInfo(HttpServletRequest request, LoggerHelper  logger){
		UserInfoDTO userInfoDTO = new UserInfoDTO();
		Cookie[] cookies = request.getCookies();
		if(cookies == null) {
			logger.logError("-", "-", "缓存(cookies)失效");
			userInfoDTO.setCode(CommonApiStatus.ServerError.code());
			userInfoDTO.setMsg("缓存失效");
			return userInfoDTO;
		}
		String sessionID = "";
		for (Cookie cookie : cookies) {
			if (cookie.getName().equals(PHPSESSIONID)) {
				sessionID = cookie.getValue();
				break;
			}
		}
		logger.logInfo("-", "-", "sessionId: " + sessionID);
		if (null == sessionID || "" == sessionID) {
			logger.logError("-", "-", "sessionID为空");
			userInfoDTO.setCode(CommonApiStatus.ServerError.code());
			userInfoDTO.setMsg("sessionID为空");
			return userInfoDTO;
		}
		userInfoDTO.setStoreId(PlatformAndAppNameUtil.defaultPlatformId(request.getParameter("storeId")));
		userInfoDTO.setAppName(PlatformAndAppNameUtil.defaultAppName(userInfoDTO.getStoreId(), request.getParameter("appName")));
		userInfoDTO.setBusinessId(PlatformAndAppNameUtil.defaultBusinessId(request.getParameter("businessId")));
		String sessionContent = memcacheUtil.getCache(sessionID);
		if (StringUtils.isBlank(sessionContent)) {
			logger.logError("-", "-", "缓存失效 sessionContent为空");
			userInfoDTO.setCode(CommonApiStatus.ServerError.code());
			userInfoDTO.setMsg("缓存失效");
			return userInfoDTO;
		}
		SerializedPhpParser serializedPhpParser = new SerializedPhpParser(sessionContent);
		HashMap<String, Object> sessionMap = serializedPhpParser.getSessionValue("nick;vipflag;user_id;table_id;list_id;corp_id");
		if (!sessionMap.containsKey(NICK) || null == sessionMap.get(NICK)) {
			userInfoDTO.setCode(CommonApiStatus.ServerError.code());
			userInfoDTO.setMsg("nick未取到");
			return userInfoDTO;
		}
		String nick = sessionMap.get(NICK).toString().replaceAll("\"", "");
		if(StringUtils.isEmpty(nick)){
			userInfoDTO.setCode(CommonApiStatus.ServerError.code());
			userInfoDTO.setMsg("nick未取到");
			return userInfoDTO;
		}
		userInfoDTO.setCode(CommonApiStatus.Success.code());
		userInfoDTO.setMsg("验证通过");
		userInfoDTO.setNick(nick);
		if(null != sessionMap.get(SESSION_VIPFLAG)){
			userInfoDTO.setVipFlag(sessionMap.get(SESSION_VIPFLAG).toString().replace("\"",""));
		}
		String userId = sessionMap.get("user_id").toString().replace("\"","");
		userInfoDTO.setSellerId(userId);
		if(null != sessionMap.get(REQUEST_CORP_ID)){
			userInfoDTO.setCorpId(sessionMap.get(REQUEST_CORP_ID).toString().replace("\"",""));
		}else{
			userInfoDTO.setCorpId(sessionMap.get("user_id").toString().replace("\"",""));
		}

		return userInfoDTO;
	}
}
