package cn.loveapp.uac.newuser.common.dto.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-07-19 17:17
 * @Description: 检验代发用户请求体
 */
@Data
public class CheckDistributeUserRequest {

    /**
     * 用户id
     */
    @JsonProperty("userId")
    @JSONField(name="userId")
    private String sellerId;

    /**
     * 平台
     */
    @JsonProperty("shopType")
    @JSONField(name="shopType")
    private String platformId;
}
