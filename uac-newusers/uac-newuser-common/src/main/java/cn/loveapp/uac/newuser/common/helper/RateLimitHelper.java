package cn.loveapp.uac.newuser.common.helper;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.dao.redis.repository.OpenUserRedisRepository;
import cn.loveapp.uac.newuser.common.dto.SaveDataDTO;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.util.concurrent.TimeUnit;

/**
 * @Author: zhong<PERSON><PERSON>e
 * @Date: 2024/1/25 18:13
 * @Description: 限频helper
 */
@Component
public class RateLimitHelper {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(RateLimitHelper.class);

    /**
     * prepare接口请求限频key业务前缀
     */
    public static final String RATE_LIMIT_KEY_PREFIX_FILTER_PREPARE_REQUEST = "filterPrepareRequest";

    /**
     * 开户拉数据限频key业务前缀
     */
    public static final String RATE_LIMIT_KEY_PREFIX_PULL_DATA = "filterPullData";

    @Autowired
    private OpenUserRedisRepository openUserRedisRepository;

    @Value("${uac.newuser.openuser.lock.expire.time:5}")
    private Integer expireTime;

    /**
     * 发送拉数据消息间隔时间(小时)
     */
    @Value("${uac.newuser.openuser.sendPullDataQueue.expire.time:12}")
    private Integer sendPullDataQueueExpireTime;

    private boolean rateLimit(String prefix, String businessId, String sellerNick, String platformId, String appName, Integer timeout, TimeUnit timeUnit) {
        try {
            Boolean result = openUserRedisRepository.setIfAbsent(prefix, businessId, platformId,
                StringUtils.trimToEmpty(appName), sellerNick,
                "1", timeout, TimeUnit.SECONDS);
            if (BooleanUtils.isFalse(result)){
                LOGGER.logInfo(sellerNick, platformId + ":" + appName, timeout + timeUnit.toString() + " 内已经来过了");
                return true;
            }
        } catch (Exception e) {
            LOGGER.logError(e.getMessage(), e);
        }
        return false;
    }

    /**
     * 通过redis过滤请求
     * @param saveDataDTO
     * @return true或false
     */
    public boolean filterPrepareRequest(SaveDataDTO saveDataDTO) {
        return rateLimit(RATE_LIMIT_KEY_PREFIX_FILTER_PREPARE_REQUEST, saveDataDTO.getBusinessId(), saveDataDTO.getSellerNick(),
            saveDataDTO.getPlatform(), saveDataDTO.getServiceName(), expireTime, TimeUnit.SECONDS);
    }

    /**
     * 通过redis过滤拉数据消息发送
     *
     * @param saveDataDTO
     * @return true或false
     */
    public boolean filterPullData(SaveDataDTO saveDataDTO) {
        return rateLimit(RATE_LIMIT_KEY_PREFIX_PULL_DATA, saveDataDTO.getBusinessId(), saveDataDTO.getSellerNick(),
            saveDataDTO.getPlatform(), saveDataDTO.getServiceName(), sendPullDataQueueExpireTime, TimeUnit.HOURS);
    }

    private boolean clearRateLimiter(String prefix, String businessId, String sellerNick, String platformId, String appName) throws UnsupportedEncodingException {
        Boolean result = openUserRedisRepository.delete(prefix, businessId, platformId,
            StringUtils.trimToEmpty(appName), sellerNick);
        if (BooleanUtils.isTrue(result)){
            LOGGER.logInfo(sellerNick, businessId + ":" + appName, "clearRateLimiter成功");
            return true;
        }
        return false;
    }

    /**
     * 清除prepare接口请求过滤key
     * @param saveDataDTO
     * @return
     * @throws UnsupportedEncodingException
     */
    public boolean clearPrepareRequestFilter(SaveDataDTO saveDataDTO) throws UnsupportedEncodingException {
        return clearRateLimiter(RATE_LIMIT_KEY_PREFIX_FILTER_PREPARE_REQUEST, saveDataDTO.getBusinessId(),
            saveDataDTO.getSellerNick(), saveDataDTO.getPlatform(), saveDataDTO.getServiceName());
    }

    /**
     * 清除拉数据过滤key
     * @param saveDataDTO
     * @return
     * @throws UnsupportedEncodingException
     */
    public boolean clearPullDataRateFilter(SaveDataDTO saveDataDTO) throws UnsupportedEncodingException {
        return clearRateLimiter(RATE_LIMIT_KEY_PREFIX_PULL_DATA, saveDataDTO.getBusinessId(),
            saveDataDTO.getSellerNick(), saveDataDTO.getPlatform(), saveDataDTO.getServiceName());
    }

}
