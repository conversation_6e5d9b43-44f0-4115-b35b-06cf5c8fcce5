package cn.loveapp.uac.newuser.common.platform.impl;

import org.springframework.stereotype.Service;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;

/**
 * <AUTHOR>
 * @date 2023-12-01 15:20
 * @description: 1688-新用户开户平台处理service实现类
 */
@Service
public class Ali1688NewUserPlatformHandleServiceImpl extends AbstractNewUserPlatformHandleServiceImpl {

    @Override
    public Boolean isNeedPullData(String businessId, String platformId, String appName) {
        if (CommonAppConstants.APP_DISTRIBUTE.equals(appName)) {
            return false;
        }

        return true;
    }

    @Override
    public String getDispatcherId() {
        return CommonPlatformConstants.PLATFORM_1688;
    }
}
