package cn.loveapp.uac.newuser.common.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/1/7 16:09
 * @Description: 重新开通 参数体类
 */
@Data
public class ReOpenSaveDataDTO {

	/**
	 * 用户nick
	 */
	String sellerNick;

	/**
	 * 用户id
	 */
	String sellerId;

	String corpId;

	/**
	 * 供应商编号
	 */
	String supplierId;

	/**
	 *会话session
	 */
	String topSession;

	/**
	 *平台id
	 */
	String storeId;

	/**
	 * 用户最近交易计数
	 */
	Long userRecentlyTradeCount;

	/**
	 * 应用名称
	 */
	String appName;

	/**
	 * 授权变更或订购变更时, 上次授权到期时间
	 */
	private LocalDateTime lastAuthDeadLine;

	/**
	 * 授权变更或订购变更时, 上次订购到期时间
	 */
	private LocalDateTime lastOrderCycleEnd;

	/**
	 * 产品业务（distribute,item,...）
	 */
	private String products;

    /**
     * 开通类型
     * 1:新开通,2:重新开通,3:开通新应用
     */
    private Integer openType;
}
