package cn.loveapp.uac.newuser.common.platform.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.doudian.DoudianSDKService;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.request.MessageRequest;
import cn.loveapp.uac.common.response.MessageResponse;
import cn.loveapp.uac.newuser.common.platform.MessageApiPlatformHandleService;
import cn.loveapp.uac.common.code.ErrorCode;
import com.alibaba.fastjson2.JSON;
import com.doudian.api.BaseDoudianResponse;
import com.doudian.api.request.OpenCloudDdpAddShopRequest;
import com.doudian.api.request.OpenCloudDdpDeleteShopRequest;
import com.doudian.api.request.OpenCloudDdpGetShopListRequest;
import com.doudian.api.response.OpenCloudDdpAddShopResponse;
import com.doudian.api.response.OpenCloudDdpDeleteShopResponse;
import com.doudian.api.response.OpenCloudDdpGetShopListResponse;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;


/**
 * 抖店不需要单独开通推送
 *
 * <AUTHOR>
 * @date 2021/6/29
 */
@Service
public class DoudianMessageApiPlatformHandleServiceImpl implements MessageApiPlatformHandleService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(DoudianMessageApiPlatformHandleServiceImpl.class);

	@Autowired
	private DoudianSDKService doudianSDKService;


	@Override
	public boolean isSubscribeUserMessageService(MessageRequest request, String platformId, String appName) {
		return true;
	}

	@Override
	public MessageResponse subscribeUserMessageService(MessageRequest request, String platformId, String appName) {
		MessageResponse response = new MessageResponse();
		response.setIsSuccess(true);
		return response;
	}

	@Override
	public MessageResponse cancelUserMesageService(@NotNull MessageRequest request, String platformId, String appName) {
		MessageResponse response = new MessageResponse();
		response.setIsSuccess(true);
		return response;
	}

	@Override
	public boolean isSubscribeUserDbService(MessageRequest request, String platformId, String appName) {
		if (StringUtils.isEmpty(request.getTopSession())) {
			return false;
		}
		OpenCloudDdpGetShopListRequest listRequest = new OpenCloudDdpGetShopListRequest();
		listRequest.setShopId(Long.parseLong(request.getSellerId()));
		OpenCloudDdpGetShopListResponse response = doudianSDKService.execute(listRequest,request.getTopSession(), appName);
		if (null != response) {
			return response.isSuccess()
					&& response.getData() != null
					&& CollectionUtils.isNotEmpty(response.getData().getShops())
					&& Long.valueOf(1).equals(response.getData().getShops().get(0).getStatus());
		}
		return false;
	}

	@Override
	public MessageResponse subscribeUserDbService(@NotNull MessageRequest request, String platformId, String appName) {
		OpenCloudDdpAddShopRequest addShopRequest = new OpenCloudDdpAddShopRequest();
		if (!StringUtils.isEmpty(request.getSellerId())) {
			addShopRequest.setShopId(Long.valueOf(request.getSellerId()));
		}
		if (!StringUtils.isEmpty(request.getRdsName())) {
			addShopRequest.setRdsInstanceId(request.getRdsName());
		}
		OpenCloudDdpAddShopResponse doudainResp = doudianSDKService.execute(addShopRequest, request.getTopSession(), appName);
		return converDoudianResp(doudainResp, request.getSellerNick());
	}

	@Override
	public MessageResponse cancelUserDbService(@NotNull MessageRequest request, String platformId, String appName) {
		if (StringUtils.isEmpty(request.getTopSession())) {
			MessageResponse response = new MessageResponse();
			response.setErrorCode(ErrorCode.BaseCode.PARAMS_ERR.getCode().toString());
			response.setMessage("topSession为空");
			response.setIsSuccess(false);
			return response;
		}
		OpenCloudDdpDeleteShopRequest deleteRequest = new OpenCloudDdpDeleteShopRequest();
		if (!StringUtils.isEmpty(request.getSellerId())) {
			deleteRequest.setShopId(Long.valueOf(request.getSellerId()));
		}
		OpenCloudDdpDeleteShopResponse doudainResp = doudianSDKService.execute(deleteRequest,request.getTopSession(), appName);
		return converDoudianResp(doudainResp, request.getSellerNick());
	}

	/**
	 * 转换抖店响应实体为爱用实体
	 * @param doudianResponse
	 * @param nick
	 * @return
	 */
	private MessageResponse converDoudianResp(BaseDoudianResponse doudianResponse,String nick){
		MessageResponse response = new MessageResponse();
		if (doudianResponse == null) {
			response.setErrorCode(ErrorCode.BaseCode.REQUEST_ERR.getCode().toString());
			response.setMessage(ErrorCode.BaseCode.REQUEST_ERR.getMessage());
			response.setIsSuccess(false);
			return response;
		}
		if (doudianResponse.isSuccess()) {
			response.setIsSuccess(true);
			response.setCode(doudianResponse.getCode());
			response.setMessage(doudianResponse.getMessage());
		} else {
			LOGGER.logError(nick, "", "抖店开通/关闭rds失败：" + JSON.toJSONString(doudianResponse));
			response.setErrorCode(doudianResponse.getCode());
			response.setMessage(doudianResponse.getMessage());
			response.setIsSuccess(false);
		}
		return response;
	}

	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_DOUDIAN;
	}

}
