package cn.loveapp.uac.newuser.common.platform.impl;

import org.springframework.stereotype.Service;

import cn.loveapp.common.constant.CommonPlatformConstants;

/**
 * <AUTHOR>
 * @date 2024-01-15 10:46
 * @description: 爱用-新用户开户平台处理service实现类
 */
@Service
public class AiyongNewUserPlatformHandleServiceImpl extends AbstractNewUserPlatformHandleServiceImpl {

    @Override
    public String getDispatcherId() {
        return CommonPlatformConstants.PLATFORM_AIYONG;
    }
}
