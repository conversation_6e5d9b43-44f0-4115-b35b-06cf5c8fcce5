package cn.loveapp.uac.newuser.common.config;

import cn.loveapp.common.constant.CommonPlatformConstants;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @Author: z<PERSON><PERSON><PERSON>e
 * @Date: 2023/3/9 18:54
 * @Description: 开户服务配置 - 分业务 - 分平台
 */
@Data
@Component
@ConfigurationProperties(prefix = "uac.newuser.dispatcher")
public class OpenUserDispatcherConfig {

    public final static String EMPTY_RDS = "EMPTY_RDS";

    private Map<String, Map<String, PlatformConfig>> businesses = Maps.newHashMap();


    @Data
    public static class PlatformConfig {

        /**
         * 主题
         */
        private String topic;

        /**
         * 标签
         */
        private String tag;
    }

    /**
     * 获取对应业务的拉数据topic和tag
     * @param businessId
     * @param platformId
     * @return
     */
    public Pair<String, String> getTargetTopicAndTag(String businessId, String platformId) {
        PlatformConfig platformConfig = getConfig(businessId, platformId);
        return Pair.of(platformConfig.getTopic(), platformConfig.getTag());
    }

    /**
     * 获取业务对应的开通配置
     * @param businessId
     * @param platformId
     * @return
     */
    private PlatformConfig getConfig(String businessId, String platformId) {
        Map<String, PlatformConfig> configMap = businesses.get(businessId);
        if (configMap == null) {
            return null;
        }
        PlatformConfig platformConfig = configMap.get(platformId);
        if (platformConfig == null) {
            platformConfig = configMap.get(CommonPlatformConstants.PLATFORM_DEFAULT);
            if (platformConfig == null) {
                throw new RuntimeException("缺少开户配置: uac.newuser.dispatcher.businesses." + businessId + ".DEFAULT");
            }
        }
        return platformConfig;
    }
}
