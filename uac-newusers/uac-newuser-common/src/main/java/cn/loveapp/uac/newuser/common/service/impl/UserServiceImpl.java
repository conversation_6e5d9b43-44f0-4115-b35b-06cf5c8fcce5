package cn.loveapp.uac.newuser.common.service.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.entity.UserProductInfoBusinessExt;
import cn.loveapp.uac.newuser.common.bo.OpenUserBo;
import cn.loveapp.uac.newuser.common.business.UserSaveDataBusinessHandleService;
import cn.loveapp.uac.newuser.common.constant.LevelConstant;
import cn.loveapp.uac.newuser.common.entity.TargetSellerInfo;
import cn.loveapp.uac.newuser.common.platform.NewUserPlatformHandleService;
import cn.loveapp.uac.newuser.common.service.UserCenterService;
import cn.loveapp.uac.newuser.common.service.UserService;
import cn.loveapp.uac.response.UserFullInfoResponse;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * @Author: zhongzijie
 * @Date: 2023/1/3 9:51
 * @Description: 用户service实现类
 */
@Service
public class UserServiceImpl implements UserService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(UserServiceImpl.class);

	@Autowired
	private UserCenterService userCenterService;

	@Autowired
	private UserSaveDataBusinessHandleService userSaveDataBusinessHandleService;

	@Autowired
	private NewUserPlatformHandleService newUserPlatformHandleService;

	@Override
	public OpenUserBo getSellerInfoBySellerNick(String platformSellerNick, String platformId, String appName) {
		UserFullInfoResponse userInfo = userCenterService.getUserFullInfo(platformSellerNick, null, appName, platformId);
		if (userInfo == null) {
			return null;
		}
		OpenUserBo openUserBo = new OpenUserBo(userInfo.getSellerId(), userInfo.getSellerNick(), userInfo.getMallName(),
			userInfo.getCorpId(),
			userInfo.getMemberId(),
			platformId,
			userInfo.getVipflag(),
			userInfo.getAccessToken(),
			userInfo.getRefreshToken(),
			userInfo.getW1Deadline(),
			userInfo.getOrderCycleEnd());

			openUserBo.setSupplierId(userInfo.getSupplierId());
			openUserBo.setSupplierNick(userInfo.getSupplierNick());
		return openUserBo;
	}

	@Override
	public OpenUserBo getUserInfoBySellerId(String platformSellerId, String platformId, String appName) {
		UserFullInfoResponse userInfo = userCenterService.getUserFullInfo(null, platformSellerId, appName, platformId);
		if (userInfo == null) {
			return null;
		}
		OpenUserBo openUserBo = new OpenUserBo(userInfo.getSellerId(), userInfo.getSellerNick(), userInfo.getMallName(),
			userInfo.getCorpId(),
			userInfo.getMemberId(),
			platformId,
			userInfo.getVipflag(),
			userInfo.getAccessToken(),
			userInfo.getRefreshToken(),
			userInfo.getW1Deadline(),
			userInfo.getOrderCycleEnd());
			openUserBo.setSupplierId(userInfo.getSupplierId());
			openUserBo.setSupplierNick(userInfo.getSupplierNick());
		return openUserBo;
	}

	@Override
	public String getAuthorization(String sellerNick, String sellerId, String platformId, String appName) {
		return userCenterService.getTopSession(platformId, appName, sellerNick, sellerId);
	}

	@Override
	public List<TargetSellerInfo> batchGetAuthorization(List<TargetSellerInfo> targetSellerInfoList) {
		return userCenterService.getTopSession(targetSellerInfoList);
	}

	@Override
	public String handleOldUserOffLine(UserProductInfoBusinessExt userExt, String businessId, String platformId, String appName) {
		String sellerNick = userExt.getSellerNick();
		String sellerId = userExt.getSellerId();
		OpenUserBo user = getSellerInfoBySellerNick(userExt.getSellerNick(), userExt.getStoreId(), appName);
		if (isNeedOffLine(user, businessId, platformId, appName)) {
			//需要离网的用户
			LOGGER.logInfo(sellerNick, sellerId, platformId + ":" + appName + " 过期, 离网过期用户 " + user);
			userSaveDataBusinessHandleService.closeSaveData(sellerNick, sellerId, platformId, businessId, appName);
			return sellerNick;
		}
		return null;
	}

	/**
	 * 判断用户是否需要离网
	 * @param user
	 * @param businessId
	 * @param platformId
	 * @param appName
	 * @return
	 */
    private boolean isNeedOffLine(OpenUserBo user, String businessId, String platformId, String appName) {
        Boolean isNeedCompareAuthDeadline = newUserPlatformHandleService.isNeedCompareAuthDeadline(businessId, platformId, appName);
        boolean isDistribute = CommonAppConstants.APP_DISTRIBUTE.equals(appName);
        if (BooleanUtils.isTrue(isNeedCompareAuthDeadline)) {
            // 当天零点
            LocalDateTime nowDateZeroStart = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            // 代发非高级版也存商品
            return (user == null || (user.getVipFlag() == LevelConstant.LEVEL_BASIC_VERSION && !isDistribute)
                || user.getAuthDeadLine() != null && user.getAuthDeadLine().isBefore(nowDateZeroStart));
        } else {
            return user == null || (user.getVipFlag() == LevelConstant.LEVEL_BASIC_VERSION && !isDistribute);
        }
    }

}
