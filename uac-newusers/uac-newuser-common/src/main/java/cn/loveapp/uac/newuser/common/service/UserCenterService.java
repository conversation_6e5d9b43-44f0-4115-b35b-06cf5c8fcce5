package cn.loveapp.uac.newuser.common.service;

import cn.loveapp.common.web.CommonApiException;
import cn.loveapp.uac.newuser.common.entity.TargetSellerInfo;
import cn.loveapp.uac.response.UserFullInfoResponse;
import cn.loveapp.uac.response.UserInfoResponse;

import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/1/3 9:51
 * @Description: 用户中心service
 */
public interface UserCenterService {
	/**
	 * 获取用户的topSession
	 *
	 * @param platformId
	 * @param appName
	 * @param sellerNick
	 * @param sellerId
	 *
	 * @return
	 */
	String getTopSession(String platformId, String appName, String sellerNick, String sellerId);

	/**
	 * 批量获取用户的topSession
	 *
	 * @param targetSellerInfoList
	 *
	 * @return
	 */
	List<TargetSellerInfo> getTopSession(List<TargetSellerInfo> targetSellerInfoList);

	/**
	 * 获取用户信息
	 *
	 * @param sellerNick
	 * @param sellerId
	 * @param platformId
	 * @param appName
	 * @return
	 */
	UserInfoResponse getUserInfo(String sellerNick, String sellerId, String appName, String platformId);

	/**
	 * 获取用户信息
	 *
	 * @param targetSellerInfoList
	 * @return
	 */
	List<UserInfoResponse> getUserInfo(List<TargetSellerInfo> targetSellerInfoList);

	/**
	 * 获取用户全部信息
	 * @param sellerNick
	 * @param sellerId
	 * @param appName
	 * @param platformId
	 * @throws CommonApiException
	 * @return
	 */
	UserFullInfoResponse getUserFullInfo(String sellerNick, String sellerId, String appName, String platformId) throws CommonApiException;

	/**
	 * 获取用户信息
	 *
	 * @param sellerId
	 * @param appName
	 * @param platformId
	 * @return
	 */
	UserInfoResponse getUserInfo(String sellerId, String appName, String platformId);


	/**
	 * 获取用户信息
	 *
	 * @param sellerId
	 * @param appName
	 * @param platformId
	 * @return
	 */
	UserInfoResponse getUserTopSessionInfo(String sellerNick, String sellerId, String appName, String platformId);
}
