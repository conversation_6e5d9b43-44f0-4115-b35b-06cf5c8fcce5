package cn.loveapp.uac.newuser.common.utils;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonBusinessConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.uac.entity.UserProductInfoBusinessExt;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

/**
 * @Author: zhongzijie
 * @Date: 2023/1/7 16:09
 * @Description: platformId和appName相关工具类
 */
public class PlatformAndAppNameUtil {
	public static final String SEPARATOR_CHARS = ",";

	/**
	 * 获取默认的businessId
	 *
	 * @param businessId
	 * @return
	 */
	public static String defaultBusinessId(String businessId) {
		if (StringUtils.isEmpty(businessId)) {
			return CommonBusinessConstants.BUSINESS_TRADE;
		}
		return businessId;
	}

	/**
	 * 获取默认的platformId
	 * @param platformId
	 * @return
	 */
	public static String defaultPlatformId(String platformId) {
		if (StringUtils.isEmpty(platformId)) {
			return CommonPlatformConstants.PLATFORM_TAO;
		}
		return platformId;
	}

	/**
	 * 获取默认的appname
	 * @param platformId
	 * @param appName
	 * @return
	 */
	public static String defaultAppName(String platformId, String appName) {
		if (StringUtils.isEmpty(platformId)) {
			return StringUtils.EMPTY;
		} else if (CommonPlatformConstants.PLATFORM_TAO.equals(platformId)) {
			if (CommonAppConstants.APP_TRADE.equals(appName) || StringUtils.isEmpty(appName)) {
				return StringUtils.EMPTY;
			}
		} else if (CommonPlatformConstants.PLATFORM_PDD.equals(platformId) && StringUtils.isEmpty(appName)) {
			return CommonAppConstants.APP_GUANDIAN;
		}
		return appName;
	}

	/**
	 * 判断openedAppNames中是否包含appName
	 *
	 * @param platformId
	 * @param openedAppNames
	 * @param appName
	 * @return
	 */
	public static boolean containsAppName(String platformId, String openedAppNames, String appName){
		if (CommonPlatformConstants.PLATFORM_TAO.equals(platformId) || StringUtils.isEmpty(platformId)) {
			if (CommonAppConstants.APP_TRADE.equals(appName) || StringUtils.isEmpty(appName)) {
				return true;
			}
			return StringUtils.contains(openedAppNames, appName);
		} else if (StringUtils.isAllEmpty(openedAppNames, appName)) {
			return true;
		} else if (StringUtils.isEmpty(appName)) {
			return true;
		} else {
			return StringUtils.contains(openedAppNames, appName);
		}
	}

	/**
	 * 合并appName到openedAppNames中
	 *
	 * @param platformId
	 * @param oldTopStatus
	 * @param openedAppNames
	 * @param appName
	 * @return
	 */
	public static String appendAppName(String platformId, String oldTopStatus, String openedAppNames, String appName){
		if ((CommonPlatformConstants.PLATFORM_TAO.equals(platformId) || StringUtils.isEmpty(platformId))
            && CommonAppConstants.APP_TRADE.equals(appName)) {
			return StringUtils.EMPTY;
		}
		if(UserProductInfoBusinessExt.DB_FAILED.toString().equals(oldTopStatus) || StringUtils.isEmpty(oldTopStatus)){
			return openedAppNames;
		}
		if(StringUtils.isEmpty(appName)){
			return openedAppNames;
		}
		String[] namses = StringUtils.split(openedAppNames, SEPARATOR_CHARS);
		if(namses == null || namses.length == 0){
			return appName;
		}
		Set<String> nameSet = Sets.newHashSet(namses);
		nameSet.add(appName);
		return StringUtils.trimToEmpty(StringUtils.join(nameSet, SEPARATOR_CHARS));
	}

	/**
	 * 从openedAppNames中去除appName
	 * @param platformId
	 * @param openedAppNames
	 * @param appName
	 * @return
	 */
	public static String removeAppName(String platformId, String openedAppNames, String appName){
		if(StringUtils.isAnyEmpty(openedAppNames, appName)){
			return openedAppNames;
		}
		String[] namses = StringUtils.split(openedAppNames, SEPARATOR_CHARS);
		if(namses == null || namses.length == 0){
			return StringUtils.EMPTY;
		}
		Set<String> nameSet = Sets.newHashSet(namses);
		nameSet.remove(appName);
		return StringUtils.trimToEmpty(StringUtils.join(nameSet, SEPARATOR_CHARS));
	}
}
