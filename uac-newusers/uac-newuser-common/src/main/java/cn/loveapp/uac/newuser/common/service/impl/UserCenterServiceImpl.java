package cn.loveapp.uac.newuser.common.service.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiException;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.uac.newuser.common.entity.TargetSellerInfo;
import cn.loveapp.uac.newuser.common.service.UserCenterService;
import cn.loveapp.uac.request.BatchRequest;
import cn.loveapp.uac.request.UserFullInfoRequest;
import cn.loveapp.uac.request.UserInfoRequest;
import cn.loveapp.uac.response.UserFullInfoResponse;
import cn.loveapp.uac.response.UserInfoResponse;
import cn.loveapp.uac.service.UserCenterInnerApiService;
import com.alibaba.fastjson2.JSON;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: zhongzijie
 * @Date: 2023/1/3 9:51
 * @Description: 用户中心service实现类
 */
@Service
public class UserCenterServiceImpl implements UserCenterService {
	private final static LoggerHelper LOGGER = LoggerHelper.getLogger(UserCenterServiceImpl.class);

	@Autowired
	private UserCenterInnerApiService userCenterInnerApiService;

	@Override
	public String getTopSession(String platformId, String appName, String sellerNick, String sellerId) {
		UserInfoRequest request = new UserInfoRequest();
		request.setPlatformId(platformId);
		request.setApp(appName);
		request.setSellerNick(sellerNick);
		request.setSellerId(sellerId);

		String topSession = null;
		try {
			LOGGER.logInfo(sellerNick, "-", "调uac获取topsession, request=" + JSON.toJSONString(request));
			CommonApiResponse<UserInfoResponse> response = userCenterInnerApiService.getTopSession(request);
			LOGGER.logInfo(sellerNick, "-", "调uac获取topsession, response=" + toJSON(response));
			if (response.getCode().equals(CommonApiStatus.Success.code()) && null == response.getSubCode()) {
				topSession = response.getBody().getTopSession();
			}
		} catch (Exception e) {
			LOGGER.logError(sellerNick, "-", "网络异常，获取topsession失败", e);
		}
		return topSession;
	}

	@Override
	public List<TargetSellerInfo> getTopSession(List<TargetSellerInfo> targetSellerInfoList) {
		List<UserInfoRequest> requestList = new LinkedList<>();

		for (TargetSellerInfo targetSellerInfo : targetSellerInfoList) {
			UserInfoRequest request = new UserInfoRequest();
			request.setPlatformId(targetSellerInfo.getTargetStoreId());
			request.setApp(targetSellerInfo.getTargetAppName());
			request.setSellerNick(targetSellerInfo.getTargetNick());
			request.setSellerId(targetSellerInfo.getTargetSellerId());
			requestList.add(request);
		}

		try {
			LOGGER.logInfo("调uac获取batchGetTopSession, request=" + JSON.toJSONString(requestList));
			BatchRequest batchRequest = new BatchRequest();
			batchRequest.setRequestList(requestList);
			CommonApiResponse<List<UserInfoResponse>> response = userCenterInnerApiService.batchGetTopSession(batchRequest);
			LOGGER.logInfo("调uac获取batchGetTopSession, response=" + toJSON(response));
			if (response.getCode().equals(CommonApiStatus.Success.code()) && null == response.getSubCode()) {
				List<UserInfoResponse> topSessionEnptyList = response.getBody().stream().filter(f -> StringUtils.isEmpty(f.getTopSession())).collect(Collectors.toList());
				LOGGER.logInfo("调uac获取batchGetTopSession,授权过期的用户集合：" + JSON.toJSONString(topSessionEnptyList));
				List<TargetSellerInfo> existTopSessionSellerList = response.getBody().stream()
						.filter(f->StringUtils.isNotEmpty(f.getTopSession()))
						.map(m -> new TargetSellerInfo(m.getSellerNick(),m.getPlatformId(),m.getAppName(), m.getTopSession()))
						.collect(Collectors.toList());
				return existTopSessionSellerList;
			}
		} catch (Exception e) {
			LOGGER.logError("网络异常，调uac获取batchGetTopSession失败:" + e.getMessage(), e);
		}
		return Collections.emptyList();
	}

	@Override
	public UserFullInfoResponse getUserFullInfo(String sellerNick, String sellerId, String appName, String platformId) throws CommonApiException {
		UserFullInfoRequest userFullInfoRequest = new UserFullInfoRequest();
		userFullInfoRequest.setSellerNick(sellerNick);
		userFullInfoRequest.setSellerId(sellerId);
		userFullInfoRequest.setApp(appName);
		userFullInfoRequest.setPlatformId(platformId);
		LOGGER.logInfo(sellerNick, "-", "调uac获取userFullInfo, request=" + JSON.toJSONString(userFullInfoRequest));
		CommonApiResponse<UserFullInfoResponse> response = null;
		try {
			response = userCenterInnerApiService.getUserFullInfo(userFullInfoRequest);
		} catch (CommonApiException e) {
			throw e;
		} catch (Exception e) {
			throw new CommonApiException(CommonApiStatus.ServerError.code(), "获取用户信息异常:"+e.getMessage(), e);
		}

		LOGGER.logInfo(sellerNick, "-", "调uac获取userFullInfo, response=" + toJSON(response));
		if (response.isSuccess()) {
			return response.getBody();
		}
		return null;
	}

	@Override
	public UserInfoResponse getUserInfo(String sellerNick, String sellerId, String appName, String platformId) {
		UserInfoRequest request = new UserInfoRequest();
		request.setPlatformId(platformId);
		request.setApp(appName);
		request.setSellerNick(sellerNick);
		request.setSellerId(sellerId);

		try {
			LOGGER.logInfo(sellerNick, "-", "调uac获取userInfo, request=" + JSON.toJSONString(request));
			CommonApiResponse<UserInfoResponse> response = userCenterInnerApiService.getUserInfo(request);
			LOGGER.logInfo(sellerNick, "-", "调uac获取userInfo, response=" + toJSON(response));
			if (response != null && response.getCode().equals(CommonApiStatus.Success.code()) && null == response.getSubCode()) {
				return response.getBody();
			}
		} catch (Exception e) {
			LOGGER.logError(sellerNick, "-", "网络异常，获取userInfo失败", e);
		}
		return null;
	}

	@Override
	public List<UserInfoResponse> getUserInfo(List<TargetSellerInfo> targetSellerInfoList) {
		try {
			List<UserInfoRequest> collect = targetSellerInfoList.stream().map(m -> {
				UserInfoRequest request = new UserInfoRequest();
				request.setPlatformId(m.getTargetStoreId());
				request.setApp(m.getTargetAppName());
				request.setSellerNick(m.getTargetNick());
				request.setSellerId(m.getTargetSellerId());
				return request;
			}).collect(Collectors.toList());
			LOGGER.logInfo("调uac获取batchUserInfo, request=" + JSON.toJSONString(targetSellerInfoList));
			CommonApiResponse<List<UserInfoResponse>> response = userCenterInnerApiService.batchGetUserInfo(collect);
			LOGGER.logInfo("调uac获取batchUserInfo, response=" + toJSON(response));
			if (response != null && response.getCode().equals(CommonApiStatus.Success.code()) && null == response.getSubCode()) {
				return response.getBody();
			}
		} catch (Exception e) {
			LOGGER.logError("网络异常，调uac获取batchUserInfo失败:" + e.getMessage(), e);
		}
		return Collections.emptyList();
	}

	/**
	 * 获取用户信息
	 *
	 * @param sellerId
	 * @param appName
	 * @param platformId
	 * @return
	 */
	@Override
	public UserInfoResponse getUserInfo(String sellerId, String appName, String platformId) {
		return getUserInfo(null, sellerId, appName, platformId);
	}

	@NotNull
	private  <T> String toJSON(CommonApiResponse<T> response) {
		return response.isSuccess() ? JSON.toJSONString(response.getBody()) : JSON.toJSONString(response);
	}

	@Override
	public UserInfoResponse getUserTopSessionInfo(String sellerNick, String sellerId, String appName, String platformId) {
		UserInfoRequest request = new UserInfoRequest();
		request.setPlatformId(platformId);
		request.setApp(appName);
		request.setSellerNick(sellerNick);
		request.setSellerId(sellerId);

		String topSession = null;
		try {
			LOGGER.logInfo(sellerNick, "-", "调uac获取topsession, request=" + JSON.toJSONString(request));
			CommonApiResponse<UserInfoResponse> response = userCenterInnerApiService.getTopSession(request);
			LOGGER.logInfo(sellerNick, "-", "调uac获取topsession, response=" + toJSON(response));
			if (response != null && response.getCode().equals(CommonApiStatus.Success.code()) && null == response.getSubCode()) {
				return response.getBody();
			}
		} catch (Exception e) {
			LOGGER.logError(sellerNick, "-", "网络异常，获取topsession失败", e);
		}
		return null;
	}
}
