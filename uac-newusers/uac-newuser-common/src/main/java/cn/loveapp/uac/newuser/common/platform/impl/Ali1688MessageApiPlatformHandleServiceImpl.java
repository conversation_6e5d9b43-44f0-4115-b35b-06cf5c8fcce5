package cn.loveapp.uac.newuser.common.platform.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.request.MessageRequest;
import cn.loveapp.uac.common.response.MessageResponse;
import cn.loveapp.uac.newuser.common.platform.MessageApiPlatformHandleService;
import jakarta.validation.constraints.NotNull;
import org.springframework.stereotype.Service;


/**
 * Ali1688MessageApiPlatformHandleServiceImpl
 *
 * <AUTHOR>
 * @date 2021/4/8
 */
@Service
public class Ali1688MessageApiPlatformHandleServiceImpl implements MessageApiPlatformHandleService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(Ali1688MessageApiPlatformHandleServiceImpl.class);

	@Override
	public boolean isSubscribeUserMessageService(MessageRequest request, String platformId, String appName) {
		return true;
	}

	@Override
	public MessageResponse subscribeUserMessageService(MessageRequest request, String platformId, String appName) {
		MessageResponse response = new MessageResponse();
		response.setIsSuccess(true);
		return response;
	}

	@Override
	public MessageResponse cancelUserMesageService(@NotNull MessageRequest request, String platformId, String appName) {
		MessageResponse response = new MessageResponse();
		response.setIsSuccess(true);
		return response;
	}

	@Override
	public boolean isSubscribeUserDbService(MessageRequest request, String platformId, String appName) {
		return false;
	}

	@Override
	public MessageResponse subscribeUserDbService(@NotNull MessageRequest request, String platformId, String appName) {
		MessageResponse response = new MessageResponse();
		response.setIsSuccess(true);
		return response;
	}

	@Override
	public MessageResponse cancelUserDbService(@NotNull MessageRequest request, String platformId, String appName) {
		MessageResponse response = new MessageResponse();
		response.setIsSuccess(true);
		return response;
	}

	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_1688;
	}

}
