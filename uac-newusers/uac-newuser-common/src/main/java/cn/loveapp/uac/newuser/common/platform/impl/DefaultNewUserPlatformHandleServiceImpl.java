package cn.loveapp.uac.newuser.common.platform.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import org.springframework.stereotype.Service;

/**
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/8/8 17:07
 * @Description: 默认 - 新用户开户平台处理service实现类
 */
@Service
public class DefaultNewUserPlatformHandleServiceImpl extends AbstractNewUserPlatformHandleServiceImpl {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(DefaultNewUserPlatformHandleServiceImpl.class);

    @Override
    public String getDispatcherId() {
        return CommonPlatformConstants.PLATFORM_DEFAULT;
    }

}
