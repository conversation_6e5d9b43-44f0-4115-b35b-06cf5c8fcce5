package cn.loveapp.uac.newuser.common.business.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonBusinessConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.constant.PretestTopicConstant;
import cn.loveapp.uac.common.dao.redis.repository.OpenUserRedisRepository;
import cn.loveapp.uac.common.dao.redis.repository.UserManageRedisRepositoryHashRedisRepository;
import cn.loveapp.uac.common.request.MessageRequest;
import cn.loveapp.uac.common.response.MessageResponse;
import cn.loveapp.uac.common.utils.RocketMqQueueHelper;
import cn.loveapp.uac.db.common.entity.AyBusinessOpenUser;
import cn.loveapp.uac.db.common.entity.AyBusinessOpenUserLog;
import cn.loveapp.uac.db.common.repository.AyBusinessOpenUserLogRepository;
import cn.loveapp.uac.db.common.repository.AyBusinessOpenUserRepository;
import cn.loveapp.uac.db.common.repository.UserProductionInfoExtRepository;
import cn.loveapp.uac.db.common.repository.UserRepository;
import cn.loveapp.uac.domain.ComLoveRpcInnerprocessRequestHead;
import cn.loveapp.uac.domain.PullDataRequestBody;
import cn.loveapp.uac.domain.PullDataRequestProto;
import cn.loveapp.uac.entity.UserProductInfoBusinessExt;
import cn.loveapp.uac.newuser.common.bo.OpenUserBo;
import cn.loveapp.uac.newuser.common.business.UserSaveDataBusinessHandleService;
import cn.loveapp.uac.newuser.common.config.OpenUserCommonConfig;
import cn.loveapp.uac.newuser.common.config.OpenUserDispatcherConfig;
import cn.loveapp.uac.newuser.common.constant.OpenResult;
import cn.loveapp.uac.newuser.common.dto.NewUserPlatformHandleDTO;
import cn.loveapp.uac.newuser.common.dto.ReOpenSaveDataDTO;
import cn.loveapp.uac.newuser.common.dto.SaveDataDTO;
import cn.loveapp.uac.newuser.common.helper.RateLimitHelper;
import cn.loveapp.uac.newuser.common.platform.MessageApiPlatformHandleService;
import cn.loveapp.uac.newuser.common.platform.NewUserPlatformHandleService;
import cn.loveapp.uac.newuser.common.service.DistributeDataHandleService;
import cn.loveapp.uac.newuser.common.service.UserCenterService;
import cn.loveapp.uac.newuser.common.service.UserService;
import cn.loveapp.uac.response.UserInfoResponse;
import com.alibaba.fastjson2.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: zhongzijie
 * @Date: 2023/1/7 16:09
 * @Description: 各业务用户存数据管理service 抽象类
 */
public abstract class AbstractUserSaveDataBusinessHandleService implements UserSaveDataBusinessHandleService {

	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(AbstractUserSaveDataBusinessHandleService.class);

	private static final String CACHE_ID = "DB_ORDER_COUNT";
	private static final String REDIS_CONTALINS_KEY = "tos";
	public static final String SPLIT_STR = ",";

	@Autowired
	private DistributeDataHandleService distributeDataHandleService;

    @Autowired
    private RateLimitHelper rateLimitHelper;

	protected static final Map<String, String> businessIdAndAppMap = Maps.newHashMap();

	static {
		businessIdAndAppMap.put(CommonBusinessConstants.BUSINESS_TRADE, "trade");
		businessIdAndAppMap.put(CommonBusinessConstants.BUSINESS_ITEM, "item");
		businessIdAndAppMap.put(CommonBusinessConstants.BUSINESS_DEFAULT, "trade");
	}

	@Autowired
	private OpenUserDispatcherConfig openUserDispatcherConfig;

	@Autowired
	private UserService userService;

	@Autowired
	private UserCenterService userCenterService;

	@Value("${uac.newuser.cache.sumCount.timeout:5}")
	protected Integer sumCountCacheTmeout;

	@Value("${uac.newuser.cache.currentCount.timeout:5}")
	protected Integer currentCountCacheTmeout;



	@Autowired
	private AyBusinessOpenUserRepository ayBusinessOpenUserRepository;

	@Autowired
	private AyBusinessOpenUserLogRepository ayBusinessOpenUserLogRepository;

	@Autowired
	private UserProductionInfoExtRepository userProductionInfoExtRepository;

	@Autowired
	private UserManageRedisRepositoryHashRedisRepository userManageRedisRepository;

	@Autowired
	private RedissonClient redissonClient;

	@Autowired
	private OpenUserCommonConfig openUserCommonConfig;

	@Autowired
	private MessageApiPlatformHandleService messageApiPlatformHandleService;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private NewUserPlatformHandleService newUserPlatformHandleService;

	@Autowired
	private OpenUserRedisRepository openUserRedisRepository;

	@Autowired
	private RocketMqQueueHelper rocketMqQueueHelper;

	@Autowired
	private DefaultMQProducer defaultMQProducer;

	/**
	 * 各库订单数缓存 (过期时间不要整点, 防止碰到数据库热点时间)
	 */
	protected Cache<String, List<UserProductInfoBusinessExt>> dbOrderCountCache = CacheBuilder
			.newBuilder()
			.expireAfterWrite(33, TimeUnit.MINUTES)
			.build();

	@Override
	public OpenResult openSaveData(AyBusinessOpenUserLog ayBusinessOpenUserLog, String sellerId, String sellerNick, String platformId, String businessId, String appName) {
		if (StringUtils.isEmpty(sellerId)) {
			LOGGER.logInfo("-", "-", "sellerId为空");
			return OpenResult.USERID_IS_NULL;
		}
		if (StringUtils.isEmpty(sellerNick)) {
			LOGGER.logInfo("-", "-", "nick为空");
			return OpenResult.USERNICK_IS_NULL;
		}
		String lockKey = sellerId + ":" + platformId + ":" + appName;
		RLock lock = redissonClient.getLock(lockKey);
		if (!lock.tryLock()) {
			LOGGER.logInfo(sellerNick, "-", "用户正在开户, 忽略此重复请求, lockKey=" + lockKey);
			return OpenResult.REPEATED_REQUESTS;
		}
		try {
			OpenUserBo openUserBo = userService.getUserInfoBySellerId(sellerId, platformId, appName);
			if (openUserBo == null) {
				LOGGER.logInfo(sellerNick, "-", "没有该用户");
				return OpenResult.NO_USER;
			}
			if (StringUtils.isEmpty(openUserBo.getSellerId())) {
				LOGGER.logInfo(sellerNick, "-", "该用户userId为空");
				return OpenResult.USERID_IS_NULL;
			}
			if (Objects.nonNull(openUserBo.getAuthDeadLine())) {
				if (LocalDateTime.now().isAfter(openUserBo.getAuthDeadLine())) {
					LOGGER.logInfo(sellerNick, "-", "授权过期");
					if (newUserPlatformHandleService.isOnlyCheckW1Deadline(businessId, platformId, appName)) {
						return OpenResult.GET_AUTH_EXCEPTION;
					} else {
						// w1授权到期时间校验不通过再看看订购到期时间，兼容淘宝爱用商品用户w1时间不准确的问题
						if (LocalDateTime.now().isAfter(openUserBo.getOrderCycleEnd())) {
							LOGGER.logInfo(sellerNick, "-", "授权过期且订购过期");
							return OpenResult.GET_AUTH_EXCEPTION;
						}
					}
				}
			}
			String topSession = openUserBo.getAccessToken();
			if (StringUtils.isEmpty(topSession)) {
				topSession = userService.getAuthorization(sellerNick, openUserBo.getSellerId(), platformId, appName);
				if (StringUtils.isEmpty(topSession)) {
					LOGGER.logInfo(sellerNick, "-", "token为空");
					return OpenResult.GET_AUTH_EXCEPTION;
				}
			}


            boolean isDistributeUser = distributeDataHandleService.checkDistributeUser(sellerId, platformId, appName);

			//新手村用户的vipflag需要排除掉
            if (!openUserCommonConfig.getAllowOpenLevelList().contains(openUserBo.getVipFlag()) && !isDistributeUser) {
                LOGGER.logInfo(sellerNick, "-",
                    "不是高级版，不开" + openUserBo.getVipFlag() + " <> " + openUserCommonConfig.getAllowOpenLevelList());
                return OpenResult.VIP_IS_INVALID;
            }

			if (StringUtils.isEmpty(platformId)) {
				LOGGER.logError(sellerNick, "-", "平台ID为空");
				return OpenResult.PLATFORM_ID_IS_NULL;
			}

			LOGGER.logInfo(sellerNick, "开始处理新用户", "-" + openUserBo);
			if (openUserCommonConfig.getExcludeLevelLists().contains(openUserBo.getVipFlag())) {
				return OpenResult.PROMOTION_ACTIVITY_USER;
			}
			HashOperations<String, String, String> op = getStringRedisTemplate().opsForHash();

			Long userRecentlyTradeCount = 0L;
			if (BooleanUtils.isTrue(newUserPlatformHandleService.isNeedPullData(businessId, platformId, appName))) {
				userRecentlyTradeCount = getSaveDataTotalResult(sellerNick, openUserBo.getSellerId(), topSession, openUserBo.getSupplierId(), platformId, appName);
				LOGGER.logInfo(sellerNick, "用户要拉取的数据量", userRecentlyTradeCount + "");
				if (null == userRecentlyTradeCount) {
					return OpenResult.SOLD_GET_EXCEPTION;
				}
			}
			UserProductInfoBusinessExt userExtInfo = userProductionInfoExtRepository.querySingleBySellerId(openUserBo.getSellerId(), platformId, appName, businessId);
			LOGGER.logInfo(sellerNick, "-", "用户已经离网或者拉单失败，重新开通这个用户");
			ReOpenSaveDataDTO reOpenSaveDataDTO = new ReOpenSaveDataDTO();
			reOpenSaveDataDTO.setSellerId(openUserBo.getSellerId());
			reOpenSaveDataDTO.setSellerNick(sellerNick);
			reOpenSaveDataDTO.setCorpId(openUserBo.getCorpId());
			reOpenSaveDataDTO.setSupplierId(openUserBo.getSupplierId());
			reOpenSaveDataDTO.setTopSession(topSession);
			reOpenSaveDataDTO.setStoreId(platformId);
			reOpenSaveDataDTO.setUserRecentlyTradeCount(userRecentlyTradeCount);
			reOpenSaveDataDTO.setAppName(appName);
			reOpenSaveDataDTO.setLastAuthDeadLine(openUserBo.getLastAuthDeadLine());
			reOpenSaveDataDTO.setLastOrderCycleEnd(openUserBo.getLastOrderCycleEnd());
            reOpenSaveDataDTO.setOpenType(ayBusinessOpenUserLog.getType());
			if (isDistributeUser) {
				reOpenSaveDataDTO.setProducts(CommonAppConstants.APP_DISTRIBUTE);
			}

			if (userExtInfo != null) {
				return reOpenSaveData(reOpenSaveDataDTO, businessId, userExtInfo);
			} else {
				// 新的用户
				LOGGER.logInfo(sellerNick, "-", "新开通的用户");
				return newOpenSaveOrder(sellerNick, openUserBo, topSession, platformId, appName, op,
						userRecentlyTradeCount, businessId, isDistributeUser);
			}
		} catch (Exception e) {
			LOGGER.logError(sellerNick, "-", "开通异常 Catch Exception: " + e.getMessage(), e);
			return OpenResult.OPEN_USER_SAVE_ORDER_FAILURE;

		} finally {
			try {
				if(lock.isLocked()){
					lock.unlock();
				}
			} catch (Exception e) {
				LOGGER.logError(sellerNick, "-", e.getMessage(), e);
			}
		}
	}

	/**
	 * 获取用户最近几个月的业务数据量
	 * @param sellerNick
	 * @param sellerId
	 * @param topSession
	 * @param supplierId
	 * @param platformId
	 * @param appName
	 * @return
	 */
	abstract protected Long getSaveDataTotalResult(String sellerNick, String sellerId, String topSession, String supplierId, String platformId, String appName);

	/**
	 * 发送消息去拉数据
	 * @param sellerId
	 * @param sellerNick
	 * @param orderCount
	 * @param platformId
	 * @param businessId
	 * @param appName
	 * @return
	 */
	protected boolean send2PullDataQueue(String sellerId, String sellerNick, Long orderCount, String platformId, String businessId, String appName) {
		if(!newUserPlatformHandleService.isNeedPullData(businessId, platformId, appName)){
			return true;
		}
		ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead = new ComLoveRpcInnerprocessRequestHead();
		comLoveRpcInnerprocessRequestHead.setSellerNick(sellerNick);
		comLoveRpcInnerprocessRequestHead.setSellerId(sellerId);
		comLoveRpcInnerprocessRequestHead.setPlatformId(platformId);
		comLoveRpcInnerprocessRequestHead.setAppName(appName);
		PullDataRequestBody pullDataRequestBody = new PullDataRequestBody();
		PullDataRequestProto pullDataRequestProto = new PullDataRequestProto();
		pullDataRequestProto.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead);
		pullDataRequestProto.setPullDataRequestBody(pullDataRequestBody);

		String messageId = null;
		LOGGER.logInfo(sellerNick, "", "发送itemlist");
		Pair<String, String> target = openUserDispatcherConfig.getTargetTopicAndTag(businessId, platformId);

		String topic = target.getLeft();
		String tag = target.getRight();
        if (openUserCommonConfig.getPretestUsers().contains(sellerNick)) {
            topic = target.getLeft() + PretestTopicConstant.PRETEST_TOPIC_SUFFIX;
        }

		messageId = rocketMqQueueHelper.push(topic, tag, pullDataRequestProto, defaultMQProducer);
		LOGGER.logInfo(sellerNick, "新开户打标", "成功开通用户: " + messageId);

		return StringUtils.isNotEmpty(messageId);
	}

    protected OpenResult newOpenSaveOrder(String sellerNick, OpenUserBo userInfo, String topSession, String platformId,
        String appName, HashOperations<String, String, String> op, Long userRecentlyTradeCount, String businessId,
        boolean isDistributeUser) {
		// 新开户用户

		String sellerId = userInfo.getSellerId();

		Integer intDbNum = getDBIdStep(sellerNick, userRecentlyTradeCount, null, businessId);

		if (intDbNum > openUserCommonConfig.getMaxAutoDbNum()) {
			LOGGER.logInfo(sellerNick, "-", "找不到可用的存单数据库,开通存单失败");
			return OpenResult.NOT_FOUND_DB;
		}
		String rdsName = this.getRdsName(sellerNick, intDbNum, platformId, appName, businessId);
		if (null == rdsName) {
			LOGGER.logInfo(sellerNick, "-", "找不到可用的rds推送库,开通存单失败");
			return OpenResult.NOT_FOUND_RDS;
		}

		//开通mc推送和rds推送
		boolean resultSubscribe = openUserDataPush(sellerNick, sellerId, rdsName, topSession, platformId, appName, businessId);
		if (!resultSubscribe) {
			LOGGER.logInfo(sellerNick, "-", "开通用户推送失败");
			return OpenResult.MESSAGE_PUSH_OPEN_FAILURE;
		}

        createTradeExtInfoAfterOpenSuccess(userInfo, op, intDbNum, userRecentlyTradeCount.intValue(), businessId,
            isDistributeUser, platformId, appName);

		updateUserProductInfoDB(sellerNick, intDbNum, platformId, appName);
		newUserPlatformHandleService.send2PullDataQueue(sellerId, sellerNick, null, businessId, platformId, appName);
		return OpenResult.OPEN_SUCCESS;

	}

	protected OpenResult reOpenSaveData(ReOpenSaveDataDTO reOpenSaveDataDTO, String businessId, UserProductInfoBusinessExt userProductInfoBusinessExt) {
		String sellerId = reOpenSaveDataDTO.getSellerId();
		String sellerNick = reOpenSaveDataDTO.getSellerNick();
		String platformId = reOpenSaveDataDTO.getStoreId();
		String topSession = reOpenSaveDataDTO.getTopSession();
		Long userRecentlyTradeCount = reOpenSaveDataDTO.getUserRecentlyTradeCount();
		String appName = reOpenSaveDataDTO.getAppName();
		String supplierId = reOpenSaveDataDTO.getSupplierId();
		String corpId = reOpenSaveDataDTO.getCorpId();
        Integer openType = reOpenSaveDataDTO.getOpenType();
        if (StringUtils.isEmpty(topSession)) {
			return OpenResult.TOP_SESSION_IS_NULL;
		}
		// 已经开过户的用户，需要判断用户名是否变更
		String dbSellerNick = userProductInfoBusinessExt.getSellerNick();
		String dbProducts = userProductInfoBusinessExt.getProducts();

		// 判断库中是否已经存在该业务
        boolean isExitProducts = StringUtils.isNotEmpty(dbProducts)
            && Arrays.stream(dbProducts.split(",")).anyMatch(s -> s.equals(reOpenSaveDataDTO.getProducts()));
        boolean isNewProducts =
            StringUtils.isEmpty(dbProducts) && StringUtils.isNotEmpty(reOpenSaveDataDTO.getProducts())
                || isExitProducts;
		if (!dbSellerNick.equals(sellerNick) || isNewProducts) {
			userProductInfoBusinessExt.setSellerNick(sellerNick);
			userProductInfoBusinessExt.setTopStatus(UserProductInfoBusinessExt.DB_DONE.toString());
            if (!newUserPlatformHandleService.isNeedPullData(businessId, platformId, appName)) {
                userProductInfoBusinessExt.setPullStatus(UserProductInfoBusinessExt.DB_DONE);
            } else {
                userProductInfoBusinessExt.setPullStatus(UserProductInfoBusinessExt.DB_WAIT);
            }

			userProductInfoBusinessExt.setTopTradeCount(userRecentlyTradeCount.intValue());
			userProductInfoBusinessExt.setGmtModified(LocalDateTime.now());

			String products = null;
			if (!StringUtils.isEmpty(dbProducts)) {
				if (!isExitProducts) {
					products = dbProducts + "," + reOpenSaveDataDTO.getProducts();
				}
			} else {
				products = reOpenSaveDataDTO.getProducts();
			}
			userProductInfoBusinessExt.setProducts(products);
			userProductionInfoExtRepository.update(userProductInfoBusinessExt, businessId);

			int intDbNum = userProductInfoBusinessExt.getDbId();
			updateUserProductInfoDB(sellerNick, intDbNum, platformId, appName);

			String rdsName = getRdsName(sellerNick, intDbNum, platformId, appName, businessId);
			openUserDataPush(sellerNick, sellerId, rdsName, topSession, platformId, appName, businessId);
			send2PullDataQueue(sellerId, sellerNick, userRecentlyTradeCount, platformId, businessId, appName);

			if (isNewProducts) {
                LOGGER.logInfo(sellerNick, dbSellerNick,
                    "用户开通新业务, 重新拉商品 dbProducts为: " + dbProducts + " > nowProducts为: " + products);
			} else {
				LOGGER.logInfo(sellerNick, dbSellerNick, "用户名已变更, 更新EXT用户昵称, 重新拉单 " + dbSellerNick + " > " + sellerNick);
			}

			newUserPlatformHandleService.send2PullDataQueue(sellerId, sellerNick, openType, businessId, platformId, appName);
			LOGGER.logInfo(sellerNick, dbSellerNick, "用户名已变更, 更新EXT用户昵称, 重新拉单 " + dbSellerNick + " > " + sellerNick);
			return OpenResult.SPECIAL_USER_HANDLE;
		}
		if (!Objects.isNull(userProductInfoBusinessExt)
				&& (UserProductInfoBusinessExt.DB_DONE.toString().equals(userProductInfoBusinessExt.getTopStatus()) && !UserProductInfoBusinessExt.DB_FAILED.equals(userProductInfoBusinessExt.getPullStatus())
				&& !UserProductInfoBusinessExt.API_STATUS_FAILED_BY_SESSION.equals(userProductInfoBusinessExt.getApiStatus()))
		) {
			LOGGER.logInfo(sellerNick, "-", "在ext中已经开通了,不需要重复开一遍");
			return OpenResult.OPEN_SUCCESS;
		}

		LOGGER.logInfo(sellerNick, "-", "<用户要拉取的数据量:" + userRecentlyTradeCount + ">");


		String topStatus = userProductInfoBusinessExt.getTopStatus();
		Integer intDbNum = userProductInfoBusinessExt.getDbId();
		if (UserProductInfoBusinessExt.DB_FAILED.toString().equals(topStatus)) {
			intDbNum = getDBIdStep(sellerNick, userRecentlyTradeCount, null, businessId);
			LOGGER.logInfo(sellerNick, "", "已离网的用户重新分配dbId=" + userProductInfoBusinessExt.getDbId() + " > " + intDbNum);
			if (intDbNum > openUserCommonConfig.getMaxAutoDbNum()) {
				LOGGER.logInfo(sellerNick, "-", "找不到可用的存单数据库,开通存单失败");
				return OpenResult.NOT_FOUND_DB;
			}
		} else {
			LOGGER.logInfo(sellerNick, "",
					"未离网的用户继使用原dbId=" + userProductInfoBusinessExt.getDbId() + " topStatus=" + topStatus);
		}

		String rdsName = getRdsName(sellerNick, intDbNum, platformId, appName, businessId);
		if (null == rdsName) {
			LOGGER.logInfo(sellerNick, "-", "找不到可用的rds推送库,开通存单失败");
			return OpenResult.NOT_FOUND_RDS;
		}

		//开通推送
		boolean resultSubscribe = openUserDataPush(sellerNick, userProductInfoBusinessExt.getSellerId(), rdsName, topSession, platformId, appName, businessId);
		if (!resultSubscribe) {
			LOGGER.logInfo(sellerNick, "-", "开通用户推送失败");
			return OpenResult.MESSAGE_PUSH_OPEN_FAILURE;
		}
		updateUserProductInfoDB(sellerNick, intDbNum, platformId, appName);
		if (CommonPlatformConstants.PLATFORM_TAO.equals(platformId) && CommonAppConstants.APP_TRADE_SUPPLIER.equals(appName)) {
			userProductInfoBusinessExt.setMemberId(supplierId);
		}
		updateTradeExtAfterOpenSuccess(userProductInfoBusinessExt, intDbNum, userRecentlyTradeCount, topStatus, businessId, platformId, appName);
		newUserPlatformHandleService.send2PullDataQueue(userProductInfoBusinessExt.getSellerId(), sellerNick, openType,
            businessId, userProductInfoBusinessExt.getStoreId(), appName);
		return OpenResult.RE_OPEN_SUCCESS;
	}

	/**
	 * 插入ext表 + 更新redis
	 */
    public void createTradeExtInfoAfterOpenSuccess(OpenUserBo userInfo, HashOperations<String, String, String> op,
        int dbNum, int topTradeCount, String businessId, boolean isDistributeUser, String storeId, String appName) {
		UserProductInfoBusinessExt userProductInfoBusinessExt = new UserProductInfoBusinessExt();
		userProductInfoBusinessExt.setSellerId(userInfo.getSellerId());
		userProductInfoBusinessExt.setSellerNick(StringUtils.isEmpty(userInfo.getSellerNick()) ? userInfo.getMallName() : userInfo.getSellerNick());
		userProductInfoBusinessExt.setStoreId(storeId);
		userProductInfoBusinessExt.setCorpId(userInfo.getCorpId() == null ? "" : userInfo.getCorpId());
		userProductInfoBusinessExt.setDbStatus(UserProductInfoBusinessExt.DB_DONE);
		userProductInfoBusinessExt.setGmtCreate(LocalDateTime.now());
		userProductInfoBusinessExt.setGmtModified(LocalDateTime.now());
		userProductInfoBusinessExt.setTopTradeCount(topTradeCount);
		userProductInfoBusinessExt.setApiStatus(UserProductInfoBusinessExt.DB_DONE);
		userProductInfoBusinessExt.setTopStatus(UserProductInfoBusinessExt.DB_DONE.toString());
		userProductInfoBusinessExt.setDbId(dbNum);
		Integer randomSearchDbId = getSearchDbId(dbNum);
        if (!newUserPlatformHandleService.isNeedPullData(businessId, storeId, appName)) {
            userProductInfoBusinessExt.setPullStatus(UserProductInfoBusinessExt.DB_DONE);
        } else {
            userProductInfoBusinessExt.setPullStatus(UserProductInfoBusinessExt.DB_WAIT);
        }

		userProductInfoBusinessExt.setDowngradeTag(openUserCommonConfig.getDefaultDowngradeTag());
		userProductInfoBusinessExt.setAppName(appName);
		// 如果appName为tradeSupplier，那么memberId表示供货商id，否则为1688、淘工厂字段
		if (CommonAppConstants.APP_TRADE_SUPPLIER.equals(appName)) {
			userProductInfoBusinessExt.setMemberId(userInfo.getSupplierId());
		} else {
			userProductInfoBusinessExt.setMemberId(userInfo.getMemberId());
		}

		if (isDistributeUser) {
			userProductInfoBusinessExt.setProducts(CommonAppConstants.APP_DISTRIBUTE);
		}

		userProductionInfoExtRepository.insert(userProductInfoBusinessExt, businessId);
		///存redis
		try {
			String key = URLEncoder.encode(userInfo.getSellerNick(), "utf-8");
			op.put(key, "corpId", userInfo.getSellerId());
			this.handleRedisOpenUserInfo(userInfo.getSellerNick(), userInfo.getSellerId(), Integer.toString(dbNum), op, randomSearchDbId, storeId, appName, businessId);
		} catch (Exception e) {
			LOGGER.logError("-", "-", "打标失败", e);
		}
	}

	/**
	 * 重新开户成功后, 更新ext表 + 更新redis
	 * @param userProductInfoBusinessExt
	 * @param dbNum
	 * @param topTradeCount
	 * @param oldTopStatus
	 * @param businessId
	 * @param platformId
	 * @param appName
	 */
	protected void updateTradeExtAfterOpenSuccess(UserProductInfoBusinessExt userProductInfoBusinessExt, int dbNum,
												  Long topTradeCount, String oldTopStatus, String businessId, String platformId, String appName) {
		Integer randomSearchDbId = getSearchDbId(dbNum);
		userProductInfoBusinessExt.setDbStatus(UserProductInfoBusinessExt.DB_DONE);
		if (null != topTradeCount) {
			userProductInfoBusinessExt.setTopTradeCount(topTradeCount.intValue());
		}
		userProductInfoBusinessExt.setApiStatus(UserProductInfoBusinessExt.DB_DONE);
		userProductInfoBusinessExt.setTopStatus(UserProductInfoBusinessExt.DB_DONE.toString());
        if (!newUserPlatformHandleService.isNeedPullData(businessId, platformId, appName)) {
            userProductInfoBusinessExt.setPullStatus(UserProductInfoBusinessExt.DB_DONE);
        } else {
            userProductInfoBusinessExt.setPullStatus(UserProductInfoBusinessExt.DB_WAIT);
        }

		userProductInfoBusinessExt.setDbId(dbNum);
		userProductInfoBusinessExt.setGmtModified(LocalDateTime.now());
		userProductionInfoExtRepository.update(userProductInfoBusinessExt, businessId);
		try {
			HashOperations<String, String, String> op = getStringRedisTemplate().opsForHash();
			// 处理redis
			handleRedisOpenUserInfo(userProductInfoBusinessExt.getSellerNick(), userProductInfoBusinessExt.getSellerId(), Integer.toString(dbNum), op,
					randomSearchDbId, platformId, appName, businessId);
		} catch (Exception e) {
			LOGGER.logError("-", "-", "打标失败", e);
		}
	}

	/**
	 * 处理redis开户信息
	 * @param nick
	 * @param sellerId
	 * @param dbNum
	 * @param op
	 * @param randomSearchDbId
	 * @param platformId
	 * @param appName
	 * @param businessId
	 * @throws UnsupportedEncodingException
	 */
	protected void handleRedisOpenUserInfo(String nick, String sellerId, String dbNum, HashOperations<String, String, String> op,
										   Integer randomSearchDbId, String platformId, String appName, String businessId) throws UnsupportedEncodingException {
		String key = userManageRedisRepository.initCollection(nick, sellerId, platformId, appName);
		if (null != dbNum) {
			op.put(key, "dbId", dbNum);
			op.put(key, "searchDbId", String.valueOf(randomSearchDbId));
		}
//		String extKey = userManageRedisRepository.initExtCollection(nick, sellerId, platformId, appName);
//		HashOperations<String, String, String> extOp = stringTradeRedisTemplate.opsForHash();
//		String value = extOp.get(extKey, "business_ids");
//		boolean isExist = false;
//		if (StringUtils.isNotEmpty(value)) {
//			for (String element : value.split(SPLIT_STR)) {
//				if (businessId.equals(element)) {
//					isExist = true;
//					break;
//				}
//			}
//		}
//		if (isExist) {
//			LOGGER.logInfo(nick, extKey, "redis中该用户已开通此功能，不做处理");
//		} else {
//			value = value + SPLIT_STR + businessId;
//			extOp.put(extKey, "business_ids", value);
//			LOGGER.logInfo(nick, extKey, "已成功更新redis");
//		}


	}

	/**
	 * 获取搜索dbId
	 * @param dbNum
	 * @return
	 */
	protected Integer getSearchDbId(Integer dbNum) {
		if (openUserCommonConfig.getRandomSearchDb()) {
			double randomNum = new Random().nextDouble() * 100;
			return randomNum < 50 ? dbNum : dbNum + openUserCommonConfig.getMaxAutoDbNum();
		} else {
			return dbNum;
		}
	}

	/**
	 * 计算开通用户应在的推送rds数据库
	 * @param sellerNick
	 * @param intDbNum
	 * @param platform
	 * @param appName
	 * @param businessId
	 * @return
	 */
	public String getRdsName(String sellerNick, int intDbNum, String platform, String appName, String businessId) {
        String useRdsRule = newUserPlatformHandleService.getRdsRule(businessId, platform, appName);
		String[] rdsGroups = useRdsRule.split(SPLIT_STR);
		String rdsName = null;
		if(rdsGroups.length <= 1){
			rdsName = useRdsRule;
		}else{
			for (String rdsGroup : rdsGroups) {
				String[] singleRdsRule = rdsGroup.split(":");
				if (singleRdsRule[1].contains(String.valueOf(intDbNum))) {
					rdsName = singleRdsRule[0];
					break;
				}
			}
		}
		LOGGER.logInfo(sellerNick, "-", "即将开通的位置," +
				"用户所开通的db_id<" + intDbNum + ">,用户所开通的rds" + rdsName + "自动开通的计算规则" + useRdsRule);
		return rdsName;
	}

	/**
	 * 更新user_productinfo_xxx表
	 * @param sellerNick
	 * @param intDbNum
	 * @param platformId
	 * @param appName
	 */
	protected void updateUserProductInfoDB(String sellerNick, int intDbNum, String platformId, String appName) {
		try {
			userRepository.updateSaveDataDB(sellerNick, Integer.toString(intDbNum), platformId, appName);
		} catch (Exception e) {
			LOGGER.logError(sellerNick, "", "更新UserProductinfo失败, 忽略此异常: " + e.getMessage(), e);
		}
	}

	/**
	 * 分配开户数据库
	 */
	protected Integer getDBIdStep(String nick, Long userRecentlyTradeCount, Integer excludeDb, String businessId) {
		//设置默认db值
		LOGGER.logInfo(nick, "", "排除的数据库: " + openUserCommonConfig.getExcludeDbId() + ",  原有不可开通的数据库:" + excludeDb);

		List<Integer> excludeDbIdArray = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(openUserCommonConfig.getExcludeDbId())) {
			excludeDbIdArray.addAll(openUserCommonConfig.getExcludeDbId());
		}
		if (excludeDb != null) {
			excludeDbIdArray.add(excludeDb);
		}
		LOGGER.logInfo(nick, "", "最后不可以开户的数据库: " + excludeDbIdArray);
		//循环出存单数据库组中，可以插入的数据库
		Map<Integer, Long> dbIds = Maps.newHashMap();
		for (int i = 1; i <= openUserCommonConfig.getMaxAutoDbNum(); i++) {
			if (excludeDbIdArray.contains(i)) {
				continue;
			}
			dbIds.put(i, getSingleDbidValidOrderSum(i, businessId));
		}

		List<Map.Entry<Integer, Long>> dbIdsWithSpace = dbIds.entrySet().stream()
				.filter(e -> e.getValue() < openUserCommonConfig.getMaxDbOrderSum()).collect(Collectors.toList());
		if (CollectionUtils.isNotEmpty(dbIdsWithSpace)) {
			LOGGER.logInfo(nick, "", "空间充足的可开户数据库: " + dbIdsWithSpace + ", maxDbOrderSum=" + openUserCommonConfig.getMaxDbOrderSum());
			Map.Entry<Integer, Long> dbId = dbIdsWithSpace
					.get(ThreadLocalRandom.current().nextInt(dbIdsWithSpace.size()));
			LOGGER.logInfo(nick, "", "随机选择一个数据库及数据量: " + dbId + "  userRecentlyTradeCount=" + userRecentlyTradeCount);
			return dbId.getKey();
		} else {
			LOGGER.logInfo(nick, "", "所有可开户的数据库空间都不足: " + dbIds + ", maxDbOrderSum=" + openUserCommonConfig.getMaxDbOrderSum());
			Map.Entry<Integer, Long> dbId = dbIds.entrySet().stream().min(Comparator.comparingLong(Map.Entry::getValue))
					.orElseGet(null);
			LOGGER.logInfo(nick, "", "选择数据量最少的数据库及数据量: " + dbId + "  userRecentlyTradeCount=" + userRecentlyTradeCount);
			return dbId.getKey();
		}
	}

	public Long getSingleDbidValidOrderSum(Integer dbId, String businessId) {
		try {
			List<UserProductInfoBusinessExt> allSum = dbOrderCountCache.get(CACHE_ID, ()->userProductionInfoExtRepository.getDbidValidOrderSum(businessId));
			Integer sumTradeCount = allSum.stream().filter(e -> e != null && dbId.equals(e.getDbId())).findFirst()
					.map(u -> u.getTopTradeCount() == null ? 0 : u.getTopTradeCount()).orElse(null);
			if (null == sumTradeCount) {
				return 0L;
			}
			return sumTradeCount.longValue();
		} catch (ExecutionException e) {
			LOGGER.logError(e.getMessage(), e);
			return 0L;
		}
	}

	/**
	 * 开通用户的 tmc,rds 推送
	 */
	private boolean openUserDataPush(String sellerNick, String sellerId, String rdsName, String topSession, String platformId, String appName, String businessId) {
		if (StringUtils.isEmpty(topSession)) {
			LOGGER.logError(sellerNick, "", "开通topSession为空 " + topSession);
			return false;
		}
		boolean isMcSuccess = false;
		boolean isRdsSuccess = false;
		MessageRequest request = new MessageRequest();
		request.setSellerId(sellerId);
		request.setSellerNick(sellerNick);
		request.setRdsName(rdsName);
		request.setTopSession(topSession);
		try {
            if (BooleanUtils.isTrue(newUserPlatformHandleService.isNeedSubscribeMc(businessId, platformId, appName))) {
				MessageResponse response = messageApiPlatformHandleService.subscribeUserMessageService(request, platformId, appName);
				if (response != null && response.isSuccess()) {
					isMcSuccess = true;
				} else {
					LOGGER.logError(request.getSellerNick(), "-", "tmc开通异常 " + JSON.toJSONString(response));
				}
			} else {
				// 不需要开通mc消息的平台直接标记为true
				isMcSuccess = true;
			}
		} catch (Exception e) {
			LOGGER.logError(request.getSellerNick(), "", "tmc开通异常 " + e.getMessage(), e);
		}
		if (CommonBusinessConstants.BUSINESS_ITEM.equals(businessId)
				&& CommonPlatformConstants.PLATFORM_TAO.equals(platformId)) {
			LOGGER.logInfo(sellerNick, "-", "淘宝存商品用户不需要开通rds");
			return true;
		}
		try {
            if (BooleanUtils.isTrue(newUserPlatformHandleService.isNeedSubscribeRds(businessId, platformId, appName))) {
				MessageResponse response = messageApiPlatformHandleService.subscribeUserDbService(request, platformId, appName);
				if (response != null && response.isSuccess()) {
					isRdsSuccess = true;
				} else {
					LOGGER.logError(request.getSellerNick(), "-", "rds开通异常 " + JSON.toJSONString(response));
				}
			} else {
				// 不需要开通RDS消息的平台直接标记为true
				isRdsSuccess = true;
			}
		} catch (Exception e) {
			LOGGER.logError(request.getSellerNick(), "", "rds开通异常 " + e.getMessage(), e);
		}
		LOGGER.logInfo(request.getSellerNick(), "-", "开通执行结束");
		return isMcSuccess && isRdsSuccess;
	}

	@Override
	public boolean closeSaveData(String sellerNick, String sellerId, String platformId, String businessId, String appName) {
		LOGGER.logInfo(sellerNick, "-", "离网");
		UserProductInfoBusinessExt userProductInfoBusinessExt = userProductionInfoExtRepository.querySingleBySellerId(sellerId, platformId, appName, businessId);

		if (!Objects.isNull(userProductInfoBusinessExt)) {
			Integer count = userProductionInfoExtRepository.queryCountBySellerId(userProductInfoBusinessExt.getSellerId(), platformId, appName, businessId);
			if (count > 1) {
				//库中存在SellerId相同的用户，将状态改为-102
				userProductInfoBusinessExt.setTopStatus(UserProductInfoBusinessExt.DB_FAILED_BY_WEB.toString());
				LOGGER.logInfo(sellerNick, sellerId, "找到重复的sellerId用户");
			} else {
				//将top_status设置为不可用状态，代表着用户被移除了
				userProductInfoBusinessExt.setTopStatus(UserProductInfoBusinessExt.DB_FAILED.toString());
			}
			userProductInfoBusinessExt.setGmtModified(LocalDateTime.now());
			userProductionInfoExtRepository.update(userProductInfoBusinessExt, businessId);
		} else {
			LOGGER.logError(sellerNick, sellerId, "在数据库中没有查到这个用户,没有办法进行数据迁移操作");
			return false;
		}

		//关闭rds推送 + 迁移数据
		MessageRequest request = new MessageRequest();
		request.setSellerNick(sellerNick);
		request.setSellerId(sellerId);
		String topSession = getTopSession(sellerNick, userProductInfoBusinessExt.getStoreId(), appName);
		request.setTopSession(topSession);
		boolean resultCancelSubscribe = closeUserDataPush(request, businessId, userProductInfoBusinessExt.getStoreId(), appName);
		if (!resultCancelSubscribe) {
			LOGGER.logInfo(sellerNick, "-", "取消平台数据推送异常");
			return false;
		}
		return true;
	}

	@Override
	public boolean prepareSaveData(String businessId, SaveDataDTO saveDataDTO) throws Exception {
		String platform = saveDataDTO.getPlatform();
		String appName = saveDataDTO.getServiceName();
		String sellerNick = saveDataDTO.getSellerNick();
		String sellerId = saveDataDTO.getSellerId();
		String platformAndAppName = platform + ":" + appName;

		if (BooleanUtils.isTrue(rateLimitHelper.filterPrepareRequest(saveDataDTO))) {
			LOGGER.logInfo(sellerNick, platformAndAppName, "重复请求，不进行处理");
			return false;
		}

		boolean isDistributeUser = distributeDataHandleService.checkDistributeUser(sellerId, platform, appName);
		if (BooleanUtils.isFalse(checkSaveOrderCondition(saveDataDTO, isDistributeUser))) {
			LOGGER.logInfo(sellerNick, platformAndAppName, "不满足开户条件，不进行处理");
			// 删除缓存, 防止用户信息刷新后无法正常开户
            rateLimitHelper.clearPullDataRateFilter(saveDataDTO);
            rateLimitHelper.clearPrepareRequestFilter(saveDataDTO);
			return false;
		}

		UserProductInfoBusinessExt userInfo = userProductionInfoExtRepository.querySingleBySellerId(sellerId, platform, appName, businessId);

		if (userInfo == null || StringUtils.isEmpty(userInfo.getTopStatus())) {
			LOGGER.logInfo(sellerNick, platformAndAppName, "新用户");
			return waitOpen(saveDataDTO, AyBusinessOpenUser.OPEN_LOG_TYPE_NEW_OPEN);

		} else if (StringUtils.isEmpty(userInfo.getTopStatus())) {
			LOGGER.logInfo(sellerNick, platformAndAppName,"已开通，但状态异常"+userInfo.getTopStatus());
			return false;
		} else if (UserProductInfoBusinessExt.DB_DONE.toString().equals(userInfo.getTopStatus())
				&& UserProductInfoBusinessExt.DB_DONE.equals(userInfo.getApiStatus())
				&& UserProductInfoBusinessExt.DB_FAILED.equals(userInfo.getPullStatus())) {
			if (newUserPlatformHandleService.checkTokenAndSend2PullDataQueue(saveDataDTO, userInfo, platform, appName)) {
				LOGGER.logInfo(sellerNick, platformAndAppName, "用户已经开通, 但需要重新拉数据");
				return true;
			}
		} else if (UserProductInfoBusinessExt.DB_FAILED.toString().equals(userInfo.getTopStatus())) {
			LOGGER.logInfo(sellerNick, platformAndAppName,"离网用户重新开通");
			return waitOpen(saveDataDTO, AyBusinessOpenUser.OPEN_LOG_TYPE_RE_OPEN);
		} else if (UserProductInfoBusinessExt.checkApiStatus(userInfo.getApiStatus())) {
			LOGGER.logInfo(sellerNick, platformAndAppName,"重新授权用户重新开通");
			return waitOpen(saveDataDTO, AyBusinessOpenUser.OPEN_LOG_TYPE_RE_OPEN);
		} else {
            NewUserPlatformHandleDTO newUserPlatformHandleDTO = new NewUserPlatformHandleDTO();
            newUserPlatformHandleDTO.setIsDistributeUser(isDistributeUser);
            newUserPlatformHandleDTO
                .setTaobaoDistributeUserFunction(() -> waitOpen(saveDataDTO, AyBusinessOpenUser.OPEN_LOG_TYPE_NEW_APP));
            // 用户已开户，且状态正常，需要做一些根据平台定制的特殊逻辑
            newUserPlatformHandleService.handlePrepareOpenOnNormalCondition(saveDataDTO, userInfo,
                newUserPlatformHandleDTO, platform, appName);
        }
		LOGGER.logInfo(sellerNick, platformAndAppName,"用户存单已经开通");
		return true;
	}

	/**
	 * 设置为待开通状态
	 * 下一步 cn.loveapp.orders.newuser.scheduler.task.open.ScanWaitOpenUser#run
	 * @param saveOrderDTO
	 * @param userType
	 * @return
	 */
	protected boolean waitOpen(SaveDataDTO saveOrderDTO, Integer userType){
		String businessId = saveOrderDTO.getBusinessId();
		String platform = saveOrderDTO.getPlatform();
		String appName = saveOrderDTO.getServiceName();
		String sellerNick = saveOrderDTO.getSellerNick();
		String sellerId = saveOrderDTO.getSellerId();
		String platformAndAppName = platform + ":" + appName;

		// 1。查询用户主表信息
		AyBusinessOpenUser ayBusinessOpenUser =
				ayBusinessOpenUserRepository.queryBySellerIdAndPlatId(sellerId, platform, appName, businessId);

		// 检验是否新应用开通，若存在新应用开通，则从拉数据
        if (AyBusinessOpenUser.OPEN_LOG_TYPE_NEW_APP != userType) {
            if (ayBusinessOpenUser != null) {
                if (ayBusinessOpenUser.getStatus().equals(AyBusinessOpenUser.WAIT_OPEN)
                    || ayBusinessOpenUser.getStatus().equals(AyBusinessOpenUser.OPENING)) {
                    LOGGER.logInfo(sellerNick, platformAndAppName, "该用户已经处于待开通或开通中状态，退出");
                    return false;
                }
            }
        }

		AyBusinessOpenUserLog userLogObject = getOpenUserLog(sellerNick, userType, ayBusinessOpenUser, appName);
		ayBusinessOpenUserLogRepository.insert(userLogObject, businessId);
		Integer userLogId = userLogObject.getId();

		if (ayBusinessOpenUser == null || StringUtils.isEmpty(ayBusinessOpenUser.getSellerNick())){
			AyBusinessOpenUser openUserInsert = new AyBusinessOpenUser();
			openUserInsert.setSellerId(sellerId);
			openUserInsert.setSellerNick(sellerNick);
			openUserInsert.setStatus(AyBusinessOpenUser.WAIT_OPEN);
			openUserInsert.setPlatId(platform);
			openUserInsert.setRetryCount(0);
			openUserInsert.setRuleId(AyBusinessOpenUser.RULE_DEFAULT);
			openUserInsert.setGmtCreate(LocalDateTime.now());
			openUserInsert.setGmtModify(openUserInsert.getGmtCreate());
			openUserInsert.setUserLogId(userLogId);
			openUserInsert.setAppName(StringUtils.trimToEmpty(appName));
			ayBusinessOpenUserRepository.insert(openUserInsert, businessId);

			int userId = openUserInsert.getId();
			//需要更新下log表的openUserId
			userLogObject.setOpenUserId(userId);
			ayBusinessOpenUserLogRepository.update(userLogObject, businessId);
		}else {
			ayBusinessOpenUser.setStatus(AyBusinessOpenUser.WAIT_OPEN);
			ayBusinessOpenUser.setRetryCount(0);
			ayBusinessOpenUser.setUserLogId(userLogId);
			ayBusinessOpenUser.setGmtModify(LocalDateTime.now());
			ayBusinessOpenUserRepository.updateByStatus(ayBusinessOpenUser, businessId);
		}
		LOGGER.logInfo(sellerNick, platformAndAppName, "提交成功");
		return true;
	}

	/**
	 * 生成AyBusinessOpenUserLog对象
	 *
	 * @param sellerNick
	 * @param userType
	 * @param ayBusinessOpenUser
	 * @param appName
	 * @return
	 */
	private AyBusinessOpenUserLog getOpenUserLog(String sellerNick, Integer userType, AyBusinessOpenUser ayBusinessOpenUser,String appName){
		AyBusinessOpenUserLog openUserLog = new AyBusinessOpenUserLog();
		openUserLog.setSellerNick(sellerNick);
		openUserLog.setStatus(AyBusinessOpenUser.WAIT_OPEN);
		openUserLog.setType(userType);
		openUserLog.setRuleId(AyBusinessOpenUser.RULE_DEFAULT);
		openUserLog.setAppName(appName);
		openUserLog.setGmtCreate(LocalDateTime.now());
		if (ayBusinessOpenUser != null && ayBusinessOpenUser.getId() != null){
			openUserLog.setOpenUserId(ayBusinessOpenUser.getId());
		}else {
			openUserLog.setOpenUserId(-1);
		}
		return openUserLog;
	}

	protected boolean checkSaveOrderCondition(SaveDataDTO saveDataDTO, boolean isDistributeUser) {
		//1判断是否为高级版 >0  < 4 vipflag
		String platform = saveDataDTO.getPlatform();
		String appName = saveDataDTO.getServiceName();
		String sellerNick = saveDataDTO.getSellerNick();
		String sellerId = saveDataDTO.getSellerId();
		String platformAndAppName = platform + ":" + appName;

		UserInfoResponse userInfoResponse = userCenterService.getUserInfo(sellerNick, null, appName, platform);

		if (userInfoResponse == null || userInfoResponse.getVipflag() == null) {
			LOGGER.logInfo(sellerNick, platformAndAppName, "爱用商品没有该用户");
			return false;
		}

		boolean result = openUserCommonConfig.getAllowOpenLevelList().contains(userInfoResponse.getVipflag());
		if (!result) {
			String appPlatform = appName + ":" + platform;
            if (!openUserCommonConfig.getIgnoreVipAppNamePlatform().contains(appPlatform.toLowerCase())) {
                if (!CommonAppConstants.APP_DISTRIBUTE.equals(appName) && !isDistributeUser) {
                    LOGGER.logInfo(sellerNick, platformAndAppName,
                        "该用户不在开户级别内" + "  vipflag=" + userInfoResponse.getVipflag());
                    return false;
                } else {
					LOGGER.logInfo(sellerNick, platformAndAppName, "代发用户, 跳过vip检验");
				}
            }
		}
		return true;
	}

	private String getTopSession(String sellerNick, String platformId, String appName) {
		String topSession = null;
		OpenUserBo openUserBo;
		openUserBo = userService.getSellerInfoBySellerNick(sellerNick, platformId, appName);
		if (null != openUserBo) {
			topSession = openUserBo.getAccessToken();
		}
		if (StringUtils.isEmpty(topSession)) {
			LOGGER.logInfo(sellerNick, "-", "top_session 失效");
		}
		return topSession;
	}

	/**
	 * 取消用户数据推送（只关RDS）
	 * @param request
	 * @param businessId
	 * @param platformId
	 * @param appName
	 * @return
	 */
	protected boolean closeUserDataPush(MessageRequest request, String businessId, String platformId, String appName) {
        if (newUserPlatformHandleService.isNeedSubscribeRds(businessId, platformId, appName)) {
			if (!Objects.isNull(request)) {
				try {
					MessageResponse response = messageApiPlatformHandleService.cancelUserDbService(request, platformId, appName);
					if (response.getIsSuccess()) {
						return true;
					} else {
						LOGGER.logError(request.getSellerNick(), "-", "rds关闭异常 " + JSON.toJSONString(response));
					}
				} finally {
					LOGGER.logInfo(request.getSellerNick(), "-", "关闭订阅执行结束");
				}
			}
			return false;
		} else {
			return true;
		}
	}

	/**
	 * 获取业务对应的redisTemplate
	 * @return
	 */
	abstract protected StringRedisTemplate getStringRedisTemplate();

}
