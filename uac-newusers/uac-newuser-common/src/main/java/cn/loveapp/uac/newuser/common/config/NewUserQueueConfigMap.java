package cn.loveapp.uac.newuser.common.config;

import cn.loveapp.common.constant.CommonBusinessConstants;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/3/10 18:43
 * @Description: 新用户开户配置映射
 */
@Component
public class NewUserQueueConfigMap {

    private Map<String, BaseNewUserQueueConfig> baseNewUserQueueConfigMap;

    public NewUserQueueConfigMap(NewUserQueueConfig newUserQueueConfig) {
        baseNewUserQueueConfigMap = Maps.newHashMap();
        baseNewUserQueueConfigMap.put(CommonBusinessConstants.BUSINESS_ITEM, newUserQueueConfig);
    }

    public BaseNewUserQueueConfig getBaseNewUserQueueConfig(String businessId) {
        if (StringUtils.isEmpty(businessId)) {
            return null;
        }
        return baseNewUserQueueConfigMap.get(businessId);
    }
}
