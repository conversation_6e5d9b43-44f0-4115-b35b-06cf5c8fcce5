package cn.loveapp.uac.newuser.common.platform.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.taobao.TaobaoSDKService;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.request.MessageRequest;
import cn.loveapp.uac.common.response.MessageResponse;
import cn.loveapp.uac.newuser.common.platform.MessageApiPlatformHandleService;
import cn.loveapp.uac.common.code.ErrorCode;
import com.taobao.api.request.*;
import com.taobao.api.response.*;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;


/**
 * @program: orders-services-group
 * @description: TAOPlatformMessageServiceImpl
 * @author: Jason
 * @create: 2019-11-28 17:23
 **/
@Service
public class TaoMessageApiPlatformHandleServiceImpl implements MessageApiPlatformHandleService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(TaoMessageApiPlatformHandleServiceImpl.class);

	@Autowired
	private TaobaoSDKService taobaoSDKService;

	@Override
	public boolean isSubscribeUserMessageService(MessageRequest request, String platformId, String appName) {
		TmcUserGetRequest tmcUserPermitRequest = new TmcUserGetRequest();
		tmcUserPermitRequest.setNick(request.getSellerNick());
		tmcUserPermitRequest.setFields("user_nick,user_id,is_valid");
		tmcUserPermitRequest.setUserPlatform("tbUIC");
		TmcUserGetResponse tmcUserGetResponse = taobaoSDKService.execute(tmcUserPermitRequest, appName);
		if (null != tmcUserGetResponse) {
			return tmcUserGetResponse.isSuccess()
				&& tmcUserGetResponse.getTmcUser()!=null
				&& BooleanUtils.isTrue(tmcUserGetResponse.getTmcUser().getIsValid());
		}
		return false;
	}

	@Override
	public MessageResponse subscribeUserMessageService(MessageRequest request, String platformId, String appName) {
		MessageResponse response = new MessageResponse();
		TmcUserPermitRequest tmcUserPermitRequest = new TmcUserPermitRequest();
		TmcUserPermitResponse tmcUserPermitResponse = taobaoSDKService.execute(tmcUserPermitRequest, request.getTopSession(), appName);
		if (null != tmcUserPermitResponse) {
			BeanUtils.copyProperties(tmcUserPermitResponse,response);
			return response;
		}
		response.setErrorCode(ErrorCode.BaseCode.REQUEST_ERR.getCode().toString());
		return response;
	}

	@Override
	public MessageResponse cancelUserMesageService(@NotNull MessageRequest request, String platformId, String appName) {
		MessageResponse response = new MessageResponse();
//		TmcUserCancelRequest tmcUserCancelRequest = new TmcUserCancelRequest();
//		if (!StringUtils.isEmpty(request.getSellerNick())) {
//			tmcUserCancelRequest.setNick(request.getSellerNick());
//		}
//		TmcUserCancelResponse tmcUserCancelResponse = execute(tmcUserCancelRequest);
//		if (null != tmcUserCancelResponse) {
//			BeanUtils.copyProperties(response, tmcUserCancelResponse);
//			return response;
//		}
//		response.setErrorCode(ErrorCode.BaseCode.REQUEST_ERR.getCode().toString());
		response.setIsSuccess(true);
		return response;
	}

	@Override
	public boolean isSubscribeUserDbService(MessageRequest request, String platformId, String appName) {
		JushitaJdpUsersGetRequest jushitaJdpUsersGetRequest = new JushitaJdpUsersGetRequest();
		jushitaJdpUsersGetRequest.setUserId(Long.parseLong(request.getSellerId()));
		JushitaJdpUsersGetResponse jushitaJdpUsersGetResponse = taobaoSDKService.execute(jushitaJdpUsersGetRequest, appName);
		if (null != jushitaJdpUsersGetResponse) {
			return jushitaJdpUsersGetResponse.isSuccess()
				&& CollectionUtils.isNotEmpty(jushitaJdpUsersGetResponse.getUsers())
				&& Long.valueOf(1).equals(jushitaJdpUsersGetResponse.getUsers().get(0).getStatus());
		}
		return false;
	}

	@Override
	public MessageResponse subscribeUserDbService(@NotNull MessageRequest request, String platformId, String appName) {
		MessageResponse response = new MessageResponse();
		JushitaJdpUserAddRequest jushitaJdpUserAddRequest = new JushitaJdpUserAddRequest();
		if (!StringUtils.isEmpty(request.getRdsName())) {
			jushitaJdpUserAddRequest.setRdsName(request.getRdsName());
		}
		JushitaJdpUserAddResponse jushitaJdpUserAddResponse = taobaoSDKService.execute(jushitaJdpUserAddRequest, request.getTopSession(), appName);
		if (null != jushitaJdpUserAddResponse) {
			BeanUtils.copyProperties(jushitaJdpUserAddResponse,response);
			return response;
		}
		response.setErrorCode(ErrorCode.BaseCode.REQUEST_ERR.getCode().toString());
		return response;
	}

	@Override
	public MessageResponse cancelUserDbService(@NotNull MessageRequest request, String platformId, String appName) {
		MessageResponse response = new MessageResponse();
        if (CommonAppConstants.APP_TRADE_SUPPLIER.equals(appName)) {
            response.setIsSuccess(true);
        } else {
            response.setIsSuccess(false);
            JushitaJdpUserDeleteRequest jushitaJdpUserDeleteRequest = new JushitaJdpUserDeleteRequest();
            if (!StringUtils.isEmpty(request.getSellerNick())) {
                jushitaJdpUserDeleteRequest.setNick(request.getSellerNick());
            }
            JushitaJdpUserDeleteResponse jushitaJdpUserDeleteResponse = taobaoSDKService.execute(jushitaJdpUserDeleteRequest, appName);
            if (null != jushitaJdpUserDeleteResponse) {
                BeanUtils.copyProperties(jushitaJdpUserDeleteResponse,response);
                return response;
            }
            response.setErrorCode(ErrorCode.BaseCode.REQUEST_ERR.getCode().toString());
        }
		return response;
	}


	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_TAO;
	}

}
