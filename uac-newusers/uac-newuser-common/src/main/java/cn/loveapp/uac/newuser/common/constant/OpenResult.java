package cn.loveapp.uac.newuser.common.constant;
/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/21 18:41
 * @Description: 新用户开通异常枚举
 */
public enum OpenResult {

	/**
	 *
	 * 可重试的103开始
	 */
	GET_AUTH_EXCEPTION(-103,"获取用户授权异常"),
	TOP_SESSION_IS_NULL(-101,"top_session为空"),
	SOLD_GET_EXCEPTION(-109,"平台拉数据接口异常"),
	OLD_ORDER_DATA_NOT_DELETE(-104,"历史数据还没有删除"),
	NOT_FOUND_DB(-105,"找不到可用的存储数据库"),
	NOT_FOUND_RDS(-106,"找不到可用的rds推送库"),
	SAVE_ORDER_SWITCH_IS_CLOSE(-107,"Apollo中新用户开通存单服务关闭"),
	MESSAGE_PUSH_OPEN_FAILURE(-108,"消息推送开通失败"),
	OPEN_USER_SAVE_ORDER_FAILURE(-109,"用户存数据开通异常失败"),
	/**
	 * 环境变量问题200开始
	 */
	NO_USER(-200,"userProductinfo没有这个用户或者用户 表userId为0"),
	VIP_FLAG_IS_NULL(-202,"vip_flag为空"),
	USERID_IS_NULL(-201,"user_id为空"),

	/**
	 * 正常不开400开始
	 */
	VIP_IS_INVALID(-400,"用户不是高级版"),
	PROMOTION_ACTIVITY_USER(-401,"新手村活动赠送的高级版用户"),
	ALREADY_REDIS_TAG(-402,"redis中tag已经存在"),
	ALREADY_OPEN(-403,"在ext中已经开通了,不需要重复开一遍"),
	USERNICK_IS_NULL(-404,"userNick 为空"),
	PLATFORM_ID_IS_NULL(-405,"platformId为空"),

	/**
	 * 忽略开户请求
	 */
	REPEATED_REQUESTS(-1000,"用户正在开户, 重复的请求"),

	/**
	 * 成功的
	 */
	SPECIAL_USER_HANDLE(10,"特殊用户，更新用户EXT昵称"),
	OPEN_SUCCESS(10,"开通成功"),
	RE_OPEN_SUCCESS(10,"重新开通成功"),
	;

	private int code;
	private String message;
	OpenResult(int code, String message){
		this.code = code;
		this.message = message;
	}

	public int code(){
		return this.code;
	}

	public String message(){
		return this.message;
	}

}
