package cn.loveapp.uac.newuser.common.platform.impl;

import cn.loveapp.common.constant.CommonBusinessConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.constant.PretestTopicConstant;
import cn.loveapp.uac.common.dao.redis.repository.OpenUserRedisRepository;
import cn.loveapp.uac.common.utils.RocketMqQueueHelper;
import cn.loveapp.uac.db.common.entity.AyBusinessOpenUser;
import cn.loveapp.uac.domain.ComLoveRpcInnerprocessRequestHead;
import cn.loveapp.uac.domain.PullDataRequestBody;
import cn.loveapp.uac.domain.PullDataRequestProto;
import cn.loveapp.uac.domain.PullTypeConstants;
import cn.loveapp.uac.entity.UserProductInfoBusinessExt;
import cn.loveapp.uac.newuser.common.config.OpenUserCommonConfig;
import cn.loveapp.uac.newuser.common.config.OpenUserDispatcherConfig;
import cn.loveapp.uac.newuser.common.dto.NewUserPlatformHandleDTO;
import cn.loveapp.uac.newuser.common.dto.SaveDataDTO;
import cn.loveapp.uac.newuser.common.helper.RateLimitHelper;
import cn.loveapp.uac.newuser.common.platform.NewUserPlatformHandleService;
import cn.loveapp.uac.newuser.common.service.UserCenterService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.Objects;

/**
 * @Author: zhongzijie
 * @Date: 2023/8/8 9:53
 * @Description: 新用户开户平台处理service抽象类
 */
public abstract class AbstractNewUserPlatformHandleServiceImpl implements NewUserPlatformHandleService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(AbstractNewUserPlatformHandleServiceImpl.class);

    /**
     * 默认的rds开通规则
     */
    private final static String EMPTY_RDS = "EMPTY_RDS";

    /**
     * 发送拉数据消息间隔时间(小时)
     */
    @Value("${uac.newuser.openuser.sendPullDataQueue.expire.time:12}")
    private Integer sendPullDataQueueExpireTime;

    @Autowired
    protected OpenUserDispatcherConfig openUserDispatcherConfig;

    @Autowired
    protected UserCenterService userCenterService;

    @Autowired
    protected RocketMqQueueHelper rocketMqQueueHelper;

    @Autowired
    protected DefaultMQProducer defaultMQProducer;

    @Autowired
    private OpenUserRedisRepository openUserRedisRepository;

    @Autowired
    private  NewUserPlatformHandleService newUserPlatformHandleService;

    @Autowired
    protected OpenUserCommonConfig openUserCommonConfig;

    @Autowired
    private RateLimitHelper rateLimitHelper;

    @Override
    public Boolean isOnlyCheckW1Deadline(String businessId, String platformId, String appName) {
        return true;
    }

    @Override
    public Boolean isNeedPullData(String businessId, String platformId, String appName) {
        return false;
    }

    @Override
    public Boolean isNeedSubscribeMc(String businessId, String platformId, String appName) {
        return false;
    }

    @Override
    public Boolean isNeedSubscribeRds(String businessId, String platformId, String appName) {
        return false;
    }

    @Override
    public String getRdsRule(String businessId, String platformId, String appName) {
        return EMPTY_RDS;
    }

    @Override
    public Boolean isNeedCompareAuthDeadline(String businessId, String platformId, String appName) {
        return true;
    }

    @Override
    public void handlePrepareOpenOnNormalCondition(SaveDataDTO saveDataDTO, UserProductInfoBusinessExt userInfo, NewUserPlatformHandleDTO newUserPlatformHandleDTO, String platformId, String appName) {

    }

    @Override
    public boolean checkTokenAndSend2PullDataQueue(SaveDataDTO saveDataDTO, UserProductInfoBusinessExt userInfo, String platformId, String appName) {
        return doCheckTokenAndSend2PullDataQueue(saveDataDTO, userInfo, platformId, appName);
    }

    @Override
    public boolean send2PullDataQueue(String sellerId, String sellerNick, Integer openType, String businessId, String platformId, String appName) {
        return doSend2PullDataQueue(sellerId, sellerNick, openType, platformId, businessId, appName);
    }

    /**
     * 先校验token后发送到拉数据队列
     *
     * @param saveDataDTO
     * @param userInfo
     * @param platformId
     * @param appName
     * @return
     */
    protected boolean doCheckTokenAndSend2PullDataQueue(SaveDataDTO saveDataDTO, UserProductInfoBusinessExt userInfo, String platformId, String appName) {
        String platformAndAppName = platformId + ":" + appName;
        String businessId = saveDataDTO.getBusinessId();
        String sellerNick = saveDataDTO.getSellerNick();
        String topSession = userCenterService.getTopSession(platformId, appName, sellerNick, userInfo.getSellerId());
        if (StringUtils.isNotEmpty(topSession)) {
            if (rateLimitHelper.filterPullData(saveDataDTO)) {
                return false;
            }
            LOGGER.logInfo(sellerNick, platformAndAppName, "发送消息重新拉数据: " + userInfo.getPullStatus());
            doSend2PullDataQueue(userInfo.getSellerId(), sellerNick, null, platformId, businessId, appName);
            return true;
        } else {
            LOGGER.logInfo(sellerNick, platformAndAppName, "发送消息重新拉数据失败, topsession失效");
            return false;
        }

    }

    /**
     * 发送消息去拉数据
     * @param sellerId
     * @param sellerNick
     * @param openType
     * @param platformId
     * @param businessId
     * @param appName
     * @return
     */
    protected boolean doSend2PullDataQueue(String sellerId, String sellerNick, Integer openType, String platformId, String businessId, String appName) {
        if (!newUserPlatformHandleService.isNeedPullData(businessId, platformId, appName)) {
            return true;
        }

        ComLoveRpcInnerprocessRequestHead comLoveRpcInnerprocessRequestHead = new ComLoveRpcInnerprocessRequestHead();
        comLoveRpcInnerprocessRequestHead.setSellerNick(sellerNick);
        comLoveRpcInnerprocessRequestHead.setSellerId(sellerId);
        comLoveRpcInnerprocessRequestHead.setPlatformId(platformId);
        comLoveRpcInnerprocessRequestHead.setAppName(appName);
        PullDataRequestBody pullDataRequestBody = new PullDataRequestBody();
        if (Objects.equals(AyBusinessOpenUser.OPEN_LOG_TYPE_RE_OPEN, openType) && CommonBusinessConstants.BUSINESS_ITEM.equals(businessId)) {
            pullDataRequestBody.setPullType(PullTypeConstants.CLEAR_OLD_DATA_AFTER_PULL);
            pullDataRequestBody.setIgnoreSameModified(false);
        } else {
            pullDataRequestBody.setPullType(PullTypeConstants.NORMAL);
        }
        PullDataRequestProto pullDataRequestProto = new PullDataRequestProto();
        pullDataRequestProto.setComLoveRpcInnerprocessRequestHead(comLoveRpcInnerprocessRequestHead);
        pullDataRequestProto.setPullDataRequestBody(pullDataRequestBody);

        String messageId = null;
        LOGGER.logInfo(sellerNick, "", "发送itemlist");
        Pair<String, String> target = openUserDispatcherConfig.getTargetTopicAndTag(businessId, platformId);

        String topic = target.getLeft();
        String tag = target.getRight();
        if (openUserCommonConfig.getPretestUsers().contains(sellerNick)) {
            topic = target.getLeft() + PretestTopicConstant.PRETEST_TOPIC_SUFFIX;
        }
        messageId = rocketMqQueueHelper.push(topic, tag, pullDataRequestProto, defaultMQProducer);
        LOGGER.logInfo(sellerNick, "新开户打标", "成功开通用户: " + messageId);

        return StringUtils.isNotEmpty(messageId);
    }

}
