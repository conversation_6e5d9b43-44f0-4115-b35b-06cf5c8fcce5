package cn.loveapp.uac.newuser.common.platform.impl;

import org.springframework.stereotype.Service;

import cn.loveapp.common.constant.CommonPlatformConstants;

/**
 * <AUTHOR>
 * @date 2023-12-14 18:06
 * @description: 视频号-新用户开户平台处理service实现类
 */
@Service
public class KwaishopNewUserPlatformHandleServiceImpl extends AbstractNewUserPlatformHandleServiceImpl {

    @Override
    public Boolean isNeedPullData(String businessId, String platformId, String appName) {
        return true;
    }

    @Override
    public String getDispatcherId() {
        return CommonPlatformConstants.PLATFORM_KWAISHOP;
    }
}
