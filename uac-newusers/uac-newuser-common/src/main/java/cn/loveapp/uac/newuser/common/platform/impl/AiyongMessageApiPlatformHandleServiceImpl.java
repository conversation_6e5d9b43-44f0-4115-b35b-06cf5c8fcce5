package cn.loveapp.uac.newuser.common.platform.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.uac.common.request.MessageRequest;
import cn.loveapp.uac.common.response.MessageResponse;
import cn.loveapp.uac.newuser.common.platform.MessageApiPlatformHandleService;
import org.springframework.stereotype.Service;


/**
 * 爱用平台 平台消息服务中心
 * <AUTHOR>
 * @date 11:07 AM 2022/11/22
 */
@Service
public class AiyongMessageApiPlatformHandleServiceImpl implements MessageApiPlatformHandleService {
    @Override
    public boolean isSubscribeUserMessageService(MessageRequest request, String platformId, String appName) {
        return true;
    }

    @Override
    public MessageResponse subscribeUserMessageService(MessageRequest request, String platformId, String appName) {
        MessageResponse response = new MessageResponse();
        response.setIsSuccess(true);
        return response;
    }

    @Override
    public MessageResponse cancelUserMesageService(MessageRequest request, String platformId, String appName) {
        MessageResponse response = new MessageResponse();
        response.setIsSuccess(true);
        return response;
    }

    @Override
    public boolean isSubscribeUserDbService(MessageRequest request, String platformId, String appName) {
        return false;
    }

    @Override
    public MessageResponse subscribeUserDbService(MessageRequest request, String platformId, String appName) {
        MessageResponse response = new MessageResponse();
        response.setIsSuccess(true);
        return response;
    }

    @Override
    public MessageResponse cancelUserDbService(MessageRequest request, String platformId, String appName) {
        MessageResponse response = new MessageResponse();
        response.setIsSuccess(true);
        return response;
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_AIYONG;
    }
}
