package cn.loveapp.uac.newuser.common.constant;

import lombok.Data;

import java.io.Serializable;

/**
 * 数据落库状态记录表(AyOrderPullRecord)实体类
 *
 * <AUTHOR>
 * @since 2018-12-24 15:57:33
 */
@Data
public class UserProductionInfoExtConst implements Serializable {
	/**
	 * 修补状态
	 */
	public static final Integer FIX_WAIT = 201;
	public static final Integer FIX_ING = 202;
	public static final Integer FIX_SUCCESS = 20;
	public static final Integer FIX_FAILED = -201;

    public static final Integer DB_DONE = 10;

    public static final Boolean ARRIVE_ENDPOINT = true;
    public static final Boolean ARRIVE_WITHOUT_ENDPOINT = false;

}
