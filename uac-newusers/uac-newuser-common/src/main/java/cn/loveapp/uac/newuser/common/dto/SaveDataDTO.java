package cn.loveapp.uac.newuser.common.dto;

import lombok.Data;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/1/7 16:09
 * @Description: 存数据传输对象
 */
@Data
public class SaveDataDTO {

	/**
	 * 卖家Id
	 */
	private String sellerId;

	/**
	 * 卖家昵称
	 */
	private String sellerNick;

	/**
	 * 平台信息
	 */
	private String platform;

	/**
	 * 服务来源
	 */
	private String serviceName;

	/**
	 * 业务id
	 */
	private String businessId;

	/**
	 * 是否关闭存数据 默认flase
	 */
	private Boolean closeSaveData = false;

	/**
	 * 授权令牌更新
	 */
	private Boolean tokenUpdated;

}
