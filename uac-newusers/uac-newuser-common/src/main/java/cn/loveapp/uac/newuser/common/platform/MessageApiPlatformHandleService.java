package cn.loveapp.uac.newuser.common.platform;

import cn.loveapp.common.autoconfigure.platform.CommonPlatformHandler;
import cn.loveapp.uac.common.request.MessageRequest;
import cn.loveapp.uac.common.response.MessageResponse;

/**
 * 平台消息服务中心
 * @program: orders-services-group
 * @description: PlatformMessageCenterService
 * @author: Jason
 * @create: 2019-11-28 17:18
 **/
public interface MessageApiPlatformHandleService extends CommonPlatformHandler {

	/**
	 * 是否已经订阅用户消息
	 * @param request
	 * @param appName
	 * @return
	 */
	boolean isSubscribeUserMessageService(MessageRequest request, String platformId, String appName);

	/**
	 * 订阅用户消息
	 * @param request 请求对象
	 * @return 响应对象
	 */
	MessageResponse subscribeUserMessageService(MessageRequest request, String platformId, String appName);

	/**
	 * 取消用户消息
	 * @param request 请求对象
	 * @return 响应对象
	 */
	MessageResponse cancelUserMesageService(MessageRequest request, String platformId, String appName);

	/**
	 * 是否已经订阅用户rds
	 * @param request
	 * @param appName
	 * @return
	 */
	boolean isSubscribeUserDbService(MessageRequest request, String platformId, String appName);

	/**
	 * 订阅用户rds
	 * @param request 请求对象
	 * @return 响应对象
	 */
	MessageResponse subscribeUserDbService(MessageRequest request, String platformId, String appName);

	/**
	 * 取消用户rds
	 * @param request 请求对象
	 * @return 响应对象
	 */
	MessageResponse cancelUserDbService(MessageRequest request, String platformId, String appName);
}
