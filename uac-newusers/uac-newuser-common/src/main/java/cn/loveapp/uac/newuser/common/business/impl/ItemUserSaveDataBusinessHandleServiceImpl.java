package cn.loveapp.uac.newuser.common.business.impl;

import cn.loveapp.common.constant.CommonBusinessConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.uac.entity.UserProductInfoBusinessExt;
import cn.loveapp.uac.domain.UserExtInfoDTO;
import cn.loveapp.uac.newuser.common.dto.SaveDataCourseDTO;
import cn.loveapp.uac.newuser.common.dto.UserInfoDTO;
import cn.loveapp.uac.newuser.dto.request.*;
import cn.loveapp.uac.newuser.dto.response.*;
import cn.loveapp.items.api.service.ItemsNewUserRpcInnerApiService;
import cn.loveapp.uac.request.GetUserInfoExtRequest;
import cn.loveapp.uac.response.GetUserInfoExtResponse;
import cn.loveapp.uac.service.UserProductInfoExtApiService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @Author: zhongzijie
 * @Date: 2023/1/10 16:20
 * @Description: 存商品用户存数据管理service 实现类
 */
@Service
public class ItemUserSaveDataBusinessHandleServiceImpl extends AbstractUserSaveDataBusinessHandleService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ItemUserSaveDataBusinessHandleServiceImpl.class);

    @Autowired
    @Qualifier("stringItemRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ItemsNewUserRpcInnerApiService itemsNewUserRpcInnerApiService;

    @Autowired
    private UserProductInfoExtApiService userProductInfoExtApiService;

    @Override
    public String getDispatcherId() {
        return CommonBusinessConstants.BUSINESS_ITEM;
    }

    @Override
    public SaveDataCourseDTO getPullDataProgress(String businessId, UserInfoDTO session) {
        GetPullDataProgressRequest request = new GetPullDataProgressRequest();
        BeanUtils.copyProperties(session, request);
        GetUserInfoExtRequest getUserInfoExtRequest = new GetUserInfoExtRequest();
        getUserInfoExtRequest.setBusinessId(businessId);
        getUserInfoExtRequest.setStoreId(request.getStoreId());
        getUserInfoExtRequest.setAppName(request.getAppName());
        getUserInfoExtRequest.setSellerId(request.getSellerId());
        SaveDataCourseDTO saveDataCourseDTO = new SaveDataCourseDTO();
        CommonApiResponse<GetUserInfoExtResponse> userInfoExtCommonResponse = userProductInfoExtApiService.getUserInfoExtBySellerId(getUserInfoExtRequest);
        if (userInfoExtCommonResponse == null
                || !userInfoExtCommonResponse.isSuccess()
                || userInfoExtCommonResponse.getBody() == null
                || userInfoExtCommonResponse.getBody().getUserInfoExt() == null
                || Objects.equals(userInfoExtCommonResponse.getBody().getUserInfoExt().getTopStatus(), UserProductInfoBusinessExt.DB_FAILED)) {
            saveDataCourseDTO.setTotal(0);
            saveDataCourseDTO.setProgress(0);
            saveDataCourseDTO.setTopStatus(UserProductInfoBusinessExt.DB_FAILED);
            return saveDataCourseDTO;
        }
        CommonApiResponse<GetPullDataProgressResponse> response = itemsNewUserRpcInnerApiService.getPullDataProgress(request);
        if (response == null || !response.isSuccess()) {
            throw new RuntimeException("调用rpc接口getPullDataProgress报错啦");
        }
        UserExtInfoDTO userInfoExt = userInfoExtCommonResponse.getBody().getUserInfoExt();
        saveDataCourseDTO.setPullStartTime(userInfoExt.getPullStartDateTime());
        saveDataCourseDTO.setPullStatus(userInfoExt.getPullStatus());
        saveDataCourseDTO.setTopStatus(Integer.parseInt(userInfoExt.getTopStatus()));
        saveDataCourseDTO.setTotal(response.getBody().getTotal());
        saveDataCourseDTO.setProgress(response.getBody().getProgress());
        return saveDataCourseDTO;
    }

    @Override
    protected Long getSaveDataTotalResult(String sellerNick, String sellerId, String topSession, String supplierId, String platformId, String appName) {
        GetSaveDataTotalResultRequest request = new GetSaveDataTotalResultRequest();
        request.setTopSession(topSession);
        request.setSupplierId(supplierId);
        request.setSellerId(sellerId);
        request.setSellerNick(sellerNick);
        request.setPlatformId(platformId);
        request.setAppName(appName);
        CommonApiResponse<GetSaveDataTotalResultResponse> commonApiResponse = itemsNewUserRpcInnerApiService.getSaveDataTotalResult(request);
        if (commonApiResponse == null) {
            throw new RuntimeException("调用rpc接口getPullDataProgress报错啦");
        } else if (!commonApiResponse.isSuccess() || commonApiResponse.getBody() == null) {
            throw new RuntimeException("调用rpc接口getPullDataProgress报错啦: " + commonApiResponse.getSubMessage());
        }
        return commonApiResponse.getBody().getTotal();
    }

    @Override
    protected StringRedisTemplate getStringRedisTemplate() {
        return stringRedisTemplate;
    }
}
