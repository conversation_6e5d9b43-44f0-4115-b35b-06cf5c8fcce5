package cn.loveapp.uac.newuser.common.service.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.uac.newuser.common.dto.request.CheckDistributeUserRequest;
import cn.loveapp.uac.newuser.common.dto.response.CheckDistributeUserResponse;
import cn.loveapp.uac.newuser.common.service.external.Distribute1688GuleService;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.newuser.common.service.DistributeDataHandleService;

/**
 * <AUTHOR>
 * @date 2023-07-19 12:12
 * @Description: 代发数据处理服务实现类
 */
@Service
public class DistributeDataHandleServiceServiceImpl implements DistributeDataHandleService {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(DistributeDataHandleServiceServiceImpl.class);
    
    @Autowired
    private Distribute1688GuleService distribute1688GuleService;

    @Override
    public boolean checkDistributeUser(String sellerId, String platformId, String appName) {
        if (CommonPlatformConstants.PLATFORM_TAO.equals(platformId)
            && CommonAppConstants.APP_DISTRIBUTE.equals(appName)) {
            CheckDistributeUserRequest request = new CheckDistributeUserRequest();
            request.setSellerId(sellerId);
            request.setPlatformId(platformId);
            CommonApiResponse<CheckDistributeUserResponse> response =
                distribute1688GuleService.checkDistributeUser(request);

            if (response == null) {
                LOGGER.logInfo("检验是否代发用户, 接口请求失败");
                return false;
            }

            CheckDistributeUserResponse checkDistributeUserResponse = response.getBody();
            if (checkDistributeUserResponse == null) {
                LOGGER.logInfo("检验是否代发用户, 响应体返回为空");
                return false;
            }

            return BooleanUtils.isTrue(checkDistributeUserResponse.getIsDistributeUser());
        }
        return false;
    }
}
