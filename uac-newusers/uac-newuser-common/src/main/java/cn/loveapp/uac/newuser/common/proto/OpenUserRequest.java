package cn.loveapp.uac.newuser.common.proto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @Author: zhong<PERSON>jie
 * @Date: 2023/1/3 9:51
 * @Description: 新用户开通请求实体类
 */
@Data
public class OpenUserRequest {

    @NotNull
    private Integer id;

    @NotNull
    private String sellerId;

    @NotBlank
    private String sellerNick;

    @NotBlank
    private String businessId;
}
