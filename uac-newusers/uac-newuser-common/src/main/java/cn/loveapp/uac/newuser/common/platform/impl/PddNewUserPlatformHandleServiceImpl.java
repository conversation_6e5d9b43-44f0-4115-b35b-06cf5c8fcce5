package cn.loveapp.uac.newuser.common.platform.impl;

import org.springframework.stereotype.Service;

import cn.loveapp.common.constant.CommonPlatformConstants;

/**
 * <AUTHOR>
 * @date 2023-12-01 15:23
 * @description: 拼多多-新用户开户平台处理service实现类
 */
@Service
public class PddNewUserPlatformHandleServiceImpl extends AbstractNewUserPlatformHandleServiceImpl {

    @Override
    public Boolean isNeedPullData(String businessId, String platformId, String appName) {
        return true;
    }

    @Override
    public Boolean isNeedSubscribeMc(String businessId, String platformId, String appName) {
        return true;
    }

    @Override
    public String getDispatcherId() {
        return CommonPlatformConstants.PLATFORM_PDD;
    }
}
