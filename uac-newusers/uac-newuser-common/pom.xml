<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>uac-newusers</artifactId>
        <groupId>cn.loveapp.uac</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>uac-newuser-common</artifactId>

    <dependencies>
        <dependency>
            <groupId>cn.loveapp.uac</groupId>
            <artifactId>uac-api</artifactId>
            <version>${uac.api.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.loveapp.uac</groupId>
            <artifactId>uac-newuser-api</artifactId>
            <version>${uac-newuser-api.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.loveapp.uac</groupId>
            <artifactId>uac-service-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.loveapp.uac</groupId>
            <artifactId>uac-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.loveapp.uac</groupId>
            <artifactId>uac-db-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>net.spy</groupId>
            <artifactId>spymemcached</artifactId>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>

    </dependencies>

</project>
