<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.loveapp.uac</groupId>
        <artifactId>uac-service-group</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>uac-common</artifactId>

    <name>爱用-uac-common</name>
    <description>爱用-uac 通用模块</description>
    <version>1.0-SNAPSHOT</version>

	<dependencies>
		<dependency>
			<groupId>cn.loveapp.uac</groupId>
			<artifactId>uac-api</artifactId>
			<version>${uac.api.version}</version>
		</dependency>
		<dependency>
			<groupId>cn.loveapp.uac</groupId>
			<artifactId>uac-domain</artifactId>
			<version>${uac.domain.version}</version>
		</dependency>
		<dependency>
			<groupId>cn.loveapp.common</groupId>
			<artifactId>common-spring-boot-web-starter</artifactId>
		</dependency>
        <dependency>
            <groupId>cn.loveapp.common</groupId>
            <artifactId>common-platformsdk-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
        </dependency>
	</dependencies>
</project>
