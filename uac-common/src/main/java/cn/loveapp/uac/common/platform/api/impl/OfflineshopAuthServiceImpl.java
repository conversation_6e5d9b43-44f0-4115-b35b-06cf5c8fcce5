package cn.loveapp.uac.common.platform.api.impl;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.common.collect.ImmutableMap;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.config.DistributeConfig;
import cn.loveapp.uac.common.config.aiyong.AiyongAppConfig;
import cn.loveapp.uac.common.platform.api.AuthService;
import cn.loveapp.uac.common.platform.api.domain.RefreshTokenCallbackResult;

/**
 * <AUTHOR>
 * @date 2024-10-22 14:50
 * @description: 线下店铺授权相关service
 */
@Service
public class OfflineshopAuthServiceImpl extends BaseAuthServiceImpl implements AuthService {

    @Value("${uac.network.retry.count:5}")
    private Integer retryCount;

    public OfflineshopAuthServiceImpl(AiyongAppConfig appConfig, DistributeConfig distributeConfig) {
        super(ImmutableMap.of(ALL_APP, appConfig), distributeConfig);
    }

    @Override
    public <T extends RefreshTokenCallbackResult> T getCallbackResultByCode(String code, String platformId,
        String appName) throws Exception {
        return null;
    }

    @Override
    public <T extends RefreshTokenCallbackResult> T refreshToken(UserInfoBo userInfoBo, String refreshToken,
        String platformId, String appName) throws Exception {
        return null;
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_OFFLINESTORE;
    }

    @Override
    public Integer getRetryCount() {
        return retryCount;
    }
}
