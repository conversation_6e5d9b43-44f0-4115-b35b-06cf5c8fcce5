package cn.loveapp.uac.common.api.domain;
import com.taobao.api.domain.ArticleBizOrder;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2021/10/9$ 14:48$
 * taobao.vas.order.search 响应商品订单对象
 *
 */
@Data
public class SellerArticleBizOrder {
    /**
     * 订单号
     */
    private String bizOrderId;
    /**
     * 子订单号
     */
    private String orderId;
    /**
     * 淘宝会员名
     */
    private String nick;
    /**
     * 应用名称
     */
    private String articleName;
    /**
     * 应用收费代码，从合作伙伴后台（my.open.taobao.com）-收费管理-收费项目列表 能够获得该应用的收费代码
     */
    private String articleCode;
    /**
     * 收费项目代码，从合作伙伴后台（my.open.taobao.com）-收费管理-收费项目列表 能够获得收费项目代码
     */
    private String itemCode;
    /**
     * 订单创建时间（订购时间）
     */
    private LocalDateTime createdate;
    /**
     * 订购周期 1表示年 ，2表示月，3表示天。
     */
    private String orderCycle;
    /**
     * 订购周期开始时间
     */
    private LocalDateTime orderCycleStart;
    /**
     * 订购周期结束时间
     */
    private LocalDateTime orderCycleEnd;
    /**
     * 订单类型，1=新订 2=续订 3=升级 4=后台赠送 5=后台自动续订 6=订单审核后生成订购关系（暂时用不到）
     */
    private Integer bizType;
    /**
     * 原价（单位为分）
     */
    private Integer fee;
    /**
     * 优惠（单位为分）
     */
    private Integer promFee;
    /**
     * 退款（单位为分；升级时，系统会将升级前老版本按照剩余订购天数退还剩余金额）
     */
    private Integer refundFee;
    /**
     * 实付（单位为分）
     */
    private Integer totalPayFee;
    /**
     * 商品模型名称
     */
    private String articleItemName;
    /**
     * activityCode
     */
    private String activityCode;

    /**
     * 转换淘宝响应体
     * @param articleBizOrder
     */
    public SellerArticleBizOrder(ArticleBizOrder articleBizOrder) {

        BeanUtils.copyProperties(articleBizOrder,this);
    }
}
