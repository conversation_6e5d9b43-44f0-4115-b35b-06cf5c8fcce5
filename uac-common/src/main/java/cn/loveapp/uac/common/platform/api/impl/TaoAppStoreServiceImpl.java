package cn.loveapp.uac.common.platform.api.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.taobao.TaobaoSDKService;
import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.api.domain.SellerArticleBizOrder;
import cn.loveapp.uac.common.api.domain.SellerArticleSub;
import cn.loveapp.uac.common.api.domain.SellerArticleUserSubscribe;
import cn.loveapp.uac.common.api.request.SellerVasOrderSearchRequest;
import cn.loveapp.uac.common.api.request.SellerVasSubscSearchRequest;
import cn.loveapp.uac.common.api.request.SellerVasSubscribeGetRequest;
import cn.loveapp.uac.common.api.response.SellerVasOrderSearchResponse;
import cn.loveapp.uac.common.api.response.SellerVasSubscSearchResponse;
import cn.loveapp.uac.common.api.response.SellerVasSubscribeGetResponse;
import cn.loveapp.uac.common.code.ErrorCode;
import cn.loveapp.uac.common.code.taobao.ApiCodeConstant;
import cn.loveapp.uac.common.platform.api.AppStoreService;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.taobao.api.TaobaoResponse;
import com.taobao.api.domain.ArticleBizOrder;
import com.taobao.api.domain.ArticleSub;
import com.taobao.api.domain.ArticleUserSubscribe;
import com.taobao.api.request.VasOrderSearchRequest;
import com.taobao.api.request.VasSubscSearchRequest;
import com.taobao.api.request.VasSubscribeGetRequest;
import com.taobao.api.response.VasOrderSearchResponse;
import com.taobao.api.response.VasSubscSearchResponse;
import com.taobao.api.response.VasSubscribeGetResponse;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @program: uac-service-group
 * @description: TaobaoTradeServiceServiceImpl
 * @author: Jason
 * @create: 2020-03-09 19:04
 **/
@Service
public class TaoAppStoreServiceImpl implements AppStoreService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(TaoAppStoreServiceImpl.class);

	@Autowired
	private TaobaoSDKService taobaoAppsSDKService;

	/**
	 * 订购关系查询
	 */
	@Override
	public SellerVasSubscribeGetResponse vasSubscribeGet(SellerVasSubscribeGetRequest sellerVasSubscribeGetRequest, String platformId, String appName) {
		SellerVasSubscribeGetResponse response = new SellerVasSubscribeGetResponse();
		VasSubscribeGetRequest vasSubscribeGetRequest = new VasSubscribeGetRequest();
		vasSubscribeGetRequest.setArticleCode(sellerVasSubscribeGetRequest.getArticleCode());
		vasSubscribeGetRequest.setNick(sellerVasSubscribeGetRequest.getSellerNick());
		VasSubscribeGetResponse vasSubscribeGetResponse = taobaoAppsSDKService.execute(vasSubscribeGetRequest, appName);
		if (Objects.nonNull(vasSubscribeGetResponse) && vasSubscribeGetResponse.isSuccess()) {
			BeanUtils.copyProperties(vasSubscribeGetResponse, response);
			boolean vasSubscribeGetItemBol = CollectionUtils.isEmpty(vasSubscribeGetResponse.getArticleUserSubscribes());
			if (!vasSubscribeGetItemBol) {
				List<SellerArticleUserSubscribe> sellerArticleUserSubscribes = copyOfSellerArticleUserSubscribe(vasSubscribeGetResponse.getArticleUserSubscribes());
				response.setArticleUserSubscribes(sellerArticleUserSubscribes);
				return response;
			} else {
				LOGGER.logInfo(sellerVasSubscribeGetRequest.getSellerNick(), sellerVasSubscribeGetRequest.getArticleCode(), "接口响应成功,但是用户长时间未发生订购返回的订购列表为空,当失败处理");
			}
		}
		if (Objects.nonNull(vasSubscribeGetResponse) && ApiCodeConstant.getIgnoreErrorCode().contains(vasSubscribeGetResponse.getErrorCode())) {
			response.setErrorCode(vasSubscribeGetResponse.getErrorCode());
		} else {
			response.setErrorCode(ErrorCode.BaseCode.REQUEST_ERR.getCode().toString());
		}
		return response;
	}

	/**
	 * taobao.vas.subsc.search
	 */
	@Override
	public SellerVasSubscSearchResponse vasSubscribeSearch(SellerVasSubscSearchRequest sellerVasSubscSearchRequest, String platformId, String appName) {
		SellerVasSubscSearchResponse response = new SellerVasSubscSearchResponse();
		VasSubscSearchRequest request = new VasSubscSearchRequest();
		request.setPageSize(sellerVasSubscSearchRequest.getPageSize());
		request.setPageNo(sellerVasSubscSearchRequest.getPageNo());
		request.setArticleCode(sellerVasSubscSearchRequest.getArticleCode());
		request.setItemCode(sellerVasSubscSearchRequest.getItemCode());
		request.setStatus(sellerVasSubscSearchRequest.getStatus());
		request.setAutosub(sellerVasSubscSearchRequest.getAutosub());
		request.setNick(sellerVasSubscSearchRequest.getSellerNick());
		VasSubscSearchResponse vasSubscSearchResponse = taobaoAppsSDKService.execute(request, appName);
		if (Objects.nonNull(vasSubscSearchResponse) && vasSubscSearchResponse.isSuccess()) {
			BeanUtils.copyProperties(vasSubscSearchResponse, response);
			if (vasSubscSearchResponse.getTotalItem() > 0) {
				List<SellerArticleSub> sellerArticleSubs = copyOfArticleSub(vasSubscSearchResponse.getArticleSubs());
				response.setArticleSubs(sellerArticleSubs);
			}
			response.setTotalItem(vasSubscSearchResponse.getTotalItem());
			return response;
		}
		if (ApiCodeConstant.getIgnoreErrorCode().contains(vasSubscSearchResponse.getErrorCode())) {
			response.setErrorCode(vasSubscSearchResponse.getErrorCode());
		} else {
			response.setErrorCode(ErrorCode.BaseCode.REQUEST_ERR.getCode().toString());
		}
		return response;
	}

	/**
	 * taobao.vas.order.search
	 */
	@Override
	public SellerVasOrderSearchResponse vasOrderSearch(SellerVasOrderSearchRequest sellerVasOrderSearchRequest, String platformId, String appName) {
		SellerVasOrderSearchResponse response = new SellerVasOrderSearchResponse();
		VasOrderSearchRequest request = new VasOrderSearchRequest();
		BeanUtils.copyProperties(sellerVasOrderSearchRequest, request);
		LOGGER.logInfo("查询vas.order.search入参：" + JSON.toJSONString(request));
		VasOrderSearchResponse vasOrderSearchResponse = taobaoAppsSDKService.execute(request, appName);
		LOGGER.logInfo("查询vas.order.search出参：" + JSON.toJSONString(vasOrderSearchResponse));
		if (Objects.nonNull(vasOrderSearchResponse) && vasOrderSearchResponse.isSuccess()) {
			List<ArticleBizOrder> articleBizOrders = vasOrderSearchResponse.getArticleBizOrders();
            if (CollectionUtils.isNotEmpty(articleBizOrders)) {
                BeanUtils.copyProperties(vasOrderSearchResponse, response);
                List<SellerArticleBizOrder> voList = Lists.transform(articleBizOrders, (entity) -> {
                    SellerArticleBizOrder sellerArticleBizOrder = new SellerArticleBizOrder(entity);
                    //与数据库插入时属性类型对照不上 这里做转换
                    sellerArticleBizOrder.setCreatedate(DateUtil.parseDate(entity.getCreate()));
                    sellerArticleBizOrder.setOrderCycleEnd(DateUtil.parseDate(entity.getOrderCycleEnd()));
                    sellerArticleBizOrder.setOrderCycleStart(DateUtil.parseDate(entity.getOrderCycleStart()));
                    sellerArticleBizOrder.setBizType(entity.getBizType() == null ? null : entity.getBizType().intValue());
                    sellerArticleBizOrder.setFee(entity.getFee() == null ? null : Integer.valueOf(entity.getFee()));
                    sellerArticleBizOrder.setPromFee(entity.getPromFee() == null ? null : Integer.valueOf(entity.getPromFee()));
                    sellerArticleBizOrder.setRefundFee(entity.getRefundFee() == null ? null : Integer.valueOf(entity.getRefundFee()));
                    sellerArticleBizOrder.setTotalPayFee(entity.getTotalPayFee() == null ? null : Integer.valueOf(entity.getTotalPayFee()));
                    sellerArticleBizOrder.setOrderId(entity.getOrderId() == null ? null : String.valueOf(entity.getOrderId()));
                    sellerArticleBizOrder.setBizOrderId(entity.getBizOrderId() == null ? null : String.valueOf(entity.getBizOrderId()));
                    return  sellerArticleBizOrder;
                });
                response.setArticleBizOrders(voList);
                return response;
            }
		}
		errorHandle(response);
		return response;
	}

	private void errorHandle(TaobaoResponse response){
		if (ApiCodeConstant.getIgnoreErrorCode().contains(response.getErrorCode())) {
			response.setErrorCode(response.getErrorCode());
		} else {
			response.setErrorCode(ErrorCode.BaseCode.REQUEST_ERR.getCode().toString());
		}
	}



	private List<SellerArticleUserSubscribe> copyOfSellerArticleUserSubscribe(List<ArticleUserSubscribe> articleUserSubscribes) {
		List<SellerArticleUserSubscribe> sellerArticleSubs = new ArrayList<>();
		for (int i = 0; i < articleUserSubscribes.size(); i++) {
			SellerArticleUserSubscribe sellerArticleSub = new SellerArticleUserSubscribe();
			sellerArticleSub.setDeadline(articleUserSubscribes.get(i).getDeadline());
			sellerArticleSub.setItemCode(articleUserSubscribes.get(i).getItemCode());
			sellerArticleSubs.add(sellerArticleSub);
		}
		return sellerArticleSubs;
	}

	private List<SellerArticleSub> copyOfArticleSub(List<ArticleSub> subs) {
		List<SellerArticleSub> sellerArticleSubs = new ArrayList<>();
		for (int i = 0; i < subs.size(); i++) {
			SellerArticleSub sellerArticleSub = new SellerArticleSub();
			sellerArticleSub.setArticleCode(subs.get(i).getArticleCode());
			sellerArticleSub.setArticleName(subs.get(i).getArticleName());
			sellerArticleSub.setItemCode(subs.get(i).getItemCode());
			sellerArticleSub.setItemName(subs.get(i).getItemName());
			sellerArticleSub.setAutosub(subs.get(i).getAutosub());
			sellerArticleSub.setDeadline(subs.get(i).getDeadline());
			sellerArticleSub.setNick(subs.get(i).getNick());
			sellerArticleSub.setExpireNotice(subs.get(i).getExpireNotice());
			sellerArticleSub.setStatus(subs.get(i).getStatus());
			sellerArticleSubs.add(sellerArticleSub);
		}
		return sellerArticleSubs;
	}

	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_TAO;
	}
}
