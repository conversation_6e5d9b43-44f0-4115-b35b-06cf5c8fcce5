package cn.loveapp.uac.common.utils;

import com.google.common.collect.Maps;

import java.util.HashMap;

/**
 *
 * RouterBatchRequest
 * <AUTHOR>
 * @since 2018/10/17 Time: 7:51 PM
 */
public class SerializedPhpParser {

	private final String input;

	private static final String SEMICOLON_SEPARATOR = ";";
	private static final String COLON_SEPARATOR = ":";
	private static final String VERTICAL_BAR_SEPARATOR = "\\|";


	public SerializedPhpParser(String input) {
		this.input = input;
	}

	public HashMap<String, Object> parse(String key) {
		String[] inputSplit = input.split(SEMICOLON_SEPARATOR);
		HashMap<String, Object> parserData = Maps.newHashMap();
		for (String is : inputSplit) {
			String[] isSplit = is.split(VERTICAL_BAR_SEPARATOR);
			String mapKey = isSplit[0];
			if (isSplit.length < 2){
				continue;
			}
			if (true == isSplit[1].contains(COLON_SEPARATOR) && key.equals(mapKey)) {
				String[] sParse = isSplit[1].split(COLON_SEPARATOR);
				String  type = sParse[0];
				if (type == "N") {
					parserData.put(mapKey, null);
				} else {
					if(sParse.length == 3)
					{
						parserData.put(mapKey, sParse[2]);
					}
					else
					{
						parserData.put(mapKey, sParse[1]);
					}

				}
			} else {
				parserData.put(mapKey, null);
			}
		}
		return parserData;
	}

	public  HashMap<String, Object> getSessionValue(String keys)
	{
		String[] keyArray = {};
		HashMap<String, Object> parserData = Maps.newHashMap();
		if(keys.contains(SEMICOLON_SEPARATOR))
		{
			keyArray = keys.split(SEMICOLON_SEPARATOR);
		}
		for (String key: keyArray) {
			HashMap<String, Object> parserDataTemp = this.parse(key);

			parserData.put(key, parserDataTemp.get(key));
		}

		return parserData;
	}

}
