package cn.loveapp.uac.common.platform.api.domain;

import com.alibaba.fastjson.annotation.JSONField;
import java.util.List;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @program: uac-service-group
 * @description: Callback<PERSON><PERSON>ult
 * @author: <PERSON>
 * @create: 2020-03-13 13:50
 **/
@Data
public class PddRefreshTokenCallbackResult extends RefreshTokenCallbackResult {
	@JSO<PERSON>ield(name = "access_token")
	private String accessToken;
	@JSONField(name = "expires_in")
	private Long expiresIn;
	@JSONField(name = "refresh_token")
	private String refreshToken;
	@JSONField(name = "scope")
	private List<String> scope;
	@J<PERSON>NField(name = "owner_name")
	private String sellerNick;
	@JSONField(name = "owner_id")
	private String sellerId;

	@Data
	public static class ErrorResponse {
		@JSONField(name = "error_code")
		private String errorCode;
		@JSONField(name = "error_msg")
		private String errorMsg;
	}

	/**
	 * 判断是否成功
	 * @return
	 */
	public boolean isSuccess() {
		return StringUtils.isNotBlank(accessToken);
	}
}
