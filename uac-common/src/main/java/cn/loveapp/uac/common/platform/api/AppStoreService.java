package cn.loveapp.uac.common.platform.api;

import cn.loveapp.common.autoconfigure.platform.CommonPlatformHandler;
import cn.loveapp.uac.common.api.request.SellerVasOrderSearchRequest;
import cn.loveapp.uac.common.api.request.SellerVasSubscSearchRequest;
import cn.loveapp.uac.common.api.request.SellerVasSubscribeGetRequest;
import cn.loveapp.uac.common.api.response.SellerVasOrderSearchResponse;
import cn.loveapp.uac.common.api.response.SellerVasSubscSearchResponse;
import cn.loveapp.uac.common.api.response.SellerVasSubscribeGetResponse;

/**
 * @program: uac-service-group
 * @description: TaobaoServiceService
 * @author: Jason
 * @create: 2020-03-05 18:52
 **/
public interface AppStoreService extends CommonPlatformHandler {

	/**
	 * 订购关系查询
	 * @param sellerVasSubscribeGetRequest
	 * @return
	 */
	SellerVasSubscribeGetResponse vasSubscribeGet(SellerVasSubscribeGetRequest sellerVasSubscribeGetRequest, String platformId, String appName);

	/**
	 * taobao.vas.subsc.search
	 * @param sellerVasSubscSearchRequest
	 * @return
	 */
	SellerVasSubscSearchResponse vasSubscribeSearch(SellerVasSubscSearchRequest sellerVasSubscSearchRequest, String platformId, String appName);

	/**
	 * taobao.vas.order.search
	 * 查询平台订购订单记录
	 * @param vasOrderSearchRequest
	 * @return
	 */
	SellerVasOrderSearchResponse vasOrderSearch(SellerVasOrderSearchRequest vasOrderSearchRequest, String platformId, String appName);
}
