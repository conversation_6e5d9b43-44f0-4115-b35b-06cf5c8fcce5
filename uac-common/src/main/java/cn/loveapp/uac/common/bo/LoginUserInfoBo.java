package cn.loveapp.uac.common.bo;

import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.common.entity.redis.UserRedisEntity;
import cn.loveapp.uac.common.utils.DateUtil;
import cn.loveapp.uac.request.LoginInfoRequest;
import cn.loveapp.uac.response.UserInfoResponse;
import lombok.Data;

/**
 * @program: uac-service-group
 * @description: RegisterUserInfoBo
 * @author: Jason
 * @create: 2020-03-10 15:28
 **/
@Data
public class LoginUserInfoBo {
	private String platformId;
	private String appType;
	private String sellerNick;
	private String sellerId;
	private String createDate;
	private String lastActivedt;
	private Integer vipflag;
	private String createIpAddress;
	private String lastIpAddress;
	private String roleId;
	private String lastUpdateTime;
	private String subDateTime;
	private String isSilent;
	private String lastActivePoint;

	private Long reminderAuthDayDiff;

	public UserProductInfo toUserProductInfo() {
		UserProductInfo userProductInfo = UserProductInfo.of(sellerNick, sellerId);
		userProductInfo.initDefault();
		userProductInfo.setCorpId(sellerId);
		userProductInfo.setVipflag(vipflag);
		userProductInfo.setCreateipaddress(createIpAddress);
		userProductInfo.setLastipaddress(lastIpAddress);
		userProductInfo.setRoleid(roleId);
		userProductInfo.setSubdatetime(DateUtil.parseString(subDateTime));
		userProductInfo.setCreateDate(DateUtil.parseString(createDate));
		userProductInfo.setLastactivedt(DateUtil.parseString(lastActivedt));
		userProductInfo.putLastActivePlatform(lastActivePoint);
		if ("0".equals(isSilent)) {
			userProductInfo.setIsSilent(false);
		} else {
			userProductInfo.setIsSilent(true);
		}
		return userProductInfo;
	}

	public UserInfoBo toUserInfoBo() {
		UserInfoBo userInfoBo = new UserInfoBo();
		userInfoBo.setSellerNick(sellerNick);
		userInfoBo.setSellerId(sellerId);
		userInfoBo.setPlatformId(platformId);
		userInfoBo.setAppType(appType);
		return userInfoBo;
	}

	public UserRedisEntity toUserRedisEntity() {
		UserRedisEntity userRedisEntity = new UserRedisEntity();
		userRedisEntity.setTaobao_user_id(sellerId);
		userRedisEntity.setCorp_id(sellerId);
		userRedisEntity.setVipflag(vipflag.toString());
		userRedisEntity.setRoleid(roleId);
		userRedisEntity.setCreatedate(createDate);
		userRedisEntity.setIs_silent(isSilent);
		return userRedisEntity;
	}

	public UserInfoResponse toUserInfo() {
		UserInfoResponse userInfo = new UserInfoResponse();
		userInfo.setSellerNick(sellerNick);
		userInfo.setSellerId(sellerId);
		userInfo.setCorpId(sellerId);
		userInfo.setVipflag(vipflag);
		userInfo.setRoleId(roleId);
		userInfo.setHasNeedAuth(false);
		userInfo.setCreateDate(DateUtil.parseString(createDate));
		return userInfo;
	}

	public static LoginUserInfoBo of(LoginInfoRequest request) {
		LoginUserInfoBo loginUserInfoBo = new LoginUserInfoBo();
		loginUserInfoBo.setSellerNick(request.getSellerNick());
		loginUserInfoBo.setPlatformId(request.getPlatformId());
		loginUserInfoBo.setAppType(request.getApp());
		loginUserInfoBo.setSellerId(request.getSellerId());
		loginUserInfoBo.setVipflag(Integer.valueOf(request.getVipflag()));
		loginUserInfoBo.setLastIpAddress(request.getLastIpAddress());
		loginUserInfoBo.setRoleId(request.getRoleId());
		loginUserInfoBo.setLastUpdateTime(request.getLastUpdateTime());
		loginUserInfoBo.setSubDateTime(request.getSubDateTime());
		loginUserInfoBo.setIsSilent(request.getIsSilent());
		loginUserInfoBo.setCreateDate(request.getCreateDate());
		loginUserInfoBo.setLastActivedt(request.getLastActivedt());
		loginUserInfoBo.setCreateIpAddress(request.getCreateIpAddress());
		loginUserInfoBo.setLastActivePoint(request.getLastActivePoint());
		return loginUserInfoBo;
	}
}
