package cn.loveapp.uac.common.consumer;

import cn.loveapp.uac.common.constant.MqUserPropertyConstant;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * @Author: zhong<PERSON><PERSON>e
 * @Date: 2022/1/18 15:59
 * @Description: 消息内容包装类
 */
@Data
public class AiyongMessageExt {
	/**
	 * 消息内容本体
	 */
	private String content;

	/**
	 * RocketMQ消息的topic
	 */
	private String topic;

	/**
	 * RocketMQ消息的tag
	 */
	private String tag;

	/**
	 * RocketMQ消息的key
	 */
	private String key;

	/**
	 * RocketMQ消息的userProperties
	 */
	private Map<String, String> userProperties;

	/**
	 * RocketMQ消息的msgId
	 */
	private String msgId;

	/**
	 * RocketMQ消息的transactionId
	 */
	private String transactionId;

	public AiyongMessageExt() {}

	public AiyongMessageExt(String content, boolean forceHandleFlag) {
		this.content = content;
		this.setForceHandleFlag(forceHandleFlag);
	}

	public String getUserProperty(String key) {
		return this.userProperties == null ? null : this.userProperties.get(key);
	}

	public void setUserProperty(String key, String value) {
		if (this.userProperties == null) {
			this.userProperties = Maps.newHashMap();
		}
		this.userProperties.put(key, value);
	}

	public void setForceHandleFlag(Boolean forceHandleFlag) {
		this.setUserProperty(MqUserPropertyConstant.FORCE_HANDLE_FLAG_FIELD, forceHandleFlag.toString());
	}

	public boolean getForceHandleFlag() {
		String forceHandleFlag = this.getUserProperty(MqUserPropertyConstant.FORCE_HANDLE_FLAG_FIELD);
		return "true".equals(forceHandleFlag);
	}

    /**
     * 获取当前业务消息重新发送次数
     * @return
     */
    public int getResendTimes() {
        String resendTimes = this.getUserProperty(MqUserPropertyConstant.RESEND_TIMES_FIELD);
        if (StringUtils.isNotEmpty(resendTimes)) {
            return Integer.valueOf(resendTimes);
        } else {
            return -1;
        }
    }
}
