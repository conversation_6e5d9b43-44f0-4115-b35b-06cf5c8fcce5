package cn.loveapp.uac.common.config.redis;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/7/12 18:40
 * @Description: 缓存超时时间配置
 */
@Configuration
@Data
public class CacheTimeoutConfig {

    /**
     * redis 用户设置缓存超时时间（秒）
     * 默认7天
     */
    @Value("${uac.redis.usersettings.timeout:604800}")
    private long userSettingsCacheTimeout;
}
