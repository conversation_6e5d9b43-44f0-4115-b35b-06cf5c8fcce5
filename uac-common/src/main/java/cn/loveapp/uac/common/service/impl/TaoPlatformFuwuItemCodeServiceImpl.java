package cn.loveapp.uac.common.service.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.common.service.PlatformFuwuItemCodeService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

/**
 * 淘宝平台 item_code 相关信息
 *
 * <AUTHOR>
 */
@Data
@Configuration
public class TaoPlatformFuwuItemCodeServiceImpl implements PlatformFuwuItemCodeService {

    /**
     * 爱用交易 itemCode -> vipflag 的map
     */
    @Value("#{${uac.taobao.fuwu.trade.itemCodeToVipFlag.map}}")
    private Map<String, Integer> tradeItemCodeToVipFlagMap = new HashMap<>();

    /**
     * 爱用商品 itemCode -> vipflag 的map
     */
    @Value("#{${uac.taobao.fuwu.item.itemCodeToVipFlag.map}}")
    private Map<String, Integer> itemItemCodeToVipFlagMap = new HashMap<>();

    /**
     * appName -> 自动续费 item_code map
     */
    @Value("#{${uac.taobao.fuwu.apps.autoRenewItemCode.map}}")
    private Map<String, String> appsAutoRenewItemCodeMap = new HashMap<>();

    @Override
    public Integer obtainVipFlag(String itemCode, String platformId, String appName) {
        if (CommonAppConstants.APP_TRADE.equals(appName)) {
            return tradeItemCodeToVipFlagMap.get(itemCode);
        } else if (CommonAppConstants.APP_ITEM.equals(appName)) {
            return itemItemCodeToVipFlagMap.get(itemCode);
        }
        return null;
    }

    @Override
    public String obtainItemCode(Integer vipFlag, String platformId, String appName) {
        if(UserProductInfo.LEVEL_FOUR.equals(vipFlag)) {
            vipFlag = UserProductInfo.LEVEL_ZERO;
        }
        Stream<Map.Entry<String, Integer>> stream = null;
        if (CommonAppConstants.APP_TRADE.equals(appName)) {
            stream = tradeItemCodeToVipFlagMap.entrySet().stream();
        } else if (CommonAppConstants.APP_ITEM.equals(appName)) {
            stream = itemItemCodeToVipFlagMap.entrySet().stream();
        }
        if(stream == null){
            return null;
        }
        Integer fVipFlag = vipFlag;
        return stream.filter(e->fVipFlag.equals(e.getValue())).map(Map.Entry::getKey).findFirst().orElse(null);
    }

    @Override
    public String obtainAutoRenewItemCode(String platformId, String appName) {
        return appsAutoRenewItemCodeMap.get(appName);
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_TAO;
    }
}
