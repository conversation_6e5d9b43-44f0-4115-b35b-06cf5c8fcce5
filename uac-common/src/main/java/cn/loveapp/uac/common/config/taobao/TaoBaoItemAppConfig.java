package cn.loveapp.uac.common.config.taobao;

import cn.loveapp.uac.common.config.AppConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @program: uac-service-group
 * @description: TaobaoItemAppConfig
 * @author: Jason
 * @create: 2020-03-09 19:05
 **/
@Configuration
@Data
@ConfigurationProperties(prefix = "uac.taobao.item.app")
public class TaoBaoItemAppConfig extends AppConfig {
	private String rebuildUserUrl;
	private String aesDecryptUrl;
	private String taobaoReportServerUrl;
	private String taobaoServerUrl;
	private String taobaoBatchServerUrl;
}
