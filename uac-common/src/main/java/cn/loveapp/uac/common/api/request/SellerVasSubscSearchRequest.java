package cn.loveapp.uac.common.api.request;

import java.util.Date;
import lombok.Data;

/**
 * @program: uac-service-group
 * @description: SellerVasSubscSearchRequest
 * @author: <PERSON>
 * @create: 2020-03-06 10:25
 **/
@Data
public class SellerVasSubscSearchRequest {
	private String articleCode;
	private Boolean autosub;
	private Date endDeadline;
	private Boolean expireNotice;
	private String itemCode;
	private String sellerNick;
	private Long pageNo;
	private Long pageSize;
	private Date startDeadline;
	private Long status;
}
