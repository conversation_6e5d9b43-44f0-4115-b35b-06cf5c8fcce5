package cn.loveapp.uac.common.platform.api.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.jd.JdAuthorizationTokenResponse;
import cn.loveapp.common.platformsdk.jd.JdSDKService;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.config.DistributeConfig;
import cn.loveapp.uac.common.config.jd.JdTradeERPAppConfig;
import cn.loveapp.uac.common.platform.api.AuthService;
import cn.loveapp.uac.common.platform.api.domain.RefreshTokenCallbackResult;
import cn.loveapp.uac.common.utils.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.ImmutableMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 京东授权相关service
 *
 * <AUTHOR>
 * @date 2025/2/7
 */
@Service
public class JdAuthServiceImpl extends BaseAuthServiceImpl implements AuthService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(JdAuthServiceImpl.class);

	@Value("${uac.network.retry.count:5}")
	private Integer retryCount;

	@Autowired
	private JdSDKService jdSDKService;

	public JdAuthServiceImpl(JdTradeERPAppConfig appConfig, DistributeConfig distributeConfig) {
		super(ImmutableMap.of(ALL_APP, appConfig), distributeConfig);
	}

	@Override
	public Integer getRetryCount() {
		return retryCount;
	}

	/**
	 * 通过code获取callbackResult
	 */
	@Override
	@SuppressWarnings("unchecked")
	public RefreshTokenCallbackResult getCallbackResultByCode(String code, String platformId, String appName) throws Exception {
		return null;
	}

	/**
	 * 刷新refrshToken
	 */
	@Override
	@SuppressWarnings("unchecked")
	public RefreshTokenCallbackResult refreshToken(UserInfoBo userInfoBo, String refreshToken, String platformId, String appName) throws Exception {
		try {
            JdAuthorizationTokenResponse response = jdSDKService.refreshToken(refreshToken, appName);
			if(response == null || response.getAccessToken() == null){
				LOGGER.logError(userInfoBo.getSellerNick(), "-", "refresh_token刷新返回信息异常 result => " + JSON.toJSONString(response, "yyyy-MM-dd HH:mm:ss"));
				return null;
			}
			RefreshTokenCallbackResult callbackResult = new RefreshTokenCallbackResult();
			String encryptAccessToken = encryptToken(response.getAccessToken(), platformId, ALL_APP);
			callbackResult.setAccessToken(encryptAccessToken);
			callbackResult.setDecryptAccessToken(response.getAccessToken());
			String encryptRefreshToken = encryptToken(response.getRefreshToken(), platformId, ALL_APP);
			callbackResult.setRefreshToken(encryptRefreshToken);
			callbackResult.setDecryptRefreshToken(response.getRefreshToken());
			callbackResult.setExpiresIn(response.getExpiresIn());
			callbackResult.setSellerId(userInfoBo.getSellerId());
			callbackResult.setSellerNick(userInfoBo.getSellerNick());
			callbackResult.setSubSellerNick(userInfoBo.getSubSellerNick());
            callbackResult.setAuthDeadLine(
                DateUtil.calculateCustomSecond(DateUtil.currentDate(), response.getExpiresIn()));
			return callbackResult;

		} catch (Exception e) {
			LOGGER.logError(userInfoBo.getSellerNick(), "-", "refresh_token刷新出现异常: " + e.getMessage(), e);
			return null;
		}
	}

	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_JD;
	}
}
