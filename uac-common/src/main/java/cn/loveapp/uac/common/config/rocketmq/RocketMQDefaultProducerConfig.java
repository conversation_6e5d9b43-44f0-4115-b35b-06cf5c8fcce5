package cn.loveapp.uac.common.config.rocketmq;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * mq默认生产者配置
 * @program: orders-services-group
 * @description: DefaultMqProducerConfig
 * @author: <PERSON>
 * @create: 2019-11-18 21:30
 **/
@Component
@ConfigurationProperties(prefix = "rocketmq.default.producer.config")
@Data
public class RocketMQDefaultProducerConfig extends BaseRocketMQDefaultConfig {
	/**
	 * 运行时间
	 */
	private int runTimeSwitch = 0;

	/**
	 * 是否使用标签
	 */
	private boolean useTag;
}
