package cn.loveapp.uac.common.platform.api.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.ali1688.Ali1688SDKService;
import cn.loveapp.common.utils.AesUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.config.DistributeConfig;
import cn.loveapp.uac.common.config.ali1688.Ali1688AppConfig;
import cn.loveapp.uac.common.config.ali1688.Ali1688DistributeAppConfig;
import cn.loveapp.uac.common.platform.api.AuthService;
import cn.loveapp.uac.common.platform.api.domain.RefreshTokenCallbackResult;
import cn.loveapp.uac.common.service.DistributeUserProcessService;
import cn.loveapp.uac.common.utils.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.ocean.rawsdk.client.entity.AuthorizationToken;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.IllegalBlockSizeException;

/**
 * 1688授权相关service
 *
 * <AUTHOR>
 * @date 2021/7/5
 */
@Service
public class Ali1688AuthServiceImpl extends BaseAuthServiceImpl implements AuthService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(Ali1688AuthServiceImpl.class);

	@Value("${uac.network.retry.count:5}")
	private Integer retryCount;

    /**
     * 1688平台 refreshToken失效异常错误信息
     */
    private static final String ALI1688_REFRESHTOKEN_EXPIRE_ERR_MSG = "Unknow message defined in response.";

	@Autowired
	private Ali1688SDKService ali1688SDKService;

	@Autowired
	private DistributeUserProcessService distributeUserProcessService;


    public Ali1688AuthServiceImpl(Ali1688AppConfig ali1688AppConfig,
        Ali1688DistributeAppConfig ali1688DistributeAppConfig, DistributeConfig distributeConfig) {
        super(ImmutableMap.of(ALL_APP, ali1688AppConfig, CommonAppConstants.APP_DISTRIBUTE, ali1688DistributeAppConfig),
            distributeConfig);
    }

	@Override
	public Integer getRetryCount() {
		return retryCount;
	}

	/**
	 * 通过code获取callbackResult
	 */
	@Override
	@SuppressWarnings("unchecked")
	public RefreshTokenCallbackResult getCallbackResultByCode(String code, String platformId, String appName) throws Exception {
		return null;
	}

	/**
	 * 刷新refrshToken
	 */
	@Override
	@SuppressWarnings("unchecked")
	public RefreshTokenCallbackResult refreshToken(UserInfoBo userInfoBo, String refreshToken, String platformId, String appName) throws Exception {
		try {
			AuthorizationToken authorizationToken = ali1688SDKService.refreshToken(refreshToken, appName);
			if(authorizationToken == null || StringUtils.isEmpty(authorizationToken.getAccess_token())){
				LOGGER.logError(userInfoBo.getSellerNick(), "-", "refresh_token刷新返回信息异常 result => " + JSON.toJSONString(authorizationToken, "yyyy-MM-dd HH:mm:ss"));
				return null;
			}
			LOGGER.logInfo(userInfoBo.getSellerNick(), "", "1688刷新token响应信息： " + JSON.toJSONString(authorizationToken));
			RefreshTokenCallbackResult callbackResult = new RefreshTokenCallbackResult();

			String encryptAccessToken = encryptToken(authorizationToken.getAccess_token(), platformId, appName);
			String encryptRefreshToken = encryptToken(authorizationToken.getRefresh_token(), platformId, appName);

			callbackResult.setDecryptAccessToken(authorizationToken.getAccess_token());
			callbackResult.setDecryptRefreshToken(authorizationToken.getRefresh_token());
			callbackResult.setAccessToken(encryptAccessToken);
			callbackResult.setRefreshToken(encryptRefreshToken);

			callbackResult.setExpiresIn(authorizationToken.getExpires_in());
			callbackResult.setSellerId(userInfoBo.getSellerId());
			callbackResult.setSellerNick(userInfoBo.getSellerNick());
			callbackResult.setSubSellerNick(userInfoBo.getSubSellerNick());
            callbackResult.setAuthDeadLine(
                DateUtil.calculateCustomSecond(DateUtil.currentDate(), authorizationToken.getExpires_in()));

			return callbackResult;

		} catch (Exception e) {
            if (e.getCause() != null && ALI1688_REFRESHTOKEN_EXPIRE_ERR_MSG.equals(e.getCause().getMessage())) {
                //refreshToken 过期时响应的错误  此错误仅代表refreshToken过期
                LOGGER.logError(userInfoBo.getSellerNick(), "-", "refresh_token:[" + refreshToken + "]过期，无法刷新: " + e.getMessage(), e);
            }else{
                LOGGER.logError(userInfoBo.getSellerNick(), "-", "refresh_token:[" + refreshToken + "]刷新出现异常: " + e.getMessage(), e);
            }
			return null;
		}
	}

    @Override
    public String decryptToken(String token, String platformId, String appName) throws Exception {
        if (org.springframework.util.StringUtils.isEmpty(token)) {
            return token;
        } else {
            int maxTopSessionLen = 81;
            String session = null;
            /*6或7开头，直接返回 //长度小于80 无需解码*/
            if (token.length() < maxTopSessionLen) {
                // 1688 代发长度小于80特殊处理
                session = distributeUserProcessService.decryptForSession(token,
                    getActualConfig(appName).getSessionkey(), platformId, appName);
                if (StringUtils.isEmpty(session)) {
                    session = token;
                }

                return session;
            } else {
                AesUtil aesUtil = AesUtil.getInstance();
                try {
                    /*返回解密后accessTokenKey*/
                    session = aesUtil.aesDecryptForSession(token, getActualConfig(appName).getSessionkey());
                    return session;
                } catch (IllegalBlockSizeException e) {
                    // 尝试过滤特殊字符
                    token = token.replaceAll("\\\\r", "");
                    token = token.replaceAll("\\\\n", "");
                    token = token.replaceAll("\\n", "");
                    token = token.replaceAll("\\r", "");
                    session = aesUtil.aesDecryptForSession(token, getActualConfig(appName).getSessionkey());
                    return session;
                }
            }
        }
    }

    @Override
    public String encryptToken(String token, String platformId, String appName) throws Exception {
        if (org.springframework.util.StringUtils.isEmpty(token)) {
            return null;
        }
        AesUtil aesUtil = AesUtil.getInstance();
        String session = null;
        session = distributeUserProcessService.encryptForSession(token, getActualConfig(appName).getSessionkey(),
            platformId, appName);
        if (StringUtils.isEmpty(session)) {
            session = aesUtil.aesEncryptForSession(token, getActualConfig(appName).getSessionkey());
        }

        return session;
    }



	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_1688;
	}
}
