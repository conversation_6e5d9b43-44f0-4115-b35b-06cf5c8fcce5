package cn.loveapp.uac.common.config.rocketmq;

import lombok.Data;

/**
 * 消息队列配置抽象类
 * @program: orders-services-group
 * @description: RocketMQDefaultConfig
 * @author: <PERSON>
 * @create: 2019-11-19 14:11
 **/
@Data
public abstract class BaseRocketMQDefaultConfig {
	/**
	 * 主题
	 */
	private String topic;

	/**
	 * 标签
	 */
	private String tag;

	/**
	 * 生产者id
	 */
	private String producerId;

	/**
	 * 消费者id
	 */
	private String consumerId;

	/**
	 * 最大线程数量
	 */
	private int maxThreadNum;
}
