package cn.loveapp.uac.common.entity.redis;

import java.time.LocalDateTime;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.constant.CommonConstant;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.common.utils.DateUtil;
import cn.loveapp.uac.common.utils.MathUtil;
import lombok.Data;
import lombok.ToString;

/**
 * @program: uac-service-group
 * @description: RedisUserEntity
 * @author: Jason
 * @create: 2020-03-04 19:23
 **/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = Include.NON_EMPTY)
@ToString
public class UserRedisEntity {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(UserRedisEntity.class);

	private String downgrade_tag;
	private String smsdispatch;
	private String memoSet;
	private String trade_access_token_m;
	private String item_access_token_mp;
	private String shophelper_access_token_mp;
	private String tsdz;
	private String table_id;
	private String tag;
	private String smsbad;
	private String smsurgerate;
	private String isOpenTmc;
	private String tradeCardType;
	private String smsstar;
	private String h;
	private String vipflag;
	private String taobao_user_id;
	private String revival_date_new;
	private String vipuser;
	private String smssend;
	private String save_num;
	private String last_way;
	private String createdate;
	private String reminder_push;
	private String searchDbId;
	private String trade_uid;
	private String corp_id;
	private String roleid;
	private String trade_refresh_token_m;
	private String item_refresh_token_mp;
	private String shophelper_refresh_token_mp;
	private String process_status;
	private String list_id;
	private String history_rate_mark_new;
	private String save_vipinfo_status;
	private String shop_name;
	private String w1_deadline;
	/**
	 * 保存的refresh到期时间
	 */
	private String w2Deadline;
	/**
	 * 授权变更或订购变更时，上次授权到期时间(淘宝)
	 */
	private String last_w1_deadline;
	private String smsgood;
	private String is_silent;
	private String online;
	private String dbId;
	private String newUserHasLogin;
	private String offline;
	private String autorate;
	private String smswl;
	private String smssecond;
	private String smscare;
	private String dbStatus;
	private String is_needauth;
	private String user_info;
	private String smspay;
	private String vip_time;
	private String upvip_time;
	private String getshopname_time;
	private String save_vipinfo_status_last_time;
	private String last_paid_time;

	/**
	 * 供货商id
	 */
	private String supplier_id;

	/**
	 * 供货商nick
	 */
	private String supplier_name;

	/**
	 * pdd实例结构
	 * @return
	 */
	private String pddOwnerId;
	private String pddOwnerName;
	private String pddAccessToken;
	private String pddRefreshToken;
	private String pddAuthDeadLine;

	/* 新的应用统一的redis key */
	private String user_id;
	private String user_nick;
	private String order_cycle_end;
	/**
	 * 授权变更或订购变更时, 上次订购到期时间
	 */
	private String last_order_cycle_end;
	private String access_token;
	private String refresh_token;
	private String auth_dead_line;
	/**
	 * 授权变更或订购变更时, 上次授权到期时间(多平台)
	 */
	private String last_auth_deadline;
	private String mall_name;

    /* ---- 必要 -----*/
    /**
     * 客户id (必要)
     */
    private String app_id;

    /**
     * 客户秘钥 (必要)
     */
    private String app_secret;

	/**
	 * 代发快手
	 */
	private String ks_access_token;
	private String ks_refresh_token;

	/**
	 * 多店tag
	 */
	private String ayMultiTags;

    /**
     * 店铺群tag
     */
    private String shops_tag;

    /**
     * 授权是否异常(0 代表正常 1 代表授权异常)
     */
    private String is_auth_except;

	@JsonIgnore
	public LocalDateTime getW1Deadline() {
		try {
			return DateUtil.parseString(w1_deadline);
		} catch (DateTimeParseException e) {
			return null;
		}
	}

	@JsonIgnore
	public LocalDateTime getW2DeadlineCache() {
		try {
			return DateUtil.parseString(w2Deadline);
		} catch (DateTimeParseException e) {
			return null;
		}
	}

	@JsonIgnore
	public Boolean getIsNeedauth() {
		if (StringUtils.isEmpty(is_needauth)) {
			return Boolean.FALSE;
		}
		return BooleanUtils.toBooleanObject(Integer.parseInt(is_needauth));
	}

    @JsonIgnore
    public Boolean getIsAuthExcept() {
        if (StringUtils.isEmpty(is_auth_except)) {
            return Boolean.FALSE;
        }
        return BooleanUtils.toBooleanObject(Integer.parseInt(is_auth_except));
    }

	@JsonIgnore
	public Integer getVipflagCache() {
		return MathUtil.parseString(vipflag);
	}

	@JsonIgnore
	public LocalDateTime getVipTime() {
		return DateUtil.parseString(vip_time);
	}

	@JsonIgnore
	public LocalDateTime getOrderCycleEnd() {
		return DateUtil.parseString(order_cycle_end);
	}

	@JsonIgnore
	public LocalDateTime getUpvipTime() {
		return DateUtil.parseString(upvip_time);
	}

	@JsonIgnore
	public LocalDateTime getGetshopnameTime() {
		return DateUtil.parseString(getshopname_time);
	}

	@JsonIgnore
	public LocalDateTime getSaveVipinfoStatusLastTime() {
		return DateUtil.parseString(save_vipinfo_status_last_time);
	}

	@JsonIgnore
	public LocalDateTime getLastPaidTime() {
		return DateUtil.parseString(last_paid_time);
	}

	@JsonIgnore
	public LocalDateTime getPddAuthDeadLineConvert() {
		return DateUtil.parseString(pddAuthDeadLine);
	}

	@JsonIgnore
	public LocalDateTime getCreateDateTime() {
		return DateUtil.parseString(createdate);
	}

	@JsonIgnore
	public LocalDateTime getAuthDeadLine() {
		return DateUtil.parseString(auth_dead_line);
	}

	@JsonIgnore
	public LocalDateTime getLastW1Deadline() {
		return DateUtil.parseString(last_w1_deadline);
	}

	@JsonIgnore
	public LocalDateTime getlastAuthDeadline() {
		return DateUtil.parseString(last_auth_deadline);
	}

	@JsonIgnore
	public LocalDateTime getlastOrderCycleEnd() {
		return DateUtil.parseString(last_order_cycle_end);
	}

	@JsonIgnore
    public Boolean isBlank(String platformId, String appType) {
        try {
            // 判断ayMultiTag是否为空则重建
            if (ayMultiTags == null) {
                return true;
            }

            // 淘宝老应用判空逻辑
            if (CommonPlatformConstants.PLATFORM_TAO.equals(platformId)) {
                if (CommonConstant.OLD_APP_NAME_LIST.contains(appType)) {
                    if (StringUtils.isAnyBlank(taobao_user_id, order_cycle_end, w1_deadline)) {
                        return true;
                    }

                    // 淘宝爱用交易 redis key 特殊处理
                    if (CommonAppConstants.APP_TRADE.equals(appType)) {
                        if (StringUtils.isAnyBlank(trade_access_token_m, trade_refresh_token_m)) {
                            return true;
                        }
                    } else if (CommonAppConstants.APP_ITEM.equals(appType)) {
                        // 淘宝爱用商品 redis key 特殊处理
                        if (StringUtils.isAnyBlank(item_access_token_mp, item_refresh_token_mp)) {
                            return true;
                        }
                    } else if (CommonAppConstants.APP_SHOP_HELPER.equals(appType)) {
                        // 淘宝开店助手 redis key 特殊处理
                        if (StringUtils.isAnyBlank(shophelper_access_token_mp, shophelper_refresh_token_mp)) {
                            return true;
                        }
                    }

                    DateUtil.parseDateString(w1_deadline);
                    DateUtil.parseDateString(order_cycle_end);
                    return false;
                }
            }

            // 代发应用 redis特殊处理
            if (CommonAppConstants.APP_DISTRIBUTE.equals(appType)) {
                if (StringUtils.isEmpty(user_nick)) {
                    return true;
                }

                if (StringUtils.isAllEmpty(pddAuthDeadLine, auth_dead_line)) {
                    return true;
                }

                if (StringUtils.isNotBlank(order_cycle_end)) {
                    // 不为空验证格式
                    DateUtil.parseDateString(order_cycle_end);
                }

                if (auth_dead_line == null) {
                    DateUtil.parseDateString(pddAuthDeadLine);
                } else {
                    DateUtil.parseDateString(auth_dead_line);
                }

                return false;
            } else {
                if (StringUtils.isAllEmpty(pddAuthDeadLine, auth_dead_line) || StringUtils.isBlank(order_cycle_end)) {
                    return true;
                }

                if (auth_dead_line == null) {
                    DateUtil.parseDateString(pddAuthDeadLine);
                } else {
                    DateUtil.parseDateString(auth_dead_line);
                }

                DateUtil.parseDateString(order_cycle_end);
                return false;
            }
        } catch (Exception e) {
            LOGGER.logError(e.getMessage(), e);
            return true;
        }
    }

	public String getAyMultiTags(){
		return this.ayMultiTags;
	}

	public List<String> generalAyMultiTagList(){
		if (this.ayMultiTags == null || UserProductInfo.SEPARATOR.equals(this.ayMultiTags)) {
			return new ArrayList<>();
		}
		return Arrays.asList(StringUtils.split(this.ayMultiTags, UserProductInfo.SEPARATOR));
	}
}
