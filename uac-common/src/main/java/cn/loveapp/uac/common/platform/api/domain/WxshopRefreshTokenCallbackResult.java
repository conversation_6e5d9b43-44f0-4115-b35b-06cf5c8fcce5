package cn.loveapp.uac.common.platform.api.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 快手平台 用长时令牌refreshToken刷新访问令牌accessToken 返回的信息接口
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WxshopRefreshTokenCallbackResult extends RefreshTokenCallbackResult {

    private static final String RESULT_SUCCESS = "1";

    /**
     * 返回结果类型，1为正确，其他为不正确
     */
    @JSONField(name = "result")
    private String result;

    /**
     * 临时访问令牌，作为调用授权API时的入参，过期时间为expires_in值，授权用户、app和权限组范围唯一决定一个access_token值
     */
    @JSONField(name = "access_token")
    private String accessToken;

    /**
     * access_token过期时间，单位秒，默认为172800，即48小时
     */
    @JSONField(name = "expires_in")
    private Long expiresIn;

    /**
     *长时访问令牌，默认为30天，会返回新的refresh_token，原有的refresh_token即失效
     */
    @JSONField(name = "refresh_token")
    private String refreshToken;

    /**
     * refresh_token 的过期时间，单位秒，默认为2592000，即30天
     */
    @JSONField(name = "refresh_token_expires_in")
    private Long refreshTokenExpiresIn;

    /**
     * access_token包含的scope
     */
    @JSONField(name = "scopes")
    private List<String> scope;

    /**
     * 错误信息
     */
    @JSONField(name = "error")
    private String error;

    /**
     * 错误message
     */
    @JSONField(name = "error_msg")
    private String errorMsg;

    public boolean isSuccess() {
        return RESULT_SUCCESS.equals(result) || StringUtils.isNotBlank(accessToken);
    }
}
