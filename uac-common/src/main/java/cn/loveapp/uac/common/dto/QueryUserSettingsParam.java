package cn.loveapp.uac.common.dto;

import lombok.Data;

import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/7/13 12:54
 * @Description: 查询用户设置-入参数据对象
 */
@Data
public class QueryUserSettingsParam {

    /**
     * 设置名（单个查询用）
     */
    private String settingKey;

    /**
     * 设置名列表（批量查询用）
     */
    private List<String> settingKeys;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 平台id
     */
    private String platformId;

    /**
     * 应用
     */
    private String appName;

    /**
     * 是否读取默认设置
     */
    private Boolean loadDefaultSetting = Boolean.TRUE;
}
