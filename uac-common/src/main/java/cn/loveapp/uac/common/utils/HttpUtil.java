package cn.loveapp.uac.common.utils;

import cn.loveapp.common.utils.LoggerHelper;
import com.alibaba.fastjson2.JSON;
import org.apache.commons.collections.MapUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: uac-service-group
 * @description: HttpUtil
 * @author: Jason
 * @create: 2020-03-13 15:00
 **/
public class HttpUtil {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(HttpUtil.class);

	public static String sendJsonPost(String url, HashMap<String,String> postFields, Map<String, String> headers) throws IOException {
		if(StringUtils.isEmpty(url)){
			throw new IOException("缺少必要参数 url");
		}

		HttpPost request = new HttpPost(url);
		request.setEntity(new StringEntity(JSON.toJSONString(postFields)));

		RequestConfig.Builder requestConfigBuilder = RequestConfig.copy(RequestConfig.DEFAULT);

		requestConfigBuilder.setConnectionRequestTimeout(10 * 1000);
		requestConfigBuilder.setSocketTimeout(10 * 1000);
		requestConfigBuilder.setConnectTimeout(10 * 1000);

		//设置请求配置
		request.setConfig(requestConfigBuilder.build());

		if (!MapUtils.isEmpty(headers)) {
			for(Map.Entry<String, String> entry: headers.entrySet()) {
				request.addHeader(entry.getKey(), entry.getValue());
			}
		}

		CloseableHttpClient httpclient = null;
		HttpResponse resp = null;
		try {
			httpclient = HttpClients.createDefault();
			resp = httpclient.execute(request);
			String responseBody = EntityUtils.toString(resp.getEntity());
			return responseBody;
		} finally {
			try {
				if(resp!=null){
					((CloseableHttpResponse) resp).close();
				}
				if(httpclient!=null){
					httpclient.close();
				}
			} catch (IOException e) {
			}
		}
	}

}
