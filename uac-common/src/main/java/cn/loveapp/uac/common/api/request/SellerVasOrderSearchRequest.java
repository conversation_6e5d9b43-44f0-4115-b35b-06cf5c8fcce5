package cn.loveapp.uac.common.api.request;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/10/9$ 14:13$
 * taobao.vas.order.search 淘宝接口请求体
 */
@Data
public class SellerVasOrderSearchRequest {
    /**
     * 应用收费代码，从合作伙伴后台（my.open.taobao.com）-收费管理-收费项目列表 能够获得该应用的收费代码
     */
    private String articleCode;
    /**
     * 收费项目代码，从合作伙伴后台（my.open.taobao.com）-收费管理-收费项目列表 能够获得收费项目代码
     */
    private String itemCode;
    /**
     * 淘宝会员名
     */
    private String nick;
    /**
     * 订单创建时间（订购时间）起始值（当start_created和end_created都不填写时，默认返回最近90天的数据）
     */
    private Date startCreated;
    /**
     * 订单创建时间（订购时间）结束值
     */
    private Date endCreated;
    /**
     * 订单类型，1=新订 2=续订 3=升级 4=后台赠送 5=后台自动续订 6=订单审核后生成订购关系（暂时用不到） 空=全部
     */
    private Long bizType;
    /**
     * 订单号
     */
    private Long bizOrderId;
    /**
     * 子订单号
     */
    private Long orderId;
    /**
     * 一页包含的记录数
     */
    private Long pageSize;
    /**
     * 页码
     */
    private Long pageNo;
}

