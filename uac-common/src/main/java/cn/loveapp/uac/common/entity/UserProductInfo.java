package cn.loveapp.uac.common.entity;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.uac.common.entity.redis.UserRedisEntity;
import cn.loveapp.uac.common.utils.DateUtil;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.*;

/**
 * @program: uac-service-group
 * @description: UserProductInfo
 * @author: Jason
 * @create: 2020-03-09 14:36
 **/
@Data
@ToString
public class UserProductInfo {


	public static final String SEPARATOR = ",";

	/**
	 * 平台
	 */
	private String platformId;
	/**
	 * 应用
	 */
	private String appName;

	private Integer id;

	/**
	 * 用户数字id
	 */
	private String userId;

	/**
	 * 用户昵称
	 */
	private String nick;

	/**
	 * 用户第一次使用爱用交易的时间
	 */
	private LocalDateTime createDate;

	/**
	 * 最后一次活动时间，用触发器更新主表
	 */
	private LocalDateTime lastactivedt;

	/**
	 * 最近一次付款时间
	 */
	private LocalDateTime lastPaidTime;

	/**
	 * 用户登记
	 */
	private Integer vipflag;

	/**
	 * 创建用户时用的IP地址
	 */
	private String createipaddress;

	/**
	 * 最后一次IP地址
	 */
	private String lastipaddress;

	/**
	 * TOP 初级版、高级版都有这个字段 订购到期时间
	 */
	private LocalDateTime orderCycleEnd;

	/**
	 * 授权变更或订购变更时, 上次订购到期时间
	 */
	private LocalDateTime lastOrderCycleEnd;

	/**
	 * 高级版-加上赠送时间的最终到期时间 初级班-就是TOP到期时间
	 */
	private LocalDateTime subdatetime;

	/**
	 * TOP的Session
	 */
	private String topsessionkey;

	/**
	 * TOP的刷新Key
	 */
	private String toprefreshkey;

	/**
	 * 用于灰度发布时控制用户发布区域
	 */
	private String roleid;

	/**
	 * 0-非默认插件 1-默认插件
	 */
	private Integer isdefault;

	/**
	 * 本记录最后编辑的时间
	 */
	private LocalDateTime lastupdatetime;

	/**
	 * 累计PC登陆次数
	 */
	private Integer logincountPc;

	/**
	 * 累计手机登陆次数
	 */
	private Integer logincountMp;

	/**
	 * 30天内累计PC登陆次数
	 */
	private Integer mauPc;

	/**
	 * 30天内累计手机登陆次数
	 */
	private Integer mauMp;

	/**
	 * topsession更新时间
	 */
	private LocalDateTime topTime;

	/**
	 * H5/QAP/PC/PCWW
	 */
	private String lastactiveplatform;

	/**
	 * 最后活跃的版本号
	 */
	private String lastactivever;

	/**
	 * 0不是多店铺，1是多店铺
	 */
	private Integer isMany;

	/**
	 * 是否是主店铺 0 否，1是主店铺
	 */
	private Integer isMain;

	/**
	 * 主店铺id
	 */
	private String corpId;

	/**
	 * tmark打标
	 */
	private String mark;

	/**
	 * 是否沉默
	 */
	private Boolean isSilent;

	/**
	 * 是否需要授权字段
	 */
	private Integer isNeedauth;

	/**
	 * w1授权到期时间
	 */
	private LocalDateTime w1Deadline;

	/**
	 * 授权变更或订购变更时, 上次订购到期时间(淘宝)
	 */
	private LocalDateTime lastW1Deadline;

	/**
	 * w2授权到期时间
	 */
	private LocalDateTime w2Deadline;

	/**
	 * 沉默用户复活时间
	 */
	private LocalDateTime revivalDate;

	/**
	 * 用户id (同user_id 字符串形式)
	 */
	private String userIdStr;

	/**
	 * 店铺名称
	 */
	private String mallName;

	/**
	 * 累计旺旺插件打开测试
	 */
	private Integer logincountWw;

	/**
	 * 30天内累计旺旺插件登陆次数
	 */
	private Integer mauWw;

	/**
	 * 授权变更或订购变更时, 上次授权到期时间(多平台)
	 */
	private LocalDateTime lastAuthDeadLine;

	/**
	 * 1688才有的字段
	 */
	private String otherRoleId;
	private String memberId;

	private String tag;

	/**
	 * 供货商id
	 */
	private String supplierId;

	/**
	 * 供货商nick
	 */
	private String supplierNick;

	/**
	 * 客户id (必要)
	 */
	private String appId;

    /**
     * 客户秘钥 (必要)
     */
    private String appSecret;

	/**
	 * 店铺名称（代发快手）
	 */
	private String shopName;

	/**
	 * 店铺名称（代发视频号小店）
	 */
	private String userName;

    /**
     * 卖家地区（tiktok）
     */
    private String sellerBaseRegion;

    /**
     * 店铺id（tiktok）
     */
    private String shopId;

    /**
     * 店铺密码（tiktok）
     */
    private String shopCipher;

	/**
	 * 多店tag
	 */
	private String ayMultiTags;

    /**
     * 专业版到期时间
     */
    private LocalDateTime professionalOrderCycleEnd;

    /**
     * 授权是否异常(false 代表正常 true 代表授权异常)
     */
    private Boolean isAuthExcept;

    /**
     * 京东字段-uid
     */
    private String jdUid;
    /**
     * 京东字段-openId
     */
    private String jdOpenId;
    /**
     * 京东字段-xid
     */
    private String jdXid;
    /**
     * 京东字段-UserName
     */
    private String jdUserName;


	public static final String ROLE_B = "B";
	public static final String ROLE_C = "C";
	public static final String ROLE_H = "H";

	/**
	 * 初级版本
	 */
	public static final Integer LEVEL_ZERO = 0;

	/**
	 * 普通高级版本
	 */
	public static final Integer LEVEL_ONE = 1;

	/**
	 * 基础版
	 */
	public static final Integer LEVEL_TWO = 2;

	/**
	 * 自动续费版本
	 */
	public static final Integer LEVEL_THREE = 3;

	/**
	 * 新手村试用版本
	 */
	public static final Integer LEVEL_FOUR = 4;

	/**
	 * PDD试用版
	 */
	public static final Integer LEVEL_FIVE = 5;

	/**
	 * 专业版(旧 专业版+库存管理)
	 */
	public static final Integer LEVEL_SIX = 6;

    /**
     * 专业版(新 近6个月功能)
     */
    public static final Integer LEVEL_EIGHT = 8;


	public static final Integer IDENTITY_UNCHANGED = 0;
	public static final Integer IDENTITY_PRIMARY_TO_HIGH = 201;
	public static final Integer IDENTITY_PRIMARY_TO_PRIMARY = 202;
	public static final Integer IDENTITY_HIGH_TO_PRIMARY = 203;
	public static final Integer IDENTITY_HIGH_TO_HIGH = 204;

	public static final Integer NO_NEED_AUTH = 0;
	public static final Integer NEED_AUTH = 1;

	public static final String ACTIVE_POINT_PC = "pc";
	public static final String ACTIVE_POINT_WW = "ww";
	public static final String ACTIVE_POINT_H5 = "h5";
	public static final String ACTIVE_POINT_QAP_ANDROID = "qapAndroid";
	public static final String ACTIVE_POINT_QAP_IPHONE = "qapIphone";
	public static final String ACTIVE_POINT_MINIAPP_IPHONE = "miniappIphone";
	public static final String ACTIVE_POINT_MINIAPP_ANDROID = "miniappAndroid";

	public static UserProductInfo of(String sellerNick, String sellerId) {
		UserProductInfo userProductInfo = new UserProductInfo();
		userProductInfo.setNick(sellerNick);
		userProductInfo.setUserIdStr(sellerId);
		userProductInfo.setUserId(sellerId);
		return userProductInfo;
	}

	public static UserProductInfo of(String sellerNick, String sellerId, Integer hasNeedAuth) {
		UserProductInfo userProductInfo = new UserProductInfo();
		userProductInfo.setNick(sellerNick);
		userProductInfo.setUserIdStr(sellerId);
        try {
            userProductInfo.setUserId(sellerId);
        } catch (NumberFormatException e) {
        }
        userProductInfo.setIsNeedauth(hasNeedAuth);
		return userProductInfo;
	}

	public String getVipflagStr(){
		return vipflag == null ? null : vipflag.toString();
	}

	public void initDefault() {
		this.lastupdatetime = DateUtil.currentDate();
	}

	public List<String> getActivePointList() {
		return Arrays.asList(
			ACTIVE_POINT_H5, ACTIVE_POINT_MINIAPP_ANDROID,
			ACTIVE_POINT_MINIAPP_IPHONE, ACTIVE_POINT_PC,
			ACTIVE_POINT_QAP_ANDROID, ACTIVE_POINT_QAP_IPHONE,
			ACTIVE_POINT_WW);
	}

	public List<String> getRoleList() {
		return Arrays.asList(ROLE_B, ROLE_C, ROLE_H);
	}


	public void putLastActivePlatform(String lastactiveplatform) {
		if (StringUtils.isBlank(lastactiveplatform)) {
			return;
		}
		this.lastactiveplatform = lastactiveplatform;
		switch (lastactiveplatform) {
			case ACTIVE_POINT_PC:
				if (Objects.isNull(logincountPc)) {
					logincountPc = 1;
				} else {
					logincountPc += 1;
				}
				break;
			case ACTIVE_POINT_WW:
				if (Objects.isNull(logincountWw)) {
					logincountWw = 1;
				} else {
					logincountWw += 1;
				}
				break;
			case ACTIVE_POINT_H5:
			case ACTIVE_POINT_QAP_ANDROID:
			case ACTIVE_POINT_QAP_IPHONE:
			case ACTIVE_POINT_MINIAPP_IPHONE:
			case ACTIVE_POINT_MINIAPP_ANDROID:
				if (Objects.isNull(logincountMp)) {
					logincountMp = 1;
				} else {
					logincountMp += 1;
				}
				break;
			default:
				break;
		}
	}

	public UserRedisEntity toUserRedisEntity() {
        if (StringUtils.isAllEmpty(userIdStr, userId)) {
            throw new IllegalArgumentException("用户sellerId为空");
        }

        String sellerId = StringUtils.isBlank(userIdStr) ? userId : userIdStr;
		String hasNeedAtuh = isNeedauth == null ? "0" : isNeedauth.toString();
		String hasSilent = Objects.isNull(isSilent) ? "0" : "1";
		UserRedisEntity userRedisEntity = new UserRedisEntity();
		userRedisEntity.setTag(tag);
		userRedisEntity.setVipflag(getVipflagStr());
		userRedisEntity.setTaobao_user_id(sellerId);
		userRedisEntity.setCorp_id(corpId);
		userRedisEntity.setRoleid(roleid);
        if (CommonAppConstants.APP_ITEM.equals(appName)) {
            userRedisEntity.setItem_access_token_mp(topsessionkey);
            userRedisEntity.setItem_refresh_token_mp(toprefreshkey);
        } else if (CommonAppConstants.APP_SHOP_HELPER.equals(appName)) {
            userRedisEntity.setShophelper_access_token_mp(topsessionkey);
            userRedisEntity.setShophelper_refresh_token_mp(toprefreshkey);
        } else {
            userRedisEntity.setTrade_access_token_m(topsessionkey);
            userRedisEntity.setTrade_refresh_token_m(toprefreshkey);
        }

		userRedisEntity.setPddOwnerId(sellerId);
		userRedisEntity.setPddOwnerName(nick);
		userRedisEntity.setPddAccessToken(topsessionkey);
		userRedisEntity.setPddRefreshToken(toprefreshkey);
		userRedisEntity.setIs_silent(hasSilent);
		userRedisEntity.setIs_needauth(hasNeedAtuh);
        userRedisEntity.setApp_id(appId);
        userRedisEntity.setApp_secret(appSecret);
		userRedisEntity.setPddAuthDeadLine(DateUtil.convertLocalDateTimetoString(w1Deadline));
		userRedisEntity.setW1_deadline(DateUtil.convertLocalDateTimetoString(w1Deadline));
		userRedisEntity.setW2Deadline(DateUtil.convertLocalDateTimetoString(w2Deadline));
		userRedisEntity.setOrder_cycle_end(DateUtil.convertLocalDateTimetoString(orderCycleEnd));
		userRedisEntity.setLast_paid_time(DateUtil.convertLocalDateTimetoString(lastPaidTime));
		userRedisEntity.setRevival_date_new(DateUtil.convertLocalDateTimetoString(revivalDate));
		userRedisEntity.setCreatedate(DateUtil.convertLocalDateTimetoString(createDate));
		/** 业务Key **/
		userRedisEntity.setDowngrade_tag(null);
		userRedisEntity.setSmsdispatch(null);
		userRedisEntity.setMemoSet(null);
		userRedisEntity.setTsdz(null);
		userRedisEntity.setTable_id(null);
		userRedisEntity.setSmsbad(null);
		userRedisEntity.setSmsurgerate(null);
		userRedisEntity.setIsOpenTmc(null);
		userRedisEntity.setTradeCardType(null);
		userRedisEntity.setSmsstar(null);
		userRedisEntity.setH(null);
		userRedisEntity.setVipuser(null);
		userRedisEntity.setSmssend(null);
		userRedisEntity.setSave_num(null);
		userRedisEntity.setLast_way(null);
		userRedisEntity.setReminder_push(null);
		userRedisEntity.setSearchDbId(null);
		userRedisEntity.setTrade_uid(null);
		userRedisEntity.setProcess_status(null);
		userRedisEntity.setList_id(null);
		userRedisEntity.setHistory_rate_mark_new(null);
		userRedisEntity.setSave_vipinfo_status(null);
		userRedisEntity.setShop_name(null);
		userRedisEntity.setUpvip_time(null);
		userRedisEntity.setGetshopname_time(null);
		userRedisEntity.setSave_vipinfo_status_last_time(null);
		userRedisEntity.setUser_info(null);
		userRedisEntity.setSmspay(null);
		userRedisEntity.setVip_time(null);
		userRedisEntity.setOnline(null);
		userRedisEntity.setDbId(null);
		userRedisEntity.setNewUserHasLogin(null);
		userRedisEntity.setOffline(null);
		userRedisEntity.setAutorate(null);
		userRedisEntity.setSmswl(null);
		userRedisEntity.setSmssecond(null);
		userRedisEntity.setSmscare(null);
		userRedisEntity.setDbStatus(null);
		userRedisEntity.setSmsgood(null);
		return userRedisEntity;

	}

	public UserRedisEntity toPddUserRedisEntity() {
		String sellerId = StringUtils.isBlank(userIdStr) ? userId.toString() : userIdStr;
		UserRedisEntity userRedisEntity = new UserRedisEntity();
		userRedisEntity.setTag(tag);
        userRedisEntity.setUser_id(sellerId);
        userRedisEntity.setUser_nick(nick);
        userRedisEntity.setMall_name(mallName);
//        userRedisEntity.setAccess_token(topsessionkey);
//        userRedisEntity.setRefresh_token(toprefreshkey);
//        userRedisEntity.setCreatedate(DateUtil.convertLocalDateTimetoString(createDate));
//        userRedisEntity.setAuth_dead_line(DateUtil.convertLocalDateTimetoString(w1Deadline));
//        userRedisEntity.setOrder_cycle_end(DateUtil.convertLocalDateTimetoString(orderCycleEnd));
		userRedisEntity.setPddOwnerId(sellerId);
		userRedisEntity.setPddOwnerName(nick);
		userRedisEntity.setPddAccessToken(topsessionkey);
		userRedisEntity.setPddRefreshToken(toprefreshkey);
        userRedisEntity.setIs_needauth(isNeedauth == null ? "0" : isNeedauth.toString());
		userRedisEntity.setPddAuthDeadLine(DateUtil.convertLocalDateTimetoString(w1Deadline));
		userRedisEntity.setVipflag(getVipflagStr());
		/** 业务Key **/
		userRedisEntity.setDowngrade_tag(null);
		userRedisEntity.setSmsdispatch(null);
		userRedisEntity.setMemoSet(null);
		userRedisEntity.setTsdz(null);
		userRedisEntity.setTable_id(null);
		userRedisEntity.setSmsbad(null);
		userRedisEntity.setSmsurgerate(null);
		userRedisEntity.setIsOpenTmc(null);
		userRedisEntity.setTradeCardType(null);
		userRedisEntity.setSmsstar(null);
		userRedisEntity.setH(null);
		userRedisEntity.setVipuser(null);
		userRedisEntity.setSmssend(null);
		userRedisEntity.setSave_num(null);
		userRedisEntity.setLast_way(null);
		userRedisEntity.setReminder_push(null);
		userRedisEntity.setSearchDbId(null);
		userRedisEntity.setTrade_uid(null);
		userRedisEntity.setProcess_status(null);
		userRedisEntity.setList_id(null);
		userRedisEntity.setHistory_rate_mark_new(null);
		userRedisEntity.setSave_vipinfo_status(null);
		userRedisEntity.setShop_name(null);
		userRedisEntity.setUpvip_time(null);
		userRedisEntity.setGetshopname_time(null);
		userRedisEntity.setSave_vipinfo_status_last_time(null);
		userRedisEntity.setUser_info(null);
		userRedisEntity.setSmspay(null);
		userRedisEntity.setVip_time(null);
		userRedisEntity.setOnline(null);
		userRedisEntity.setDbId(null);
		userRedisEntity.setNewUserHasLogin(null);
		userRedisEntity.setOffline(null);
		userRedisEntity.setAutorate(null);
		userRedisEntity.setSmswl(null);
		userRedisEntity.setSmssecond(null);
		userRedisEntity.setSmscare(null);
		userRedisEntity.setDbStatus(null);
		userRedisEntity.setSmsgood(null);
		return userRedisEntity;

	}

	public UserRedisEntity toMultiPlatformRedisEntity() {
		UserRedisEntity userRedisEntity = new UserRedisEntity();
		String sellerId = StringUtils.isBlank(userIdStr) ? userId.toString() : userIdStr;

		userRedisEntity.setUser_id(sellerId);
		userRedisEntity.setUser_nick(nick);
		userRedisEntity.setAccess_token(topsessionkey);
		userRedisEntity.setRefresh_token(toprefreshkey);
		userRedisEntity.setCreatedate(DateUtil.convertLocalDateTimetoString(createDate));
		userRedisEntity.setAuth_dead_line(DateUtil.convertLocalDateTimetoString(w1Deadline));
		userRedisEntity.setVipflag(getVipflagStr());
		userRedisEntity.setMall_name(mallName);
		userRedisEntity.setOrder_cycle_end(DateUtil.convertLocalDateTimetoString(orderCycleEnd));
		userRedisEntity.setIs_needauth(isNeedauth == null ? "0" : isNeedauth.toString());
		userRedisEntity.setApp_id(appId);
		userRedisEntity.setApp_secret(appSecret);
        userRedisEntity.setIs_auth_except(BooleanUtils.isTrue(isAuthExcept) ? "1" : "0");
		return userRedisEntity;
	}

	public UserRedisEntity toDistributePlatformRedisEntity() {
		UserRedisEntity userRedisEntity = new UserRedisEntity();
		String sellerId = StringUtils.isBlank(userIdStr) ? userId.toString() : userIdStr;

		userRedisEntity.setUser_id(sellerId);
		userRedisEntity.setUser_nick(StringUtils.isBlank(nick) ? shopName : nick);
		userRedisEntity.setAccess_token(topsessionkey);
		userRedisEntity.setRefresh_token(toprefreshkey);
		userRedisEntity.setCreatedate(DateUtil.convertLocalDateTimetoString(createDate));
		userRedisEntity.setAuth_dead_line(DateUtil.convertLocalDateTimetoString(w1Deadline));
		userRedisEntity.setVipflag(getVipflagStr());
		userRedisEntity.setMall_name(mallName);
		userRedisEntity.setOrder_cycle_end(DateUtil.convertLocalDateTimetoString(orderCycleEnd));
		userRedisEntity.setIs_needauth(isNeedauth == null ? "0" : isNeedauth.toString());
		userRedisEntity.setApp_id(appId);
		userRedisEntity.setApp_secret(appSecret);
		userRedisEntity.setShop_name(shopName);
		return userRedisEntity;
	}


	public String getAyMultiTagStr(){
		return this.ayMultiTags;
	}

	public List<String> getAyMultiTags(){
		if (this.ayMultiTags == null) {
			return new ArrayList<>();
		}
		return Arrays.asList(StringUtils.split(this.ayMultiTags, SEPARATOR));
	}
}
