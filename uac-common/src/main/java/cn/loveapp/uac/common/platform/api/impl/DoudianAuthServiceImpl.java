package cn.loveapp.uac.common.platform.api.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.doudian.DoudianSDKService;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.config.DistributeConfig;
import cn.loveapp.uac.common.config.doudian.DoudianAppConfig;
import cn.loveapp.uac.common.platform.api.AuthService;
import cn.loveapp.uac.common.platform.api.domain.RefreshTokenCallbackResult;
import cn.loveapp.uac.common.utils.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.doudian.api.domain.AuthorizationToken;
import com.doudian.api.response.AuthorizationTokenResponse;
import com.google.common.collect.ImmutableMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 抖店授权相关service
 *
 * <AUTHOR>
 * @date 2021/7/7
 */
@Service
public class DoudianAuthServiceImpl extends BaseAuthServiceImpl implements AuthService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(DoudianAuthServiceImpl.class);

	@Value("${uac.network.retry.count:5}")
	private Integer retryCount;

	@Autowired
	private DoudianSDKService doudianSDKService;

	public DoudianAuthServiceImpl(DoudianAppConfig appConfig, DistributeConfig distributeConfig) {
		super(ImmutableMap.of(ALL_APP, appConfig), distributeConfig);
	}

	@Override
	public Integer getRetryCount() {
		return retryCount;
	}

	/**
	 * 通过code获取callbackResult
	 */
	@Override
	@SuppressWarnings("unchecked")
	public RefreshTokenCallbackResult getCallbackResultByCode(String code, String platformId, String appName) throws Exception {
		return null;
	}

	/**
	 * 刷新refrshToken
	 */
	@Override
	@SuppressWarnings("unchecked")
	public RefreshTokenCallbackResult refreshToken(UserInfoBo userInfoBo, String refreshToken, String platformId, String appName) throws Exception {
		try {
			AuthorizationTokenResponse response = doudianSDKService.refreshToken(refreshToken, appName);
			if(response == null || !response.isSuccess() || response.getData() == null){
				LOGGER.logError(userInfoBo.getSellerNick(), "-", "refresh_token刷新返回信息异常 result => " + JSON.toJSONString(response, "yyyy-MM-dd HH:mm:ss"));
				return null;
			}
			AuthorizationToken authorizationToken = response.getData();
			RefreshTokenCallbackResult callbackResult = new RefreshTokenCallbackResult();
			String encryptAccessToken = encryptToken(authorizationToken.getAccessToken(), platformId, ALL_APP);
			callbackResult.setAccessToken(encryptAccessToken);
			callbackResult.setDecryptAccessToken(authorizationToken.getAccessToken());
			String encryptRefreshToken = encryptToken(authorizationToken.getRefreshToken(), platformId, ALL_APP);
			callbackResult.setRefreshToken(encryptRefreshToken);
			callbackResult.setDecryptRefreshToken(authorizationToken.getRefreshToken());
			callbackResult.setExpiresIn(authorizationToken.getExpiresIn());
			callbackResult.setSellerId(userInfoBo.getSellerId());
			callbackResult.setSellerNick(userInfoBo.getSellerNick());
			callbackResult.setSubSellerNick(userInfoBo.getSubSellerNick());
            callbackResult.setAuthDeadLine(
                DateUtil.calculateCustomSecond(DateUtil.currentDate(), authorizationToken.getExpiresIn()));
			return callbackResult;

		} catch (Exception e) {
			LOGGER.logError(userInfoBo.getSellerNick(), "-", "refresh_token刷新出现异常: " + e.getMessage(), e);
			return null;
		}
	}

	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_DOUDIAN;
	}
}
