package cn.loveapp.uac.common.platform.api.domain;

import cn.loveapp.uac.code.ApiCode;
import cn.loveapp.uac.exception.UserException;
import com.alibaba.fastjson.annotation.JSONField;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @program: uac-service-group
 * @description: AuthResult
 * @author: Jason
 * @create: 2020-03-13 12:14
 **/
@Data
public class TaobaoRefreshTokenCallbackResult extends RefreshTokenCallbackResult {
	@J<PERSON>NField(name = "access_token")
	private String accessToken;
	@JSONField(name = "expires_in")
	private Long expiresIn;
	@JSONField(name = "token_type")
	private String tokenType;
	@JSONField(name = "refresh_token")
	private String refreshToken;
	@JSONField(name = "re_expires_in")
	private Long reExpiresIn;
	@JSO<PERSON>ield(name = "r1_expires_in")
	private Long r1ExpiresIn;
	@J<PERSON><PERSON>ield(name = "r2_expires_in")
	private Long r2ExpiresIn;
	@JSONField(name = "w1_expires_in")
	private Long w1ExpiresIn;
	@JSONField(name = "w2_expires_in")
	private Long w2ExpiresIn;
	@JSONField(name = "taobao_user_nick")
	private String sellerNick;
	@JSONField(name = "taobao_user_id")
	private String sellerId;
	@JSONField(name = "sub_taobao_user_id")
	private String subSellerNick;
	@JSONField(name = "sub_taobao_user_nick")
	private String subSellerId;
	@JSONField(name = "error_description")
	private String errorDescription;
	@JSONField(name = "error")
	private String error;

	public String getUrlDecodeSellerNick() throws UnsupportedEncodingException, UserException {
		if (StringUtils.isBlank(sellerNick)) {
			throw new UserException(ApiCode.NO_EXIST_USER.code(), ApiCode.NO_EXIST_USER.message());
		}
		return URLDecoder.decode(sellerNick, "utf-8");
	}

	public String getUrlDecodeSubSellerNick() throws UnsupportedEncodingException {
		if (StringUtils.isBlank(subSellerNick)) {
			return null;
		}
		return URLDecoder.decode(subSellerNick, "utf-8");
	}

	/**
	 * 判断是否成功
	 * @return
	 */
	public boolean isSuccess() {
		return StringUtils.isBlank(errorDescription) || StringUtils.isBlank(error) || StringUtils.isNotBlank(accessToken);
	}

}
