package cn.loveapp.uac.common.platform.api.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.uac.common.api.request.SellerVasOrderSearchRequest;
import cn.loveapp.uac.common.api.request.SellerVasSubscSearchRequest;
import cn.loveapp.uac.common.api.request.SellerVasSubscribeGetRequest;
import cn.loveapp.uac.common.api.response.SellerVasOrderSearchResponse;
import cn.loveapp.uac.common.api.response.SellerVasSubscSearchResponse;
import cn.loveapp.uac.common.api.response.SellerVasSubscribeGetResponse;
import cn.loveapp.uac.common.platform.api.AppStoreService;
import org.springframework.stereotype.Service;

/**
 * @program: usercenter-service-group
 * @description: 抖店服务市场接口
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/2/8 15:59
 **/
@Service
public class XhsAppStoreServiceImpl implements AppStoreService {


    @Override
    public SellerVasSubscribeGetResponse vasSubscribeGet(SellerVasSubscribeGetRequest sellerVasSubscribeGetRequest, String platformId, String appName) {
        return null;
    }

    @Override
    public SellerVasSubscSearchResponse vasSubscribeSearch(SellerVasSubscSearchRequest sellerVasSubscSearchRequest, String platformId, String appName) {
        return null;
    }

    @Override
    public SellerVasOrderSearchResponse vasOrderSearch(SellerVasOrderSearchRequest vasOrderSearchRequest, String platformId, String appName) {
        return null;
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_XHS;
    }
}

