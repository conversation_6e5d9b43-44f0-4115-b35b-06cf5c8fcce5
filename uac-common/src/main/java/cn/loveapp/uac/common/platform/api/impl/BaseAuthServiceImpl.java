package cn.loveapp.uac.common.platform.api.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.AesUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.utils.NetworkUtil;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.config.AppConfig;
import cn.loveapp.uac.common.config.DistributeConfig;
import cn.loveapp.uac.common.entity.redis.UserRedisEntity;
import cn.loveapp.uac.common.exception.UserNeedAuthException;
import cn.loveapp.uac.common.platform.api.domain.RefreshTokenCallbackResult;
import cn.loveapp.uac.common.service.DistributeUserProcessService;
import cn.loveapp.uac.common.utils.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import jakarta.annotation.Nullable;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import javax.crypto.IllegalBlockSizeException;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: uac-service-group
 * @description: TaobaoAuthServiceImpl
 * @author: Jason
 * @create: 2020-03-05 13:44
 **/
abstract public class BaseAuthServiceImpl {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(BaseAuthServiceImpl.class);

	protected static final String ALL_APP = "ALL";

	protected Map<String, AppConfig> appConfigMap;

	private DistributeConfig distributeConfig;

	@Autowired
	private DistributeUserProcessService distributeUserProcessService;

	public BaseAuthServiceImpl(Map<String, AppConfig> appConfigMap, DistributeConfig distributeConfig) {
		this.appConfigMap = appConfigMap;
		this.distributeConfig = distributeConfig;
	}

	abstract public Integer getRetryCount();

	/**
	 * 调用API获得sessionkey
	 * @param refreshToken refreshToken
	 * @return 刷新后的sessionkey
	 */
	protected <T extends RefreshTokenCallbackResult> T reGetAccessTokenWithRefreshToken(String refreshToken, String platformId, Map<String, String> headers, Class<T> tClass, String appName) throws UserNeedAuthException {
		String result = executeRefreshTokenNetwork(refreshToken, platformId, headers, appName);
		if (StringUtils.isEmpty(result)) {
			throw new UserNeedAuthException(404, "有refresh_token，但是刷新失败 " + refreshToken + " " + result);
		}
		if (!StringUtils.isEmpty(result)) {
			return JSONObject.parseObject(result, tClass);
		}
		return null;
	}

	/**
	 * 解密topSession
	 * @param token token
	 * @return 解密后的topSession
	 */
    public String decryptToken(String token, String platformId, String appName) throws Exception {
        if (StringUtils.isEmpty(token)) {
            return token;
        } else {
            int maxTopSessionLen = 81;
            if (token.length() < maxTopSessionLen) {
                /*6或7开头，直接返回 //长度小于80 无需解码*/
                return token;
            } else {
				String session = null;
                AesUtil aesUtil = AesUtil.getInstance();
                try {
                    /*返回解密后accessTokenKey*/
                    session = aesUtil.aesDecryptForSession(token, getActualConfig(appName).getSessionkey());
                    return session;
                } catch (IllegalBlockSizeException e) {
                    // 尝试过滤特殊字符
                    token = token.replaceAll("\\\\r", "");
                    token = token.replaceAll("\\\\n", "");
                    token = token.replaceAll("\\n", "");
                    token = token.replaceAll("\\r", "");
                    session = aesUtil.aesDecryptForSession(token, getActualConfig(appName).getSessionkey());
                    return session;
                }
            }
        }
    }

	/**
	 * 加密
	 * @param token
	 * @param platformId
	 * @return
	 * @throws Exception
	 */
	public String encryptToken(String token, String platformId, String appName) throws Exception {
        if (StringUtils.isEmpty(token)) {
            return null;
        }
        AesUtil aesUtil = AesUtil.getInstance();
        return aesUtil.aesEncryptForSession(token, getActualConfig(appName).getSessionkey());
    }

	private String getNetworkContent(String url, HashMap<String, String> postFields) throws Exception {
		return NetworkUtil.http(url, postFields, true,"","",false,false,"");
	}

	protected Map<String, String> builderHeader(Map<String, String> map) {
		int initcalCapacity = 0;
		if (MapUtils.isEmpty(map)) {
			initcalCapacity = map.size();
		}
		Map<String, String> headers = new HashMap<>(4 + initcalCapacity);
		headers.put("Content-type", "application/json;charset='utf-8'");
		headers.put("Accept", "application/json");
		headers.put("Cache-Control", "no-cache");
		headers.put("Pragma", "no-cache");
		for (Map.Entry<String, String> entry : map.entrySet()) {
			headers.put(entry.getKey(), entry.getValue());
		}
		return headers;
	}

	@Nullable
	protected String executeRefreshTokenNetwork(String refreshToken, String platformId, Map<String, String> headers, String appName) {
		HashMap<String,String> postMap = Maps.newHashMap();
		postMap.put("client_id", getActualConfig(appName).getAppkey());
		postMap.put("client_secret", getActualConfig(appName).getAppSecret());
		postMap.put("grant_type", "refresh_token");
		postMap.put("refresh_token", refreshToken);
		postMap.put("state", "1212");
		postMap.put("scope", "item");
		postMap.put("view", "wap");
		LOGGER.logInfo("auth code request platform is <" + platformId + ">, refreshToken is <" + refreshToken + ">, "
			+ "headers is <" + JSON.toJSONString(headers) + ">, post data is <" + JSON.toJSONString(postMap) + ">");
		String result = org.apache.commons.lang3.StringUtils.EMPTY;
		if (CommonPlatformConstants.PLATFORM_PDD.equals(platformId)) {
			try {
				result = HttpUtil.sendJsonPost(getActualConfig(appName).getAuthCodeTokenUrl(), postMap, headers);
			} catch (IOException e) {
				LOGGER.logError(refreshToken, refreshToken, "当前网络请求异常, platform is "+platformId+", current refreshToken is " + refreshToken + ", error is " + e.getMessage(), e);
			} finally {
				LOGGER.logInfo("auth code response platform is <" + platformId + ">, response data is <" + result + ">");
			}
		}else if (CommonPlatformConstants.PLATFORM_TAO.equals(platformId)) {
			try {
				result = this.getNetworkContent(getActualConfig(appName).getRefreshTokenUrl(), postMap);
			} catch (Exception e) {
				LOGGER.logError("-","-", "网络连接失败 ",e);
				return null;
			} finally {
				LOGGER.logInfo("auth code response platform is <" + platformId + ">, response data is <" + result + ">");
			}
		}
		return result;
	}

	/**
	 * 通过code获取callbackResult
	 */
	public <T extends RefreshTokenCallbackResult> T getCallbackResultByCode(String code, String platformId, Map<String, String> headers, Class<T> tClass, String appName) {
		Integer retryCount = getRetryCount();
		HashMap<String, String> postMap = new HashMap<>(5);
		postMap.put("code", code);
		postMap.put("grant_type", "authorization_code");
		postMap.put("client_id", getActualConfig(appName).getAppkey());
		postMap.put("client_secret", getActualConfig(appName).getAppSecret());
		postMap.put("redirect_uri", getActualConfig(appName).getRedirectUrl());
		LOGGER.logInfo("auth code request platform is <" + platformId + ">, code is <" + code + ">, "
			+ "headers is <" + JSON.toJSONString(headers) + ">, post data is <" + JSON.toJSONString(postMap) + ">");
		if (CommonPlatformConstants.PLATFORM_PDD.equals(platformId)) {
			while (retryCount > 0) {
				retryCount--;
				String result = org.apache.commons.lang3.StringUtils.EMPTY;
				try {
					result = HttpUtil.sendJsonPost(getActualConfig(appName).getAuthCodeTokenUrl(), postMap, headers);
					if (!StringUtils.isEmpty(result)) {
						return JSONObject.parseObject(result, tClass);
					}
				} catch (IOException e) {
					LOGGER.logError(code, code, "当前网络请求异常, platform is "+platformId+", current code is " + code + ", error is " + e.getMessage(), e);
				} finally {
					LOGGER.logInfo("auth code response platform is <" + platformId + ">, response data is <" + result +
						">, retry count is " + retryCount);
				}
			}
			return null;
		}else if (CommonPlatformConstants.PLATFORM_TAO.equals(platformId)) {
			while (retryCount > 0) {
				retryCount--;
				String result = org.apache.commons.lang3.StringUtils.EMPTY;
				try{
					if (MapUtils.isEmpty(headers)) {
						result = NetworkUtil
							.http(getActualConfig(appName).getAuthCodeTokenUrl(), postMap, true, "", "", false, false, "");
					} else {
						result = NetworkUtil
							.http(getActualConfig(appName).getAuthCodeTokenUrl(), postMap, true, "", "", false, false, "", headers);
					}
				} catch (IOException e) {
					LOGGER.logError(code, code, "当前网络请求异常, platform is "+platformId+", current code is " + code + ", error is " + e.getMessage(), e);
				} finally {
					LOGGER.logInfo("auth code response platform is <" + platformId + ">, response data is <" + result +
						">, retry count is " + retryCount);
				}
				if (!StringUtils.isEmpty(result)) {
					return JSONObject.parseObject(result, tClass);
				}
			}
			return null;
		}
		return null;
	}

	protected AppConfig getActualConfig(String appName) {
		AppConfig appConfig = appConfigMap.get(appName);
		if(appConfig == null && !ALL_APP.equals(appName)){
			// 获取全局配置
			appConfig = appConfigMap.get(ALL_APP);
		}
		return appConfig;
	}

	public void convertUserRedisEntity2UserInfoBo(UserInfoBo userInfoBo, UserRedisEntity userRedisEntity, String platformId, String appName) {
		String sellerNick = org.apache.commons.lang3.StringUtils.isNotEmpty(userInfoBo.getSellerNick())
				? userInfoBo.getSellerNick() : userRedisEntity.getUser_nick();
		if (CommonAppConstants.APP_DISTRIBUTE.equals(userInfoBo.getAppType())
				&& distributeConfig.getDistributeSpecialRedisPrefixMap().containsKey(platformId)) {
			// 代发用户单独处理
			userInfoBo.setSellerId(userRedisEntity.getUser_id());
			userInfoBo.setSellerNick(sellerNick);
			userInfoBo.setSellerRole(userRedisEntity.getRoleid());
			userInfoBo.setLevel(userRedisEntity.getVipflagCache());
			userInfoBo.setOrderCycleEnd(userRedisEntity.getOrderCycleEnd());
			userInfoBo.setAccessToken(userRedisEntity.getAccess_token());
			userInfoBo.setRefreshToken(userRedisEntity.getRefresh_token());
			userInfoBo.setMallName(userRedisEntity.getMall_name());
			userInfoBo.setShopName(userRedisEntity.getShop_name());
			if (org.apache.commons.lang3.StringUtils.isEmpty(userInfoBo.getSellerNick())) {
				setDistributeSellerNickBySellerId(userInfoBo);
			}
		} else {
			userInfoBo.setSellerId(userRedisEntity.getUser_id());
			userInfoBo.setSellerNick(sellerNick);
			userInfoBo.setSellerRole(userRedisEntity.getRoleid());
			userInfoBo.setLevel(userRedisEntity.getVipflagCache());
			userInfoBo.setOrderCycleEnd(userRedisEntity.getOrderCycleEnd());
			userInfoBo.setAccessToken(userRedisEntity.getAccess_token());
			userInfoBo.setRefreshToken(userRedisEntity.getRefresh_token());
			userInfoBo.setMallName(userRedisEntity.getMall_name());
			userInfoBo.setShopName(userRedisEntity.getShop_name());
            userInfoBo.setIsAuthExcept(userRedisEntity.getIsAuthExcept());
        }

        // 兼容pdd授权到期时间
        if (userRedisEntity.getAuthDeadLine() == null && userRedisEntity.getPddAuthDeadLineConvert() != null) {
            userInfoBo.setAuthDeadLine(userRedisEntity.getPddAuthDeadLineConvert());
        } else {
            userInfoBo.setAuthDeadLine(userRedisEntity.getAuthDeadLine());
        }
		userInfoBo.setW2Deadline(userRedisEntity.getW2DeadlineCache());
	}

	/**
	 * 设置爱用代发nick（淘宝、必要除外，代发表没存nick，直接是用id做为nick）
	 *
	 * @param userInfoBo
	 */
	private void setDistributeSellerNickBySellerId(UserInfoBo userInfoBo) {
		if (CommonAppConstants.APP_DISTRIBUTE.equals(userInfoBo.getAppType()) && !CommonPlatformConstants.PLATFORM_BIYAO.equals(userInfoBo.getPlatformId())
				&& !CommonPlatformConstants.PLATFORM_TAO.equals(userInfoBo.getPlatformId())) {
			userInfoBo.setSellerNick(userInfoBo.getShopName());
		}
	}
}
