package cn.loveapp.uac.common.api.domain;

import cn.loveapp.uac.common.utils.DateUtil;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @program: uac-service-group
 * @description: TaobaoArticleUserSubscribe
 * @author: <PERSON>
 * @create: 2020-03-05 19:02
 **/
public class SellerArticleUserSubscribe {

	public LocalDateTime getDeadline() {
		return deadline;
	}

	public void setDeadline(Date deadline) {
		this.deadline = DateUtil.parseDate(deadline);
	}

	public String getItemCode() {
		return itemCode;
	}

	public void setItemCode(String itemCode) {
		this.itemCode = itemCode;
	}

	private LocalDateTime deadline;
	private String itemCode;
}
