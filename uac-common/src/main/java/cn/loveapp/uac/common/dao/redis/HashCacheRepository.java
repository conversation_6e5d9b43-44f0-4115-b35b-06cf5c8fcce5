package cn.loveapp.uac.common.dao.redis;

/**
 * @program: uac-service-group
 * @description: DataCacheRepository
 * @author: <PERSON>
 * @create: 2020-03-05 09:20
 **/
public interface HashCacheRepository<T> {

	/**
	 * 增加
	 * @param collection
	 * @param hkey
	 * @param object
	 * @return
	 */
	boolean add(String collection, String hkey, T object, String appName);

	/**
	 * 更新/增加
	 * @param collection
	 * @param hkey
	 * @param v
	 * @return
	 */
	boolean put(String collection, String hkey, String v, String appName);

	/**
	 * 增加所有
	 * @param collection
	 * @param t
	 * @return
	 */
	boolean putAll(String collection, T t, String platformId, String appName);

	/**
	 * 删除
	 * @param collection
	 * @param hkey
	 * @return
	 */
	boolean delete(String collection, String hkey, String appName);

	/**
	 * 查找
	 * @param collection
	 * @param hkey
	 * @return
	 */
	String find(String collection, String hkey, String appName);

	/**
	 * 查找全部
	 */
	T findAll(String collection, Class<T> tClass, String platformId, String appName);

	/**
	 * 初始化collection
	 * @param sellerNick
	 * @param sellerId
	 */
	String initCollection(String sellerNick, String sellerId, String platformId, String appName);

	/**
	 * 初始化扩展collection
	 * @param sellerNick
	 * @param sellerId
	 * @param platformId
	 * @param appName
	 * @return
	 */
	String initExtCollection(String sellerNick, String sellerId, String platformId, String appName);

	/**
	 * 初始化扩展collection
	 * @param sellerId
	 * @param platformId
	 * @param appName
	 * @return
	 */
	String initUserExtCollection(String sellerId, String platformId, String appName, String businessId);
}
