package cn.loveapp.uac.common.dao.redis.base;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.utils.LoggerHelper;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.Map;
import java.util.TimeZone;

/**
 * @program: uac-service-group
 * @description: BaseRedisRepository
 * @author: Jason
 * @create: 2020-03-04 18:19
 **/
public class RedisRepositoryBase<T> {
	private static LoggerHelper LOGGER = LoggerHelper.getLogger(RedisRepositoryBase.class);

	protected Map<String, StringRedisTemplate> stringRedisTemplateMap;

	public RedisRepositoryBase(Map<String, StringRedisTemplate> stringRedisTemplateMap) {
		this.stringRedisTemplateMap = stringRedisTemplateMap;
	}

	protected static final ObjectMapper OBJECT_MAPPER;

	static {
		OBJECT_MAPPER = new ObjectMapper();
		OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		OBJECT_MAPPER.setTimeZone(TimeZone.getTimeZone("GMT+8"));
	}

	/**
	 * 判断是否存在
	 * @param collection
	 * @return
	 */
	protected Boolean hasExistCollection(String collection, String appName) {
		try {
            if (StringUtils.isEmpty(collection)) {
                return false;
            }
			return getRealStringRedisTemplate(appName).hasKey(collection);
		} catch (Exception e) {
			if(e.getMessage() == null){
				LOGGER.logError("Entry '"+collection+"' does not exist in cache", e);
			} else {
				LOGGER.logError("Unable to find entry '"+collection+"' in cache collection '"+collection+"': "+e.getMessage()+"", e);
			}
			return false;
		}
	}

	/**
	 * 清除collection
	 * @param collection
	 * @return
	 */
	protected Boolean cleanCollection(String collection, String appName) {
        if (StringUtils.isEmpty(collection)) {
            return false;
        }
		try {
			return getRealStringRedisTemplate(appName).delete(collection);
		} catch (Exception e) {
			if(e.getMessage() == null){
				LOGGER.logError("Entry '"+collection+"' does not exist in cache", e);
			} else {
				LOGGER.logError("Unable to find entry '"+collection+"' in cache collection '"+collection+"': "+e.getMessage()+"", e);
			}
			return null;
		}
	}

	protected StringRedisTemplate getRealStringRedisTemplate(String appName) {
		if (CommonAppConstants.APP_ITEM.equals(appName)
				|| CommonAppConstants.APP_DISTRIBUTE.equals(appName)
				|| CommonAppConstants.APP_SHOP_HELPER.equals(appName)) {
			return stringRedisTemplateMap.get(appName);
		}
		return stringRedisTemplateMap.get(CommonAppConstants.APP_TRADE);
	}

}
