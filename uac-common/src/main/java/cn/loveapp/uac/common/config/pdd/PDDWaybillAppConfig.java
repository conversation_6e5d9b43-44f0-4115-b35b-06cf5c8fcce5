package cn.loveapp.uac.common.config.pdd;

import cn.loveapp.uac.common.config.AppConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 拼多多 电子面单应用 配置
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Configuration
@ConfigurationProperties(prefix = "uac.pdd.waybill.app")
public class PDDWaybillAppConfig extends AppConfig {

}
