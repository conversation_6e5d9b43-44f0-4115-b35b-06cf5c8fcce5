package cn.loveapp.uac.common.platform.api.impl;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.common.collect.ImmutableMap;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.config.DistributeConfig;
import cn.loveapp.uac.common.config.aiyong.AiyongAppConfig;
import cn.loveapp.uac.common.platform.api.AuthService;
import cn.loveapp.uac.common.platform.api.domain.RefreshTokenCallbackResult;

/**
 * 爱用授权相关service
 *
 * <AUTHOR>
 * @date 2022/9/17
 */
@Service
public class AiyongAuthServiceImpl extends BaseAuthServiceImpl implements AuthService {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(AiyongAuthServiceImpl.class);

    @Value("${uac.network.retry.count:5}")
    private Integer retryCount;

    public AiyongAuthServiceImpl(AiyongAppConfig appConfig, DistributeConfig distributeConfig) {
        super(ImmutableMap.of(ALL_APP, appConfig), distributeConfig);
    }

    @Override
    public Integer getRetryCount() {
        return retryCount;
    }

    /**
     * 通过code获取callbackResult
     */
    @Override
    @SuppressWarnings("unchecked")
    public RefreshTokenCallbackResult getCallbackResultByCode(String code, String platformId, String appName) throws Exception {
        return null;
    }

    /**
     * 刷新refrshToken
     */
    @Override
    @SuppressWarnings("unchecked")
    public RefreshTokenCallbackResult refreshToken(UserInfoBo userInfoBo, String refreshToken, String platformId, String appName) throws Exception {
        return null;
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_AIYONG;
    }
}
