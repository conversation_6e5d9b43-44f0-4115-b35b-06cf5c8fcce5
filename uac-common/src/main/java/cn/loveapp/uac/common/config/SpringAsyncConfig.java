package cn.loveapp.uac.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * @program: uac-service-group
 * @description: SpringAsyncConfig
 * @author: Jason
 * @create: 2020-09-24 10:22
 **/
@Configuration
@EnableAsync
public class SpringAsyncConfig {

	@Bean("commonThreadPoolTaskExecutor")
	public TaskExecutor getAsyncExecutor() {
		ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
		executor.setCorePoolSize(10);
		executor.setMaxPoolSize(10);
		executor.setQueueCapacity(10);
		executor.setThreadNamePrefix("common-task");
		executor.initialize();
		return executor;
	}

}
