package cn.loveapp.uac.common.platform.api.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.ocean.rawsdk.client.entity.AuthorizationToken;
import com.google.common.collect.ImmutableMap;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.tgc.TgcSDKService;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.config.DistributeConfig;
import cn.loveapp.uac.common.config.tgc.TgcTradeERPAppConfig;
import cn.loveapp.uac.common.platform.api.AuthService;
import cn.loveapp.uac.common.platform.api.domain.RefreshTokenCallbackResult;
import cn.loveapp.uac.common.utils.DateUtil;

/**
 * <AUTHOR>
 * @date 2025-04-15 10:34
 * @description: tgc授权相关service
 */
@Service
public class TgcAuthServiceImpl extends BaseAuthServiceImpl implements AuthService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(TgcAuthServiceImpl.class);

    @Value("${uac.network.retry.count:5}")
    private Integer retryCount;

    /**
     * tgc平台 refreshToken失效异常错误信息
     */
    private static final String TGC_REFRESHTOKEN_EXPIRE_ERR_MSG = "Unknow message defined in response.";

    @Autowired
    private TgcSDKService tgcSDKService;

    public TgcAuthServiceImpl(TgcTradeERPAppConfig tgcTradeERPAppConfig, DistributeConfig distributeConfig) {
        super(ImmutableMap.of(ALL_APP, tgcTradeERPAppConfig), distributeConfig);
    }

    @Override
    public <T extends RefreshTokenCallbackResult> T getCallbackResultByCode(String code, String platformId,
        String appName) throws Exception {
        return null;
    }

    @Override
    @SuppressWarnings("unchecked")
    public RefreshTokenCallbackResult refreshToken(UserInfoBo userInfoBo, String refreshToken, String platformId,
        String appName) throws Exception {
        try {
            AuthorizationToken authorizationToken = tgcSDKService.refreshToken(refreshToken, appName);
            if (authorizationToken == null || StringUtils.isEmpty(authorizationToken.getAccess_token())) {
                LOGGER.logError(userInfoBo.getSellerNick(), "-", "refresh_token刷新返回信息异常 result => "
                    + JSON.toJSONString(authorizationToken, "yyyy-MM-dd HH:mm:ss"));
                return null;
            }
            LOGGER.logInfo(userInfoBo.getSellerNick(), "", "TGC 刷新token响应信息： " + JSON.toJSONString(authorizationToken));
            RefreshTokenCallbackResult callbackResult = new RefreshTokenCallbackResult();

            String encryptAccessToken = encryptToken(authorizationToken.getAccess_token(), platformId, appName);
            String encryptRefreshToken = encryptToken(authorizationToken.getRefresh_token(), platformId, appName);

            callbackResult.setDecryptAccessToken(authorizationToken.getAccess_token());
            callbackResult.setDecryptRefreshToken(authorizationToken.getRefresh_token());
            callbackResult.setAccessToken(encryptAccessToken);
            callbackResult.setRefreshToken(encryptRefreshToken);

            callbackResult.setExpiresIn(authorizationToken.getExpires_in());
            callbackResult.setSellerId(userInfoBo.getSellerId());
            callbackResult.setSellerNick(userInfoBo.getSellerNick());
            callbackResult.setSubSellerNick(userInfoBo.getSubSellerNick());
            callbackResult.setAuthDeadLine(
                DateUtil.calculateCustomSecond(DateUtil.currentDate(), authorizationToken.getExpires_in()));

            return callbackResult;
        } catch (Exception e) {
            if (e.getCause() != null && TGC_REFRESHTOKEN_EXPIRE_ERR_MSG.equals(e.getCause().getMessage())) {
                // refreshToken 过期时响应的错误 此错误仅代表refreshToken过期
                LOGGER.logError(userInfoBo.getSellerNick(), "-",
                    "refresh_token:[" + refreshToken + "]过期，无法刷新: " + e.getMessage(), e);
            } else {
                LOGGER.logError(userInfoBo.getSellerNick(), "-",
                    "refresh_token:[" + refreshToken + "]刷新出现异常: " + e.getMessage(), e);
            }
        }
        return null;
    }

    @Override
    public String decryptToken(String token, String platformId, String appName) throws Exception {
        return super.decryptToken(token, platformId, appName);
    }

    @Override
    public Integer getRetryCount() {
        return retryCount;
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_TGC;
    }

}
