package cn.loveapp.uac.common.config.rocketmq.product;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.config.rocketmq.RocketMQAppConfig;
import cn.loveapp.uac.common.config.rocketmq.RocketMQDefaultProducerConfig;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @program: orders-services-group
 * @description: OrdersDefaultProducerConfig
 * @author: Jason
 * @create: 2018-12-07 17:51
 **/
@Configuration
public class DefaultProducerConfig {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(DefaultProducerConfig.class);

    @Autowired
    private RocketMQAppConfig rocketMQAppConfig;

    @Autowired
    private RocketMQDefaultProducerConfig rocketMQDefaultProducerConfig;

    @Bean(destroyMethod = "shutdown", name = "defaultProducer")
    public DefaultMQProducer defaultProducer() throws Exception {
        //启动ONS消息队列
        DefaultMQProducer producer = new DefaultMQProducer(rocketMQDefaultProducerConfig.getProducerId());
        producer.setSendMsgTimeout(5000);
        producer.setNamesrvAddr(rocketMQAppConfig.getNamesrvAddr());
        producer.start();
        LOGGER.logInfo("defaultProducer started");
        return producer;
    }
}
