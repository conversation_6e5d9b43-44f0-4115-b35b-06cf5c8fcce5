package cn.loveapp.uac.common.config.cache;

import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.uac.common.api.domain.SellerArticleUserSubscribe;
import cn.loveapp.uac.common.code.taobao.ApiCodeConstant.CodeEnum;
import java.time.LocalDateTime;

import cn.loveapp.uac.common.entity.UserProductInfo;
import lombok.Data;
import lombok.ToString;

/**
 * @program: uac-service-group
 * @description: VasGetResult
 * @author: Jason
 * @create: 2020-05-27 16:56
 **/
@Data
@ToString
public class VasGetResult {
	private Integer level;
	private LocalDateTime orderCycleEnd;
	private CodeEnum apiCode;

	/**
	 * taobao.vas.subscribe.get( 订购关系查询 ) 响应集合.get(0)，最新一条订购关系数据
	 */
	private SellerArticleUserSubscribe articleUserSubscribe;

	public static VasGetResult of(Integer level, LocalDateTime orderCycleEnd) {
		VasGetResult vasGetResult = new VasGetResult();
        vasGetResult.setLevel(level == null ? UserProductInfo.LEVEL_ZERO : level);
		if (null == orderCycleEnd) {
			vasGetResult.setOrderCycleEnd(DateUtil.currentDate(true).plusDays(7L));
		} else {
			vasGetResult.setOrderCycleEnd(orderCycleEnd);
		}
		return vasGetResult;
	}
}
