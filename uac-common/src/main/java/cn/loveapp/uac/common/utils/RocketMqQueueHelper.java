package cn.loveapp.uac.common.utils;

import cn.loveapp.common.utils.LoggerHelper;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.ImmutableMap;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;

/**
 * @program: orders-services-group
 * @description: OnsQueue
 * @author: Jason
 * @create: 2018-11-19 13:57
 **/
@Component
public class RocketMqQueueHelper {

	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(RocketMqQueueHelper.class);

	public <T> String push(String topic, String tag, T pushData, DefaultMQProducer producer) {
		return push(topic, tag, pushData, producer, 10);
	}

	public <T> String push(String topic, String tag, T pushData, DefaultMQProducer producer, int retryCount) {
		if (! Objects.nonNull(producer)) {
			return null;
		}
		String check = checkPushData(pushData);
		String logFormat = "RocketMQ 队列名称为: %s, 队列tag: %s, 消息内容为: %s, MessageId为: %s";
		String errorFormat = "发送MQ消息最终失败 队列名称为: %s, 队列tag: %s, 消息内容为: %s, 异常为: %s";
		if (null != check) {
			byte[] msgBytes = check.getBytes(StandardCharsets.UTF_8);
			org.apache.rocketmq.common.message.Message onsMsg = new org.apache.rocketmq.common.message.Message(
				//Message Topic
				topic,
				//Message Tag,
				//可理解为Gmail中的标签，对消息进行再归类，方便common指定过滤条件在ONS服务器过滤
				tag,
				//Message Body
				//任何二进制形式的数据，ONS不做任何干预，需要Producer与common协商好一致的序列化和反序列化方式
				msgBytes);
			String messageId = null;
			try {
				int retry = 0;
				while (retry <= retryCount) {
					try {
						org.apache.rocketmq.client.producer.SendResult sr = producer.send(onsMsg);
						messageId = sr.getMsgId();
						break;
					} catch (Exception e) {
						if (retry >= retryCount) {
							throw e;
						} else {
							retry++;
							LOGGER.logWarn("发送MQ消息失败, 稍后第" + retry + "次重试: " + e.getMessage());
							try {
								Thread.sleep(200 * retry);
							} catch (InterruptedException ex) {
							}
						}
					}
				}
				return messageId;
			} catch (Exception e) {
				LOGGER.logError(String.format(errorFormat, topic, tag, check, e.getMessage()), e);
			} finally {
				LOGGER.logInfo(String.format(logFormat, topic, tag, check, messageId), ImmutableMap.of("topic", topic, "tag", tag));
			}
		}
		return null;
	}

	private static <T> String checkPushData(T pushData) {
		if (null == pushData) {
			return null;
		}
		if (pushData instanceof String) {
			return (String) pushData;
		} else {
			return JSON.toJSONString(pushData);
		}
	}

	private long getDelayTime() {
		return delayTime;
	}

	public void setDelayTime(long delayTime) {
		this.delayTime = delayTime;
	}

	private long delayTime;

	public <T> String push(String topic, String tag, T pushData, DefaultMQProducer producer,int delayTimeLevel, Map<String, String> userProperties) {
		return push(topic, tag, pushData, producer, 10,delayTimeLevel, userProperties);
	}

	public <T> String push(String topic, String tag, T pushData, DefaultMQProducer producer, int retryCount,int delayTimeLevel) {
		return push(topic, tag, pushData, producer, retryCount, delayTimeLevel, null);
	}

	public <T> String push(String topic, String tag, T pushData, DefaultMQProducer producer, int retryCount, int delayTimeLevel, Map<String, String> userProperties) {
		if (! Objects.nonNull(producer)) {
			return null;
		}
		String check = checkPushData(pushData);
		String logFormat = "RocketMQ 队列名称为: %s, 队列tag: %s, 消息内容为: %s, MessageId为: %s, delayTimeLevel为: %d";
		String errorFormat = "发送MQ消息最终失败 队列名称为: %s, 队列tag: %s, 消息内容为: %s, 异常为: %s, delayTimeLevel为: %d";
		if (null != check) {
			byte[] msgBytes = check.getBytes(StandardCharsets.UTF_8);
			org.apache.rocketmq.common.message.Message onsMsg = getRocketMqMessage(topic,tag,msgBytes);
			if (delayTimeLevel > 0){
				onsMsg.setDelayTimeLevel(delayTimeLevel);
			}
			if (userProperties != null) {
				userProperties.forEach((key, value)->{
					onsMsg.putUserProperty(key, value);
				});
			}
			String messageId = null;
			try {
				int retry = 0;
				while (retry <= retryCount) {
					try {
						org.apache.rocketmq.client.producer.SendResult sr = producer.send(onsMsg);
						messageId = sr.getMsgId();
						break;
					} catch (Exception e) {
						if (retry >= retryCount) {
							throw e;
						} else {
							retry++;
							LOGGER.logWarn("发送MQ消息失败, 稍后第" + retry + "次重试: " + e.getMessage());
							try {
								Thread.sleep(200 * retry);
							} catch (InterruptedException ex) {
							}
						}
					}
				}
				return messageId;
			} catch (Exception e) {
				LOGGER.logError(String.format(errorFormat, topic, tag, check, e.getMessage(), delayTimeLevel), e);
			} finally {
				LOGGER.logInfo(String.format(logFormat, topic, tag, check, messageId, delayTimeLevel), ImmutableMap.of("topic", topic, "tag", tag));
			}
		}
		return null;
	}

	public org.apache.rocketmq.common.message.Message getRocketMqMessage(String topic,String tag,byte[] msgBytes){
		return new org.apache.rocketmq.common.message.Message(
				//Message Topic
				topic,
				//Message Tag,
				//可理解为Gmail中的标签，对消息进行再归类，方便common指定过滤条件在ONS服务器过滤
				tag,
				//Message Body
				//任何二进制形式的数据，ONS不做任何干预，需要Producer与common协商好一致的序列化和反序列化方式
				msgBytes);
	}

}
