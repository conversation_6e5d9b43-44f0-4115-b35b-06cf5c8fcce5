package cn.loveapp.uac.common.request;

import lombok.Data;

/**
 * @program: orders-services-group
 * @description: MessageRequest
 * @author: <PERSON>
 * @create: 2019-11-28 17:32
 **/
@Data
public class MessageRequest {

	/**
	 * message_subscribe
	 */
	private String topics;

	/**
	 * tao bao message db subscribe
	 */
	private String rdsName;

	/**
	 * scope: 取消用户消息订阅与取消用户db订阅
	 */
	/**
	 * tao bao
	 */
	private String sellerNick;
	/**
	 * pdd
	 */
	private String sellerId;

	private String topSession;
}
