package cn.loveapp.uac.common.service.impl;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Map;

import cn.loveapp.common.utils.AesUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.config.DistributeConfig;
import cn.loveapp.uac.common.service.DistributeUserProcessService;

/**
 * <AUTHOR>
 * @date 2023-05-17 11:09
 * @Description: 代发兼容处理服务实现类
 */
@Service
public class DistributeUserProcessServiceImpl implements DistributeUserProcessService {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(DistributeUserProcessServiceImpl.class);

    protected static final String TOKEN_SUFFIX_KEY = "access_token";
    protected static final String PDD_TOKEN_SUFFIX_KEY = "pddAccessToken";
    protected static final String TOKEN_SUFFIX_KEY_1688 = "_access_token_mp";
    protected static final String REFRESH_TOKEN_KEY = "refresh_token";
    protected static final String PDD_REFRESH_TOKEN_KEY = "pddRefreshToken";
    protected static final String REFRESH_TOKEN_KEY_1688 = "_refresh_token_mp";
    protected static final String USER_ID_KEY = "user_id";

    @Autowired
    private DistributeConfig distributeConfig;

    @Override
    public String createdUserRedisKey(String sellerNick, String sellerId, String platformId, String appName)
        throws UnsupportedEncodingException {

        if (!CommonAppConstants.APP_DISTRIBUTE.equals(appName)) {
            return null;
        }

        String key = null;
        if (distributeConfig.getDistributeSpecialRedisPrefixMap().containsKey(platformId)) {
            // 代发视频号小店是拼接sellerNick
            if (CommonPlatformConstants.PLATFORM_WXVIDEOSHOP.equals(platformId)) {
                if (StringUtils.isEmpty(sellerNick)) {
                    LOGGER.logError("缺少sellerNick, 无法初始化Key");
                    return null;
                }

                key = URLEncoder.encode(
                    distributeConfig.getDistributeSpecialRedisPrefixMap().get(platformId) + ":" + sellerNick, "utf-8");
            } else if (CommonPlatformConstants.PLATFORM_1688.equals(platformId)) {
                if (StringUtils.isEmpty(sellerNick)) {
                    LOGGER.logError("缺少sellerNick, 无法初始化Key");
                    return null;
                }

                key = URLEncoder.encode(sellerNick, "utf-8");
            } else {
                if (StringUtils.isEmpty(sellerId)) {
                    LOGGER.logError("缺少sellerId, 无法初始化Key");
                    return null;
                }

                key = URLEncoder.encode(
                    distributeConfig.getDistributeSpecialRedisPrefixMap().get(platformId) + ":" + sellerId, "utf-8");
            }
        } else {
            if (StringUtils.isEmpty(sellerId)) {
                LOGGER.logError("缺少sellerId, 无法初始化Key");
                return null;
            }
            // 代发新的应用 统一使用 platformId:appName:sellerId 做redis key
            key = platformId + ":" + appName + ":" + sellerId;
        }
        return key;
    }

    @Override
    public Map appendUserRedisField(Map entries, String platformId, String appName, boolean isUpdate) {
        if (CommonAppConstants.APP_DISTRIBUTE.equals(appName)
            && distributeConfig.getDistributeSpecialRedisPrefixMap().containsKey(platformId)) {
            String accessToken = null;
            String refreshToken = null;
            String userId = null;
            if (CommonPlatformConstants.PLATFORM_1688.equals(platformId)) {
                // 兼容1688代发格式
                accessToken = getDistributeRedisPrefix(platformId) + TOKEN_SUFFIX_KEY_1688;
                refreshToken = getDistributeRedisPrefix(platformId) + REFRESH_TOKEN_KEY_1688;
                userId = getDistributeRedisPrefix(platformId) + "_" + USER_ID_KEY;
            } else {
                // 代发 其他平台
                accessToken = getDistributeRedisPrefix(platformId) + "_" + TOKEN_SUFFIX_KEY;
                refreshToken = getDistributeRedisPrefix(platformId) + "_" + REFRESH_TOKEN_KEY;
                userId = getDistributeRedisPrefix(platformId) + "_id";
            }

            if (isUpdate) {
                String entriesAccessToken = TOKEN_SUFFIX_KEY;
                String entriesRefreshToken = REFRESH_TOKEN_KEY;
                if (CommonPlatformConstants.PLATFORM_PDD.equals(platformId)) {
                    entriesAccessToken = PDD_TOKEN_SUFFIX_KEY;
                    entriesRefreshToken =PDD_REFRESH_TOKEN_KEY;
                }

                if (entries.get(entriesAccessToken) != null) {
                    entries.put(accessToken, entries.get(entriesAccessToken));
                }

                if (entries.get(entriesRefreshToken) != null) {
                    entries.put(refreshToken, entries.get(entriesRefreshToken));
                }

                if (entries.put(userId, entries.get(USER_ID_KEY)) != null) {
                    entries.put(userId, entries.get(USER_ID_KEY));
                }

                entries.remove(entriesAccessToken);
                entries.remove(entriesRefreshToken);
                entries.remove(USER_ID_KEY);

            } else {
                entries.put(TOKEN_SUFFIX_KEY, entries.get(accessToken));
                entries.put(REFRESH_TOKEN_KEY, entries.get(refreshToken));
                entries.put(USER_ID_KEY, entries.get(userId));
            }
        }
        return entries;
    }

    @Override
    public String decryptForSession(String token, String sessionKey, String platformId, String appName)
        throws Exception {
        if (CommonAppConstants.APP_DISTRIBUTE.equals(appName)
                && CommonPlatformConstants.PLATFORM_1688.equals(platformId)) {
            AesUtil aesUtil = AesUtil.getInstance();
            String session = null;
            try {
                session = aesUtil.aesDecryptFor1688DistributeSession(token, sessionKey);
                return session;
            } catch (Exception e) {
                token = token.replaceAll("\\\\r", "");
                token = token.replaceAll("\\\\n", "");
                token = token.replaceAll("\\n", "");
                token = token.replaceAll("\\r", "");
                session = aesUtil.aesDecryptFor1688DistributeSession(token, sessionKey);
                return session;
            }
        }
        return null;
    }

    @Override
    public String encryptForSession(String token, String sessionKey, String platformId, String appName)
        throws Exception {
        if (CommonAppConstants.APP_DISTRIBUTE.equals(appName)
            && CommonPlatformConstants.PLATFORM_1688.equals(platformId)) {
            AesUtil aesUtil = AesUtil.getInstance();
            return aesUtil.aesEncryptFor1688DistributeSession(token, sessionKey);
        }

        return null;
    }

    /**
     * 获取代发redis前缀
     *
     * @param platformId
     * @return
     */
    private String getDistributeRedisPrefix(String platformId) {
        return distributeConfig.getDistributeSpecialRedisPrefixMap().get(platformId);
    }
}
