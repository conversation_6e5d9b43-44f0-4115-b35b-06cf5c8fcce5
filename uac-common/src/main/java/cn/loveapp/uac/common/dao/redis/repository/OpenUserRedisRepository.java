package cn.loveapp.uac.common.dao.redis.repository;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.dao.redis.base.BaseValueRedisRepository;
import com.google.common.collect.ImmutableMap;
import io.netty.util.CharsetUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.concurrent.TimeUnit;

/**
 * @Author: zhongzijie
 * @Date: 2023/8/9 14:21
 * @Description: 用户开通 - Redis Repository
 */
@Component
public class OpenUserRedisRepository extends BaseValueRedisRepository<String> {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OpenUserRedisRepository.class);

    @Autowired
    @Qualifier("stringItemRedisTemplate")
    protected StringRedisTemplate stringRedisTemplate;

    public OpenUserRedisRepository(
            @Qualifier("stringTradeRedisTemplate") StringRedisTemplate stringTradeRedisTemplate,
            @Qualifier("stringItemRedisTemplate") StringRedisTemplate stringItemRedisTemplate) {
        super(ImmutableMap.of(
                CommonAppConstants.APP_TRADE, stringTradeRedisTemplate,
                CommonAppConstants.APP_ITEM, stringItemRedisTemplate,
                CommonAppConstants.APP_DISTRIBUTE, stringItemRedisTemplate
        ));
    }


    /**
     * 设置value，如果已存在，则设置失败
     *
     * @param businessId
     * @param platformId
     * @param appName
     * @param sellerNick
     * @param value
     * @param timeout
     * @param unit
     * @return
     * @throws UnsupportedEncodingException
     */
    public Boolean setIfAbsent(String prefix, String businessId, String platformId, String appName, String sellerNick,
                               String value, long timeout, TimeUnit unit) throws Exception {
        String key = initRedisOpenUserKey(prefix, businessId, platformId, appName, sellerNick);
        return super.setIfAbsent(key, value, appName, timeout, unit);
    }

    /**
     * 删除
     *
     * @param businessId
     * @param platformId
     * @param appName
     * @param sellerNick
     * @return
     * @throws UnsupportedEncodingException
     */
    public Boolean delete(String prefix, String businessId, String platformId, String appName, String sellerNick) throws UnsupportedEncodingException {
        String key = initRedisOpenUserKey(prefix, businessId, platformId, appName, sellerNick);
        return super.delete(key, appName);
    }

    /**
     * 生成redis用户开通key
     *
     * @param businessId
     * @param platformId
     * @param appName
     * @param sellerNick
     * @return
     */
    private String initRedisOpenUserKey(String prefix, String businessId, String platformId, String appName, String sellerNick) throws UnsupportedEncodingException {
        String key = "open_user:" + prefix + ":" + businessId + ":" + platformId + ":" + appName + ":" + URLEncoder.encode(sellerNick, CharsetUtil.UTF_8.name());
        return key;
    }

    @Override
    public String initCollection(String collection, String platformId, String appName) {
        return null;
    }
}
