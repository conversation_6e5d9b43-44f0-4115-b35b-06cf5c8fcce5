package cn.loveapp.uac.common.platform.api.domain;

import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * @program: uac-service-group
 * @description: CallbackResult
 * @author: <PERSON>
 * @create: 2020-03-13 15:51
 **/
@Data
public class RefreshTokenCallbackResult {
	private String decryptAccessToken;
	private String decryptRefreshToken;
	private String accessToken;
	private Long expiresIn;
	private String refreshToken;
	private List<String> scope;
	private String sellerNick;
	private String sellerId;
	private String subSellerNick;
	private String subSellerId;
	/**
	 * 授权到期时间
	 */
	private LocalDateTime authDeadLine;
}
