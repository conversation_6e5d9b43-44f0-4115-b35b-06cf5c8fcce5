package cn.loveapp.uac.common.platform.api;

import cn.loveapp.common.autoconfigure.platform.CommonPlatformHandler;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.entity.redis.UserRedisEntity;
import cn.loveapp.uac.common.exception.UserNeedAuthException;
import cn.loveapp.uac.common.platform.api.domain.RefreshTokenCallbackResult;

/**
 * @program: uac-service-group
 * @description: TaobaoAuthService
 * @author: Jason
 * @create: 2020-03-05 13:42
 **/
public interface AuthService extends CommonPlatformHandler {

	/**
	 * 解密accessToken
	 * @param accessToken
	 * @return
	 * @throws Exception
	 */
	String decryptToken(String accessToken, String platformId, String appName) throws Exception;

	/**
	 * 加密accessToken
	 * @param token
	 * @param platformId
	 * @param appName
	 * @return
	 * @throws Exception
	 */
	String encryptToken(String token, String platformId, String appName) throws Exception;

	/**
	 * 通过code获取callbackResult
	 * @param code
	 * @return
	 */
	<T extends RefreshTokenCallbackResult> T getCallbackResultByCode(String code, String platformId, String appName) throws Exception;

	/**
	 * 刷新token
	 *
	 * @param userInfoBo
	 * @param refreshToken
	 * @return
	 * @throws UserNeedAuthException
	 */
	<T extends RefreshTokenCallbackResult> T refreshToken(UserInfoBo userInfoBo, String refreshToken, String platformId, String appName) throws Exception;

	/**
	 * 读取userRedisEntity，转化为userInfoBo
	 * @param userInfoBo
	 * @param userRedisEntity
	 * @param platformId
	 * @param appName
	 */
	void convertUserRedisEntity2UserInfoBo(UserInfoBo userInfoBo, UserRedisEntity userRedisEntity, String platformId, String appName);
}
