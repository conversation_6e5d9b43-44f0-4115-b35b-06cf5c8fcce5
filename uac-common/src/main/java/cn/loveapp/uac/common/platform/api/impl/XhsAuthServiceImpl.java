package cn.loveapp.uac.common.platform.api.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.xhs.XhsSDKService;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.config.DistributeConfig;
import cn.loveapp.uac.common.config.xhs.XhsAppConfig;
import cn.loveapp.uac.common.platform.api.AuthService;
import cn.loveapp.uac.common.platform.api.domain.RefreshTokenCallbackResult;
import cn.loveapp.uac.common.utils.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.ImmutableMap;
import com.xhs.api.request.XhsRefreshTokenRequest;
import com.xhs.api.response.XhsRefreshTokenResponse;
import com.xiaohongshu.fls.opensdk.entity.oauth.response.RefreshTokenResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @program: usercenter-service-group
 * @description: 小红书授权相关service
 * @author: zhangchunhui
 * @create: 2023/2/8 16:02
 **/
@Service
public class XhsAuthServiceImpl extends BaseAuthServiceImpl implements AuthService {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(XhsAuthServiceImpl.class);

    @Autowired
    private XhsSDKService xhsSDKService;

    @Value("${uac.network.retry.count:5}")
    private Integer retryCount;

    public XhsAuthServiceImpl(XhsAppConfig xhsAppConfig, DistributeConfig distributeConfig) {
        super(ImmutableMap.of(CommonAppConstants.APP_TRADE, xhsAppConfig, ALL_APP, xhsAppConfig), distributeConfig);
    }

    @Override
    public <T extends RefreshTokenCallbackResult> T getCallbackResultByCode(String code, String platformId, String appName) throws Exception {
        return null;
    }

    @Override
    public RefreshTokenCallbackResult refreshToken(UserInfoBo userInfoBo, String refreshToken, String platformId, String appName) throws Exception {

        try {
            XhsRefreshTokenRequest xhsRefreshTokenRequest = new XhsRefreshTokenRequest();
            xhsRefreshTokenRequest.setRefreshToken(refreshToken);
            XhsRefreshTokenResponse response = xhsSDKService.execute(xhsRefreshTokenRequest, null, appName);
            if(response == null || !response.isSuccess()){
                LOGGER.logError(userInfoBo.getSellerNick(), "-", "refresh_token刷新返回信息异常 result => " + JSON.toJSONString(response));
                return null;
            }
            RefreshTokenResponse refreshTokenResponse = response.getData();
            RefreshTokenCallbackResult callbackResult = new RefreshTokenCallbackResult();
            String encryptAccessToken = encryptToken(refreshTokenResponse.getAccessToken(), platformId, ALL_APP);
            callbackResult.setAccessToken(encryptAccessToken);
            callbackResult.setDecryptAccessToken(refreshTokenResponse.getAccessToken());
            String encryptRefreshToken = encryptToken(refreshTokenResponse.getRefreshToken(), platformId, ALL_APP);
            callbackResult.setRefreshToken(encryptRefreshToken);
            callbackResult.setDecryptRefreshToken(refreshTokenResponse.getRefreshToken());
            callbackResult.setExpiresIn(refreshTokenResponse.getAccessTokenExpiresAt());
            callbackResult.setSellerId(userInfoBo.getSellerId());
            callbackResult.setSellerNick(userInfoBo.getSellerNick());
            callbackResult.setSubSellerNick(userInfoBo.getSubSellerNick());
            callbackResult
                .setAuthDeadLine(DateUtil.parseDate(new Date(refreshTokenResponse.getAccessTokenExpiresAt())));
            return callbackResult;
        } catch (Exception e) {
            LOGGER.logError(userInfoBo.getSellerNick(), "-", "refresh_token刷新出现异常: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_XHS;
    }

    @Override
    public Integer getRetryCount() {
        return retryCount;
    }
}
