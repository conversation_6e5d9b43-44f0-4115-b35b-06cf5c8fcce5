package cn.loveapp.uac.common.config.pdd;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import cn.loveapp.uac.common.config.AppConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024-09-11 17:44
 * @description: pdd tradeERP应用配置
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Configuration
@ConfigurationProperties(prefix = "uac.pdd.tradeerp.app")
public class PDDTradeERPAppConfig extends AppConfig {}
