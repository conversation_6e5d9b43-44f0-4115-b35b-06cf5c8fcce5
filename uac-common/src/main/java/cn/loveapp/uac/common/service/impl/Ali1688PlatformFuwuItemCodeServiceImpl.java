package cn.loveapp.uac.common.service.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.uac.common.service.PlatformFuwuItemCodeService;

/**
 * 1688平台 item_code 相关信息
 *
 * <AUTHOR>
 */
public class Ali1688PlatformFuwuItemCodeServiceImpl implements PlatformFuwuItemCodeService {
    @Override
    public Integer obtainVipFlag(String itemCode, String platformId, String appName) {
        return null;
    }

    @Override
    public String obtainItemCode(Integer vipFlag, String platformId, String appName) {
        return null;
    }

    @Override
    public String obtainAutoRenewItemCode(String platformId, String appName) {
        return null;
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_1688;
    }
}
