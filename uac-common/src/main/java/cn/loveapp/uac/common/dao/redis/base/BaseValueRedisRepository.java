package cn.loveapp.uac.common.dao.redis.base;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.dao.redis.ValueRedisRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @program: uac-service-group
 * @description: ValueRedisRepository
 * @author: Jason
 * @create: 2020-03-06 11:00
 **/
abstract public class BaseValueRedisRepository<T> extends RedisRepositoryBase implements ValueRedisRepository<T> {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(BaseValueRedisRepository.class);

	public BaseValueRedisRepository(Map<String, StringRedisTemplate> stringRedisTemplateMap) {
		super(stringRedisTemplateMap);
	}

	/**
	 * 增加
	 * @param collection
	 * @param object
	 * @return
	 */
	@Override
	public boolean add(String collection, T object, String appName) {
		try {
			String jsonObject;
			if (object instanceof String) {
				jsonObject = object.toString();
			} else {
				jsonObject = OBJECT_MAPPER.writeValueAsString(object);
			}
			getRealStringRedisTemplate(appName).opsForValue().set(collection, jsonObject);
			return true;
		} catch (Exception e) {
			LOGGER.logError("Unable to add object to cache collection '"+collection+"': "+e.getMessage()+"", e);
			return false;
		}
	}

	@Override
	public boolean add(String collection, T object, String appName, long timeout, TimeUnit timeUnit) {
		try {
			String jsonObject;
			if (object instanceof String) {
				jsonObject = object.toString();
			} else {
				jsonObject = OBJECT_MAPPER.writeValueAsString(object);
			}
			getRealStringRedisTemplate(appName).opsForValue().set(collection, jsonObject, timeout, timeUnit);
			return true;
		} catch (Exception e) {
			LOGGER.logError("Unable to add object to cache collection '"+collection+"': "+e.getMessage()+"", e);
			return false;
		}
	}

	@Override
	public Boolean setIfAbsent(String collection, T object, String appName, long timeout, TimeUnit timeUnit) throws JsonProcessingException {
		String jsonObject;
		if (object instanceof String) {
			jsonObject = object.toString();
		} else {
			jsonObject = OBJECT_MAPPER.writeValueAsString(object);
		}
		return getRealStringRedisTemplate(appName).opsForValue().setIfAbsent(collection, jsonObject, timeout, timeUnit);
	}

	/**
	 * 删除
	 * @param collection
	 * @return
	 */
	@Override
	public boolean delete(String collection, String appName) {
		try {
			getRealStringRedisTemplate(appName).delete(collection);
			return true;
		} catch (Exception e) {
			LOGGER.logError("Unable to delete from cache collection '"+collection+"': "+e.getMessage()+"", e);
			return false;
		}
	}

	/**
	 * 查找
	 * @param collection
	 * @param tClass
	 * @return
	 */
	@Override
	@SuppressWarnings("unchecked")
	public T find(String collection, Class<T> tClass, String appName) {
		try {
			ValueOperations<String, String> operations = getRealStringRedisTemplate(appName).opsForValue();
			String jsonObj = operations.get(collection);
			if (tClass.equals(String.class)) {
				return (T) jsonObj;
			}
			return OBJECT_MAPPER.readValue(jsonObj, tClass);
		} catch (Exception e) {
			if(e.getMessage() == null){
				LOGGER.logError("Entry '"+collection+"' does not exist in cache", e);
			} else {
				LOGGER.logError("Unable to find entry '"+collection+"' in cache collection '"+collection+"': "+e.getMessage()+"", e);
			}
			return null;
		}
	}

}
