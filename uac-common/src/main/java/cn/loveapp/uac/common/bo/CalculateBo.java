package cn.loveapp.uac.common.bo;

import cn.loveapp.uac.common.entity.PromotionActivity;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: uac-service-group
 * @description: PromotionActivityBo
 * @author: <PERSON>
 * @create: 2020-03-09 13:31
 **/
@Data
@ToString
public class CalculateBo {
	private PromotionActivity promotionActivity;
	private String sellerNick;
	private Integer level;
	private LocalDateTime cycleEndTime;
	private String promotionItemCode;

	/**
	 * 新手村
	 */
	private Boolean hasGivePresent;
	private LocalDateTime currentDateTime;
	private int targetLevel;

	/**
	 * 当前处理的赠送记录id
	 */
	private List<Integer> currentIds;
}
