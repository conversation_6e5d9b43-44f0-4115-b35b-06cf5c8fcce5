package cn.loveapp.uac.common.api.response;

import cn.loveapp.uac.common.api.domain.SellerArticleSub;
import cn.loveapp.uac.common.code.taobao.ApiCodeConstant;
import cn.loveapp.uac.common.code.taobao.ApiCodeConstant.CodeEnum;
import com.taobao.api.TaobaoResponse;
import java.util.List;
import lombok.Data;

/**
 * @program: uac-service-group
 * @description: SellerVasSubscSearchResponse
 * @author: Jason
 * @create: 2020-03-06 10:28
 **/
@Data
public class SellerVasSubscSearchResponse extends BaseResponse {
	private List<SellerArticleSub> articleSubs;
	private Long totalItem;

}
