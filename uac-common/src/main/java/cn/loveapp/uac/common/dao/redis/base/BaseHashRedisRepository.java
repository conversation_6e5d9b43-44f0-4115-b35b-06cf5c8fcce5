package cn.loveapp.uac.common.dao.redis.base;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.dao.redis.HashCacheRepository;
import cn.loveapp.uac.common.service.DistributeUserProcessService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.List;
import java.util.Map;

/**
 * @program: uac-service-group
 * @description: CacheRepository
 * @author: Jason
 * @create: 2020-03-05 09:14
 **/
abstract public class BaseHashRedisRepository<T> extends RedisRepositoryBase implements HashCacheRepository<T> {
	private static LoggerHelper LOGGER = LoggerHelper.getLogger(BaseHashRedisRepository.class);

	public BaseHashRedisRepository(Map<String, StringRedisTemplate> stringRedisTemplateMap) {
		super(stringRedisTemplateMap);
	}
	
	@Autowired
	private DistributeUserProcessService distributeUserProcessService;

	@Override
	public boolean add(String collection, String hkey, T object, String appName) {
        if (StringUtils.isEmpty(collection)) {
            return false;
        }
		try {
			String jsonObject = OBJECT_MAPPER.writeValueAsString(object);
			getRealStringRedisTemplate(appName).opsForHash().put(collection, hkey, jsonObject);
			return true;
		} catch (Exception e) {
			LOGGER.logError("Unable to add object of key "+hkey+" to cache collection '"+collection+"': "+e.getMessage()+"", e);
			return false;
		}
	}

	@Override
	public boolean putAll(String collection, T t, String platformId, String appName) {
        if (StringUtils.isEmpty(collection)) {
            return false;
        }
        try {
            Map entries = OBJECT_MAPPER.convertValue(t, Map.class);
            entries = distributeUserProcessService.appendUserRedisField(entries, platformId, appName, true);

            getRealStringRedisTemplate(appName).opsForHash().putAll(collection, entries);
            return true;
		} catch (Exception e) {
			LOGGER.logError("Unable to add object of key "+collection+" to cache collection '"+collection+"': "+e.getMessage()+"", e);
			return false;
		}
	}

	@Override
	public boolean put(String collection, String hkey, String v, String appName) {
        if (StringUtils.isEmpty(collection)) {
            return false;
        }
		try {
			getRealStringRedisTemplate(appName).opsForHash().put(collection, hkey, v);
			return true;
		} catch (Exception e) {
			LOGGER.logError("Unable to add object of key "+hkey+" to cache collection '"+collection+"': "+e.getMessage()+"", e);
			return false;
		}
	}

	@Override
	public boolean delete(String collection, String hkey, String appName) {
        if (StringUtils.isEmpty(collection)) {
            return false;
        }
		try {
			getRealStringRedisTemplate(appName).opsForHash().delete(collection, hkey);
			return true;
		} catch (Exception e) {
			LOGGER.logError("Unable to delete entry "+hkey+" from cache collection '"+collection+"': "+e.getMessage()+"", e);
			return false;
		}
	}

	@Override
	public String find(String collection, String hkey, String appName) {
        if (StringUtils.isEmpty(collection)) {
            return null;
        }
		try {
			HashOperations<String, String ,String> op = getRealStringRedisTemplate(appName).opsForHash();
			return op.get(collection, hkey);
		} catch (Exception e) {
			if(e.getMessage() == null){
				LOGGER.logError("Entry '"+hkey+"' does not exist in cache", e);
			} else {
				LOGGER.logError("Unable to find entry '"+hkey+"' in cache collection '"+collection+"': "+e.getMessage()+"", e);
			}
			return null;
		}
	}

	public List<String> find(String collection, List<String> hkey, String appName) {
        if (StringUtils.isEmpty(collection)) {
            return null;
        }
		try {
			HashOperations<String, String ,String> op = getRealStringRedisTemplate(appName).opsForHash();
			return op.multiGet(collection, hkey);
		} catch (Exception e) {
			if(e.getMessage() == null){
				LOGGER.logError("Entry '"+hkey+"' does not exist in cache", e);
			} else {
				LOGGER.logError("Unable to find entry '"+hkey+"' in cache collection '"+collection+"': "+e.getMessage()+"", e);
			}
			return null;
		}
	}

	/**
	 * 查找全部(兼容代发平台)
	 */
	@Override
	public T findAll(String collection, Class<T> tClass, String platformId, String appName) {
		if (StringUtils.isEmpty(collection)) {
			return null;
		}
		try {
            Map<Object, Object> entries = getRealStringRedisTemplate(appName).opsForHash().entries(collection);
            entries = distributeUserProcessService.appendUserRedisField(entries, platformId, appName, false);

			return OBJECT_MAPPER.convertValue(entries, tClass);
		} catch (Exception e) {
			if(e.getMessage() == null){
				LOGGER.logError("Entry '"+collection+"' does not exist in cache", e);
			} else {
				LOGGER.logError("Unable to find entry '"+collection+"' in cache collection '"+collection+"': "+e.getMessage()+"", e);
			}
			return null;
		}
	}
}
