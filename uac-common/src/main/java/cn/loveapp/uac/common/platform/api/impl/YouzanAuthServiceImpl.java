package cn.loveapp.uac.common.platform.api.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.youzan.YouzanSDKService;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.config.DistributeConfig;
import cn.loveapp.uac.common.config.youzan.YouzanAppConfig;
import cn.loveapp.uac.common.platform.api.AuthService;
import cn.loveapp.uac.common.platform.api.domain.RefreshTokenCallbackResult;
import cn.loveapp.uac.common.utils.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.ImmutableMap;
import com.youzan.cloud.open.sdk.core.oauth.model.OAuthToken;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 有赞授权相关service
 *
 * <AUTHOR>
 * @date 2022/9/17
 */
@Service
public class YouzanAuthServiceImpl extends BaseAuthServiceImpl implements AuthService {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(YouzanAuthServiceImpl.class);

    @Value("${uac.network.retry.count:5}")
    private Integer retryCount;

    @Value("${uac.refresh-token.youzan.trade.enabled:false}")
    private Boolean tradeRefreshTokenEnabled;


    @Autowired
    private YouzanSDKService youzanSDKService;

    public YouzanAuthServiceImpl(YouzanAppConfig appConfig, DistributeConfig distributeConfig) {
        super(ImmutableMap.of(ALL_APP, appConfig), distributeConfig);
    }

    @Override
    public Integer getRetryCount() {
        return retryCount;
    }

    /**
     * 通过code获取callbackResult
     */
    @Override
    @SuppressWarnings("unchecked")
    public RefreshTokenCallbackResult getCallbackResultByCode(String code, String platformId, String appName) throws Exception {
        return null;
    }

    /**
     * 刷新refrshToken
     */
    @Override
    @SuppressWarnings("unchecked")
    public RefreshTokenCallbackResult refreshToken(UserInfoBo userInfoBo, String refreshToken, String platformId, String appName) throws Exception {
        if (CommonAppConstants.APP_TRADE.equals(appName) && BooleanUtils.isFalse(tradeRefreshTokenEnabled)) {
            LOGGER.logWarn(userInfoBo.getSellerNick(), "-", "有赞交易应用不支持刷新token");
            return null;
        }
        try {
            OAuthToken response = youzanSDKService.refreshToken(refreshToken, appName);
            if (response == null || StringUtils.isEmpty(response.getAccessToken())) {
                LOGGER.logError(userInfoBo.getSellerNick(), "-", "refresh_token刷新返回信息异常 result => " + JSON.toJSONString(response, "yyyy-MM-dd HH:mm:ss"));
                return null;
            }
            RefreshTokenCallbackResult callbackResult = new RefreshTokenCallbackResult();
            String encryptAccessToken = encryptToken(response.getAccessToken(), platformId, ALL_APP);
            callbackResult.setAccessToken(encryptAccessToken);
            callbackResult.setDecryptAccessToken(response.getAccessToken());
            String encryptRefreshToken = encryptToken(response.getRefreshToken(), platformId, ALL_APP);
            callbackResult.setRefreshToken(encryptRefreshToken);
            callbackResult.setDecryptRefreshToken(response.getRefreshToken());
            callbackResult.setExpiresIn((response.getExpires() - System.currentTimeMillis())/1000L);
            callbackResult.setSellerId(userInfoBo.getSellerId());
            callbackResult.setSellerNick(userInfoBo.getSellerNick());
            callbackResult.setSubSellerNick(userInfoBo.getSubSellerNick());
            callbackResult
                .setAuthDeadLine(DateUtil.calculateCustomSecond(DateUtil.currentDate(), callbackResult.getExpiresIn()));
            return callbackResult;

        } catch (Exception e) {
            LOGGER.logError(userInfoBo.getSellerNick(), "-", "refresh_token刷新出现异常: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_YOUZAN;
    }

}
