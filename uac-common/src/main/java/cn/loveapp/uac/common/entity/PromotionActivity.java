package cn.loveapp.uac.common.entity;

import java.time.LocalDateTime;
import lombok.Data;
import lombok.ToString;

/**
 * @program: uac-service-group
 * @description: PromotionActivity
 * @author: <PERSON>
 * @create: 2020-03-09 12:18
 **/
@Data
@ToString
public class PromotionActivity {

	private Integer id;
	//卖家昵称
	private String sellernick;
	//赠送的时长，单位天
	private Integer actCycle;
	//何时给用户赠送运营时长的时间
	private LocalDateTime optime;
	//活动类型
	private String actflag;
	//赠送的客服名字
	private String sender;
	//活动Code
	private String promotionCode;
	//是否已经使用
	private Boolean isused;

	public static final Boolean USED = true;
	public static final Boolean UNUSED = false;

	public static final String TRANSFER_ACTFLAG = "transfer";
	public static final String BUYER_ACTFLAG = "buyer";
}
