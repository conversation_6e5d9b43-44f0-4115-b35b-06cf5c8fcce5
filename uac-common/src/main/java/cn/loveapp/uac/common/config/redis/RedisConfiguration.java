package cn.loveapp.uac.common.config.redis;

import io.lettuce.core.api.StatefulConnection;
import io.lettuce.core.resource.ClientResources;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties.Pool;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisPassword;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration.LettuceClientConfigurationBuilder;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.StringUtils;

import java.net.URI;
import java.net.URISyntaxException;
import java.net.UnknownHostException;

/**
 * RdsRedisConfiguration class
 * <AUTHOR>
 */
@Configuration
public class RedisConfiguration {

	@Bean("redisShophelperProperties")
	@ConfigurationProperties(prefix = "spring.data.redis.shophelper")
	public RedisProperties getShophelperRedisProperties() {
		RedisProperties redisProperties = new RedisProperties();
		return redisProperties;
	}

	@Bean("redisItemProperties")
	@ConfigurationProperties(prefix = "spring.data.redis.item")
	public RedisProperties getItemRedisProperties() {
		RedisProperties redisProperties = new RedisProperties();
		return redisProperties;
	}

	@Bean("redisTradeProperties")
	@ConfigurationProperties(prefix = "spring.data.redis.trade")
	public RedisProperties getTradeRedisProperties() {
		RedisProperties redisProperties = new RedisProperties();
		return redisProperties;
	}

    @Bean("redisDistributeProperties")
    @ConfigurationProperties(prefix = "spring.data.redis.distribute")
    public RedisProperties getDistributeRedisProperties() {
        RedisProperties redisProperties = new RedisProperties();
        return redisProperties;
    }

	@Bean(name = "redisTradeConnectionFactory")
	@Primary
	public LettuceConnectionFactory redisTradeConnectionFactory(
		@Qualifier("redisTradeProperties") RedisProperties properties) throws UnknownHostException {
		ClientResources clientResources = ClientResources.create();
		LettuceClientConfiguration clientConfig = getLettuceClientConfiguration(clientResources, properties.getLettuce().getPool(), properties);
		return createLettuceConnectionFactory(clientConfig, properties);
	}

	@Bean(name = "redisItemConnectionFactory")
	public LettuceConnectionFactory redisItemConnectionFactory(
		@Qualifier("redisItemProperties") RedisProperties properties) throws UnknownHostException {
		ClientResources clientResources = ClientResources.create();
		LettuceClientConfiguration clientConfig = getLettuceClientConfiguration(clientResources, properties.getLettuce().getPool(), properties);
		return createLettuceConnectionFactory(clientConfig, properties);
	}

	@Bean(name = "redisShophelperConnectionFactory")
	public LettuceConnectionFactory redisShophelperConnectionFactory(
			@Qualifier("redisShophelperProperties") RedisProperties properties) throws UnknownHostException {
		ClientResources clientResources = ClientResources.create();
		LettuceClientConfiguration clientConfig = getLettuceClientConfiguration(clientResources, properties.getLettuce().getPool(), properties);
		return createLettuceConnectionFactory(clientConfig, properties);
	}

    @Bean(name = "redisDistributeConnectionFactory")
    public LettuceConnectionFactory
        redisDistributeConnectionFactory(@Qualifier("redisDistributeProperties") RedisProperties properties) {
        ClientResources clientResources = ClientResources.create();
        LettuceClientConfiguration clientConfig =
            getLettuceClientConfiguration(clientResources, properties.getLettuce().getPool(), properties);
        return createLettuceConnectionFactory(clientConfig, properties);
    }

	private LettuceConnectionFactory createLettuceConnectionFactory(LettuceClientConfiguration clientConfiguration, RedisProperties properties) {
		return new LettuceConnectionFactory(getStandaloneConfig(properties), clientConfiguration);
	}

	protected final RedisStandaloneConfiguration getStandaloneConfig(RedisProperties properties) {
		RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
		config.setHostName(properties.getHost());
		config.setPort(properties.getPort());
		config.setPassword(RedisPassword.of(properties.getPassword()));
		config.setDatabase(properties.getDatabase());
		return config;
	}

	private LettuceClientConfiguration getLettuceClientConfiguration(ClientResources clientResources, Pool pool, RedisProperties properties) {
		LettuceClientConfigurationBuilder builder = createBuilder(pool);
		applyProperties(builder, properties);
		if (StringUtils.hasText(properties.getUrl())) {
			customizeConfigurationFromUrl(builder, properties);
		}
		builder.clientResources(clientResources);
		return builder.build();
	}

	private LettuceClientConfigurationBuilder createBuilder(Pool pool) {
		if (pool == null) {
			return LettuceClientConfiguration.builder();
		}
		return new PoolBuilderFactory().createBuilder(pool);
	}

	private LettuceClientConfigurationBuilder applyProperties(
		LettuceClientConfigurationBuilder builder, RedisProperties properties) {
        if (properties.getSsl() != null && properties.getSsl().isEnabled()) {
            builder.useSsl();
        }
		if (properties.getTimeout() != null) {
			builder.commandTimeout(properties.getTimeout());
		}
		if (properties.getLettuce() != null) {
			RedisProperties.Lettuce lettuce = properties.getLettuce();
			if (lettuce.getShutdownTimeout() != null
				&& !lettuce.getShutdownTimeout().isZero()) {
				builder.shutdownTimeout(
					properties.getLettuce().getShutdownTimeout());
			}
		}
		return builder;
	}

	private void customizeConfigurationFromUrl(
		LettuceClientConfigurationBuilder builder, RedisProperties properties) {
		ConnectionInfo connectionInfo = parseUrl(properties.getUrl());
		if (connectionInfo.isUseSsl()) {
			builder.useSsl();
		}
	}

	protected ConnectionInfo parseUrl(String url) {
		try {
			URI uri = new URI(url);
			boolean useSsl = (url.startsWith("rediss://"));
			String password = null;
			if (uri.getUserInfo() != null) {
				password = uri.getUserInfo();
				int index = password.indexOf(':');
				if (index >= 0) {
					password = password.substring(index + 1);
				}
			}
			return new ConnectionInfo(uri, useSsl, password);
		}
		catch (URISyntaxException ex) {
			throw new IllegalArgumentException("Malformed url '" + url + "'", ex);
		}
	}


	protected static class ConnectionInfo {

		private final URI uri;

		private final boolean useSsl;

		private final String password;

		public ConnectionInfo(URI uri, boolean useSsl, String password) {
			this.uri = uri;
			this.useSsl = useSsl;
			this.password = password;
		}

		public boolean isUseSsl() {
			return this.useSsl;
		}

		public String getHostName() {
			return this.uri.getHost();
		}

		public int getPort() {
			return this.uri.getPort();
		}

		public String getPassword() {
			return this.password;
		}

	}

	/**
	 * Inner class to allow optional commons-pool2 dependency.
	 */
	private static class PoolBuilderFactory {

		public LettuceClientConfigurationBuilder createBuilder(Pool properties) {
			return LettucePoolingClientConfiguration.builder()
				.poolConfig(getPoolConfig(properties));
		}

		private GenericObjectPoolConfig<StatefulConnection<?, ?>> getPoolConfig(Pool properties) {
			GenericObjectPoolConfig<StatefulConnection<?, ?>> config = new GenericObjectPoolConfig<>();
			config.setMaxTotal(properties.getMaxActive());
			config.setMaxIdle(properties.getMaxIdle());
			config.setMinIdle(properties.getMinIdle());
			if (properties.getMaxWait() != null) {
				config.setMaxWaitMillis(properties.getMaxWait().toMillis());
			}
			return config;
		}

	}

	@Bean(name = "stringTradeRedisTemplate")
	public StringRedisTemplate stringTradeRedisTemplate(@Qualifier("redisTradeConnectionFactory") RedisConnectionFactory cf) {
		StringRedisTemplate stringRedisTemplate = new StringRedisTemplate();
		stringRedisTemplate.setConnectionFactory(cf);
		return stringRedisTemplate;
	}

	@Bean(name = "stringItemRedisTemplate")
	public StringRedisTemplate stringItemRedisTemplate(@Qualifier("redisItemConnectionFactory") RedisConnectionFactory cf) {
		StringRedisTemplate stringRedisTemplate = new StringRedisTemplate();
		stringRedisTemplate.setConnectionFactory(cf);
		return stringRedisTemplate;
	}

	@Bean(name = "stringShophelperRedisTemplate")
	public StringRedisTemplate stringShophelperRedisTemplate(@Qualifier("redisShophelperConnectionFactory") RedisConnectionFactory cf) {
		StringRedisTemplate stringRedisTemplate = new StringRedisTemplate();
		stringRedisTemplate.setConnectionFactory(cf);
		return stringRedisTemplate;
	}

    @Bean(name = "stringDistributeRedisTemplate")
    public StringRedisTemplate
        stringDistributeRedisTemplate(@Qualifier("redisDistributeConnectionFactory") RedisConnectionFactory cf) {
        StringRedisTemplate stringRedisTemplate = new StringRedisTemplate();
        stringRedisTemplate.setConnectionFactory(cf);
        return stringRedisTemplate;
    }

}
