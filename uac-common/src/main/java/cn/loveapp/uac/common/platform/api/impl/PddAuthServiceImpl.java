package cn.loveapp.uac.common.platform.api.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.config.AppConfig;
import cn.loveapp.uac.common.config.DistributeConfig;
import cn.loveapp.uac.common.config.pdd.*;
import cn.loveapp.uac.common.platform.api.AuthService;
import cn.loveapp.uac.common.platform.api.domain.RefreshTokenCallbackResult;
import cn.loveapp.uac.common.platform.api.domain.PddRefreshTokenCallbackResult;
import cn.loveapp.uac.common.utils.DateUtil;
import com.google.common.collect.ImmutableMap;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * @program: uac-service-group
 * @description: PddAuthServiceImpl
 * @author: Jason
 * @create: 2020-03-12 19:18
 **/
@Service
public class PddAuthServiceImpl extends BaseAuthServiceImpl implements AuthService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PddAuthServiceImpl.class);

	@Value("${uac.network.retry.count:5}")
	private Integer retryCount;

    public PddAuthServiceImpl(PDDTradeAppConfig pddTradeAppConfig, PDDItemAppConfig pddItemAppConfig,
        PDDGuanDianAppConfig pddGuanDianAppConfig, PDDDistributeAppConfig pddDistributeAppConfig,
        PDDWaybillAppConfig pddWaybillAppConfig, PDDTradeERPAppConfig pddTradeERPAppConfig,
        DistributeConfig distributeConfig) {
        super(new ImmutableMap.Builder<String, AppConfig>().put(CommonAppConstants.APP_TRADE, pddTradeAppConfig)
            .put(CommonAppConstants.APP_ITEM, pddItemAppConfig)
            .put(CommonAppConstants.APP_GUANDIAN, pddGuanDianAppConfig)
            .put(CommonAppConstants.APP_DISTRIBUTE, pddDistributeAppConfig)
            .put(CommonAppConstants.APP_WAYBILL, pddWaybillAppConfig)
            .put(CommonAppConstants.APP_TRADE_ERP, pddTradeERPAppConfig).build(), distributeConfig);
    }

	@Override
	public Integer getRetryCount() {
		return retryCount;
	}

	/**
	 * 通过code获取callbackResult
	 */
	@Override
	@SuppressWarnings("unchecked")
	public RefreshTokenCallbackResult getCallbackResultByCode(String code, String platformId, String appName) throws Exception {
		Map<String, String> headers = new HashMap<>(4);
		headers.put("Content-type", "application/json;charset='utf-8'");
		headers.put("Accept", "application/json");
		headers.put("Cache-Control", "no-cache");
		headers.put("Pragma", "no-cache");
		headers.put("User-Agent", "Apache-HttpClient/4.5.8 (Java/1.8.0_201)");
		RefreshTokenCallbackResult refreshTokenCallbackResult = new RefreshTokenCallbackResult();
		PddRefreshTokenCallbackResult pddCallbackResult = super.getCallbackResultByCode(code, CommonPlatformConstants.PLATFORM_PDD, headers, PddRefreshTokenCallbackResult.class, appName);
		if (Objects.isNull(pddCallbackResult) || !pddCallbackResult.isSuccess()) {
			return null;
		}
		String accessToken = encryptToken(pddCallbackResult.getAccessToken(), platformId, appName);
		String refreshToken = encryptToken(pddCallbackResult.getRefreshToken(), platformId, appName);
		/**
		 * 存一份没加密的返回直接能用
		 */
		refreshTokenCallbackResult.setDecryptAccessToken(pddCallbackResult.getAccessToken());
		refreshTokenCallbackResult.setDecryptRefreshToken(pddCallbackResult.getRefreshToken());
		refreshTokenCallbackResult.setAccessToken(accessToken);
		refreshTokenCallbackResult.setRefreshToken(refreshToken);
		refreshTokenCallbackResult.setSellerId(pddCallbackResult.getSellerId());
		refreshTokenCallbackResult.setSellerNick(pddCallbackResult.getSellerNick());
		refreshTokenCallbackResult.setSubSellerNick(pddCallbackResult.getSubSellerNick());
		refreshTokenCallbackResult.setSubSellerId(pddCallbackResult.getSubSellerId());
		refreshTokenCallbackResult.setExpiresIn(pddCallbackResult.getExpiresIn());
		refreshTokenCallbackResult.setScope(pddCallbackResult.getScope());
        refreshTokenCallbackResult
            .setAuthDeadLine(DateUtil.calculateCustomSecond(DateUtil.currentDate(), pddCallbackResult.getExpiresIn()));
		return refreshTokenCallbackResult;
	}

	/**
	 * 刷新refrshToken
	 */
	@Override
	@SuppressWarnings("unchecked")
	public RefreshTokenCallbackResult refreshToken(UserInfoBo userInfoBo, String refreshToken, String platformId, String appName) throws Exception {
		RefreshTokenCallbackResult refreshTokenCallbackResult = new RefreshTokenCallbackResult();
		Map<String, String> headers = new HashMap<>(4);
		headers.put("Content-type", "application/json;charset='utf-8'");
		headers.put("Accept", "application/json");
		headers.put("Cache-Control", "no-cache");
		headers.put("Pragma", "no-cache");
		headers.put("User-Agent", "Apache-HttpClient/4.5.8 (Java/1.8.0_201)");
		PddRefreshTokenCallbackResult pddCallbackResult = super.reGetAccessTokenWithRefreshToken(refreshToken, CommonPlatformConstants.PLATFORM_PDD, headers, PddRefreshTokenCallbackResult.class, appName);
		if (Objects.isNull(pddCallbackResult) || !pddCallbackResult.isSuccess()) {
			LOGGER.logInfo(refreshToken, refreshToken, "请求数据异常");
			return null;
		}
		String newAccessToken = encryptToken(pddCallbackResult.getAccessToken(), platformId, appName);
		String newRefreshToken = encryptToken(pddCallbackResult.getRefreshToken(), platformId, appName);
		/**
		 * 存一份没加密的返回直接能用
		 */
		refreshTokenCallbackResult.setDecryptAccessToken(pddCallbackResult.getAccessToken());
		refreshTokenCallbackResult.setDecryptRefreshToken(pddCallbackResult.getRefreshToken());
		refreshTokenCallbackResult.setAccessToken(newAccessToken);
		refreshTokenCallbackResult.setRefreshToken(newRefreshToken);
		refreshTokenCallbackResult.setSellerId(pddCallbackResult.getSellerId());
		refreshTokenCallbackResult.setSellerNick(pddCallbackResult.getSellerNick());
		refreshTokenCallbackResult.setSubSellerNick(pddCallbackResult.getSubSellerNick());
		refreshTokenCallbackResult.setSubSellerId(pddCallbackResult.getSubSellerId());
		refreshTokenCallbackResult.setExpiresIn(pddCallbackResult.getExpiresIn());
		refreshTokenCallbackResult.setScope(pddCallbackResult.getScope());
        refreshTokenCallbackResult
            .setAuthDeadLine(DateUtil.calculateCustomSecond(DateUtil.currentDate(), pddCallbackResult.getExpiresIn()));
		return refreshTokenCallbackResult;
	}

	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_PDD;
	}
}
