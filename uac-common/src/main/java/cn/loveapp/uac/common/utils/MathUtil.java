package cn.loveapp.uac.common.utils;

import org.springframework.util.StringUtils;

/**
 * @program: orders-services-group
 * @description: MathUtil
 * @author: <PERSON>
 * @create: 2018-11-26 13:06
 **/
public class MathUtil {

	public static Double parseDouble(String s) {
		if (StringUtils.isEmpty(s)) {
			return 0.0;
		}
		return Double.parseDouble(s);
	}

	public static String parseLong(Long l) {
		if (null == l || 0L == l) {
			return null;
		}
		return l.toString();
	}

	public static Long parseStringByLong(String s) {
		if (StringUtils.isEmpty(s)) {
			return null;
		}
		return Long.parseLong(s);
	}

	public static Integer parseInteger(Long l) {
		if (null == l || 0L == l) {
			return 0;
		}
		return l.intValue();
	}

	public static Double parseLongByDouble(Long l) {
		if (null == l || 0L == l) {
			return 0.0;
		}
		return l.doubleValue();
	}

	public static Integer parseString(String s) {
		if (StringUtils.isEmpty(s)) {
			return 0;
		}
		return Integer.parseInt(s);
	}

}
