package cn.loveapp.uac.common.api.response;

import cn.loveapp.uac.common.code.taobao.ApiCodeConstant;
import cn.loveapp.uac.common.code.taobao.ApiCodeConstant.CodeEnum;
import com.taobao.api.TaobaoResponse;

/**
 * @program: uac-service-group
 * @description: BaseResponse
 * @author: <PERSON>
 * @create: 2020-05-27 15:40
 **/
public class BaseResponse extends TaobaoResponse {

	public CodeEnum hasSuccess() {
		String errorCode = getErrorCode();
		String subCode = getSubCode();
		String flag = getFlag();
		if(ApiCodeConstant.getIgnoreErrorCode().contains(errorCode)) {
			return CodeEnum.CALL_LIMIT;
		}
		boolean code = (errorCode == null || errorCode.length() == 0 || "0".equals(errorCode)) && (subCode == null || subCode.length() == 0) && (flag == null || flag.length() == 0);
		if (code) {
			return CodeEnum.SUCCESS;
		} else {
			return CodeEnum.ERROR;
		}
	}

}
