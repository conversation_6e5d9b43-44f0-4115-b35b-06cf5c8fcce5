package cn.loveapp.uac.common.platform.api.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.uac.common.api.request.SellerVasOrderSearchRequest;
import cn.loveapp.uac.common.api.request.SellerVasSubscSearchRequest;
import cn.loveapp.uac.common.api.request.SellerVasSubscribeGetRequest;
import cn.loveapp.uac.common.api.response.SellerVasOrderSearchResponse;
import cn.loveapp.uac.common.api.response.SellerVasSubscSearchResponse;
import cn.loveapp.uac.common.api.response.SellerVasSubscribeGetResponse;
import cn.loveapp.uac.common.platform.api.AppStoreService;
import org.springframework.stereotype.Service;

/**
 * @program: uac-service-group
 * @description: Ali1688AppStoreServiceImpl
 * @author: Jason
 * @create: 2021-04-20 15:37
 **/
@Service
public class Ali1688AppStoreServiceImpl implements AppStoreService {

	/**
	 * TODO: 目前暂不支持服务市场相关逻辑，等入口接入后需要修复
	 * @param sellerVasSubscribeGetRequest
	 * @param platformId
	 * @param appName
	 * @return
	 */
	@Override
	public SellerVasSubscribeGetResponse vasSubscribeGet(SellerVasSubscribeGetRequest sellerVasSubscribeGetRequest, String platformId, String appName) {
		return null;
	}

	@Override
	public SellerVasSubscSearchResponse vasSubscribeSearch(SellerVasSubscSearchRequest sellerVasSubscSearchRequest, String platformId, String appName) {
		return null;
	}

	@Override
	public SellerVasOrderSearchResponse vasOrderSearch(SellerVasOrderSearchRequest vasOrderSearchRequest, String platformId, String appName) {
		return null;
	}

	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_1688;
	}
}
