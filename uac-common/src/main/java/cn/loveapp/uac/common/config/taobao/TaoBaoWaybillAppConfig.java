package cn.loveapp.uac.common.config.taobao;

import cn.loveapp.uac.common.config.AppConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 淘宝 电子面单应用 配置
 */
@EqualsAndHashCode(callSuper = true)
@Configuration
@Data
@ConfigurationProperties(prefix = "uac.taobao.waybill.app")
public class TaoBaoWaybillAppConfig extends AppConfig {
    private String rebuildUserUrl;
    private String aesDecryptUrl;
}
