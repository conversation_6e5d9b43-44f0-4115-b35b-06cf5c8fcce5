package cn.loveapp.uac.common.config.pdd;

import cn.loveapp.uac.common.config.AppConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @program: uac-service-group
 * @description: ItemAppConfig
 * @author: <PERSON>
 * @create: 2020-03-12 19:15
 **/
@Data
@Configuration
@ConfigurationProperties(prefix = "uac.pdd.item.app")
public class PDDItemAppConfig extends AppConfig {
}
