package cn.loveapp.uac.common.dao.redis;

import com.fasterxml.jackson.core.JsonProcessingException;

import java.util.concurrent.TimeUnit;

/**
 * @program: uac-service-group
 * @description: ValueRedisRepository
 * @author: <PERSON>
 * @create: 2020-03-06 11:09
 **/
 public interface ValueRedisRepository<T> {
	/**
	 * 增加
	 * @param collection
	 * @param object
	 * @return
	 */
	 boolean add(String collection, T object, String appName);

	/**
	 * 增加且设置超时时间
	 *
	 * @param collection
	 * @param object
	 * @param appName
	 * @param timeout
	 * @param timeUnit
	 * @return
	 */
	 boolean add(String collection, T object, String appName, long timeout, TimeUnit timeUnit);

	/**
	 * 同时设置值和超时时间，如果存在则设置失败
	 *
	 * @param collection
	 * @param object
	 * @param appName
	 * @param timeout
	 * @param timeUnit
	 * @return
	 * @throws Exception
	 */
	 Boolean setIfAbsent(String collection, T object, String appName, long timeout, TimeUnit timeUnit) throws JsonProcessingException;

	/**
	 * 删除
	 * @param collection
	 * @return
	 */
	 boolean delete(String collection, String appName);

	/**
	 * 查找
	 * @param collection
	 * @param tClass
	 * @return
	 */
	 T find(String collection, Class<T> tClass, String appName);

	/**
	 * 初始化collection
	 * @param collection
	 */
	String initCollection(String collection, String platformId, String appName);
}
