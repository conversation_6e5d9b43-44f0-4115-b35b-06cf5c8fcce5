package cn.loveapp.uac.common.config.pdd;

import cn.loveapp.uac.common.config.AppConfig;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @program: orders-services-group
 * @description: PDDAppConfig
 * @author: Jason
 * @create: 2019-11-19 14:24
 **/
@Data
@Configuration
@ConfigurationProperties(prefix = "uac.pdd.trade.app")
public class PDDTradeAppConfig extends AppConfig {
}
