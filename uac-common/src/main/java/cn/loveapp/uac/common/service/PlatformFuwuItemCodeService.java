package cn.loveapp.uac.common.service;

import cn.loveapp.common.autoconfigure.platform.CommonPlatformHandler;

/**
 * 多平台服务市场 item_code 相关配置信息
 *
 * <AUTHOR>
 */
public interface PlatformFuwuItemCodeService extends CommonPlatformHandler {
    /**
     * 获取指定itemCode 对应的vipFlag 信息
     *
     * @param platformId
     * @param appName
     * @return
     */
    Integer obtainVipFlag(String itemCode, String platformId, String appName);

    /**
     * 获取指定 vipFlag 对应的 itemCode  信息
     *
     * @param vipFlag
     * @param platformId
     * @param appName
     * @return
     */
    String obtainItemCode(Integer vipFlag, String platformId, String appName);

    /**
     * 获取指定appName 自动续费的item_code
     *
     * @param platformId
     * @param appName
     * @return
     */
    String obtainAutoRenewItemCode(String platformId, String appName);
}
