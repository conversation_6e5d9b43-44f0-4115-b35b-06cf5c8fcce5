package cn.loveapp.uac.common.bo;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.uac.common.constant.CommonConstant;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.common.entity.redis.UserRedisEntity;
import cn.loveapp.uac.response.CallbackResponse;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @program: uac-service-group
 * @description: UserBo
 * @author: Jason
 * @create: 2020-03-09 16:43
 **/
@Data
@NoArgsConstructor
public class UserBo {
    private String platformId;
    private String appType;
    private String sellerNick;
    private String subSellerNick;
    private String decryptAccessToken;

    private String sellerId;

    private String sellerAppId;

    /**
     *  成员id（1688）
     */
    private String memberId;

    /**
     * 店铺id（TikTok）
     */
    private String shopId;

    private Boolean hasReadTag;

    /**
     * use scope Update or insert
     */
    @Setter(AccessLevel.NONE)
    private UserProductInfo userProductInfo;

    /**
     * use scopre update or insert
     */
    @Setter(AccessLevel.NONE)
    private UserRedisEntity userRedisEntity;

    public UserBo(UserInfoBo userInfoBo) {
        this.platformId = userInfoBo.getPlatformId();
        this.appType = userInfoBo.getAppType();
        this.sellerNick = userInfoBo.getSellerNick();
        this.sellerId = userInfoBo.getSellerId();
        this.sellerAppId = userInfoBo.getSellerAppId();
        this.memberId = userInfoBo.getMemberId();
        this.shopId = userInfoBo.getShopId();
    }

    public UserBo(String platformId, String appType, String sellerNick, String sellerId) {
        this.platformId = platformId;
        this.appType = appType;
        this.sellerNick = sellerNick;
        this.sellerId = sellerId;
    }

    public UserBo(String platformId, String appType, String sellerNick, UserProductInfo userProductInfo) {
        this.platformId = platformId;
        this.appType = appType;
        this.sellerNick = sellerNick;
        setUserProductInfo(userProductInfo);
    }

    public void setUserProductInfo(UserProductInfo userProductInfo) {
        this.userProductInfo = userProductInfo;
        if(userProductInfo != null){
            if(this.sellerId == null){
                this.sellerId = userProductInfo.getUserIdStr();
            }
            if(this.sellerAppId == null){
                this.sellerAppId = userProductInfo.getAppId();
            }
            if(this.memberId == null) {
                this.memberId = userProductInfo.getMemberId();
            }
        }
        if (platformId.equals(CommonPlatformConstants.PLATFORM_PDD)) {
            if (CommonConstant.OLD_APP_NAME_LIST.contains(appType)) {
                this.userRedisEntity = userProductInfo.toPddUserRedisEntity();
            } else {
                // 新应用不会出现key重复问题，走新逻辑
                this.userRedisEntity = userProductInfo.toMultiPlatformRedisEntity();
            }
        } else if (platformId.equals(CommonPlatformConstants.PLATFORM_TAO)) {
            if (CommonConstant.OLD_APP_NAME_LIST.contains(appType)) {
                this.userRedisEntity = userProductInfo.toUserRedisEntity();
            } else {
                // 新应用不会出现key重复问题，走新逻辑
                this.userRedisEntity = userProductInfo.toMultiPlatformRedisEntity();
            }
        } else if (appType.equals(CommonAppConstants.APP_DISTRIBUTE)
            && !CommonPlatformConstants.PLATFORM_BIYAO.equals(platformId)) {
            this.userRedisEntity = userProductInfo.toDistributePlatformRedisEntity();
        } else {
            this.userRedisEntity = userProductInfo.toMultiPlatformRedisEntity();
        }

        // 设置多店tag
        if (this.userRedisEntity != null) {
            this.userRedisEntity.setAyMultiTags(userProductInfo.getAyMultiTagStr());
        }
    }

    public CallbackResponse toCallbackResponse() {
        return CallbackResponse.of(sellerNick, decryptAccessToken);
    }
}
