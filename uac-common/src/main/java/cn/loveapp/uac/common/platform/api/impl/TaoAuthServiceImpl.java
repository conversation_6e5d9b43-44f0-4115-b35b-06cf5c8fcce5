package cn.loveapp.uac.common.platform.api.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.AesUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.config.DistributeConfig;
import cn.loveapp.uac.common.config.taobao.*;
import cn.loveapp.uac.common.entity.redis.UserRedisEntity;
import cn.loveapp.uac.common.platform.api.AuthService;
import cn.loveapp.uac.common.platform.api.domain.RefreshTokenCallbackResult;
import cn.loveapp.uac.common.platform.api.domain.TaobaoRefreshTokenCallbackResult;
import cn.loveapp.uac.common.utils.DateUtil;
import com.google.common.collect.ImmutableMap;

import java.time.LocalDateTime;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.crypto.IllegalBlockSizeException;

/**
 * @program: uac-service-group
 * @description: TaobaoTradeAuthServiceImpl
 * @author: Jason
 * @create: 2020-03-10 11:10
 **/
@Service
public class TaoAuthServiceImpl extends BaseAuthServiceImpl implements AuthService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(TaoAuthServiceImpl.class);

	@Value("${uac.network.retry.count:5}")
	private Integer retryCount;

	public TaoAuthServiceImpl(TaoBaoTradeAppConfig taobaoTradeAppConfig, TaoBaoItemAppConfig taoBaoItemAppConfig,
							  TaoBaoWaybillAppConfig taoBaoWaybillAppConfig,
							  TaoBaoTradeSupplierAppConfig taoBaoTradeSupplierAppConfig,
                              TaoBaoTradeERPAppConfig taoBaoTradeERPAppConfig,
							  DistributeConfig distributeConfig) {
		super(ImmutableMap.of(
				CommonAppConstants.APP_TRADE, taobaoTradeAppConfig,
				CommonAppConstants.APP_ITEM, taoBaoItemAppConfig,
				CommonAppConstants.APP_WAYBILL, taoBaoWaybillAppConfig,
				CommonAppConstants.APP_TRADE_SUPPLIER, taoBaoTradeSupplierAppConfig,
				CommonAppConstants.APP_TRADE_ERP, taoBaoTradeERPAppConfig
		), distributeConfig);
	}

	@Override
	public Integer getRetryCount() {
		return retryCount;
	}

	/**
	 * 通过code获取callbackResult
	 */
	@Override
	@SuppressWarnings("unchecked")
	public RefreshTokenCallbackResult getCallbackResultByCode(String code, String platformId, String appName) throws Exception {
		RefreshTokenCallbackResult refreshTokenCallbackResult = new RefreshTokenCallbackResult();
		TaobaoRefreshTokenCallbackResult taobaoCallbackResult = super.getCallbackResultByCode(code, CommonPlatformConstants.PLATFORM_TAO, null, TaobaoRefreshTokenCallbackResult.class, appName);
		if (Objects.isNull(taobaoCallbackResult) || !taobaoCallbackResult.isSuccess()) {
			LOGGER.logInfo(code, code, "请求数据异常");
			return null;
		}
		String accessToken = encryptToken(taobaoCallbackResult.getAccessToken(), platformId, appName);
		String refreshToken = encryptToken(taobaoCallbackResult.getRefreshToken(), platformId, appName);
		/**
		 * 存一份没加密的返回直接能用
		 */
		refreshTokenCallbackResult.setDecryptAccessToken(taobaoCallbackResult.getAccessToken());
		refreshTokenCallbackResult.setDecryptRefreshToken(taobaoCallbackResult.getRefreshToken());
		refreshTokenCallbackResult.setAccessToken(accessToken);
		refreshTokenCallbackResult.setRefreshToken(refreshToken);
		refreshTokenCallbackResult.setSellerId(taobaoCallbackResult.getSellerId());
		refreshTokenCallbackResult.setSellerNick(taobaoCallbackResult.getUrlDecodeSellerNick());
		refreshTokenCallbackResult.setSubSellerNick(taobaoCallbackResult.getUrlDecodeSubSellerNick());
		refreshTokenCallbackResult.setSubSellerId(taobaoCallbackResult.getSubSellerId());
		refreshTokenCallbackResult.setExpiresIn(taobaoCallbackResult.getW1ExpiresIn());
        refreshTokenCallbackResult.setAuthDeadLine(createdAuthDeadLine(taobaoCallbackResult.getW1ExpiresIn()));
        return refreshTokenCallbackResult;

	}

	/**
	 * 刷新refrshToken
	 */
	@Override
	@SuppressWarnings("unchecked")
	public RefreshTokenCallbackResult refreshToken(UserInfoBo userInfoBo, String refreshToken, String platformId, String appName) throws Exception {
		RefreshTokenCallbackResult refreshTokenCallbackResult = new RefreshTokenCallbackResult();
		TaobaoRefreshTokenCallbackResult taobaoCallbackResult = super.reGetAccessTokenWithRefreshToken(refreshToken, CommonPlatformConstants.PLATFORM_TAO, null, TaobaoRefreshTokenCallbackResult.class, appName);
		if (Objects.isNull(taobaoCallbackResult) || !taobaoCallbackResult.isSuccess()) {
			LOGGER.logInfo(refreshToken, refreshToken, "请求数据异常");
			return null;
		}

		String newAccessToken = encryptToken(taobaoCallbackResult.getAccessToken(), platformId, appName);
		String newRefreshToken = encryptToken(taobaoCallbackResult.getRefreshToken(), platformId, appName);

		/**
		 * 存一份没加密的返回直接能用
		 */
		refreshTokenCallbackResult.setDecryptAccessToken(taobaoCallbackResult.getAccessToken());
		refreshTokenCallbackResult.setDecryptRefreshToken(taobaoCallbackResult.getRefreshToken());
		refreshTokenCallbackResult.setAccessToken(newAccessToken);
		refreshTokenCallbackResult.setRefreshToken(newRefreshToken);
		refreshTokenCallbackResult.setSellerId(taobaoCallbackResult.getSellerId());
		refreshTokenCallbackResult.setSellerNick(taobaoCallbackResult.getUrlDecodeSellerNick());
		refreshTokenCallbackResult.setSubSellerNick(taobaoCallbackResult.getUrlDecodeSubSellerNick());
		refreshTokenCallbackResult.setSubSellerId(taobaoCallbackResult.getSubSellerId());
		refreshTokenCallbackResult.setExpiresIn(taobaoCallbackResult.getW1ExpiresIn());
        refreshTokenCallbackResult.setAuthDeadLine(createdAuthDeadLine(taobaoCallbackResult.getW1ExpiresIn()));
		return refreshTokenCallbackResult;
	}

	/**
	 * 生成授权到期时间
	 *
	 * @param expiresIn
	 * @return
	 */
	private LocalDateTime createdAuthDeadLine(Long expiresIn) {
        return DateUtil.calculateCustomSecond(DateUtil.currentDate(), expiresIn).withHour(0).withMinute(0).withSecond(0)
            .withNano(0);
	}

	@Override
	public String decryptToken(String token, String platformId, String appName) throws Exception{
		if (StringUtils.isEmpty(token)) {
			return token;
		} else if (CommonAppConstants.APP_ITEM.equals(appName)) {
			// 淘宝爱用商品用户主表token不加密，所以这里不需要解密
			return token;
		} else {
			int maxTopSessionLen = 81;
			if (token.length() < maxTopSessionLen) {
				/*6或7开头，直接返回 //长度小于80 无需解码*/
				return token;
			} else {
				AesUtil aesUtil = AesUtil.getInstance();
				try {
					/*返回解密后accessTokenKey*/
					return aesUtil.aesDecryptForSession(token, getActualConfig(appName).getSessionkey());
				} catch (IllegalBlockSizeException e) {
					// 尝试过滤特殊字符
					token = token.replaceAll ("\\\\r", "");
					token = token.replaceAll ("\\\\n", "");
					token = token.replaceAll ("\\n", "");
					token = token.replaceAll ("\\r", "");
					return aesUtil.aesDecryptForSession(token, getActualConfig(appName).getSessionkey());
				}
			}
		}
	}

	/**
	 * 加密
	 * @param token
	 * @param platformId
	 * @return
	 * @throws Exception
	 */
	@Override
	public String encryptToken(String token, String platformId, String appName) throws Exception {
		if (StringUtils.isEmpty(token)) {
			return null;
		}
		if (CommonAppConstants.APP_ITEM.equals(appName)) {
			// 淘宝爱用商品token不加密
			return token;
		}
		AesUtil aesUtil = AesUtil.getInstance();
		return aesUtil.aesEncryptForSession(token, getActualConfig(appName).getSessionkey());
	}

	@Override
	public void convertUserRedisEntity2UserInfoBo(UserInfoBo userInfoBo, UserRedisEntity userRedisEntity, String platformId, String appName) {
		if (CommonAppConstants.APP_TRADE.equals(userInfoBo.getAppType())) {
			// 淘宝爱用交易用户单独处理
			userInfoBo.setSellerId(userRedisEntity.getTaobao_user_id());
			userInfoBo.setCorpId(userRedisEntity.getCorp_id());
			userInfoBo.setSellerRole(userRedisEntity.getRoleid());
			userInfoBo.setLevel(userRedisEntity.getVipflagCache());
			userInfoBo.setOrderCycleEnd(userRedisEntity.getOrderCycleEnd());
			userInfoBo.setAuthDeadLine(userRedisEntity.getW1Deadline());
			userInfoBo.setW2Deadline(userRedisEntity.getW2DeadlineCache());
			userInfoBo.setLastPaidTime(userRedisEntity.getLastPaidTime());
			userInfoBo.setOriginalIdentityLevel(userRedisEntity.getVipflagCache());
			userInfoBo.setOriginalIdentityOrderCycleEnd(userRedisEntity.getOrderCycleEnd());
			userInfoBo.setAccessToken(userRedisEntity.getTrade_access_token_m());
			userInfoBo.setRefreshToken(userRedisEntity.getTrade_refresh_token_m());

		} else if (CommonAppConstants.APP_ITEM.equals(userInfoBo.getAppType())) {
			// 淘宝爱用商品用户单独处理
			userInfoBo.setSellerId(userRedisEntity.getTaobao_user_id());
			userInfoBo.setCorpId(userRedisEntity.getCorp_id());
			userInfoBo.setSellerRole(userRedisEntity.getRoleid());
			userInfoBo.setLevel(userRedisEntity.getVipflagCache());
			userInfoBo.setOrderCycleEnd(userRedisEntity.getOrderCycleEnd());
			userInfoBo.setAuthDeadLine(userRedisEntity.getW1Deadline());
			userInfoBo.setW2Deadline(userRedisEntity.getW2DeadlineCache());
			userInfoBo.setLastPaidTime(userRedisEntity.getLastPaidTime());
			userInfoBo.setOriginalIdentityLevel(userRedisEntity.getVipflagCache());
			userInfoBo.setOriginalIdentityOrderCycleEnd(userRedisEntity.getOrderCycleEnd());
			userInfoBo.setAccessToken(userRedisEntity.getItem_access_token_mp());
			userInfoBo.setRefreshToken(userRedisEntity.getItem_refresh_token_mp());
		} else if (CommonAppConstants.APP_SHOP_HELPER.equals(userInfoBo.getAppType())) {
			// 淘宝开店助手用户单独处理
			userInfoBo.setSellerId(userRedisEntity.getTaobao_user_id());
			userInfoBo.setCorpId(userRedisEntity.getCorp_id());
			userInfoBo.setSellerRole(userRedisEntity.getRoleid());
			userInfoBo.setLevel(userRedisEntity.getVipflagCache());
			userInfoBo.setOrderCycleEnd(userRedisEntity.getOrderCycleEnd());
			userInfoBo.setAuthDeadLine(userRedisEntity.getW1Deadline());
			userInfoBo.setW2Deadline(userRedisEntity.getW2DeadlineCache());
			userInfoBo.setLastPaidTime(userRedisEntity.getLastPaidTime());
			userInfoBo.setOriginalIdentityLevel(userRedisEntity.getVipflagCache());
			userInfoBo.setOriginalIdentityOrderCycleEnd(userRedisEntity.getOrderCycleEnd());
			userInfoBo.setAccessToken(userRedisEntity.getShophelper_access_token_mp());
			userInfoBo.setRefreshToken(userRedisEntity.getShophelper_refresh_token_mp());
		} else {
			// 其他appName redis字段统一
			userInfoBo.setSellerId(userRedisEntity.getUser_id());
			String sellerNick = org.apache.commons.lang3.StringUtils.isNotEmpty(userInfoBo.getSellerNick())
					? userInfoBo.getSellerNick() : userRedisEntity.getUser_nick();
			userInfoBo.setSellerNick(sellerNick);
			userInfoBo.setSellerRole(userRedisEntity.getRoleid());
			userInfoBo.setLevel(userRedisEntity.getVipflagCache());
			userInfoBo.setOrderCycleEnd(userRedisEntity.getOrderCycleEnd());
			userInfoBo.setAuthDeadLine(userRedisEntity.getAuthDeadLine());
			userInfoBo.setW2Deadline(userRedisEntity.getW2DeadlineCache());
			userInfoBo.setAccessToken(userRedisEntity.getAccess_token());
			userInfoBo.setRefreshToken(userRedisEntity.getRefresh_token());
			userInfoBo.setMallName(userRedisEntity.getMall_name());
			userInfoBo.setShopName(userRedisEntity.getShop_name());
            userInfoBo.setIsAuthExcept(userRedisEntity.getIsAuthExcept());
		}
	}

	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_TAO;
	}

}
