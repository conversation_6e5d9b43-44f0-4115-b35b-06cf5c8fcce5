package cn.loveapp.uac.common.api.domain;

import cn.loveapp.uac.common.utils.DateUtil;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * @program: uac-service-group
 * @description: SellerArticleSub
 * @author: Jason
 * @create: 2020-03-06 10:26
 **/
@Data
public class SellerArticleSub {
	private String articleCode;
	private String articleName;
	private Boolean autosub;
	private Date deadline;
	private Boolean expireNotice;
	private String itemCode;
	private String itemName;
	private String nick;
	private Long status;

	public LocalDateTime getDeadline() {
		return DateUtil.parseDate(deadline);
	}
}
