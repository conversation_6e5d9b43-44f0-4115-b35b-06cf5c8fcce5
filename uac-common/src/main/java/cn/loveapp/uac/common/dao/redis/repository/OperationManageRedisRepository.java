package cn.loveapp.uac.common.dao.redis.repository;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.dao.redis.base.BaseValueRedisRepository;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableMultimap;
import com.google.common.collect.Maps;
import java.net.URLEncoder;
import java.util.HashMap;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

/**
 * @program: uac-service-group
 * @description: OperationManageRedisDao
 * @author: Jason
 * @create: 2020-03-06 10:57
 **/
@Component
public class OperationManageRedisRepository extends BaseValueRedisRepository<String> {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OperationManageRedisRepository.class);

	private String hasPrefixKey = "AUTOSUB";

	public OperationManageRedisRepository(@Qualifier("stringTradeRedisTemplate") StringRedisTemplate stringTradeRedisTemplate, @Qualifier("stringItemRedisTemplate") StringRedisTemplate stringItemRedisTemplate) {
		super(ImmutableMap.of(
			CommonAppConstants.APP_TRADE, stringTradeRedisTemplate,
			CommonAppConstants.APP_ITEM, stringItemRedisTemplate
		));
	}

	public String find(String collection, String platformId, String appName) {
		String k = initCollection(collection, platformId, appName);
		return super.find(k, String.class, appName);
	}

	/**
	 * 初始化collection
	 */
	@Override
	public String initCollection(String collection, String platformId, String appName) {
		try {
			return hasPrefixKey + URLEncoder.encode(collection, "utf-8");
		} catch(Exception e) {
			LOGGER.logError(collection, "-", "初始化Key失败", e);
			return null;
		}
	}
}
