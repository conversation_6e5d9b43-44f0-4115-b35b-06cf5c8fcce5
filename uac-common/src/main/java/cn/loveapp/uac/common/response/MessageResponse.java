package cn.loveapp.uac.common.response;

import com.taobao.api.TaobaoResponse;
import lombok.Data;

/**
 * @program: orders-services-group
 * @description: MessageResponse
 * @author: <PERSON>
 * @create: 2019-11-28 17:37
 **/
@Data
public class MessageResponse extends TaobaoResponse {
	private Boolean isSuccess;

	public Boolean getIsSuccess() {
		if(isSuccess == null){
			return isSuccess();
		}
		return isSuccess;
	}
}
