package cn.loveapp.uac.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-05-22 10:17
 * @description: 交易配置
 */
@Data
@Configuration
public class TradeConfig {

    /**
     * 交易专业版vipFlag列表
     */
    @Value("${uac.professional.vipflag.list:6,8}")
    private List<Integer> professionalVipFlagList;
}
