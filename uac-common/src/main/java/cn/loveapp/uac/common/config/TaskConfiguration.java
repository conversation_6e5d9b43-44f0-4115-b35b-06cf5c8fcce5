package cn.loveapp.uac.common.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.task.ThreadPoolTaskSchedulerBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

/**
 * TaskConfiguration
 *
 * <AUTHOR>
 * @date 2025/7/21
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass(ThreadPoolTaskScheduler.class)
public class TaskConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public ThreadPoolTaskScheduler taskScheduler(ThreadPoolTaskSchedulerBuilder threadPoolTaskSchedulerBuilder) {
        // 忽略虚拟线程的设置强制使用线程模式, 解决 fixedDelay 在虚拟线程下不同任务间会相互阻塞的问题
        return threadPoolTaskSchedulerBuilder.build();
    }
}
