package cn.loveapp.uac.common.platform.api.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.wxvideoshop.WxvideoshopSDKService;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.config.DistributeConfig;
import cn.loveapp.uac.common.config.wxshop.WxshopTradeAppConfig;
import cn.loveapp.uac.common.platform.api.AuthService;
import cn.loveapp.uac.common.platform.api.domain.RefreshTokenCallbackResult;
import cn.loveapp.uac.common.utils.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.ImmutableMap;
import com.wxvideoshop.api.request.RefreshTokenRequest;
import com.wxvideoshop.api.response.AuthorizationTokenResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;


/**
 * @program: uac-service-group
 * @description: Ali1688AuthServiceImpl
 * @author: Jason
 * @create: 2021-04-20 15:30
 **/
@Service
public class WxvideoshopAuthServiceImpl extends BaseAuthServiceImpl implements AuthService {

	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(WxvideoshopAuthServiceImpl.class);

	@Value("${uac.network.retry.count:5}")
	private Integer retryCount;

	@Value("${uac.refresh.access.token.wxvideoshop.hashKey.config:@@wx_shop_verify_ticket}")
	private String wxshopConfHashKey;

	@Value("${uac.refresh.access.token.wxvideoshop.componentTokenField.config:_component_access_token}")
	private String wxshopConfComponentTokenField;

	@Autowired
    @Qualifier("stringItemRedisTemplate")
	private StringRedisTemplate redisTemplate;

	@Autowired
	private WxvideoshopSDKService wxvideoshopSDKService;

	public WxvideoshopAuthServiceImpl(WxshopTradeAppConfig wxshopTradeAppConfig, DistributeConfig distributeConfig) {
        super(ImmutableMap.of(
				CommonAppConstants.APP_TRADE, wxshopTradeAppConfig, ALL_APP, wxshopTradeAppConfig
		), distributeConfig);

	}

	@Override
	public Integer getRetryCount() {
		return retryCount;
	}

	/**
	 * 通过code获取callbackResult
	 */
	@Override
	@SuppressWarnings("unchecked")
	public RefreshTokenCallbackResult getCallbackResultByCode(String code, String platformId, String appName) throws Exception {
		return null;
	}

	/**
	 * 刷新refrshToken
	 */
	@Override
	@SuppressWarnings("unchecked")
	public RefreshTokenCallbackResult refreshToken(UserInfoBo userInfoBo, String refreshToken, String platformId, String appName) throws Exception {
		try {
			RefreshTokenRequest request = new RefreshTokenRequest();
			HashOperations<String,String,String> operations = redisTemplate.opsForHash();
			String componentToken = operations.get(wxshopConfHashKey, appName+wxshopConfComponentTokenField);
			if (StringUtils.isBlank(componentToken)) {
				LOGGER.logError(userInfoBo.getSellerNick(), "-", "微信刷新视频号小店token时获取应用token失败，value=" + componentToken);
				return null;
			}
			request.setComponentAccessToken(componentToken);
			request.setAuthorizerAppid(userInfoBo.getSellerId());
			request.setAuthorizerRefreshToken(refreshToken);
			AuthorizationTokenResponse response = wxvideoshopSDKService.refreshToken(request, appName);
			if(response == null || !response.isSuccess() || response == null){
				LOGGER.logError(userInfoBo.getSellerNick(), "-", "refresh_token刷新返回信息异常 result => "  + JSON.toJSONString(response, "yyyy-MM-dd HH:mm:ss"));
				return null;
			}
			RefreshTokenCallbackResult callbackResult = new RefreshTokenCallbackResult();
			String encryptAccessToken = encryptToken(response.getAuthorizerAccessToken(), platformId, appName);
			callbackResult.setAccessToken(encryptAccessToken);
			callbackResult.setDecryptAccessToken(response.getAuthorizerAccessToken());
			String encryptRefreshToken = encryptToken(response.getAuthorizerRefreshToken(), platformId, appName);
			callbackResult.setRefreshToken(encryptRefreshToken);
			callbackResult.setDecryptRefreshToken(response.getAuthorizerRefreshToken());
			callbackResult.setExpiresIn(response.getExpiresIn());
			callbackResult.setSellerId(userInfoBo.getSellerId());
			callbackResult.setSellerNick(userInfoBo.getSellerNick());
			callbackResult.setSubSellerNick(userInfoBo.getSubSellerNick());
            callbackResult
                .setAuthDeadLine(DateUtil.calculateCustomSecond(DateUtil.currentDate(), response.getExpiresIn()));
			return callbackResult;

		} catch (Exception e) {
			LOGGER.logError(userInfoBo.getSellerNick(), "-", "refresh_token刷新出现异常: " + e.getMessage(), e);
			return null;
		}
	}

	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
	}
}
