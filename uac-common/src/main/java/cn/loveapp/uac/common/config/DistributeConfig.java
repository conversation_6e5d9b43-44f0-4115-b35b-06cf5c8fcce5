package cn.loveapp.uac.common.config;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022-11-23 15:04
 * @Description: 代发配置
 */
@Data
@Configuration
public class DistributeConfig {

    /**
     * 代发数据库后缀平台集合
     */
    @Value("#{${uac.distribute.special.table.suffix.map: {DOUDIAN:'douyin'}}}")
    private Map<String, String> distributeSpecialTableSuffixMap = new HashMap<>();

    /**
     * 代发redis平台集合
     */
    @Value("#{${uac.distribute.special.redis.prefix.map: {DOUDIAN:'dy'}}}")
    private Map<String, String> distributeSpecialRedisPrefixMap = new HashMap<>();

}
