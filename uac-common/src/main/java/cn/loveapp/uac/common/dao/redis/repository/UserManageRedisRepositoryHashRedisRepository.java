package cn.loveapp.uac.common.dao.redis.repository;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.bo.UserBo;
import cn.loveapp.uac.common.constant.AuthInfoParamConstant;
import cn.loveapp.uac.common.dao.redis.base.BaseHashRedisRepository;
import cn.loveapp.uac.common.entity.redis.UserRedisEntity;
import cn.loveapp.uac.common.service.DistributeUserProcessService;
import cn.loveapp.uac.utils.UserCacheUtils;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: orders-services-group
 * @description: UserManageRedisDao
 * @author: Jason
 * @create: 2018-12-26 21:06
 **/
@Component
public class UserManageRedisRepositoryHashRedisRepository extends BaseHashRedisRepository<UserRedisEntity> {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(UserManageRedisRepositoryHashRedisRepository.class);

    @Autowired
    private DistributeUserProcessService distributeUserProcessService;

    public UserManageRedisRepositoryHashRedisRepository(
            @Qualifier("stringTradeRedisTemplate") StringRedisTemplate stringTradeRedisTemplate,
            @Qualifier("stringItemRedisTemplate") StringRedisTemplate stringItemRedisTemplate,
            @Qualifier("stringShophelperRedisTemplate") StringRedisTemplate stringShophelperRedisTemplate,
            @Qualifier("stringDistributeRedisTemplate") StringRedisTemplate stringDistributeRedisTemplate) {
        super(ImmutableMap.of(
                CommonAppConstants.APP_TRADE, stringTradeRedisTemplate,
                CommonAppConstants.APP_ITEM, stringItemRedisTemplate,
                CommonAppConstants.APP_DISTRIBUTE, stringDistributeRedisTemplate,
                CommonAppConstants.APP_SHOP_HELPER, stringShophelperRedisTemplate
        ));
    }

    public UserRedisEntity entries(String redisKey, String sellerNick, String sellerId, String platformId, String appName) {
        String k = StringUtils.isNotEmpty(redisKey) ? redisKey : initCollection(sellerNick, sellerId, platformId, appName);
        if (hasExistCollection(k, appName)) {
            return findAll(k, UserRedisEntity.class, platformId, appName);
        }
        return null;
    }

    public UserRedisEntity entries(String sellerNick, String sellerId, String platformId, String appName) {
        String k = initCollection(sellerNick, sellerId, platformId, appName);
        if (hasExistCollection(k, appName)) {
            return findAll(k, UserRedisEntity.class, platformId, appName);
        }
        return null;
    }

    public UserRedisEntity getLastAuthInfo(String sellerNick, String sellerId, String platformId, String appName) {
        String k = initCollection(sellerNick, sellerId, platformId, appName);
        UserRedisEntity userRedisEntity = null;
        if (hasExistCollection(k, appName)) {
            ArrayList<String> fields = new ArrayList<>(3);
            fields.add(AuthInfoParamConstant.LAST_ORDER_CYCLE_END);
            fields.add(AuthInfoParamConstant.LAST_W1_DEADLINE);
            fields.add(AuthInfoParamConstant.LAST_AUTH_DEADLINE);

            List<String> values = find(k, fields, appName);
            if (values.get(0) != null) {
                userRedisEntity = new UserRedisEntity();
                userRedisEntity.setLast_order_cycle_end(values.get(0));
                userRedisEntity.setLast_w1_deadline(values.get(1));
                userRedisEntity.setLast_auth_deadline(values.get(2));
            }
        }
        return userRedisEntity;
    }


    public void hmSetLevelByKeys(List<String> sellerNicks, String level, String platformId, String appName) {
        String luaScript = "redis.call('hset',KEYS[%d],ARGV[1],ARGV[2]);";
        for (int i = 0; i < sellerNicks.size(); i++) {
            if (i == 0) {
                luaScript = String.format(luaScript, i + 1);
            } else {
                luaScript += String.format(luaScript, i + 1);
            }
        }
        luaScript += "return 1";
        RedisScript script = RedisScript.of(luaScript, Boolean.class);
        // FIXME 不支持 sellerId
        List<String> sellerNickEncodes = sellerNicks.stream()
                .map(s -> initCollection(s, null, platformId, appName)).filter(Objects::nonNull)
                .collect(Collectors.toList());
        getRealStringRedisTemplate(appName).execute(script, sellerNickEncodes, "vipflag", level);
    }

    public boolean putUserData(UserBo userBo) {
        String sellerNick = userBo.getSellerNick();
        String sellerId = userBo.getSellerId();
        UserRedisEntity userEntity = userBo.getUserRedisEntity();
        String platformId = userBo.getPlatformId();
        String appName = userBo.getAppType();

        String k = initCollection(sellerNick, sellerId, platformId, appName);
        if (putAll(k, userEntity, platformId, appName)) {
            if (CommonPlatformConstants.PLATFORM_DOUDIAN.equals(platformId)) {
                // XXX: 特殊为代发处理抖店的redis, 兼容以sellerId结尾的key
                if (StringUtils.isEmpty(userEntity.getUser_id())) {
                    LOGGER.logWarn(sellerNick, k, "sellerId为空, 无法保存sellerId结尾的特殊key");
                    return true;
                }
                try {
                    k = initCollection(userEntity.getUser_id(), userBo.getSellerId(), platformId, appName);
                    putAll(k, userEntity, platformId, appName);
                    LOGGER.logInfo(sellerNick, k, "保存sellerId结尾的特殊key成功");
                } catch (Exception e) {
                    LOGGER.logError(sellerNick, k, "保存sellerId结尾的特殊key失败: " + e.getMessage(), e);
                }
            } else if (CommonAppConstants.APP_DISTRIBUTE.equalsIgnoreCase(appName) && CommonPlatformConstants.PLATFORM_BIYAO.equalsIgnoreCase(platformId)) {
                // 代发应用因为key不同, 要多存一份
                String distributeKey = null;
                try {
                    distributeKey = URLEncoder.encode(platformId.toLowerCase() + ":" + userEntity.getUser_id(), "UTF-8");
                    UserRedisEntity copyEntity = new UserRedisEntity();
                    copyEntity.setUser_id(userEntity.getUser_id());
                    copyEntity.setUser_nick(userEntity.getUser_nick());
                    copyEntity.setApp_id(userEntity.getApp_id());
                    copyEntity.setApp_secret(userEntity.getApp_secret());
                    copyEntity.setAccess_token(userEntity.getAccess_token());
                    copyEntity.setRefresh_token(userEntity.getRefresh_token());
                    copyEntity.setOrder_cycle_end(userEntity.getOrder_cycle_end());
                    copyEntity.setAuth_dead_line(userEntity.getAuth_dead_line());
                    putAll(distributeKey, copyEntity, platformId, CommonAppConstants.APP_ITEM);
                } catch (Exception e) {
                    LOGGER.logError(sellerNick, k, "保存代发redis特殊key失败, key=" + distributeKey + ": " + e.getMessage(), e);
                }
            }
            return true;
        } else {
            return false;
        }
    }

    /**
     * 初始化collection
     */
    @Override
    public String initCollection(String sellerNick, String sellerId, String platformId, String appName) {
        String key = UserCacheUtils.userCacheKey(sellerNick, sellerId, platformId, appName);
        try {
            if (key == null && !CommonPlatformConstants.PLATFORM_PDD.equals(platformId) && CommonAppConstants.APP_DISTRIBUTE.equals(appName)) {
                // 特殊处理 代发应用 的用户key
                key = distributeUserProcessService.createdUserRedisKey(sellerNick, sellerId, platformId,
                        appName);
            }
        } catch (Exception e) {
            LOGGER.logError(sellerNick, sellerId, "初始化Key失败: " + e.getMessage(), e);
        }
        return key;
    }

    @Override
    public String initExtCollection(String sellerNick, String sellerId, String platformId, String appName) {
        return "userext:" + platformId + ':' + appName + ':' + sellerId;
    }

    @Override
    public String initUserExtCollection(String sellerId, String platformId, String appName, String businessId) {
        return "userext:" + businessId + ":" + platformId + ':' + appName + ':' + sellerId;
    }
}
