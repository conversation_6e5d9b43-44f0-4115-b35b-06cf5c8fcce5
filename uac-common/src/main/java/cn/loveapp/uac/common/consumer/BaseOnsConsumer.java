package cn.loveapp.uac.common.consumer;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.constant.MqUserPropertyConstant;
import cn.loveapp.uac.common.exception.ResendMessageException;
import cn.loveapp.uac.common.utils.RocketMqQueueHelper;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.RateLimiter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import jakarta.validation.ValidationException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * consumer base 类
 * @program: orders-services-group
 * @description: BaseOnsConsumer
 * @author: Jason
 * @create: 2018-11-21 14:57
 **/
abstract public class BaseOnsConsumer implements MessageListenerConcurrently {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(BaseOnsConsumer.class);

	/**
	 * 是否停止接收消息
	 */
	protected static volatile boolean stop = false;

	/**
	 * 限流计数器
	 */
	protected RateLimiter rateLimiter;

	private Timer executeTimer;

	private Environment environment;

	@Autowired
	private RocketMqQueueHelper rocketMqQueueHelper;

	@Autowired
	private DefaultMQProducer defaultMQProducer;

	/**
	 * 获取限流配置的key
	 *
	 * @return
	 */
	abstract protected String getRateLimitKey();

	public BaseOnsConsumer(MeterRegistry registry, Environment environment, String timerName){
		this.environment = environment;
		executeTimer = registry.timer(timerName);
		Double rateLimit = environment.getProperty(getRateLimitKey(), Double.class, Double.MAX_VALUE);
		rateLimiter = RateLimiter.create(rateLimit);

		List namespaces = environment.getProperty("apollo.bootstrap.namespaces", List.class);
		if(CollectionUtils.isEmpty(namespaces)){
			return;
		}
		for(Object namespace : namespaces){
			ConfigService.getConfig(namespace.toString())
				.addChangeListener(this::configChangeLisenner, ImmutableSet.of(getRateLimitKey()));
		}

	}

	/**
	 * 根据apollo配置动态设置
	 *
	 * @param changeEvent ConfigChangeEvent
	 */
	public void configChangeLisenner(ConfigChangeEvent changeEvent){
		String rateLimitKey = null;
		try {
			rateLimitKey = getRateLimitKey();
			ConfigChange change = changeEvent.getChange(rateLimitKey);
			if(change == null){
				return;
			}
			// 实际的最新值
			String value = environment.getProperty(rateLimitKey);
			//重新设置限流
			double newLimit = StringUtils.isEmpty(value) ? 0 : Double.parseDouble(value);
			LOGGER.logInfo(rateLimitKey + " 设置新值为: " + newLimit);
			if(newLimit > 0){
				rateLimiter.setRate(newLimit);
			}
		} catch (Exception e) {
			LOGGER.logError(rateLimitKey + " 设置的数值非法", e);
		}
	}

	public double getRateLimiter() {
		return rateLimiter.getRate();
	}

	/**
	 * 限流
	 */
	public void rateLimiterAcquire() {
		//接受速率限流
		rateLimiter.acquire();
	}

	@Override
	public ConsumeConcurrentlyStatus consumeMessage(final List<MessageExt> msgs,
		final ConsumeConcurrentlyContext context){
		if(stop){
			if(msgs.size() > 0){
				LOGGER.logInfo("服务已停止, 返回接收到的消息: " + msgs.get(0).getMsgId());
			}
			return ConsumeConcurrentlyStatus.RECONSUME_LATER;
		}
		MDC.remove("tid");
		MDC.remove("sellerNick");
		for(MessageExt message : msgs){
			long startTime = System.currentTimeMillis();
			AiyongMessageExt aiyongMessageExt = convertAiyongMessageExtFromRocketMQ(message);
			String topic = aiyongMessageExt.getTopic();
			String tag = aiyongMessageExt.getTag();
			Timer.Sample sm = Timer.start();
			String content = aiyongMessageExt.getContent();

			//消息业务逻辑进行的重试次数累加，非mq本身错误重试次数
			int reconsumeTimes = 0;
			Map<String, String> properties = message.getProperties();
			if (MapUtils.isNotEmpty(properties) && properties.containsKey(MqUserPropertyConstant.RESEND_TIMES_FIELD)) {
				String timesStr = properties.get(MqUserPropertyConstant.RESEND_TIMES_FIELD);
				reconsumeTimes = StringUtils.isNotEmpty(timesStr) ? Integer.valueOf(timesStr) : 0;
			}


			String messageId = aiyongMessageExt.getMsgId();
			MDC.put("messageId", messageId);
			MDC.put("topic", topic);
			MDC.put("tag", StringUtils.defaultString(tag, "*"));
			try {
				LOGGER.logInfo(aiyongMessageExt.getTag(), messageId, "begin pop数据=>" + content);
				if (! StringUtils.isEmpty(content)) {
					execute(aiyongMessageExt);
				}
			} catch (ValidationException ve) {
				LOGGER.logError(aiyongMessageExt.getTag(), messageId, "消息参数出错", ve);
				return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
			} catch (ResendMessageException e) {
				Map<String, String> userProperties = Maps.newHashMap();
				userProperties.put(MqUserPropertyConstant.FORCE_HANDLE_FLAG_FIELD, e.isForceHandleFlag().toString());
				userProperties.put(MqUserPropertyConstant.RESEND_TIMES_FIELD, String.valueOf(reconsumeTimes + 1));
				String newMsgId = rocketMqQueueHelper.push(topic, tag, content, defaultMQProducer, e.getDelayLevel(), userProperties);
				LOGGER.logError("-", messageId, "消息重发回队列: " + e.getMessage() + " newMsgId: " + newMsgId, e.getCause());
			} catch (Exception e) {
				LOGGER.logError("-", messageId, "程序异常出错: " + e.getMessage(), e);
				return ConsumeConcurrentlyStatus.RECONSUME_LATER;
			} finally {
				sm.stop(executeTimer);
				LOGGER.logInfo("-", messageId, "end 处理完成=>" + topic,
					Collections.singletonMap("costTime", String.valueOf(System.currentTimeMillis()- startTime)));
				postExecute();
			}
		}
		return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
	}


	/**
	 * 真正处理的数据类
	 * @param aiyongMessageExt pop出来的数据
	 * @throws Exception
	 */
	abstract protected void execute(AiyongMessageExt aiyongMessageExt) throws Exception;

	/**
	 * execute后置处理
	 * @throws Exception
	 */
	protected void postExecute() {
	}

	/**
	 * Handle an application event.
	 *
	 */
	public void stop() {
		LOGGER.logInfo("ONS消息处理关闭.");
		stop = true;
	}

	public static void setStop(boolean stop) {
		BaseOnsConsumer.stop = stop;
	}

	/**
	 * 开源RocketMQ消息对象转AiyongMessageExt
	 * @param message
	 */
	private AiyongMessageExt convertAiyongMessageExtFromRocketMQ(MessageExt message) {
		AiyongMessageExt aiyongMessageExt = new AiyongMessageExt();
		aiyongMessageExt.setContent(new String(message.getBody(), StandardCharsets.UTF_8));
		aiyongMessageExt.setTopic(message.getTopic());
		aiyongMessageExt.setTag(message.getTags());
		aiyongMessageExt.setKey(message.getKeys());
		aiyongMessageExt.setUserProperties(message.getProperties());
		aiyongMessageExt.setMsgId(message.getMsgId());
		aiyongMessageExt.setTransactionId(message.getTransactionId());
		return aiyongMessageExt;
	}

}
