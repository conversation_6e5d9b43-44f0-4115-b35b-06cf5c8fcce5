package cn.loveapp.uac.common.bo;

import cn.loveapp.uac.request.CallbackRequest;
import lombok.Data;

/**
 * @program: uac-service-group
 * @description: AuthBo
 * @author: <PERSON>
 * @create: 2020-03-13 16:09
 **/
@Data
public class AuthBo {
	private String code;
	private String platformId;
	private String appType;


	public static AuthBo of(CallbackRequest callbackRequest) {
		AuthBo authBo = new AuthBo();
		authBo.setAppType(callbackRequest.getApp());
		authBo.setPlatformId(callbackRequest.getPlatformId());
		authBo.setCode(callbackRequest.getCode());
		return authBo;
	}

}
