package cn.loveapp.uac.common.platform.api.impl;

import java.util.Date;

import cn.loveapp.common.platformsdk.tiktok.TikTokSDKService;
import cn.loveapp.uac.common.config.tiktok.TikTokAppConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.ImmutableMap;
import com.tiktok.api.dto.request.RefreshTokenRequest;
import com.tiktok.api.dto.response.RefreshTokenResponse;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.config.DistributeConfig;
import cn.loveapp.uac.common.platform.api.AuthService;
import cn.loveapp.uac.common.platform.api.domain.RefreshTokenCallbackResult;
import cn.loveapp.uac.common.utils.DateUtil;

/**
 * <AUTHOR>
 * @date 2024-04-11 16:10
 * @description: tiktok授权相关service
 */
@Service
public class TikTokAuthServiceImpl extends BaseAuthServiceImpl implements AuthService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(TikTokAuthServiceImpl.class);

    /**
     * 授予令牌的方式。只接受“refresh_token”
     */
    public static final String GRANT_TYPE = "refresh_token";

    @Autowired
    private TikTokSDKService tikTokSDKService;

    @Value("${uac.network.retry.count:5}")
    private Integer retryCount;

    public TikTokAuthServiceImpl(TikTokAppConfig tikTokAppConfig, DistributeConfig distributeConfig) {
        super(ImmutableMap.of(CommonAppConstants.APP_TRADE, tikTokAppConfig, ALL_APP, tikTokAppConfig),
            distributeConfig);
    }

    @Override
    public <T extends RefreshTokenCallbackResult> T getCallbackResultByCode(String code, String platformId,
        String appName) throws Exception {
        return null;
    }

    @Override
    public RefreshTokenCallbackResult refreshToken(UserInfoBo userInfoBo, String refreshToken, String platformId,
        String appName) throws Exception {

        RefreshTokenRequest refreshTokenRequest = new RefreshTokenRequest();
        RefreshTokenRequest.QueryParameter refreshTokenQueryParameter = new RefreshTokenRequest.QueryParameter();
        refreshTokenQueryParameter.setRefreshToken(userInfoBo.getRefreshToken());
        refreshTokenQueryParameter.setGrantType(GRANT_TYPE);
        refreshTokenRequest.setQueryParameter(refreshTokenQueryParameter);

        RefreshTokenResponse response =
            tikTokSDKService.execute(refreshTokenRequest, userInfoBo.getAccessToken(), appName);
        if (response == null || !response.isSuccess()) {
            LOGGER.logError(userInfoBo.getSellerNick(), "-",
                "refresh_token刷新返回信息异常 result => " + JSON.toJSONString(response));
            return null;
        }

        RefreshTokenResponse.Data data = response.getData();

        String encryptAccessToken = encryptToken(data.getAccessToken(), platformId, ALL_APP);
        String encryptRefreshToken = encryptToken(data.getRefreshToken(), platformId, ALL_APP);
        RefreshTokenCallbackResult callbackResult = new RefreshTokenCallbackResult();
        callbackResult.setAccessToken(encryptAccessToken);
        callbackResult.setDecryptAccessToken(data.getAccessToken());
        callbackResult.setRefreshToken(encryptRefreshToken);
        callbackResult.setDecryptRefreshToken(data.getAccessToken());
        callbackResult.setExpiresIn(data.getAccessTokenExpireIn().longValue());
        callbackResult.setSellerId(userInfoBo.getSellerId());
        callbackResult.setSellerNick(userInfoBo.getSellerNick());
        callbackResult.setSellerNick(userInfoBo.getSellerNick());
        callbackResult.setAuthDeadLine(DateUtil.parseDate(new Date(data.getAccessTokenExpireIn())));
        return callbackResult;
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_TIKTOK;
    }

    @Override
    public Integer getRetryCount() {
        return retryCount;
    }
}
