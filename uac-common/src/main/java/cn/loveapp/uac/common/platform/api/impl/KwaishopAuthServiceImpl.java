package cn.loveapp.uac.common.platform.api.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.utils.NetworkUtil;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.config.DistributeConfig;
import cn.loveapp.uac.common.config.kwaishop.KwaishopDistributeAppConfig;
import cn.loveapp.uac.common.config.kwaishop.KwaishopTradeAppConfig;
import cn.loveapp.uac.common.config.kwaishop.KwaishopTradeERPAppConfig;
import cn.loveapp.uac.common.platform.api.AuthService;
import cn.loveapp.uac.common.platform.api.domain.KwaishopRefreshTokenCallbackResult;
import cn.loveapp.uac.common.platform.api.domain.RefreshTokenCallbackResult;
import cn.loveapp.uac.common.utils.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * @program: uac-service-group
 * @description: Ali1688AuthServiceImpl
 * @author: Jason
 * @create: 2021-04-20 15:30
 **/
@Service
public class KwaishopAuthServiceImpl extends BaseAuthServiceImpl implements AuthService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(KwaishopAuthServiceImpl.class);

    @Value("${uac.network.retry.count:5}")
    private Integer retryCount;

    public KwaishopAuthServiceImpl(KwaishopTradeAppConfig kwaishopTradeAppConfig,
                                   KwaishopDistributeAppConfig kwaishopDistributeAppConfig,
                                   KwaishopTradeERPAppConfig kwaishopTradeERPAppConfig,
                                   DistributeConfig distributeConfig) {
        super(ImmutableMap.of(
                CommonAppConstants.APP_TRADE, kwaishopTradeAppConfig,
                CommonAppConstants.APP_DISTRIBUTE, kwaishopDistributeAppConfig,
                CommonAppConstants.APP_TRADE_ERP, kwaishopTradeERPAppConfig
        ), distributeConfig);
    }

    @Override
    public Integer getRetryCount() {
        return retryCount;
    }

    /**
     * 通过code获取callbackResult
     */
    @Override
    @SuppressWarnings("unchecked")
    public RefreshTokenCallbackResult getCallbackResultByCode(String code, String platformId, String appName) throws Exception {
        return null;
    }

    /**
     * 刷新refrshToken
     */
    @Override
    @SuppressWarnings("unchecked")
    public RefreshTokenCallbackResult refreshToken(UserInfoBo userInfoBo, String refreshToken, String platformId, String appName) throws Exception {
        String refreshTokenUrl = getActualConfig(appName).getRefreshTokenUrl();
        String appkey = getActualConfig(appName).getAppkey();
        String appSecret = getActualConfig(appName).getAppSecret();
        HashMap<String, String> postFields = new HashMap<>(4);
        postFields.put("grant_type", "refresh_token");
        postFields.put("refresh_token", refreshToken);
        postFields.put("app_id", appkey);
        postFields.put("app_secret", appSecret);
        KwaishopRefreshTokenCallbackResult callbackResult = null;
        try {
            String response = NetworkUtil.http(refreshTokenUrl, postFields, true, StringUtils.EMPTY, StringUtils.EMPTY, false, false, StringUtils.EMPTY);
            callbackResult = JSON.parseObject(response, KwaishopRefreshTokenCallbackResult.class);
        } catch (Exception e) {
            LOGGER.logError(userInfoBo.getSellerNick(), platformId, "refresh_token 出现异常: " + e.getMessage(), e);
            return null;
        }
        if (callbackResult == null || !callbackResult.isSuccess()) {
            LOGGER.logWarn(userInfoBo.getSellerNick(), platformId, "refresh_token 返回信息异常: " + JSON.toJSONString(callbackResult));
            return null;
        } else {
            LOGGER.logInfo(userInfoBo.getSellerNick(), platformId, "refresh_token 返回结果: " + JSON.toJSONString(callbackResult));
        }
        String newAccessToken = encryptToken(callbackResult.getAccessToken(), platformId, appName);
        String newRefreshToken = encryptToken(callbackResult.getRefreshToken(), platformId, appName);

		callbackResult.setDecryptAccessToken(callbackResult.getAccessToken());
		callbackResult.setDecryptRefreshToken(callbackResult.getRefreshToken());
		callbackResult.setAccessToken(newAccessToken);
		callbackResult.setRefreshToken(newRefreshToken);
		callbackResult.setSellerId(userInfoBo.getSellerId());
		callbackResult.setSellerNick(userInfoBo.getSellerNick());
		callbackResult.setSubSellerNick(userInfoBo.getSubSellerNick());
        callbackResult
            .setAuthDeadLine(DateUtil.calculateCustomSecond(DateUtil.currentDate(), callbackResult.getExpiresIn()));

        return callbackResult;
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_KWAISHOP;
    }
}
