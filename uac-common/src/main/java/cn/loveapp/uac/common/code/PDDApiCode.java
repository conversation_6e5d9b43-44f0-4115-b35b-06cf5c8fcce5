package cn.loveapp.uac.common.code;

/**
 * @program: orders-services-group
 * @description: PDDApiCode
 * @author: <PERSON>
 * @create: 2019-11-21 12:29
 **/
public class PDDApiCode {
	/**
	 * 参数值有误，按照文档要求填写请求参数
	 */
	public static final Integer PARAMS_ERR = 10000;
	/**
	 * 请检查请求的公共参数
	 */
	public static final Integer HEAD_PARAMS_ERR = 10001;
	/**
	 * 请使用POST请求
	 */
	public static final Integer METHOD_ERR = 10002;
	/**
	 * 您的应用不存在
	 */
	public static final Integer APP_KEY_ERR = 10010;
	/**
	 * 请前往拼多多开放平台查看应用驳回的原因，及时修改并重新提交应用，或者创建新的应用
	 */
	public static final Integer REJECTED_ERR = 10011;
	/**
	 * 商家和您的授权关系已经取消了
	 */
	public static final Integer AUTH_CANCEL_ERR = 10014;
	/**
	 * 请核查您的client_id是否正确
	 */
	public static final Integer CLIENT_ID_ERR = 10016;
	/**
	 * 刷新access_token或者重新授权再次获取access_token
	 */
	public static final Integer ACCESS_TOKEN_ERR = 10019;
	/**
	 * 请刷新access_token或者重新授权获取access_token
	 */
	public static final Integer ACCESS_TOKEN_ERR1 = 10035;
	/**
	 * 请按照接入指南第三部分指导，生成签名
	 */
	public static final Integer SIGN_ERR = 20004;
	/**
	 * 把ip白名单加入白名单
	 */
	public static final Integer IP_WHITE_ERR = 20005;
	/**
	 * 请查看接入指南第三部分和API文档，核对公共参数和业务必填参数是否正确
	 */
	public static final Integer PARAMS_ERR1 = 20007;
	/**
	 * 您创建的应用中不包含此接口，请查看API文档，了解相关权限包
	 */
	public static final Integer API_ERR = 20031;
	/**
	 * 检查access_token或client_id
	 */
	public static final Integer ACCESS_TOKEN_OR_CLIENT_ID_ERR = 20032;
	/**
	 * 系统内部错误，请加群联系相关负责人
	 */
	public static final Integer SYS_ERR = 50000;
	/**
	 * 请根据子错误判断错误详情，无法解决请联系相关负责人
	 */
	public static final Integer SUB_CODE_ERR = 50001;
	/**
	 * 此API已下线
	 */
	public static final Integer DEPRECATED_ERR = 50012;
	/**
	 * 调用过于频繁，请调整调用频率
	 */
	public static final Integer CALL_FREQUENT_ERR = 70031;
	/**
	 * 调用过于频繁，请调整调用频率
	 */
	public static final Integer CALL_FREQUENT_ERR1 = 70032;
	/**
	 * 当前接口因系统维护，暂时下线，请稍后再试！
	 */
	public static final Integer API_MAINTAIN_ERR = 70033;
	/**
	 * 当前用户存在风险接，禁止调用！
	 */
	public static final Integer USR_RISK_ERR = 70034;

}
