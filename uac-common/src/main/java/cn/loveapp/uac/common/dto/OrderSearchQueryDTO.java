package cn.loveapp.uac.common.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @Date 2024/8/15 4:40 PM
 */
@Data
public class OrderSearchQueryDTO {

    /**
     * 用户nick
     */
    private String sellerNick;

    /**
     * 分页
     */
    private int pageNo;
    private int pageSize;

    /**
     * 排序方向 asc / desc
     */
    private String sortDirection;

    /**
     * 订购项目
     */
    private List<String> itemCodes;

    /**
     * 开始时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

}
