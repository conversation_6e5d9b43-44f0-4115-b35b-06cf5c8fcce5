package cn.loveapp.uac.common.config.taobao;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import cn.loveapp.uac.common.config.AppConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024-09-13 10:23
 * @description: 淘宝tradeERP配置
 */
@EqualsAndHashCode(callSuper = true)
@Configuration
@Data
@ConfigurationProperties(prefix = "uac.taobao.tradeerp.app")
public class TaoBaoTradeERPAppConfig extends AppConfig {

}
