package cn.loveapp.uac.common.config.taobao;

import cn.loveapp.uac.common.config.AppConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @program: orders-services-group
 * @description: BaseTaobaoService
 * @author: Jason
 * @create: 2018-12-07 13:52
 **/
@Configuration
@Data
@ConfigurationProperties(prefix = "uac.taobao.trade.app")
public class TaoBaoTradeAppConfig extends AppConfig {
	private String rebuildUserUrl;
	private String aesDecryptUrl;
}
