package cn.loveapp.uac.common.code;

/**
 * @Author: yxm
 * @Date: 2019/3/8 16:46
 * @Description:
 */
public class ApiCode {

    /**10000 - 19999 订单 预留 */
    /**
     * 订单未在数据库中
     */
    public static final Integer DB_NO_ORDER_CODE = 10001;
    /**
     * 灰度用户
     */
    public static final Integer GRAY_USER_CODE = 10002;
    /**
     * 正常用户
     */
    public static final Integer NORMAL_USER_CODE = 10003;


    /**
     * 用户正在拉单
     */
    public static final Integer USER_PULL_ORDER_ING = 10004;

    /**
     * 非法访问平台信息
     */
    public static final Integer ILLEGAL_ACCESS_PLATFORM_INFO = 10005;


    /** 爱用code通用 20000 29999 */
    /**
     * aiyongCode默认的code
     */
    public static final Integer DEFAULT_AIYONG_CODE = 20001;
    /**
     * 因为网络或者数据库的报错可以重试
     */
    public static final Integer NETWORK_DB_ERROR = 20002;
    /**
     * 无效的用户信息,提示重新登陆
     */
    public static final Integer INVALID_USERINFO = 20003;
    /**
     * 用户授权失效,需要前端自己查接口
     */
    public static final Integer INVALID_SESSION = 20004;
    /**
     * 这个用户是初级版用户或者存数据没有启用，直接打回在前端调用接口来处理
     */
    public static final Integer INVALID_VIPFLAG_INFO = 20005;
    /**
     * 这个用户的某个存数据接口 *暂时* 降级掉了，之后会恢复
     */
    public static final Integer MOMENT_INVALID = 20006;
    /**
     * 本次无效的请求无效(针对 aiyongTradeApi/search.list.get 下的接口),标志着高级搜索限流的情况
     */
    public static final Integer INVALID_SEARCH_REQUEST = 20007;
    /**
     * 本次无效的请求无效,查询列表的时候报错啦 (aiyongTradeApi/base.list.get)
     */
    public static final Integer INVALID_LIST_REQUEST = 20008;
    /**
     * 请求参数有问题
     */
    public static final Integer INVALID_FIELDS_REQUEST = 20009;
    /**
     * 多店查询时，target目标店铺为空
     */
    public static final Integer INVALID_TARGET_REQUEST = 20010;
}
