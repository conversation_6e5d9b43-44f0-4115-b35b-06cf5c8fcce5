package cn.loveapp.uac.common.bo;

import java.util.concurrent.ThreadPoolExecutor;
import lombok.Data;

/**
 * @program: uac-service-group
 * @description: UserAutoRenewBo
 * @author: <PERSON>
 * @create: 2020-03-18 13:50
 **/
@Data
public class UserAutoRenewBo {
	private String platformId;
	private String appType;
	private String articleCode;
	private String fuwuItemCode;
	private Long pageSize;
	private Integer maxRetryCount;
	private ThreadPoolExecutor executor;
}
