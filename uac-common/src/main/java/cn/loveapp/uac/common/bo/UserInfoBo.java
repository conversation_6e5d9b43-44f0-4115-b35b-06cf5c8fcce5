package cn.loveapp.uac.common.bo;

import cn.loveapp.uac.common.api.domain.SellerArticleUserSubscribe;
import cn.loveapp.uac.entity.LevelCycleEndTime;
import cn.loveapp.uac.request.BaseHttpRequest;
import cn.loveapp.uac.request.RefreshUserInfoRequest;
import cn.loveapp.uac.request.UserInfoRequest;
import java.time.LocalDateTime;
import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * @program: uac-service-group
 * @description: SellerBo
 * @author: Jason
 * @create: 2020-03-05 10:39
 **/
@ToString
@Data
@NoArgsConstructor
public class UserInfoBo {
	private String platformId;
	private String appType;
	private String sellerNick;
	private String subSellerNick;
	private String sellerId;
	private String corpId;
	private String sellerRole;
	private String hVersion;
	private Integer level;
	private LocalDateTime orderCycleEnd;
	private LocalDateTime authDeadLine;
	private LocalDateTime w2Deadline;
	private LocalDateTime lastPaidTime;
	private LocalDateTime lastActiveDateTime;
	private LocalDateTime createDateTime;
	private String ipAddress;
	//最后触发的端点
	private String lastActivePoint;
	private Boolean hasSilent;
	private IdentityProperty identityProperty;

	private String accessToken;
	private String refreshToken;
	private String tag;

	private Integer originalIdentityLevel;
	private LocalDateTime originalIdentityOrderCycleEnd;
	private String originalIdentityHversion;
	private String originalIdentityRole;
	private Boolean originalHasNeedAuth;

	//用于更新count
	private Integer countPc;
	private Integer countWw;
	private Integer countMp;

	private Boolean hasExist;

	/**
	 * pdd用户设置的店铺名称  nick存储的为pdd生成的，mallName为用户自定义的名称
	 */
	private String mallName;

	/**
	 * taobao.vas.subscribe.get 接口返回的 订购关系到期时间  用于查询订购订单记录
	 */
	private LocalDateTime vasSubscibeGetDeadline;


	/**
	 * taobao.vas.subscribe.get( 订购关系查询 ) 响应集合.get(0)，最新一条订购关系数据
	 */
	private SellerArticleUserSubscribe articleUserSubscribe;

	/**
	 * vas
	 */
	private LocalDateTime fuwuOrderCycleEnd;
	private Integer fuwuVipFlag;

	/**
	 * 供货商id
	 */
	private String supplierId;

	/**
	 * 供货商nick
	 */
	private String supplierNick;

    /**
     * 客户appId (必要)
     */
    private String memberId;

    /**
     * 客户appId (必要)
     */
    private String sellerAppId;

    /**
     * 客户app秘钥 (必要)
     */
    private String sellerAppSecret;

	/**
	 * 店铺名（代发快手店铺nick）
	 */
	private String shopName;

	/**
	 * 多店tag
	 */
	private List<String> ayMultiTags;

    /**
     * 比当前使用中版本低的剩余天数集合
     */
    private List<LevelCycleEndTime> lowerSentLevelList;

    /**
     * 店铺密码（tiktok）
     */
    private String shopCipher;

    /**
     * 店铺id列表（tiktok）
     */
    private String shopId;

    /**
     * 专业版到期时间
     */
    private LocalDateTime professionalOrderCycleEnd;

    /**
     * 授权是否异常(false 代表正常 true 代表授权异常)
     */
    private Boolean isAuthExcept;

	@Data
	@ToString(callSuper = true, includeFieldNames = true)
	public static class IdentityProperty {
		private Integer property;
		private Boolean hasNeedAuth;
		private Boolean hasChanged;
	}

	/**
	 * 构造函数
	 * @param platformId 平台ID
	 * @param appType 平台应用
	 * @param sellerNick 平台用户名称
	 */
	public UserInfoBo(String platformId, String appType, String sellerNick) {
		this.platformId = platformId;
		this.appType = appType;
		this.sellerNick = sellerNick;
	}

	/**
	 * 构造函数
	 * @param platformId
	 * @param appType
	 * @param sellerNick
	 * @param sellerId
	 */
	public UserInfoBo(String platformId, String appType, String sellerNick, String sellerId) {
		this.platformId = platformId;
		this.appType = appType;
		this.sellerNick = sellerNick;
		this.sellerId = sellerId;
	}

	public static <T extends BaseHttpRequest> UserInfoBo of(T userInfoRequest) {
		UserInfoBo userInfoBo = new UserInfoBo(userInfoRequest.getPlatformId(), userInfoRequest.getApp(), userInfoRequest.getSellerNick());
		String subSellerNick = userInfoRequest.getSubSellerNick();
		if (!StringUtils.isEmpty(subSellerNick)) {
			userInfoBo.setSubSellerNick(subSellerNick);
		}
		userInfoBo.setHasExist(Boolean.TRUE);
		userInfoBo.setSellerId(userInfoRequest.getSellerId());
		userInfoBo.setMemberId(userInfoRequest.getMemberId());
		userInfoBo.setSellerAppId(userInfoRequest.getSellerAppId());
        userInfoBo.setShopId(userInfoRequest.getShopId());
		userInfoBo.setMallName(userInfoRequest.getMallName());
		return userInfoBo;
	}

	public static UserInfoBo ofSellerId(UserInfoRequest userInfoRequest){
		UserInfoBo userInfoBo = new UserInfoBo();
		userInfoBo.setPlatformId(userInfoRequest.getPlatformId());
		userInfoBo.setAppType(userInfoRequest.getApp());
		userInfoBo.setSellerId(userInfoRequest.getSellerId());
		String subSellerNick = userInfoRequest.getSubSellerNick();
		if (!StringUtils.isEmpty(subSellerNick)) {
			userInfoBo.setSubSellerNick(subSellerNick);
		}
		return userInfoBo;
	}

    public static UserInfoBo ofSellerAppId(UserInfoRequest userInfoRequest){
        UserInfoBo userInfoBo = new UserInfoBo();
        userInfoBo.setPlatformId(userInfoRequest.getPlatformId());
        userInfoBo.setAppType(userInfoRequest.getApp());
        userInfoBo.setSellerAppId(userInfoRequest.getSellerAppId());
        String subSellerNick = userInfoRequest.getSubSellerNick();
        if (!StringUtils.isEmpty(subSellerNick)) {
            userInfoBo.setSubSellerNick(subSellerNick);
        }
        return userInfoBo;
    }


	public static UserInfoBo of(RefreshUserInfoRequest request) {
		UserInfoBo userInfoBo = new UserInfoBo(request.getPlatformId(), request.getApp(), request.getSellerNick());
		userInfoBo.setLevel(request.getVipflag());
		userInfoBo.setSellerRole(request.getRoleId());
		userInfoBo.setIpAddress(request.getLastIpAddress());
		if (StringUtils.isNotBlank(request.getLastActiveDateTime())) {
			userInfoBo.setLastActiveDateTime(request.convertLastActiveDateTime());
		}
		if (StringUtils.isNotBlank(request.getOrderCycleEndTime())) {
			userInfoBo.setOrderCycleEnd(request.convertOrderCycleEndTime());
		}
		if (StringUtils.isNotBlank(request.getLastPaidTime())) {
			userInfoBo.setLastPaidTime(request.convertLastPaidTime());
		}
		if (StringUtils.isNotBlank(request.getLastactivePoint())) {
			userInfoBo.setLastActivePoint(request.getLastactivePoint());
		}
		userInfoBo.setHasSilent(request.convertIsSilent());
		return userInfoBo;
	}

}
