package cn.loveapp.uac.common.dao.redis.repository;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.dao.redis.base.BaseValueRedisRepository;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * @Author: zhong<PERSON><PERSON>e
 * @Date: 2023/8/8 18:24
 * @Description: 高搜活跃标记 - redis repository
 */
@Component
public class SearchActiveRedisRepository extends BaseValueRedisRepository<String> {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(SearchActiveRedisRepository.class);

    /**
     * 高搜活跃用户标识key前缀
     */
    private static final String SEARCH_ACTIVE_PREFIX = "search.active:";

    public SearchActiveRedisRepository(
            @Qualifier("stringTradeRedisTemplate") StringRedisTemplate stringTradeRedisTemplate,
            @Qualifier("stringItemRedisTemplate") StringRedisTemplate stringItemRedisTemplate) {
        super(ImmutableMap.of(
                CommonAppConstants.APP_TRADE, stringTradeRedisTemplate,
                CommonAppConstants.APP_ITEM, stringItemRedisTemplate,
                CommonAppConstants.APP_DISTRIBUTE, stringItemRedisTemplate
        ));
    }

    /**
     * 获取高搜活跃标识
     *
     * @param sellerId
     * @param platformId
     * @param appName
     * @return
     */
    public String get(String sellerId, String platformId, String appName) {
        String searchActiveKey = initCollection(sellerId, platformId, appName);
        return super.find(searchActiveKey, String.class, appName);
    }

    /**
     * 设置高搜活跃标识
     *
     * @param sellerId
     * @param platformId
     * @param appName
     * @param value
     * @param timeout
     * @param timeUnit
     */
    public void set(String sellerId, String platformId, String appName, String value, long timeout, TimeUnit timeUnit) {
        String searchActiveKey = initCollection(sellerId, platformId, appName);
        super.add(searchActiveKey, value, appName, timeout, timeUnit);
    }

    @Override
    public String initCollection(String sellerId, String platformId, String appName) {
        if (StringUtils.isAnyEmpty(sellerId, platformId, appName)) {
            LOGGER.logError(sellerId, platformId, "缺少必要入参，无法生成SearchActiveKey");
            return null;
        }
        return SEARCH_ACTIVE_PREFIX + platformId + ":" + appName + ":" + sellerId;
    }

}
