package cn.loveapp.uac.common.constant;

import cn.loveapp.uac.common.entity.UserProductInfo;

/**
 * 促销活动的ActFlag
 *
 * <AUTHOR>
 * @date 2021/9/7
 */
public enum PromotionActFlag {
    /**
     * 基础版
     */
    JCMZ(0, UserProductInfo.LEVEL_TWO),
    /**
     * 新手村
     */
    ZS(1, UserProductInfo.LEVEL_FOUR),
    /**
     * 高级版
     */
    MZ(2, UserProductInfo.LEVEL_ONE),
    /**
     * 旧专业版(包含所有新专业版功能+库存管理功能)
     */
    ZYMZ(4, UserProductInfo.LEVEL_SIX),
    /**
     * 新专业版(近6个月功能)
     */
    NZYMZ(3, UserProductInfo.LEVEL_EIGHT);

    public final int priority;
    public final int level;

    PromotionActFlag(int priority, int level) {
        this.priority = priority;
        this.level = level;
    }

    public static PromotionActFlag of(String actFlag) {
        try {
            return PromotionActFlag.valueOf(actFlag.toUpperCase());
        } catch (IllegalArgumentException e) {
            return MZ;
        }
    }

}
