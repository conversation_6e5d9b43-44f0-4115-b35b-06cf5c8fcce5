package cn.loveapp.uac.common.code;

/**
 * @program: orders-services-group
 * @description: ErrorCode
 * @author: <PERSON>
 * @create: 2018-12-29 11:04
 **/
public interface ErrorCode {
	enum BaseCode implements ErrorCode{
		/**
		 * basecode
		 */
		SUCCESS(0, "成功"),
		SYS_ERR(1, "程序异常"),
		PARAMS_ERR(2, "参数错误"),
		REQUEST_ERR(3, "请求错误"),
		SIGN_PRIAVET_KEY_ERR(4, "签名秘钥错误"),
		PACK_PRIAVET_KEY_ERR(5, "数据加密密钥错误"),
		SIGN_ERR(6, "签名错误"),
		SYS_MAINTAIN(7, "系统维护"),
		SYS_DB_ERR(8, "DB错误"),
		SYS_CACHE_ERR(9, "Cache错误");

		private Integer code;

		public Integer getCode() {
			return code;
		}

		public void setCode(Integer code) {
			this.code = code;
		}

		public String getMessage() {
			return message;
		}

		public void setMessage(String message) {
			this.message = message;
		}

		private String message;
		private BaseCode(Integer code, String message){
			this.code = code;
			this.message = message;
		}
	}
	enum SubCode implements ErrorCode{
		/**
		 * basecode
		 */
		RECORD_NOEXIST("record.noexist", "记录不存在"),
		FULLINFO_ERR("fullinfo.err", "fullinfo结果未获取到"),
		TRADE_ERR("trade.err", "trade结果异常"),
		SUCCESS("success", "成功");

		private String code;

		public String getCode() {
			return code;
		}

		public void setCode(String code) {
			this.code = code;
		}

		public String getMessage() {
			return message;
		}

		public void setMessage(String message) {
			this.message = message;
		}

		private String message;
		private SubCode(String code, String message){
			this.code = code;
			this.message = message;
		}
	}





}
