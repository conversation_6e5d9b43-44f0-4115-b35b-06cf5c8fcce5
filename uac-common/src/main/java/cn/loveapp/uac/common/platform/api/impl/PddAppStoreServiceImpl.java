package cn.loveapp.uac.common.platform.api.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.uac.common.api.request.SellerVasOrderSearchRequest;
import cn.loveapp.uac.common.api.request.SellerVasSubscSearchRequest;
import cn.loveapp.uac.common.api.request.SellerVasSubscribeGetRequest;
import cn.loveapp.uac.common.api.response.SellerVasOrderSearchResponse;
import cn.loveapp.uac.common.api.response.SellerVasSubscSearchResponse;
import cn.loveapp.uac.common.api.response.SellerVasSubscribeGetResponse;
import cn.loveapp.uac.common.platform.api.AppStoreService;
import org.springframework.stereotype.Service;

/**
 * @program: uac-service-group
 * @description: PddAppStoreFuWuServiceImpl
 * @author: Jason
 * @create: 2021-04-20 15:24
 **/
@Service
public class PddAppStoreServiceImpl implements AppStoreService {

	@Override
	public SellerVasSubscribeGetResponse vasSubscribeGet(
		SellerVasSubscribeGetRequest sellerVasSubscribeGetRequest,
		String platformId, String appName) {
		return null;
	}

	@Override
	public SellerVasSubscSearchResponse vasSubscribeSearch(
		SellerVasSubscSearchRequest sellerVasSubscSearchRequest,
		String platformId, String appName) {
		return null;
	}

	@Override
	public SellerVasOrderSearchResponse vasOrderSearch(SellerVasOrderSearchRequest vasOrderSearchRequest, String platformId, String appName) {
		return null;
	}

	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_PDD;
	}
}
