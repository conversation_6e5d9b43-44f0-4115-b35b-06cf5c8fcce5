package cn.loveapp.uac.common.config.app;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @program: uac-service-group
 * @description: TaobaoArticleCodeConfig
 * @author: Jason
 * @create: 2020-03-05 19:38
 **/
@Configuration
@Data
public class ArticleCodeConfig {
	@Value("${uac.taobao.trade.article.code:FW_GOODS-1827490}")
	private String tradeArtiCleCode;
	@Value("${uac.taobao.item.article.code:FW_GOODS-1828810}")
	private String itemArtiCleCode;

	public String getArticleCode(String platformId, String appType) {
		if (platformId.equals(CommonPlatformConstants.PLATFORM_TAO)) {
			switch (appType) {
				case CommonAppConstants.APP_TRADE:
					return tradeArtiCleCode;
				case CommonAppConstants.APP_ITEM:
					return itemArtiCleCode;
				default:
					return null;
			}
		}
		return null;
	}
}
