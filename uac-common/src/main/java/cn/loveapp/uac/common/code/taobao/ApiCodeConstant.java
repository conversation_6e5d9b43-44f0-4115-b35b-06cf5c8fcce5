package cn.loveapp.uac.common.code.taobao;

import java.util.Arrays;
import java.util.List;

/**
 * @program: uac-service-group
 * @description: ApiCodeConstant
 * @author: <PERSON>
 * @create: 2020-05-26 14:45
 **/
public class ApiCodeConstant {

	/**
	 * App Call Limited
	 */
	public static final String APP_CALL_LIMITED_CODE = "7";

	/**
	 * This ban will last for 1 more seconds
	 */
	public static final String APP_CALL_LIMITED_SUB_CODE = "accesscontrol.limited-by-api-access-count";

	/**
	 * 获取需要忽略成功并且返回原始code的集合
	 * @return
	 */
	public static List<String> getIgnoreErrorCode() {
		return Arrays.asList(
			APP_CALL_LIMITED_CODE,
			APP_CALL_LIMITED_SUB_CODE
		);
	}

	/**
	 *
	 */
	public enum CodeEnum {
		/**
		 * 成功
		 */
		SUCCESS,
		/**
		 * 被限流了
		 */
		CALL_LIMIT,
		/**
		 * 系统错误
		 */
		ERROR;
	}

}
