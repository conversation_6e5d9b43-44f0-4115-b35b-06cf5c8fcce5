package cn.loveapp.uac.common.service;

import java.io.UnsupportedEncodingException;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-05-17 11:05
 * @Description: 代发兼容处理服务
 */
public interface DistributeUserProcessService {

    /**
     * 创建代发用户redis key
     * 
     * @param sellerNick
     * @param sellerId
     * @param platformId
     * @param appName
     * @return
     */
    String createdUserRedisKey(String sellerNick, String sellerId, String platformId, String appName)
        throws UnsupportedEncodingException;

    /**
     * 追加代发用户redis实体字段
     * 
     * @param entries
     * @param platformId
     * @param appName
     * @param isUpdate
     *            是否是更新操作
     * @return
     */
    Map appendUserRedisField(Map entries, String platformId, String appName, boolean isUpdate);

    /**
     *  1688 代发应用token解密
     *
     * @param cipherText
     * @param sessionKey
     * @param platformId
     * @param appName
     * @return
     * @throws Exception
     */
    String decryptForSession(String cipherText, String sessionKey, String platformId, String appName) throws Exception;

    /**
     * 1688 代发应用token加密
     * 
     * @param token
     * @param sessionKey
     * @param platformId
     * @param appName
     * @return
     */
    String encryptForSession(String token, String sessionKey, String platformId, String appName) throws Exception;

}
