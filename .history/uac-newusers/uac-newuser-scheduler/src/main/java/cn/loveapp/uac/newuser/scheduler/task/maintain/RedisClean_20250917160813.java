package cn.loveapp.uac.newuser.scheduler.task.maintain;

import cn.loveapp.common.constant.CommonLogisticsConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.platformsdk.properties.AbstractPlatformProperties;
import cn.loveapp.uac.db.common.repository.UserRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;

@Component
public class RedisClean {

    @Autowired
    @Qualifier("stringTradeRedisTemplate")
    private StringRedisTemplate tradeRedisTemplate;

    @Autowired
    @Qualifier("stringItemRedisTemplate")
    private StringRedisTemplate itemRedisTemplate;

    @Autowired
    @Qualifier("stringDistributeRedisTemplate")
    private StringRedisTemplate distributeRedisTemplate;

    @Autowired
    private UserRepository userRepository;

    private static String[] EXCLUDE_PLATFORM_LIST = new String[]{
        CommonPlatformConstants.PLATFORM_AIYONG, CommonPlatformConstants.PLATFORM_OFFLINESTORE,
        CommonPlatformConstants.PLATFORM_BIYAO, CommonPlatformConstants.PLATFORM_DEFAULT,
        CommonLogisticsConstants.PLATFORM_CAINIAO, CommonLogisticsConstants.PLATFORM_KDNIAO
    };

    /**
     * platformID(storeId) -> appNames
     */
    private final LinkedHashMap<String, List<String>> platformAppNamesMap = new LinkedHashMap<>();

    public RedisClean(ObjectProvider<List<AbstractPlatformProperties>> platformPropertiesProvider) {
        List<AbstractPlatformProperties> platformProperties = platformPropertiesProvider.getIfAvailable();
        if (platformProperties == null) {
            return;
        }
        platformProperties.sort(Comparator.comparing(o -> o.getClass().getName()));
        // 将 platformProperties 按照 className 转为 LinkedHashMap, className 截取 PlatformXXXProperties 之间的XXX部分
        for (AbstractPlatformProperties property : platformProperties) {
            String className = property.getClass().getSimpleName();
            if(StringUtils.containsAny(className.toUpperCase(), EXCLUDE_PLATFORM_LIST)) {
                continue;
            }
            // 截取 PlatformXXXProperties 之间的XXX部分
            String platformName = className.substring(className.indexOf("Platform") + 8, className.indexOf("Properties")).toUpperCase();
            if (platformName.equals("TAOBAO")) {
                platformName = CommonPlatformConstants.PLATFORM_TAO;
            }

            List<String> appNames = property.getApps().keySet().stream().sorted().toList();
            this.platformAppNamesMap.put(platformName, appNames);
        }
        System.out.println(platformAppNamesMap);
    }

    @Scheduled(cron = "${uac.newuser.task.redisClean.cron:0 0 1 * * ?}")
    public void clean() {

    }
}
