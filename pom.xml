<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>cn.loveapp.uac</groupId>
    <artifactId>uac-service-group</artifactId>
    <packaging>pom</packaging>

    <parent>
        <groupId>cn.loveapp.common</groupId>
        <artifactId>common-spring-boot-parent</artifactId>
        <version>2.0.0-SNAPSHOT</version>
    </parent>

    <name>爱用-uac</name>
    <description>爱用-uac</description>
    <version>1.0-SNAPSHOT</version>

    <organization>
        <name>Loveapp Inc.</name>
        <url>http://www.aiyongbao.com</url>
    </organization>

    <properties>
        <uac.api.version>2.0.0-SNAPSHOT</uac.api.version>
        <uac-newuser-api.version>2.0.0-SNAPSHOT</uac-newuser-api.version>
        <uac.domain.version>2.0.0-SNAPSHOT</uac.domain.version>
    </properties>

    <modules>
        <module>uac-api</module>
        <module>uac-newuser-api</module>
        <module>uac-common</module>
        <module>uac-service</module>
		<module>uac-job</module>
		<module>uac-db-common</module>
		<module>uac-service-common</module>
        <module>uac-newusers</module>
        <module>uac-domain</module>
    </modules>


    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>
