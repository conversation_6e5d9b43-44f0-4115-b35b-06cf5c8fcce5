package cn.loveapp.uac.service.service.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.uac.common.bo.UserBo;
import cn.loveapp.uac.common.config.DistributeConfig;
import cn.loveapp.uac.common.config.app.ArticleCodeConfig;
import cn.loveapp.uac.common.dao.redis.repository.UserManageRedisRepositoryHashRedisRepository;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.common.entity.redis.UserRedisEntity;
import cn.loveapp.uac.common.platform.api.AppStoreService;
import cn.loveapp.uac.common.platform.api.AuthService;
import cn.loveapp.uac.db.common.repository.OrderSearchRepository;
import cn.loveapp.uac.db.common.repository.UserRepository;
import cn.loveapp.uac.db.common.service.PlatformUserProductInfoService;
import cn.loveapp.uac.service.cache.ReminderCache;
import cn.loveapp.uac.service.service.OperationService;
import cn.loveapp.uac.service.service.SellerOrderSearchService;
import cn.loveapp.uac.service.service.SellerService;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @date 2022-12-26 16:11
 * @Description:
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE,
    classes = {StringRedisTemplate.class, UserRepository.class, SellerService.class, ReminderCache.class,
        AuthService.class, UserManageRedisRepositoryHashRedisRepository.class, DistributeConfig.class,
        PlatformUserProductInfoService.class, AppStoreService.class, OperationService.class,
        OrderSearchRepository.class, SellerOrderSearchService.class, ArticleCodeConfig.class})
public class UserManageRedisRepositoryHashRedisRepositoryTest {

    @MockitoSpyBean
    private UserManageRedisRepositoryHashRedisRepository userManageRedisRepositoryHashRedisRepository;

    @MockitoBean
    private PlatformUserProductInfoService platformUserProductInfoService;

    @MockitoBean
    private AuthService authService;

    @MockitoBean
    private StringRedisTemplate stringRedisTemplate;

    @MockitoBean
    @Qualifier("stringTradeRedisTemplate")
    private StringRedisTemplate stringTradeRedisTemplate;

    @MockitoBean
    @Qualifier("stringItemRedisTemplate")
    private StringRedisTemplate stringItemRedisTemplate;

    @MockitoBean
    private UserRepository userRepository;

    @MockitoBean
    private DistributeConfig distributeConfig;

    @MockitoBean
    private AppStoreService appStoreService;

    @MockitoBean
    private OperationService operationService;

    @MockitoBean
    private OrderSearchRepository orderSearchRepository;

    @MockitoBean
    private SellerOrderSearchService sellerOrderSearchService;

    @MockitoBean
    private ArticleCodeConfig articleCodeConfig;

    @MockitoBean
    private OAuthDecorationServiceImpl oAuthDecorationService;

    @MockitoBean
    private RedisConnectionFactory redisConnectionFactory;

    @org.junit.jupiter.api.Test
    public void testInitCollection() {
        String sellerNick = "gh_4ebc6c1c889a";
        String sellerId = "wxc37ce2922b49c0b8";
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        doReturn(getDistributeSpecialRedisPrefixMap()).when(distributeConfig).getDistributeSpecialRedisPrefixMap();
        String redKey =
            userManageRedisRepositoryHashRedisRepository.initCollection(sellerNick, sellerId, platformId, appName);
        String redisKey = URLEncoder.encode("videoShop:" + sellerNick);
        Assertions.assertEquals(redisKey, redKey);
    }

    @org.junit.jupiter.api.Test
    public void testEntries() {
        String sellerNick = "gh_4ebc6c1c889a";
        String sellerId = "wxc37ce2922b49c0b8";
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        String redisKey = URLEncoder.encode("videoShop:" + sellerNick);

        doReturn(getDistributeSpecialRedisPrefixMap()).when(distributeConfig).getDistributeSpecialRedisPrefixMap();
        when(stringTradeRedisTemplate.hasKey(any())).thenReturn(true);
        when(stringItemRedisTemplate.hasKey(any())).thenReturn(true);
        doReturn(getUserRedisEntity()).when(userManageRedisRepositoryHashRedisRepository).findAll(any(), any(), any(),
            any());
        UserRedisEntity entries =
            userManageRedisRepositoryHashRedisRepository.entries(sellerNick, sellerId, platformId, appName);

        verify(userManageRedisRepositoryHashRedisRepository).initCollection(eq(sellerNick), eq(sellerId),
            eq(platformId), eq(appName));
        verify(userManageRedisRepositoryHashRedisRepository).findAll(eq(redisKey), any(), eq(platformId), eq(appName));

    }

    /**
     * 保存用户数据
     */
    @org.junit.jupiter.api.Test
    public void testPutUserData1() {
        String sellerNick = "gh_4ebc6c1c889a";
        String sellerId = "wxc37ce2922b49c0b8";
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;

        String redisKey = URLEncoder.encode("videoShop:" + sellerNick);

        UserBo userBo = new UserBo();
        userBo.setSellerNick(sellerNick);
        userBo.setSellerId(sellerId);
        userBo.setPlatformId(platformId);
        userBo.setAppType(appName);
        userBo.setUserProductInfo(getUserProductInfo());

        doReturn(getDistributeSpecialRedisPrefixMap()).when(distributeConfig).getDistributeSpecialRedisPrefixMap();
        doReturn(true).when(userManageRedisRepositoryHashRedisRepository).putAll(any(), any(), any(), any());
        boolean b = userManageRedisRepositoryHashRedisRepository.putUserData(userBo);

        Assertions.assertTrue(b);
        verify(userManageRedisRepositoryHashRedisRepository).initCollection(eq(sellerNick), eq(sellerId),
            eq(platformId), eq(appName));
        verify(userManageRedisRepositoryHashRedisRepository).putAll(eq(redisKey), eq(userBo.getUserRedisEntity()),
            eq(platformId), eq(appName));
    }

    /**
     * 保存抖店用户数据
     */
    @org.junit.jupiter.api.Test
    public void testPutUserData2() {
        String sellerNick = "gh_4ebc6c1c889a";
        String sellerId = "wxc37ce2922b49c0b8";
        String platformId = CommonPlatformConstants.PLATFORM_DOUDIAN;
        String appName = CommonAppConstants.APP_DISTRIBUTE;

        String redisKey = URLEncoder.encode("dy:" + sellerId);

        UserBo userBo = new UserBo();
        userBo.setSellerNick(sellerNick);
        userBo.setSellerId(sellerId);
        userBo.setPlatformId(platformId);
        userBo.setAppType(appName);
        userBo.setUserProductInfo(getUserProductInfo());

        doReturn(getDistributeSpecialRedisPrefixMap()).when(distributeConfig).getDistributeSpecialRedisPrefixMap();
        doReturn(true).when(userManageRedisRepositoryHashRedisRepository).putAll(any(), any(), any(), any());
        boolean b = userManageRedisRepositoryHashRedisRepository.putUserData(userBo);

        verify(userManageRedisRepositoryHashRedisRepository).initCollection(eq(sellerNick), eq(sellerId),
            eq(platformId), eq(appName));
        verify(userManageRedisRepositoryHashRedisRepository).initCollection(eq(sellerId), eq(sellerId), eq(platformId),
            eq(appName));
        verify(userManageRedisRepositoryHashRedisRepository, times(2)).putAll(eq(redisKey),
            eq(userBo.getUserRedisEntity()), eq(platformId), eq(appName));
        Assertions.assertTrue(b);
    }

    /**
     * 保存必要代发用户数据
     */
    @org.junit.jupiter.api.Test
    public void testPutUserData3() throws UnsupportedEncodingException {
        String sellerNick = "gh_4ebc6c1c889a";
        String sellerId = "wxc37ce2922b49c0b8";
        String platformId = CommonPlatformConstants.PLATFORM_BIYAO;
        String appName = CommonAppConstants.APP_DISTRIBUTE;

        UserBo userBo = new UserBo();
        userBo.setSellerNick(sellerNick);
        userBo.setSellerId(sellerId);
        userBo.setPlatformId(platformId);
        userBo.setAppType(appName);
        userBo.setUserProductInfo(getUserProductInfo());
        UserRedisEntity userEntity = userBo.getUserRedisEntity();

        UserRedisEntity copyEntity = new UserRedisEntity();
        copyEntity.setUser_id(userEntity.getUser_id());
        copyEntity.setUser_nick(userEntity.getUser_nick());
        copyEntity.setApp_id(userEntity.getApp_id());
        copyEntity.setApp_secret(userEntity.getApp_secret());
        copyEntity.setAccess_token(userEntity.getAccess_token());
        copyEntity.setRefresh_token(userEntity.getRefresh_token());
        copyEntity.setOrder_cycle_end(userEntity.getOrder_cycle_end());
        copyEntity.setAuth_dead_line(userEntity.getAuth_dead_line());

        String redisKey = platformId + ":" + appName + ":" + sellerId;
        String distributeKey = URLEncoder.encode(platformId.toLowerCase() + ":" + userEntity.getUser_id(), "UTF-8");

        doReturn(getDistributeSpecialRedisPrefixMap()).when(distributeConfig).getDistributeSpecialRedisPrefixMap();
        doReturn(true).when(userManageRedisRepositoryHashRedisRepository).putAll(any(), any(), any(), any());
        boolean b = userManageRedisRepositoryHashRedisRepository.putUserData(userBo);

        verify(userManageRedisRepositoryHashRedisRepository).initCollection(eq(sellerNick), eq(sellerId),
            eq(platformId), eq(appName));
        verify(userManageRedisRepositoryHashRedisRepository).putAll(eq(redisKey), eq(userBo.getUserRedisEntity()),
            eq(platformId), eq(appName));
        verify(userManageRedisRepositoryHashRedisRepository).putAll(eq(distributeKey), eq(copyEntity), eq(platformId),
            eq(CommonAppConstants.APP_ITEM));
        Assertions.assertTrue(b);
    }

    @org.junit.jupiter.api.Test
    public void testPutUserData4() {
        String sellerNick = "gh_4ebc6c1c889a";
        String sellerId = "wxc37ce2922b49c0b8";
        String platformId = CommonPlatformConstants.PLATFORM_DOUDIAN;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        String redisKey = URLEncoder.encode("dy:" + sellerId);

        UserBo userBo = new UserBo();
        userBo.setSellerNick(sellerNick);
        userBo.setSellerId(sellerId);
        userBo.setPlatformId(platformId);
        userBo.setAppType(appName);
        userBo.setUserProductInfo(getUserProductInfo());

        doReturn(getDistributeSpecialRedisPrefixMap()).when(distributeConfig).getDistributeSpecialRedisPrefixMap();
        doReturn(true).when(userManageRedisRepositoryHashRedisRepository).putAll(any(), any(), any(), any());
        boolean b = userManageRedisRepositoryHashRedisRepository.putUserData(userBo);

        Assertions.assertTrue(b);
        verify(userManageRedisRepositoryHashRedisRepository).initCollection(eq(sellerNick), eq(sellerId),
            eq(platformId), eq(appName));
        verify(userManageRedisRepositoryHashRedisRepository)
            .initCollection(eq(userBo.getUserRedisEntity().getUser_id()), eq(sellerId), eq(platformId), eq(appName));
        verify(userManageRedisRepositoryHashRedisRepository, times(2)).putAll(eq(redisKey),
            eq(userBo.getUserRedisEntity()), eq(platformId), eq(appName));
    }

    public Map<String, String> getDistributeSpecialRedisPrefixMap() {
        Map<String, String> map = new HashMap<>();
        map.put("DOUDIAN", "dy");
        map.put("KWAISHOP", "ks");
        map.put("YOUZAN", "yz");
        map.put("WXVIDEOSHOP", "videoShop");
        return map;
    }

    public UserRedisEntity getUserRedisEntity() {
        UserRedisEntity userRedisEntity = new UserRedisEntity();
        userRedisEntity.setUser_id("wxc37ce2922b49c0b8");
        userRedisEntity.setUser_nick("gh_4ebc6c1c889a");
        userRedisEntity.setVipflag("1");
        userRedisEntity.setOrder_cycle_end("2023-12-22 16:05:55");
        userRedisEntity.setW1_deadline("2023-12-22 16:05:55");
        userRedisEntity.setAuth_dead_line("2023-12-22 16:05:55");
        userRedisEntity.setAccess_token("1111111111111111111");
        userRedisEntity.setRefresh_token("222222222222222222222");
        return userRedisEntity;
    }

    public UserProductInfo getUserProductInfo() {
        UserProductInfo userProductInfo = new UserProductInfo();
        userProductInfo.setTopsessionkey("1111111111111111");
        userProductInfo.setLastAuthDeadLine(LocalDateTime.now().plusDays(2));
        userProductInfo.setToprefreshkey("22222222222222222");
        userProductInfo.setW1Deadline(LocalDateTime.now().plusDays(2));
        userProductInfo.setLastOrderCycleEnd(LocalDateTime.now().plusDays(2));
        userProductInfo.setMallName("视频号小店");
        userProductInfo.setUserIdStr("wxc37ce2922b49c0b8");
        userProductInfo.setCreateDate(LocalDateTime.now().minusDays(2));
        userProductInfo.setVipflag(1);
        userProductInfo.setShopName("爱用小店");
        userProductInfo.setUserId("1112121");
        return userProductInfo;
    }
}
