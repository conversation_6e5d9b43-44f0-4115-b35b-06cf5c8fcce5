package cn.loveapp.uac.service.service.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.uac.code.ApiCode;
import cn.loveapp.uac.common.bo.UserBo;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.code.ErrorCode;
import cn.loveapp.uac.common.config.DistributeConfig;
import cn.loveapp.uac.common.config.app.ArticleCodeConfig;
import cn.loveapp.uac.common.dao.redis.repository.UserManageRedisRepositoryHashRedisRepository;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.common.entity.redis.UserRedisEntity;
import cn.loveapp.uac.common.exception.CacheWriteException;
import cn.loveapp.uac.common.exception.DbWriteException;
import cn.loveapp.uac.common.platform.api.AppStoreService;
import cn.loveapp.uac.common.platform.api.AuthService;
import cn.loveapp.uac.db.common.repository.OrderSearchRepository;
import cn.loveapp.uac.db.common.repository.UserRepository;
import cn.loveapp.uac.db.common.service.PlatformUserProductInfoService;
import cn.loveapp.uac.exception.UserException;
import cn.loveapp.uac.response.CallbackResponse;
import cn.loveapp.uac.response.UserInfoResponse;
import cn.loveapp.uac.service.cache.ReminderCache;
import cn.loveapp.uac.service.service.OperationService;
import cn.loveapp.uac.service.service.SellerOrderSearchService;
import cn.loveapp.uac.service.service.SellerService;
import com.alibaba.fastjson2.JSON;
import org.junit.jupiter.api.Assertions;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;

import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @date 2022-12-23 11:03
 * @Description:
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE,
    classes = {StringRedisTemplate.class, UserRepository.class, SellerService.class, ReminderCache.class,
        AuthService.class, UserManageRedisRepositoryHashRedisRepository.class, DistributeConfig.class,
        PlatformUserProductInfoService.class, AppStoreService.class, OperationService.class,
        OrderSearchRepository.class, SellerOrderSearchService.class, ArticleCodeConfig.class})
public class SellerServiceImplTest {

    @MockitoSpyBean
    private SellerServiceImpl sellerService;

    @MockitoBean
    private StringRedisTemplate stringRedisTemplate;

    @MockitoBean
    private PlatformUserProductInfoService platformUserProductInfoService;

    @MockitoBean
    private AuthService authService;

    @MockitoBean
    private UserRepository userRepository;

    @MockitoBean
    private DistributeConfig distributeConfig;

    @MockitoBean
    private UserManageRedisRepositoryHashRedisRepository userManageRedisRepositoryHashRedisRepository;

    @MockitoBean
    private AppStoreService appStoreService;

    @MockitoBean
    private OperationService operationService;

    @MockitoBean
    private OrderSearchRepository orderSearchRepository;

    @MockitoBean
    private SellerOrderSearchService sellerOrderSearchService;

    @MockitoBean
    private ArticleCodeConfig articleCodeConfig;

    @MockitoBean
    private OAuthDecorationServiceImpl oAuthDecorationService;

    /**
     * redisKey为null，直接从数据库获取用户信息成功
     *
     * @throws UserException
     */
    @org.junit.jupiter.api.Test
    public void testGetUserInfo1() throws UserException {
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        UserBo userBo = new UserBo(getUserInfoBo());
        userBo.setHasReadTag(false);
        UserProductInfo userProductInfo = getUserProductInfo();
        UserInfoResponse userInfoResponse = getUserInfoResponse();
        userInfoResponse.setSellerNick(userProductInfo.getNick());
        userInfoResponse.setSellerId(userProductInfo.getUserId());
        userInfoResponse.setVipflag(userProductInfo.getVipflag());
        userInfoResponse.setHasNeedAuth(false);
        userInfoResponse.setAuthDeadLine(userProductInfo.getW1Deadline());
        userInfoResponse.setPlatformId(platformId);
        userInfoResponse.setAppName(appName);

        when(platformUserProductInfoService.getUserInfo(eq(userBo), any(), eq(platformId), eq(appName)))
            .thenReturn(userProductInfo);
        UserInfoResponse userInfo = sellerService.getUserInfo(getUserInfoBo(), platformId, appName);

        verify(userManageRedisRepositoryHashRedisRepository, never()).entries(anyString(), anyString(), anyString(),
            anyString());
        Assertions.assertEquals(userInfoResponse, userInfo);
    }

    /**
     * redisKey为null，从数据库获取用户信息失败
     *
     * @throws UserException
     */
    @org.junit.jupiter.api.Test
    public void testGetUserInfo2() throws UserException {
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        UserBo userBo = new UserBo(getUserInfoBo());
        userBo.setHasReadTag(false);

        when(platformUserProductInfoService.getUserInfo(eq(userBo), any(), eq(platformId), eq(appName)))
            .thenReturn(null);
        try {
            UserInfoResponse userInfo = sellerService.getUserInfo(getUserInfoBo(), platformId, appName);
            fail();
        } catch (UserException e) {
            verify(userManageRedisRepositoryHashRedisRepository, never()).entries(anyString(), anyString(), anyString(),
                anyString());
        }
    }

    /**
     * 从缓存获取用户信息成功(代发用户)
     *
     * @throws UserException
     */
    @org.junit.jupiter.api.Test
    public void testGetUserInfo3() throws UserException {
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        String redisKey = URLEncoder
            .encode(getDistributeSpecialRedisPrefixMap().get(platformId) + ":" + getUserInfoBo().getSellerNick());
        UserRedisEntity userRedisEntity = getUserRedisEntity();

        UserInfoResponse userInfoResponse = getUserInfoResponse();
        userInfoResponse.setSellerNick(userRedisEntity.getUser_nick());
        userInfoResponse.setSellerId(userRedisEntity.getUser_id());
        userInfoResponse.setVipflag(Integer.valueOf(userRedisEntity.getVipflag()));
        userInfoResponse.setHasNeedAuth(false);
        userInfoResponse.setAuthDeadLine(userRedisEntity.getW1Deadline());
        userInfoResponse.setOrderCycleEnd(userRedisEntity.getOrderCycleEnd());
        userInfoResponse.setPlatformId(platformId);
        userInfoResponse.setAppName(appName);

        when(platformUserProductInfoService.getUserInfo(eq(new UserBo(getUserInfoBo())), any(), eq(platformId),
            eq(appName))).thenReturn(getUserProductInfo());
        when(userManageRedisRepositoryHashRedisRepository.initCollection(eq(getUserInfoBo().getSellerNick()),
            eq(getUserInfoBo().getSellerId()), eq(platformId), eq(appName))).thenReturn(redisKey);
        when(userManageRedisRepositoryHashRedisRepository.entries(eq(getUserInfoBo().getSellerNick()),
            eq(getUserInfoBo().getSellerId()), eq(platformId), eq(appName))).thenReturn(userRedisEntity);
        when(distributeConfig.getDistributeSpecialRedisPrefixMap()).thenReturn(getDistributeSpecialRedisPrefixMap());

        UserInfoResponse userInfo = sellerService.getUserInfo(getUserInfoBo(), platformId, appName);

        verify(platformUserProductInfoService, never()).getUserInfo(any(), any(), eq(platformId), eq(appName));
        assertEquals(userInfoResponse, userInfo);
    }

    /**
     * 从缓存获取用户信息成功(交易淘宝)
     *
     * @throws UserException
     */
    @org.junit.jupiter.api.Test
    public void testGetUserInfo4() throws UserException {
        String platformId = CommonPlatformConstants.PLATFORM_TAO;
        String appName = CommonAppConstants.APP_TRADE;
        UserInfoBo userInfoBo = getUserInfoBo();
        userInfoBo.setPlatformId(platformId);
        userInfoBo.setAppType(appName);
        String redisKey =
            URLEncoder.encode(getDistributeSpecialRedisPrefixMap().get(platformId) + ":" + userInfoBo.getSellerNick());
        UserRedisEntity userRedisEntity = getUserRedisEntity();
        userRedisEntity.setTaobao_user_id(userInfoBo.getSellerId());
        userRedisEntity.setTrade_access_token_m("11111111111111111");
        userRedisEntity.setTrade_refresh_token_m("22222222222222222");

        UserInfoResponse userInfoResponse = getUserInfoResponse();
        userInfoResponse.setSellerNick(userRedisEntity.getUser_nick());
        userInfoResponse.setSellerId(userRedisEntity.getUser_id());
        userInfoResponse.setVipflag(Integer.valueOf(userRedisEntity.getVipflag()));
        userInfoResponse.setHasNeedAuth(false);
        userInfoResponse.setAuthDeadLine(userRedisEntity.getW1Deadline());
        userInfoResponse.setOrderCycleEnd(userRedisEntity.getOrderCycleEnd());
        userInfoResponse.setPlatformId(platformId);
        userInfoResponse.setAppName(appName);

        when(platformUserProductInfoService.getUserInfo(eq(new UserBo(userInfoBo)), any(), eq(platformId), eq(appName)))
            .thenReturn(getUserProductInfo());
        when(userManageRedisRepositoryHashRedisRepository.initCollection(eq(userInfoBo.getSellerNick()),
            eq(userInfoBo.getSellerId()), eq(platformId), eq(appName))).thenReturn(redisKey);
        when(userManageRedisRepositoryHashRedisRepository.entries(eq(userInfoBo.getSellerNick()),
            eq(userInfoBo.getSellerId()), eq(platformId), eq(appName))).thenReturn(userRedisEntity);
        when(distributeConfig.getDistributeSpecialRedisPrefixMap()).thenReturn(getDistributeSpecialRedisPrefixMap());

        UserInfoResponse userInfo = sellerService.getUserInfo(userInfoBo, platformId, appName);

        verify(platformUserProductInfoService, never()).getUserInfo(any(), any(), eq(platformId), eq(appName));
        assertEquals(userInfoResponse, userInfo);
    }

    /**
     * 从缓存获取用户信息成功(其他平台)
     *
     * @throws UserException
     */
    @org.junit.jupiter.api.Test
    public void testGetUserInfo5() throws UserException {
        String platformId = CommonPlatformConstants.PLATFORM_KWAISHOP;
        String appName = CommonAppConstants.APP_TRADE;
        UserInfoBo userInfoBo = getUserInfoBo();
        userInfoBo.setPlatformId(platformId);
        userInfoBo.setAppType(appName);
        String redisKey =
            URLEncoder.encode(getDistributeSpecialRedisPrefixMap().get(platformId) + ":" + userInfoBo.getSellerNick());
        UserRedisEntity userRedisEntity = getUserRedisEntity();
        userRedisEntity.setTaobao_user_id(userInfoBo.getSellerId());

        UserInfoResponse userInfoResponse = getUserInfoResponse();
        userInfoResponse.setSellerNick(userRedisEntity.getUser_nick());
        userInfoResponse.setSellerId(userRedisEntity.getUser_id());
        userInfoResponse.setVipflag(Integer.valueOf(userRedisEntity.getVipflag()));
        userInfoResponse.setHasNeedAuth(false);
        userInfoResponse.setAuthDeadLine(userRedisEntity.getW1Deadline());
        userInfoResponse.setOrderCycleEnd(userRedisEntity.getOrderCycleEnd());
        userInfoResponse.setPlatformId(platformId);
        userInfoResponse.setAppName(appName);

        when(platformUserProductInfoService.getUserInfo(eq(new UserBo(userInfoBo)), any(), eq(platformId), eq(appName)))
            .thenReturn(getUserProductInfo());
        when(userManageRedisRepositoryHashRedisRepository.initCollection(eq(userInfoBo.getSellerNick()),
            eq(userInfoBo.getSellerId()), eq(platformId), eq(appName))).thenReturn(redisKey);
        when(userManageRedisRepositoryHashRedisRepository.entries(eq(userInfoBo.getSellerNick()),
            eq(userInfoBo.getSellerId()), eq(platformId), eq(appName))).thenReturn(userRedisEntity);
        when(distributeConfig.getDistributeSpecialRedisPrefixMap()).thenReturn(getDistributeSpecialRedisPrefixMap());

        UserInfoResponse userInfo = sellerService.getUserInfo(userInfoBo, platformId, appName);

        verify(platformUserProductInfoService, never()).getUserInfo(any(), any(), eq(platformId), eq(appName));
        assertEquals(userInfoResponse, userInfo);
    }

    /**
     * 从缓存中获取为NULL，从数据库中获取用户信息，不刷新缓存
     *
     * @throws UserException
     */
    @org.junit.jupiter.api.Test
    public void testGetUserInfo6() throws UserException {
        String platformId = CommonPlatformConstants.PLATFORM_TAO;
        String appName = CommonAppConstants.APP_TRADE;
        UserInfoBo userInfoBo = getUserInfoBo();
        userInfoBo.setPlatformId(platformId);
        userInfoBo.setAppType(appName);
        String redisKey =
            URLEncoder.encode(getDistributeSpecialRedisPrefixMap().get(platformId) + ":" + userInfoBo.getSellerNick());
        UserRedisEntity userRedisEntity = getUserRedisEntity();
        userRedisEntity.setTaobao_user_id(userInfoBo.getSellerId());

        UserInfoResponse userInfoResponse = getUserInfoResponse();
        userInfoResponse.setSellerNick(userRedisEntity.getUser_nick());
        userInfoResponse.setSellerId(userRedisEntity.getUser_id());
        userInfoResponse.setVipflag(Integer.valueOf(userRedisEntity.getVipflag()));
        userInfoResponse.setHasNeedAuth(false);
        userInfoResponse.setAuthDeadLine(userRedisEntity.getW1Deadline());
        userInfoResponse.setPlatformId(platformId);
        userInfoResponse.setAppName(appName);

        UserBo userBo = new UserBo(userInfoBo);
        userBo.setHasReadTag(false);

        when(platformUserProductInfoService.getUserInfo(eq(userBo), any(), eq(platformId), eq(appName)))
            .thenReturn(getUserProductInfo());
        when(userManageRedisRepositoryHashRedisRepository.initCollection(eq(userInfoBo.getSellerNick()),
            eq(userInfoBo.getSellerId()), eq(platformId), eq(appName))).thenReturn(redisKey);
        when(userManageRedisRepositoryHashRedisRepository.entries(eq(userInfoBo.getSellerNick()),
            eq(userInfoBo.getSellerId()), eq(platformId), eq(appName))).thenReturn(null);
        when(distributeConfig.getDistributeSpecialRedisPrefixMap()).thenReturn(getDistributeSpecialRedisPrefixMap());

        UserInfoResponse userInfo = sellerService.getUserInfo(userInfoBo, platformId, appName);

        System.out.println(JSON.toJSONString(userInfo));

        assertEquals(userInfoResponse, userInfo);
    }

    /**
     * 从缓存中获取为NULL，从数据库中获取用户信息，并刷新缓存
     *
     * @throws UserException
     */
    @org.junit.jupiter.api.Test
    public void testGetUserInfo7() throws UserException {
        String platformId = CommonPlatformConstants.PLATFORM_TAO;
        String appName = CommonAppConstants.APP_TRADE;
        UserInfoBo userInfoBo = getUserInfoBo();
        userInfoBo.setPlatformId(platformId);
        userInfoBo.setAppType(appName);
        String redisKey =
            URLEncoder.encode(getDistributeSpecialRedisPrefixMap().get(platformId) + ":" + userInfoBo.getSellerNick());
        UserRedisEntity userRedisEntity = getUserRedisEntity();
        userRedisEntity.setTaobao_user_id(userInfoBo.getSellerId());

        UserProductInfo userProductInfo = getUserProductInfo();
        userProductInfo.setOrderCycleEnd(LocalDateTime.now());
        UserBo userBo = new UserBo(userInfoBo);
        userBo.setHasReadTag(false);

        UserInfoResponse userInfoResponse = getUserInfoResponse();
        userInfoResponse.setSellerNick(userProductInfo.getNick());
        userInfoResponse.setSellerId(userProductInfo.getUserId());
        userInfoResponse.setVipflag(userProductInfo.getVipflag());
        userInfoResponse.setHasNeedAuth(false);
        userInfoResponse.setAuthDeadLine(userProductInfo.getW1Deadline());
        userInfoResponse.setPlatformId(platformId);
        userInfoResponse.setAppName(appName);
        userInfoResponse.setOrderCycleEnd(userProductInfo.getOrderCycleEnd());

        when(platformUserProductInfoService.getUserInfo(eq(userBo), any(), eq(platformId), eq(appName)))
            .thenReturn(userProductInfo);
        when(userManageRedisRepositoryHashRedisRepository.initCollection(eq(userInfoBo.getSellerNick()),
            eq(userInfoBo.getSellerId()), eq(platformId), eq(appName))).thenReturn(redisKey);
        when(userManageRedisRepositoryHashRedisRepository.entries(eq(userInfoBo.getSellerNick()),
            eq(userInfoBo.getSellerId()), eq(platformId), eq(appName))).thenReturn(null);
        when(distributeConfig.getDistributeSpecialRedisPrefixMap()).thenReturn(getDistributeSpecialRedisPrefixMap());

        UserInfoResponse userInfo = sellerService.getUserInfo(userInfoBo, platformId, appName);

        assertEquals(userInfoResponse, userInfo);
    }

    /**
     * 获取用户信息失败（用户不存在）
     *
     * @throws UserException
     */
    @org.junit.jupiter.api.Test
    public void testGetUserInfo8() throws UserException {
        String platformId = CommonPlatformConstants.PLATFORM_TAO;
        String appName = CommonAppConstants.APP_TRADE;
        UserInfoBo userInfoBo = getUserInfoBo();
        userInfoBo.setPlatformId(platformId);
        userInfoBo.setAppType(appName);
        String redisKey =
            URLEncoder.encode(getDistributeSpecialRedisPrefixMap().get(platformId) + ":" + userInfoBo.getSellerNick());
        UserRedisEntity userRedisEntity = getUserRedisEntity();
        userRedisEntity.setTaobao_user_id(userInfoBo.getSellerId());

        UserProductInfo userProductInfo = getUserProductInfo();
        userProductInfo.setOrderCycleEnd(LocalDateTime.now());
        UserBo userBo = new UserBo(userInfoBo);
        userBo.setHasReadTag(false);

        when(platformUserProductInfoService.getUserInfo(eq(userBo), any(), eq(platformId), eq(appName)))
            .thenReturn(null);
        when(userManageRedisRepositoryHashRedisRepository.initCollection(eq(userInfoBo.getSellerNick()),
            eq(userInfoBo.getSellerId()), eq(platformId), eq(appName))).thenReturn(redisKey);
        when(userManageRedisRepositoryHashRedisRepository.entries(eq(userInfoBo.getSellerNick()),
            eq(userInfoBo.getSellerId()), eq(platformId), eq(appName))).thenReturn(null);
        when(distributeConfig.getDistributeSpecialRedisPrefixMap()).thenReturn(getDistributeSpecialRedisPrefixMap());

        UserInfoResponse userInfo = null;
        try {
            userInfo = sellerService.getUserInfo(userInfoBo, platformId, appName);
            fail();
        } catch (UserException e) {
            boolean result = verify(sellerService).checkUserInfo(eq(userInfoBo), eq(false), any(), any());
            assertFalse(result);
        }
    }

    /**
     * 获取用户完整信息
     *
     * @throws Exception
     */
    @org.junit.jupiter.api.Test
    public void testGetUserFullInfo1() throws Exception {
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        UserRedisEntity userRedisEntity = new UserRedisEntity();
        userRedisEntity.setLast_order_cycle_end("2023-12-22 16:05:55");
        userRedisEntity.setLast_w1_deadline("2023-12-22 16:05:55");
        userRedisEntity.setLast_auth_deadline("2023-12-22 16:05:55");
        UserInfoBo userInfoBo = getUserInfoBo();
        UserProductInfo userProductInfo = getUserProductInfo();

        when(platformUserProductInfoService.getUserInfo(eq(new UserBo(userInfoBo)), any(), eq(platformId), eq(appName)))
            .thenReturn(userProductInfo);
        when(userManageRedisRepositoryHashRedisRepository.getLastAuthInfo(eq(userProductInfo.getNick()),
            eq(userProductInfo.getUserId()), eq(platformId), eq(appName))).thenReturn(userRedisEntity);
        when(authService.decryptToken(any(), any(), any())).thenReturn("3333333333333333");

        UserProductInfo userFullInfo = sellerService.getUserFullInfo(userInfoBo, true, platformId, appName);

        verify(authService).decryptToken(eq(getUserProductInfo().getTopsessionkey()), eq(platformId), eq(appName));
        verify(authService).decryptToken(eq(getUserProductInfo().getToprefreshkey()), eq(platformId), eq(appName));
        assertEquals(userProductInfo, userFullInfo);
    }

    /**
     * 获取用户完整信息失败（用户不存在）
     *
     * @throws Exception
     */
    @org.junit.jupiter.api.Test
    public void testGetUserFullInfo2() throws Exception {
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        UserRedisEntity userRedisEntity = new UserRedisEntity();
        userRedisEntity.setLast_order_cycle_end("2023-12-22 16:05:55");
        userRedisEntity.setLast_w1_deadline("2023-12-22 16:05:55");
        userRedisEntity.setLast_auth_deadline("2023-12-22 16:05:55");
        UserInfoBo userInfoBo = getUserInfoBo();
        UserProductInfo userProductInfo = getUserProductInfo();

        when(platformUserProductInfoService.getUserInfo(eq(new UserBo(userInfoBo)), any(), eq(platformId), eq(appName)))
            .thenReturn(null);

        UserProductInfo userFullInfo = null;
        try {
            userFullInfo = sellerService.getUserFullInfo(userInfoBo, true, platformId, appName);
            fail();
        } catch (UserException e) {
            assertEquals(e.getMessage(), ApiCode.NO_EXIST_USER.message());
            assertEquals(e.getCode(), ApiCode.NO_EXIST_USER.code());
            assertNull(userFullInfo);
        }
    }

    /**
     * 获取用户完整信息成功（获取上一次订购信息为空）
     *
     * @throws Exception
     */
    @org.junit.jupiter.api.Test
    public void testGetUserFullInfo3() throws Exception {
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        UserInfoBo userInfoBo = getUserInfoBo();
        UserProductInfo userProductInfo = getUserProductInfo();

        when(platformUserProductInfoService.getUserInfo(eq(new UserBo(userInfoBo)), any(), eq(platformId), eq(appName)))
            .thenReturn(userProductInfo);
        when(userManageRedisRepositoryHashRedisRepository.getLastAuthInfo(eq(userProductInfo.getNick()),
            eq(userProductInfo.getUserId()), eq(platformId), eq(appName))).thenReturn(null);
        when(authService.decryptToken(any(), any(), any())).thenReturn("3333333333333333");

        UserProductInfo userFullInfo = sellerService.getUserFullInfo(userInfoBo, true, platformId, appName);

        verify(authService).decryptToken(eq(getUserProductInfo().getTopsessionkey()), eq(platformId), eq(appName));
        verify(authService).decryptToken(eq(getUserProductInfo().getToprefreshkey()), eq(platformId), eq(appName));
        assertEquals(userProductInfo, userFullInfo);
    }

    /**
     * 获取token(授权过期, 尝试使用refresh_token刷新成功)
     *
     * @throws Exception
     */
    @org.junit.jupiter.api.Test
    public void testGetAccessToken1() throws Exception {
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        UserInfoBo userInfoBo = getUserInfoBo();
        userInfoBo.setAccessToken("******************************");
        userInfoBo.setRefreshToken("**********************************");

        UserInfoResponse response = new UserInfoResponse();
        response.setSellerId(userInfoBo.getSellerId());
        response.setSellerNick(userInfoBo.getSellerNick());
        response.setHasNeedAuth(false);
        response.setTopSession("55555555555555555555");
        response.setPlatformId(platformId);
        response.setAppName(appName);

        doReturn(true).when(sellerService).checkUserInfo(eq(userInfoBo), eq(false), any(), any());
        doReturn(true).when(sellerService).accessTokenExpireValidator(eq(userInfoBo));
        doReturn("55555555555555555555").when(sellerService).refreshAccessToken(eq(userInfoBo),
            eq(userInfoBo.getRefreshToken()), eq(platformId), eq(appName));
        when(authService.decryptToken(eq(userInfoBo.getSellerAppSecret()), eq(platformId), eq(appName)))
            .thenReturn("222222222222222");
        UserInfoResponse accessToken = sellerService.getAccessToken(userInfoBo, platformId, appName);
        assertEquals(response, accessToken);
    }

    /**
     * 获取token
     *
     * @throws Exception
     */
    @org.junit.jupiter.api.Test
    public void testGetAccessToken2() throws Exception {
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        UserInfoBo userInfoBo = getUserInfoBo();
        userInfoBo.setAccessToken("******************************");
        userInfoBo.setRefreshToken("**********************************");

        UserInfoResponse response = new UserInfoResponse();
        response.setSellerId(userInfoBo.getSellerId());
        response.setSellerNick(userInfoBo.getSellerNick());
        response.setHasNeedAuth(false);
        response.setTopSession("222222222222222");
        response.setPlatformId(platformId);
        response.setAppName(appName);

        doReturn(true).when(sellerService).checkUserInfo(eq(userInfoBo), eq(false), any(), any());
        doReturn(false).when(sellerService).accessTokenExpireValidator(eq(userInfoBo));
        doReturn("55555555555555555555").when(sellerService).refreshAccessToken(eq(userInfoBo),
            eq(userInfoBo.getRefreshToken()), eq(platformId), eq(appName));
        when(authService.decryptToken(eq(userInfoBo.getAccessToken()), eq(platformId), eq(appName)))
            .thenReturn("222222222222222");
        UserInfoResponse accessToken = sellerService.getAccessToken(userInfoBo, platformId, appName);

        System.out.println(JSON.toJSONString(accessToken));
        System.out.println(JSON.toJSONString(response));
        assertEquals(response, accessToken);
    }

    /**
     * 获取token失败（用户信息不存在）
     *
     * @throws Exception
     */
    @org.junit.jupiter.api.Test
    public void testGetAccessToken3() throws Exception {
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        UserInfoBo userInfoBo = getUserInfoBo();
        userInfoBo.setAccessToken("******************************");
        userInfoBo.setRefreshToken("**********************************");

        doReturn(false).when(sellerService).checkUserInfo(eq(userInfoBo), eq(false), any(), any());
        doReturn(true).when(sellerService).accessTokenExpireValidator(eq(userInfoBo));
        doReturn("55555555555555555555").when(sellerService).refreshAccessToken(eq(userInfoBo),
            eq(userInfoBo.getRefreshToken()), eq(platformId), eq(appName));
        when(authService.decryptToken(eq(userInfoBo.getSellerAppSecret()), eq(platformId), eq(appName)))
            .thenReturn("222222222222222");
        UserInfoResponse accessToken = null;
        try {
            accessToken = sellerService.getAccessToken(userInfoBo, platformId, appName);
        } catch (UserException e) {
            assertEquals(e.getCode(), ApiCode.NO_EXIST_USER.code());
            assertEquals(e.getMessage(), ApiCode.NO_EXIST_USER.message());
        }
        assertNull(accessToken);
    }

    /**
     * 获取token（授权时间为空, 并且订购时间未过期, 兼容使用老的accessToken）
     *
     * @throws Exception
     */
    @org.junit.jupiter.api.Test
    public void testGetAccessToken4() throws Exception {
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        UserInfoBo userInfoBo = getUserInfoBo();
        userInfoBo.setAccessToken("******************************");
        userInfoBo.setOrderCycleEnd(LocalDateTime.now().plusDays(2));

        UserInfoResponse response = new UserInfoResponse();
        response.setSellerId(userInfoBo.getSellerId());
        response.setSellerNick(userInfoBo.getSellerNick());
        response.setHasNeedAuth(true);
        response.setPlatformId(platformId);
        response.setAppName(appName);
        response.setTopSession("222222222222222");

        doReturn(true).when(sellerService).checkUserInfo(eq(userInfoBo), eq(false), any(), any());
        doReturn(true).when(sellerService).accessTokenExpireValidator(eq(userInfoBo));
        doReturn("55555555555555555555").when(sellerService).refreshAccessToken(eq(userInfoBo),
            eq(userInfoBo.getRefreshToken()), eq(platformId), eq(appName));
        when(authService.decryptToken(eq(userInfoBo.getAccessToken()), eq(platformId), eq(appName)))
            .thenReturn("222222222222222");
        UserInfoResponse accessToken = sellerService.getAccessToken(userInfoBo, platformId, appName);

        assertEquals(response, accessToken);
    }

    /**
     * 刷新tocken
     */
    @org.junit.jupiter.api.Test
    public void testRefreshAccessToken() {
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        String refreshToken = "2222222222222";

        UserInfoBo userInfoBo = new UserInfoBo();
        userInfoBo.setSellerNick("gh_4ebc6c1c889a");
        userInfoBo.setSellerId("wxc37ce2922b49c0b8");
        userInfoBo.setAppType(platformId);
        userInfoBo.setPlatformId(appName);

        CallbackResponse response = new CallbackResponse();
        response.setAccessToken("111111111111111111111");
        response.setSellerNick("gh_4ebc6c1c889a");
        when(oAuthDecorationService.refreshAccessToken(eq(userInfoBo), eq(refreshToken), eq(platformId), eq(appName)))
            .thenReturn(response);
        String accessToken = sellerService.refreshAccessToken(userInfoBo, refreshToken, platformId, appName);
        assertEquals(response.getAccessToken(), accessToken);
    }

    @org.junit.jupiter.api.Test
    public void testGetUserInfoByDb() {
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        UserBo userBo = getUserBo();
        UserProductInfo userProductInfo = getUserProductInfo();
        when(platformUserProductInfoService.getUserInfo(eq(userBo), any(), eq(platformId), eq(appName)))
            .thenReturn(userProductInfo);
        UserProductInfo userInfoByDb = sellerService.getUserInfoByDb(userBo, platformId, appName);

        assertEquals(userProductInfo, userInfoByDb);
    }

    @org.junit.jupiter.api.Test
    public void testGetUserInfoByCache() {
        String sellerNick = "gh_4ebc6c1c889a";
        String sellerId = "wxc37ce2922b49c0b8";
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        UserRedisEntity userRedisEntity = getUserRedisEntity();
        when(userManageRedisRepositoryHashRedisRepository.entries(eq(sellerNick), eq(sellerId), eq(platformId),
            eq(appName))).thenReturn(getUserRedisEntity());
        UserRedisEntity userInfoByCache = sellerService.getUserInfoByCache(null, sellerNick, sellerId,
            userManageRedisRepositoryHashRedisRepository, platformId, appName);

        assertEquals(userRedisEntity, userInfoByCache);
    }

    @org.junit.jupiter.api.Test
    public void testRefreshUserInfoCache() throws CacheWriteException {
        when(userManageRedisRepositoryHashRedisRepository.putUserData(eq(getUserBo()))).thenReturn(true);
        sellerService.refreshUserInfoCache(getUserBo(), userManageRedisRepositoryHashRedisRepository);
    }

    /**
     * 刷新数据库及缓存用户信息成功
     *
     * @throws DbWriteException
     * @throws CacheWriteException
     */
    @org.junit.jupiter.api.Test
    public void testRefreshUserInfoCacheAndTable1() throws DbWriteException, CacheWriteException {
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        when(userManageRedisRepositoryHashRedisRepository.putUserData(eq(getUserBo()))).thenReturn(true);
        when(userRepository.update(eq(getUserBo().getUserProductInfo()), eq(getUserBo().getPlatformId()),
            eq(getUserBo().getAppType()))).thenReturn(1);
        sellerService.refreshUserInfoCacheAndTable(getUserBo(), platformId, appName);
    }

    /**
     * 刷新数据库及缓存用户信息失败（数据库入库失败）
     *
     * @throws DbWriteException
     * @throws CacheWriteException
     */
    @org.junit.jupiter.api.Test
    public void testRefreshUserInfoCacheAndTable2() throws Exception {
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        UserBo userBo = getUserBo();
        when(userManageRedisRepositoryHashRedisRepository.putUserData(eq(userBo))).thenReturn(true);
        when(
            userRepository.update(eq(userBo.getUserProductInfo()), eq(userBo.getPlatformId()), eq(userBo.getAppType())))
                .thenThrow(new RuntimeException());
        try {
            sellerService.refreshUserInfoCacheAndTable(userBo, platformId, appName);
            fail();
        } catch (DbWriteException | CacheWriteException e) {
            System.out.println(e.getMessage());
            assertEquals(e.getCode(), ErrorCode.BaseCode.SYS_DB_ERR.getCode().intValue());
        }
    }

    /**
     * 刷新数据库及缓存用户信息失败（数据库入库成功， 缓存第一次失败第二次成功）
     *
     * @throws DbWriteException
     * @throws CacheWriteException
     */
    @org.junit.jupiter.api.Test
    public void testRefreshUserInfoCacheAndTable3() throws Exception {
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        UserBo userBo = getUserBo();
        when(userManageRedisRepositoryHashRedisRepository.putUserData(eq(userBo))).thenThrow(new RuntimeException())
            .thenReturn(true);
        when(
            userRepository.update(eq(userBo.getUserProductInfo()), eq(userBo.getPlatformId()), eq(userBo.getAppType())))
                .thenReturn(1);
        try {
            sellerService.refreshUserInfoCacheAndTable(userBo, platformId, appName);
            fail();
        } catch (DbWriteException | CacheWriteException e) {
            System.out.println(e.getMessage());
            assertEquals(e.getCode(), ErrorCode.BaseCode.SYS_CACHE_ERR.getCode().intValue());
        }
    }

    /**
     * 检验用户信息成功（redisKey初始化失败，数据库获取用户信息成功）
     */
    @org.junit.jupiter.api.Test
    public void testCheckUserInfo1() {
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        UserInfoBo userInfoBo = getUserInfoBo();
        UserBo userBo = new UserBo(userInfoBo);
        userBo.setHasReadTag(false);

        when(userManageRedisRepositoryHashRedisRepository.initCollection(eq(userInfoBo.getSellerNick()),
            eq(userInfoBo.getSellerId()), eq(platformId), eq(appName))).thenReturn(null);
        when(userManageRedisRepositoryHashRedisRepository.entries(any(), any(), any(), any()))
            .thenReturn(getUserRedisEntity());
        when(platformUserProductInfoService.getUserInfo(eq(userBo), any(), eq(platformId), eq(appName)))
            .thenReturn(getUserProductInfo());
        boolean b = sellerService.checkUserInfo(userInfoBo, false, userManageRedisRepositoryHashRedisRepository,
            userRepository);

        verify(userManageRedisRepositoryHashRedisRepository, never()).entries(any(), any(), any(), any());
        assertTrue(b);
    }

    /**
     * 检验用户信息成功（redisKey初始化失败，数据库获取用户信息不存在）
     */
    @org.junit.jupiter.api.Test
    public void testCheckUserInfo2() {
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        UserInfoBo userInfoBo = getUserInfoBo();
        UserBo userBo = new UserBo(userInfoBo);
        userBo.setHasReadTag(false);

        when(userManageRedisRepositoryHashRedisRepository.initCollection(eq(userInfoBo.getSellerNick()),
            eq(userInfoBo.getSellerId()), eq(platformId), eq(appName))).thenReturn(null);
        when(userManageRedisRepositoryHashRedisRepository.entries(any(), any(), any(), any()))
            .thenReturn(getUserRedisEntity());
        when(platformUserProductInfoService.getUserInfo(eq(userBo), any(), eq(platformId), eq(appName)))
            .thenReturn(null);
        boolean b = sellerService.checkUserInfo(userInfoBo, false, userManageRedisRepositoryHashRedisRepository,
            userRepository);

        verify(userManageRedisRepositoryHashRedisRepository, never()).entries(any(), any(), any(), any());
        assertFalse(b);
    }

    /**
     * 检验用户信息成功（代发应用redisKey初始化成功，缓存获取用户信息成功）
     */
    @org.junit.jupiter.api.Test
    public void testCheckUserInfo3() {
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        UserInfoBo userInfoBo = getUserInfoBo();
        when(userManageRedisRepositoryHashRedisRepository.initCollection(eq(userInfoBo.getSellerNick()),
            eq(userInfoBo.getSellerId()), eq(platformId), eq(appName)))
                .thenReturn(URLEncoder
                    .encode(getDistributeSpecialRedisPrefixMap().get(platformId) + ":" + userInfoBo.getSellerNick()));
        when(userManageRedisRepositoryHashRedisRepository.entries(eq(userInfoBo.getSellerNick()),
            eq(userInfoBo.getSellerId()), eq(platformId), eq(appName))).thenReturn(getUserRedisEntity());
        boolean b = sellerService.checkUserInfo(userInfoBo, false, userManageRedisRepositoryHashRedisRepository,
            userRepository);

        verify(platformUserProductInfoService, never()).getUserInfo(any(), any(), eq(platformId), eq(appName));
        assertTrue(b);
    }

    /**
     * 检验用户信息成功（淘宝交易redisKey初始化成功，缓存获取用户信息成功）
     */
    @org.junit.jupiter.api.Test
    public void testCheckUserInfo4() {
        String platformId = CommonPlatformConstants.PLATFORM_TAO;
        String appName = CommonAppConstants.APP_TRADE;
        UserInfoBo userInfoBo = getUserInfoBo();
        userInfoBo.setPlatformId(platformId);
        userInfoBo.setAppType(appName);
        String redisKey =
            URLEncoder.encode(getDistributeSpecialRedisPrefixMap().get(platformId) + ":" + userInfoBo.getSellerNick());
        UserRedisEntity userRedisEntity = getUserRedisEntity();
        userRedisEntity.setTaobao_user_id(userInfoBo.getSellerId());
        userRedisEntity.setTrade_access_token_m("11111111111111111");
        userRedisEntity.setTrade_refresh_token_m("22222222222222222");

        UserInfoResponse userInfoResponse = getUserInfoResponse();
        userInfoResponse.setSellerNick(userRedisEntity.getUser_nick());
        userInfoResponse.setSellerId(userRedisEntity.getUser_id());
        userInfoResponse.setVipflag(Integer.valueOf(userRedisEntity.getVipflag()));
        userInfoResponse.setHasNeedAuth(false);
        userInfoResponse.setAuthDeadLine(userRedisEntity.getW1Deadline());
        userInfoResponse.setOrderCycleEnd(userRedisEntity.getOrderCycleEnd());
        userInfoResponse.setPlatformId(platformId);
        userInfoResponse.setAppName(appName);

        when(platformUserProductInfoService.getUserInfo(eq(new UserBo(userInfoBo)), any(), eq(platformId), eq(appName)))
            .thenReturn(getUserProductInfo());
        when(userManageRedisRepositoryHashRedisRepository.initCollection(eq(userInfoBo.getSellerNick()),
            eq(userInfoBo.getSellerId()), eq(platformId), eq(appName))).thenReturn(redisKey);
        when(userManageRedisRepositoryHashRedisRepository.entries(eq(userInfoBo.getSellerNick()),
            eq(userInfoBo.getSellerId()), eq(platformId), eq(appName))).thenReturn(userRedisEntity);
        when(distributeConfig.getDistributeSpecialRedisPrefixMap()).thenReturn(getDistributeSpecialRedisPrefixMap());

        boolean b = sellerService.checkUserInfo(userInfoBo, false, userManageRedisRepositoryHashRedisRepository,
            userRepository);

        verify(platformUserProductInfoService, never()).getUserInfo(any(), any(), eq(platformId), eq(appName));
        assertTrue(b);
    }

    /**
     * 检验用户信息成功（非代发、淘宝交易，redisKey初始化成功，缓存获取用户信息成功）
     */
    @org.junit.jupiter.api.Test
    public void testCheckUserInfo5() {
        String platformId = CommonPlatformConstants.PLATFORM_KWAISHOP;
        String appName = CommonAppConstants.APP_TRADE;
        UserInfoBo userInfoBo = getUserInfoBo();
        userInfoBo.setPlatformId(platformId);
        userInfoBo.setAppType(appName);
        String redisKey =
            URLEncoder.encode(getDistributeSpecialRedisPrefixMap().get(platformId) + ":" + userInfoBo.getSellerNick());
        UserRedisEntity userRedisEntity = getUserRedisEntity();
        userRedisEntity.setTaobao_user_id(userInfoBo.getSellerId());

        UserInfoResponse userInfoResponse = getUserInfoResponse();
        userInfoResponse.setSellerNick(userRedisEntity.getUser_nick());
        userInfoResponse.setSellerId(userRedisEntity.getUser_id());
        userInfoResponse.setVipflag(Integer.valueOf(userRedisEntity.getVipflag()));
        userInfoResponse.setHasNeedAuth(false);
        userInfoResponse.setAuthDeadLine(userRedisEntity.getW1Deadline());
        userInfoResponse.setOrderCycleEnd(userRedisEntity.getOrderCycleEnd());
        userInfoResponse.setPlatformId(platformId);
        userInfoResponse.setAppName(appName);

        when(platformUserProductInfoService.getUserInfo(eq(new UserBo(userInfoBo)), any(), eq(platformId), eq(appName)))
            .thenReturn(getUserProductInfo());
        when(userManageRedisRepositoryHashRedisRepository.initCollection(eq(userInfoBo.getSellerNick()),
            eq(userInfoBo.getSellerId()), eq(platformId), eq(appName))).thenReturn(redisKey);
        when(userManageRedisRepositoryHashRedisRepository.entries(eq(userInfoBo.getSellerNick()),
            eq(userInfoBo.getSellerId()), eq(platformId), eq(appName))).thenReturn(userRedisEntity);
        when(distributeConfig.getDistributeSpecialRedisPrefixMap()).thenReturn(getDistributeSpecialRedisPrefixMap());

        boolean b = sellerService.checkUserInfo(userInfoBo, false, userManageRedisRepositoryHashRedisRepository,
            userRepository);

        verify(platformUserProductInfoService, never()).getUserInfo(any(), any(), eq(platformId), eq(appName));
        assertTrue(b);
    }

    /**
     * 检验用户信息成功（redisKey初始化成功，缓存中获取用户信息失败，从数据库获取用户信息并刷新缓存）
     */
    @org.junit.jupiter.api.Test
    public void testCheckUserInfo6() {
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        UserInfoBo userInfoBo = getUserInfoBo();
        UserBo userBo = new UserBo(userInfoBo);
        userBo.setHasReadTag(false);
        when(userManageRedisRepositoryHashRedisRepository.initCollection(eq(userInfoBo.getSellerNick()),
            eq(userInfoBo.getSellerId()), eq(platformId), eq(appName)))
                .thenReturn(URLEncoder
                    .encode(getDistributeSpecialRedisPrefixMap().get(platformId) + ":" + userInfoBo.getSellerNick()));
        when(userManageRedisRepositoryHashRedisRepository.entries(eq(userInfoBo.getSellerNick()),
            eq(userInfoBo.getSellerId()), eq(platformId), eq(appName))).thenReturn(null);
        when(platformUserProductInfoService.getUserInfo(eq(userBo), any(), eq(platformId), eq(appName)))
            .thenReturn(getUserProductInfo());
        boolean b = sellerService.checkUserInfo(userInfoBo, false, userManageRedisRepositoryHashRedisRepository,
            userRepository);

        assertTrue(b);
    }

    public UserInfoBo getUserInfoBo() {
        UserInfoBo userInfoBo = new UserInfoBo();
        userInfoBo.setSellerNick("gh_4ebc6c1c889a");
        userInfoBo.setSellerId("wxc37ce2922b49c0b8");
        userInfoBo.setAppType(CommonAppConstants.APP_DISTRIBUTE);
        userInfoBo.setPlatformId(CommonPlatformConstants.PLATFORM_WXVIDEOSHOP);
        return userInfoBo;
    }

    public UserInfoResponse getUserInfoResponse() {
        UserInfoResponse userInfoResponse = new UserInfoResponse();
        userInfoResponse.setVipflag(1);
        userInfoResponse.setSellerNick("gh_4ebc6c1c889a");
        userInfoResponse.setSellerId("wxc37ce2922b49c0b8");
        userInfoResponse.setAppName(CommonAppConstants.APP_DISTRIBUTE);
        userInfoResponse.setPlatformId(CommonPlatformConstants.PLATFORM_WXVIDEOSHOP);
        userInfoResponse.setAuthDeadLine(LocalDateTime.now());
        return userInfoResponse;
    }

    public UserProductInfo getUserProductInfo() {
        UserProductInfo userProductInfo = new UserProductInfo();
        userProductInfo.setUserId("wxc37ce2922b49c0b8");
        userProductInfo.setNick("gh_4ebc6c1c889a");
        userProductInfo.setTopsessionkey("111111111111111111111111");
        userProductInfo.setToprefreshkey("222222222222222222222222");
        userProductInfo.setW1Deadline(DateUtil.parseString("2023-12-22 16:05:55"));
        userProductInfo.setLastOrderCycleEnd(DateUtil.parseString("2023-12-22 16:05:55"));
        userProductInfo.setLastAuthDeadLine(DateUtil.parseString("2023-12-22 16:05:55"));
        userProductInfo.setVipflag(1);
        return userProductInfo;
    }

    public Map<String, String> getDistributeSpecialRedisPrefixMap() {
        HashMap<String, String> map = new HashMap<>();
        map.put("DOUDIAN", "dy");
        map.put("KWAISHOP", "ks");
        map.put("YOUZAN", "yz");
        map.put("WXVIDEOSHOP", "videoShop");
        return map;
    }

    public UserRedisEntity getUserRedisEntity() {
        UserRedisEntity userRedisEntity = new UserRedisEntity();
        userRedisEntity.setUser_id("wxc37ce2922b49c0b8");
        userRedisEntity.setUser_nick("gh_4ebc6c1c889a");
        userRedisEntity.setVipflag("1");
        userRedisEntity.setAccess_token("12321");
        userRedisEntity.setOrder_cycle_end("2023-12-22 16:05:55");
        userRedisEntity.setW1_deadline("2023-12-22 16:05:55");
        userRedisEntity.setLast_auth_deadline("2023-12-22 16:05:55");
        userRedisEntity.setAuth_dead_line("2023-12-22 16:05:55");
        return userRedisEntity;
    }

    private UserBo getUserBo() {
        UserBo userBo = new UserBo();
        userBo.setHasReadTag(Boolean.TRUE);
        userBo.setPlatformId(CommonPlatformConstants.PLATFORM_WXVIDEOSHOP);
        userBo.setAppType(CommonAppConstants.APP_DISTRIBUTE);
        userBo.setSellerNick("gh_4ebc6c1c889a");
        userBo.setDecryptAccessToken("wxc37ce2922b49c0b8");
        return userBo;
    }

}
