package cn.loveapp.uac.service.service.impl;

import cn.loveapp.uac.common.api.domain.SellerArticleUserSubscribe;
import cn.loveapp.uac.common.api.response.SellerVasSubscribeGetResponse;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.code.ErrorCode;
import cn.loveapp.uac.common.config.app.ArticleCodeConfig;
import cn.loveapp.uac.common.dao.redis.repository.OperationManageRedisRepository;
import cn.loveapp.uac.common.platform.api.AppStoreService;
import cn.loveapp.uac.common.platform.api.AuthService;
import cn.loveapp.uac.common.service.PlatformFuwuItemCodeService;
import cn.loveapp.uac.common.utils.DateUtil;
import cn.loveapp.uac.common.utils.RocketMqQueueHelper;
import cn.loveapp.uac.db.common.repository.UserRepository;
import cn.loveapp.uac.service.base.BaseOperationService.OperationUserInfo;
import cn.loveapp.uac.service.event.SubscribeUserMessageEventHandler;
import cn.loveapp.uac.service.service.ActivityService;
import cn.loveapp.uac.service.service.SellerOrderSearchService;
import cn.loveapp.uac.service.service.SellerService;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.junit.jupiter.api.Assertions;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;

import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * @Created by: IntelliJ IDEA.
 * @description:
 * @authr: jason
 * @date: 2020/3/25
 * @time: 1:36 PM
 */
@SpringBootTest(webEnvironment = WebEnvironment.NONE,
	classes = {OperationServiceImpl.class, StringRedisTemplate.class,
		SellerService.class, UserRepository.class, DefaultMQProducer.class, RocketMqQueueHelper.class,
		AuthService.class
	})
public class OperationServiceImplTest {

	@MockitoSpyBean
	private OperationServiceImpl operationService;

	@MockitoBean
	private StringRedisTemplate stringRedisTemplate;
	@MockitoBean
	private ArticleCodeConfig articleCodeConfig;
	@MockitoBean
	private PlatformFuwuItemCodeService platformFuwuItemCodeService;
	@MockitoBean
	private ActivityService activityService;
	@MockitoBean
	private DefaultMQProducer producer;
	@MockitoBean
	private RocketMqQueueHelper rocketMqQueueHelper;
	@MockitoBean
	private AppStoreService appStoreFuWuService;
	@MockitoBean
	private OperationManageRedisRepository operationManageRedisRepository;
	@MockitoBean
	private SubscribeUserMessageEventHandler subscribeUserMessageEventHandler;
	@MockitoBean
	private SellerOrderSearchService sellerOrderSearchService;

	@org.junit.jupiter.api.Test
	public void calculateLevelTest1() throws Exception {
		when(appStoreFuWuService.vasSubscribeGet(any(), any(), any())).thenReturn(getSellerVasSubscribeGetErrResponse());
		when(activityService.calculatePromotionActivityLevelAndCycleEndTime(any(), any(), any(), any(), any())).thenReturn(
			Pair.of(1, DateUtil.parseString("2020-03-25 13:58:12")));
		OperationUserInfo valueCapture = new OperationUserInfo();
		UserInfoBo userInfoBo = getUserInfoBo();
		operationService.calculateLevel(userInfoBo, valueCapture, userInfoBo.getPlatformId(), userInfoBo.getAppType());
		Assertions.assertEquals(Integer.valueOf(1), valueCapture.getLevel());
	}

	@org.junit.jupiter.api.Test
	public void calculateLevelTest2() throws Exception {
		when(appStoreFuWuService.vasSubscribeGet(any(), any(), any())).thenReturn(getSellerVasSubscribeGetResponse());
		when(activityService.calculatePromotionActivityLevelAndCycleEndTime(any(), any(), any(), any(), any())).thenReturn(
			Pair.of(1, DateUtil.parseString("2020-03-25 13:58:12")));
		OperationUserInfo valueCapture = new OperationUserInfo();
		UserInfoBo userInfoBo = getUserInfoBo();
		operationService.calculateLevel(userInfoBo, valueCapture, userInfoBo.getPlatformId(), userInfoBo.getAppType());
		Assertions.assertEquals(Integer.valueOf(1), valueCapture.getLevel());
	}

	@org.junit.jupiter.api.Test
	public void calculateLevelTest3() throws Exception {
		when(appStoreFuWuService.vasSubscribeGet(any(), any(), any())).thenReturn(getSellerVasSubscribeGetResponse());
		when(activityService.calculatePromotionActivityLevelAndCycleEndTime(any(), any(), any(), any(), any())).thenReturn(
			Pair.of(1, DateUtil.parseString("2020-03-25 13:58:12")));
		OperationUserInfo valueCapture = new OperationUserInfo();
		UserInfoBo userInfoBo = getUserInfoBo();
		operationService.calculateLevel(userInfoBo, valueCapture, userInfoBo.getPlatformId(), userInfoBo.getAppType());
		Assertions.assertEquals(Integer.valueOf(1), valueCapture.getLevel());
	}

	private UserInfoBo getUserInfoBo() {
		UserInfoBo userInfoBo = new UserInfoBo();
		userInfoBo.setSellerNick("赵东昊的测试店铺");
		userInfoBo.setSellerId("123123");
		userInfoBo.setPlatformId("TAO");
		userInfoBo.setAppType("trade");
		userInfoBo.setLevel(1);
		userInfoBo.setOriginalIdentityLevel(1);
		userInfoBo.setOrderCycleEnd(DateUtil.parseString("2020-03-25 13:58:12"));
		userInfoBo.setOriginalIdentityOrderCycleEnd(DateUtil.parseString("2020-03-25 13:58:12"));
		return userInfoBo;
	}

	private SellerVasSubscribeGetResponse getSellerVasSubscribeGetResponse() {
		SellerArticleUserSubscribe sellerArticleUserSubscribe = new SellerArticleUserSubscribe();
		sellerArticleUserSubscribe.setItemCode("v2");
		sellerArticleUserSubscribe.setDeadline(DateUtil.convertLocalDateTimetoDate(DateUtil.currentDate(true)));
		SellerVasSubscribeGetResponse sellerVasSubscribeGetResponse = new SellerVasSubscribeGetResponse();
		sellerVasSubscribeGetResponse.setArticleUserSubscribes(Collections.singletonList(sellerArticleUserSubscribe));
		return sellerVasSubscribeGetResponse;
	}

	private SellerVasSubscribeGetResponse getSellerVasSubscribeGetErrResponse() {
		SellerVasSubscribeGetResponse sellerVasSubscribeGetResponse = new SellerVasSubscribeGetResponse();
		sellerVasSubscribeGetResponse.setErrorCode(ErrorCode.BaseCode.REQUEST_ERR.getCode().toString());
		return sellerVasSubscribeGetResponse;
	}
}
