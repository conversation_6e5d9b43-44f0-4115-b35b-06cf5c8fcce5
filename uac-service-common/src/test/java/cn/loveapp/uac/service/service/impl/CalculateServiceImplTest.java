package cn.loveapp.uac.service.service.impl;

import cn.loveapp.uac.common.bo.CalculateBo;
import cn.loveapp.uac.common.entity.PromotionActivity;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.common.utils.DateUtil;
import cn.loveapp.uac.db.common.entity.OrderSearch;
import cn.loveapp.uac.db.common.repository.OrderSearchRepository;
import cn.loveapp.uac.db.common.repository.PromotionActivityRepository;
import cn.loveapp.uac.service.service.impl.CalculateServiceImpl.SellerLevelCycleEndTimeTemporary;
import org.junit.jupiter.api.Assertions;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * @Created by: IntelliJ IDEA.
 * @description:
 * @authr: jason
 * @date: 2020/3/24
 * @time: 5:01 PM
 */
@SpringBootTest(webEnvironment = WebEnvironment.NONE,
	classes = {CalculateServiceImpl.class, StringRedisTemplate.class,
		OrderSearchRepository.class, PromotionActivityRepository.class
	})
public class CalculateServiceImplTest {

	@MockitoSpyBean
	private CalculateServiceImpl calculateService;

	@MockitoSpyBean
	private OrderSearchRepository orderSearchRepository;
	@MockitoSpyBean
	private PromotionActivityRepository promotionActivityRepository;
	@MockitoSpyBean
	private StringRedisTemplate stringRedisTemplate;

	private final String platformId = "TAO";
	private final String appName = "trade";

	@org.junit.jupiter.api.Test
	public void calculateAdvancedSellerTest1() {
		CalculateBo calculateBo = new CalculateBo();
		calculateBo.setPromotionActivity(getPromotionActivity());
		calculateBo.setSellerNick("赵东昊的测试店铺");
		calculateBo.setLevel(1);
		calculateBo.setCycleEndTime(DateUtil.parseString("2020-03-24 16:22:45"));
		calculateBo.setPromotionItemCode("FW_GOODS-1827490-v2");
		when(orderSearchRepository.queryBySellerNickAndOrderCycleStartAndOrderCycleEndAndItemCode(any(), any(), any(), any(), any(), any(), any())).thenReturn(null);
		SellerLevelCycleEndTimeTemporary temporary = calculateService.calculateAdvancedSeller(calculateBo, platformId, appName);
		Assertions.assertEquals(Objects.requireNonNull(DateUtil.parseString("2020-03-24 16:22:45")).plusDays(11), temporary.getOrderCycleEndTime());
	}

	@org.junit.jupiter.api.Test
	public void calculateAdvancedSellerTest2() {
		CalculateBo calculateBo = new CalculateBo();
		calculateBo.setPromotionActivity(getPromotionActivity());
		calculateBo.setSellerNick("赵东昊的测试店铺");
		calculateBo.setLevel(1);
		calculateBo.setCycleEndTime(DateUtil.parseString("2020-03-24 16:22:45"));
		calculateBo.setPromotionItemCode("FW_GOODS-1827490-v2");
		when(orderSearchRepository.queryBySellerNickAndOrderCycleStartAndOrderCycleEndAndItemCode(any(), any(), any(), any(), any(), any(), any())).thenReturn(getTaobaoOrderSearch());
		SellerLevelCycleEndTimeTemporary temporary = calculateService.calculateAdvancedSeller(calculateBo, platformId, appName);
        Assertions.assertEquals(Objects.requireNonNull(DateUtil.parseString("2020-03-24 16:22:45")).plusDays(11), temporary.getOrderCycleEndTime());
	}

	@org.junit.jupiter.api.Test
	public void calculateAdvancedSellerTest3() {
		PromotionActivity promotionActivity = getPromotionActivity();
		promotionActivity.setOptime(DateUtil.parseString("2020-04-31 14:22:45"));
		CalculateBo calculateBo = new CalculateBo();
		calculateBo.setPromotionActivity(promotionActivity);
		calculateBo.setSellerNick("赵东昊的测试店铺");
		calculateBo.setLevel(1);
		calculateBo.setCycleEndTime(DateUtil.parseString("2020-03-24 16:22:45"));
		calculateBo.setPromotionItemCode("FW_GOODS-1827490-v2");
		when(orderSearchRepository.queryBySellerNickAndOrderCycleStartAndOrderCycleEndAndItemCode(any(), any(), any(), any(), any(), any(), any())).thenReturn(getTaobaoOrderSearch());
		when(orderSearchRepository.queryBySellerNickAndItemCodeAndIdSortIdDesc(any(), any(), any(), any(), any())).thenReturn(null);
		//order_cycle_start:"2020-03-24 17:21:54" optime:"2020-03-30 14:22:45"
		SellerLevelCycleEndTimeTemporary temporary = calculateService.calculateAdvancedSeller(calculateBo, platformId, appName);
        Assertions.assertEquals(Boolean.TRUE, temporary.getIsAllUsed());
	}

	@org.junit.jupiter.api.Test
	public void calculateAdvancedSellerTest4() {
		PromotionActivity promotionActivity = getPromotionActivity();
		promotionActivity.setOptime(DateUtil.parseString("2020-03-04 14:22:45"));
		CalculateBo calculateBo = new CalculateBo();
		calculateBo.setPromotionActivity(promotionActivity);
		calculateBo.setSellerNick("赵东昊的测试店铺");
		calculateBo.setLevel(1);
		calculateBo.setCycleEndTime(DateUtil.parseString("2020-03-24 16:22:45"));
		calculateBo.setPromotionItemCode("FW_GOODS-1827490-v2");
		when(orderSearchRepository.queryBySellerNickAndOrderCycleStartAndOrderCycleEndAndItemCode(any(), any(), any(), any(), any(), any(), any())).thenReturn(getTaobaoOrderSearch());
		when(orderSearchRepository.queryBySellerNickAndItemCodeAndIdSortIdDesc(any(), any(), any(), any(), any())).thenReturn(null);
		//order_cycle_start:"2020-03-24 17:21:54" optime:"2020-03-04 14:22:45"
		when(promotionActivityRepository .queryByIsUsedAndSellerNickSortOptime(any(), any(), eq(platformId), eq(appName))).thenReturn(null);
		SellerLevelCycleEndTimeTemporary temporary = calculateService.calculateAdvancedSeller(calculateBo, platformId, appName);
		List<Integer> useUpIds = new ArrayList<>(Collections.emptyList());
        Assertions.assertEquals(Boolean.TRUE, temporary.getReAggregationCalculate());
        Assertions.assertEquals(useUpIds, temporary.getUseUpIds());
	}

	@org.junit.jupiter.api.Test
	public void calculateAdvancedSellerTest5() {
		PromotionActivity promotionActivity = getPromotionActivity();
		promotionActivity.setOptime(DateUtil.parseString("2020-04-04 14:22:45"));
		CalculateBo calculateBo = new CalculateBo();
		calculateBo.setPromotionActivity(promotionActivity);
		calculateBo.setSellerNick("赵东昊的测试店铺");
		calculateBo.setLevel(1);
		calculateBo.setCycleEndTime(DateUtil.parseString("2020-03-24 16:22:45"));
		calculateBo.setPromotionItemCode("FW_GOODS-1827490-v2");
		when(orderSearchRepository.queryBySellerNickAndOrderCycleStartAndOrderCycleEndAndItemCode(any(), any(), any(), any(), any(), any(), any())).thenReturn(getTaobaoOrderSearch());
		when(orderSearchRepository.queryBySellerNickAndItemCodeAndIdSortIdDesc(any(), any(), any(), any(), any())).thenReturn(null);
		//order_cycle_start:"2020-03-24 17:21:54" optime:"2020-04-04 14:22:45"
		when(promotionActivityRepository.queryByIsUsedAndSellerNickSortOptime(any(), any(), eq(platformId), eq(appName))).thenReturn(Collections.singletonList(getPromotionActivity()));
		SellerLevelCycleEndTimeTemporary temporary = calculateService.calculateAdvancedSeller(calculateBo, platformId, appName);
        Assertions.assertEquals(Boolean.TRUE, temporary.getReAggregationCalculate());
        Assertions.assertEquals(Integer.valueOf(1), temporary.getLastUseId());
        Assertions.assertNotNull(temporary.getNewSplitActivity());
	}

	@org.junit.jupiter.api.Test
	public void calculateAdvancedSellerTest6() {
		PromotionActivity promotionActivity = getPromotionActivity();
		promotionActivity.setOptime(DateUtil.parseString("2020-04-04 14:22:45"));
		CalculateBo calculateBo = new CalculateBo();
		calculateBo.setPromotionActivity(promotionActivity);
		calculateBo.setSellerNick("赵东昊的测试店铺");
		calculateBo.setLevel(1);
		calculateBo.setCycleEndTime(DateUtil.parseString("2020-03-24 16:22:45"));
		calculateBo.setPromotionItemCode("FW_GOODS-1827490-v2");
		when(orderSearchRepository.queryBySellerNickAndOrderCycleStartAndOrderCycleEndAndItemCode(any(), any(), any(), any(), any(), any(), any())).thenReturn(getTaobaoOrderSearch());
		when(orderSearchRepository.queryBySellerNickAndItemCodeAndIdSortIdDesc(any(), any(), any(), any(), any())).thenReturn(null);
		//order_cycle_start:"2020-03-24 17:21:54" optime:"2020-04-04 14:22:45"
		PromotionActivity promotionActivity1 = getPromotionActivity();
		promotionActivity1.setActCycle(1);
		when(promotionActivityRepository.queryByIsUsedAndSellerNickSortOptime(any(), any(), eq(platformId), eq(appName))).thenReturn(Collections.singletonList(promotionActivity1));
		SellerLevelCycleEndTimeTemporary temporary = calculateService.calculateAdvancedSeller(calculateBo, platformId, appName);
		List<Integer> useUpIds = new ArrayList<>(Collections.emptyList());
		useUpIds.add(1);
		Assertions.assertEquals(Boolean.TRUE, temporary.getReAggregationCalculate());
		Assertions.assertEquals(useUpIds, temporary.getUseUpIds());
	}

	@org.junit.jupiter.api.Test
	public void calculateAdvancedSellerTest7() {
		PromotionActivity promotionActivity = getPromotionActivity();
		promotionActivity.setOptime(DateUtil.parseString("2020-04-04 14:22:45"));
		CalculateBo calculateBo = new CalculateBo();
		calculateBo.setPromotionActivity(promotionActivity);
		calculateBo.setSellerNick("赵东昊的测试店铺");
		calculateBo.setLevel(1);
		calculateBo.setCycleEndTime(DateUtil.parseString("2020-03-24 16:22:45"));
		calculateBo.setPromotionItemCode("FW_GOODS-1827490-v2");
		OrderSearch orderSearch = new OrderSearch();
		orderSearch.setNick("赵东昊的测试店铺");
		orderSearch.setActivityCode("FW_GOODS-1827490-v2");
		orderSearch.setId(2);
		orderSearch.setOrderCycleStart(DateUtil.parseString("2020-03-24 17:21:54"));
		orderSearch.setOrderCycleEnd(DateUtil.parseString("2020-04-04 14:22:45"));
		when(orderSearchRepository.queryBySellerNickAndOrderCycleStartAndOrderCycleEndAndItemCode(any(), any(), any(), any(), any(), any(), any())).thenReturn(getTaobaoOrderSearch());
		when(orderSearchRepository.queryBySellerNickAndItemCodeAndIdSortIdDesc(any(), any(), any(), any(), any())).thenReturn(orderSearch);
		//order_cycle_start:"2020-03-24 17:21:54" optime:"2020-04-04 14:22:45"
		PromotionActivity promotionActivity1 = getPromotionActivity();
		promotionActivity1.setActCycle(1);
		when(promotionActivityRepository.queryByIsUsedAndSellerNickSortOptime(any(), any(), eq(platformId), eq(appName))).thenReturn(Collections.singletonList(promotionActivity1));
		SellerLevelCycleEndTimeTemporary temporary = calculateService.calculateAdvancedSeller(calculateBo, platformId, appName);
		List<Integer> useUpIds = new ArrayList<>(Collections.emptyList());
		useUpIds.add(1);
        Assertions.assertEquals(Boolean.TRUE, temporary.getReAggregationCalculate());
        Assertions.assertEquals(useUpIds, temporary.getUseUpIds());
	}

	@org.junit.jupiter.api.Test
	public void calculatePrimarySellerTest1() {
		CalculateBo calculateBo = new CalculateBo();
		calculateBo.setPromotionActivity(getPromotionActivity());
		calculateBo.setSellerNick("赵东昊的测试店铺");
		calculateBo.setLevel(0);
		calculateBo.setCycleEndTime(DateUtil.parseString("2020-03-24 16:22:45"));
		calculateBo.setPromotionItemCode("FW_GOODS-1827490-v2");
		calculateBo.setCurrentDateTime(DateUtil.parseString("2020-02-24 16:22:45"));
		calculateBo.setHasGivePresent(Boolean.FALSE);
		when(orderSearchRepository.queryBySellerNickAndOrderCycleStartAndOrderCycleEndAndItemCode(any(), any(), any(), any(), any(), any(), any())).thenReturn(null);
		SellerLevelCycleEndTimeTemporary temporary = calculateService.calculateAdvancedSeller(calculateBo, platformId, appName);
        Assertions.assertEquals(UserProductInfo.LEVEL_ONE, temporary.getLevel());
	}

	@org.junit.jupiter.api.Test
	public void calculatePrimarySellerTest2() {
		PromotionActivity promotionActivity = getPromotionActivity();
		promotionActivity.setOptime(DateUtil.parseString("2020-01-04 14:22:45"));
		CalculateBo calculateBo = new CalculateBo();
		calculateBo.setPromotionActivity(promotionActivity);
		calculateBo.setSellerNick("赵东昊的测试店铺");
		calculateBo.setLevel(0);
		calculateBo.setCycleEndTime(DateUtil.parseString("2020-03-24 16:22:45"));
		calculateBo.setPromotionItemCode("FW_GOODS-1827490-v2");
		calculateBo.setCurrentDateTime(DateUtil.parseString("2020-02-24 16:22:45"));
		calculateBo.setHasGivePresent(Boolean.FALSE);
		when(orderSearchRepository.queryBySellerNickAndOrderCycleStartAndOrderCycleEndAndItemCode(any(), any(), any(), any(), any(), any(), any())).thenReturn(null);
		SellerLevelCycleEndTimeTemporary temporary = calculateService.calculateAdvancedSeller(calculateBo, platformId, appName);
        Assertions.assertEquals(Boolean.TRUE, temporary.getIsAllUsed());
	}

	@org.junit.jupiter.api.Test
	public void calculatePrimarySellerTest3() {
		PromotionActivity promotionActivity = getPromotionActivity();
		promotionActivity.setOptime(DateUtil.parseString("2020-04-04 14:22:45"));
		promotionActivity.setActCycle(1);
		CalculateBo calculateBo = new CalculateBo();
		calculateBo.setPromotionActivity(promotionActivity);
		calculateBo.setSellerNick("赵东昊的测试店铺");
		calculateBo.setLevel(0);
		calculateBo.setCycleEndTime(DateUtil.parseString("2020-03-24 16:22:45"));
		calculateBo.setPromotionItemCode("FW_GOODS-1827490-v2");
		calculateBo.setCurrentDateTime(DateUtil.parseString("2020-04-24 16:22:45"));
		calculateBo.setHasGivePresent(Boolean.FALSE);
		when(orderSearchRepository.queryBySellerNickAndOrderCycleStartAndOrderCycleEndAndItemCode(any(), any(), any(), any(), any(), any(), any())).thenReturn(getTaobaoOrderSearch());
		SellerLevelCycleEndTimeTemporary temporary = calculateService.calculatePrimarySeller(calculateBo, platformId, appName);
        Assertions.assertEquals(Boolean.TRUE, temporary.getIsAllUsed());
	}

	@org.junit.jupiter.api.Test
	public void calculatePrimarySellerTest4() {
		PromotionActivity promotionActivity = getPromotionActivity();
		promotionActivity.setOptime(DateUtil.parseString("2020-04-04 14:22:45"));
		promotionActivity.setActCycle(10);
		CalculateBo calculateBo = new CalculateBo();
		calculateBo.setPromotionActivity(promotionActivity);
		calculateBo.setSellerNick("赵东昊的测试店铺");
		calculateBo.setLevel(0);
		calculateBo.setCycleEndTime(DateUtil.parseString("2020-03-24 16:22:45"));
		calculateBo.setPromotionItemCode("FW_GOODS-1827490-v2");
		calculateBo.setCurrentDateTime(DateUtil.parseString("2020-02-24 16:22:45"));
		calculateBo.setHasGivePresent(Boolean.FALSE);
		when(orderSearchRepository.queryBySellerNickAndOrderCycleStartAndOrderCycleEndAndItemCode(any(), any(), any(), any(), any(), any(), any())).thenReturn(getTaobaoOrderSearch());
		SellerLevelCycleEndTimeTemporary temporary = calculateService.calculatePrimarySeller(calculateBo, platformId, appName);
        Assertions.assertEquals(UserProductInfo.LEVEL_ONE, temporary.getLevel());
	}

	@org.junit.jupiter.api.Test
	public void calculatePrimarySellerTest5() {
		PromotionActivity promotionActivity = getPromotionActivity();
		promotionActivity.setOptime(DateUtil.parseString("2020-04-04 14:22:45"));
		promotionActivity.setActCycle(10);
		CalculateBo calculateBo = new CalculateBo();
		calculateBo.setPromotionActivity(promotionActivity);
		calculateBo.setSellerNick("赵东昊的测试店铺");
		calculateBo.setLevel(0);
		calculateBo.setCycleEndTime(DateUtil.parseString("2020-03-24 16:22:45"));
		calculateBo.setPromotionItemCode("FW_GOODS-1827490-v2");
		calculateBo.setCurrentDateTime(DateUtil.parseString("2020-04-24 16:22:45"));
		calculateBo.setHasGivePresent(Boolean.FALSE);
		OrderSearch orderSearch = new OrderSearch();
		orderSearch.setNick("赵东昊的测试店铺");
		orderSearch.setActivityCode("FW_GOODS-1827490-v2");
		orderSearch.setId(2);
		orderSearch.setOrderCycleStart(DateUtil.parseString("2020-03-24 17:21:54"));
		orderSearch.setOrderCycleEnd(DateUtil.parseString("2020-04-10 17:21:54"));
		when(orderSearchRepository.queryBySellerNickAndOrderCycleStartAndOrderCycleEndAndItemCode(any(), any(), any(), any(), any(), any(), any())).thenReturn(orderSearch);
		SellerLevelCycleEndTimeTemporary temporary = calculateService.calculateAdvancedSeller(calculateBo, platformId, appName);
		Assertions.assertEquals(Boolean.TRUE, temporary.getIsAllUsed());
	}

	private PromotionActivity getPromotionActivity() {
		PromotionActivity promotionActivity = new PromotionActivity();
		promotionActivity.setId(1);
		promotionActivity.setActCycle(11);
		promotionActivity.setOptime(DateUtil.parseString("2020-03-24 18:22:45"));
		return promotionActivity;
	}

	public OrderSearch getTaobaoOrderSearch() {
		OrderSearch orderSearch = new OrderSearch();
		orderSearch.setNick("赵东昊的测试店铺");
		orderSearch.setActivityCode("FW_GOODS-1827490-v2");
		orderSearch.setId(2);
		orderSearch.setOrderCycleStart(DateUtil.parseString("2020-03-24 17:21:54"));
		orderSearch.setOrderCycleEnd(DateUtil.parseString("2020-04-01 17:21:54"));
		return orderSearch;
	}

}
