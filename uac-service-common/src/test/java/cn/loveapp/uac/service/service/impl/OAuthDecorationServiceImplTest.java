package cn.loveapp.uac.service.service.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.uac.common.bo.AuthBo;
import cn.loveapp.uac.common.bo.UserBo;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.dao.redis.repository.UserManageRedisRepositoryHashRedisRepository;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.common.exception.CacheWriteException;
import cn.loveapp.uac.common.exception.DbWriteException;
import cn.loveapp.uac.common.platform.api.AuthService;
import cn.loveapp.uac.common.platform.api.domain.RefreshTokenCallbackResult;
import cn.loveapp.uac.common.utils.DateUtil;
import cn.loveapp.uac.common.utils.RocketMqQueueHelper;
import cn.loveapp.uac.db.common.repository.UserRepository;
import cn.loveapp.uac.response.CallbackResponse;
import cn.loveapp.uac.service.event.SubscribeUserMessageEventHandler;
import cn.loveapp.uac.service.service.SellerService;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.junit.jupiter.api.Assertions;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;

import java.time.LocalDateTime;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * @Created by: IntelliJ IDEA.
 * @description:
 * @authr: jason
 * @date: 2020/3/25
 * @time: 12:02 PM
 */
@SpringBootTest(webEnvironment = WebEnvironment.NONE,
    classes = {OAuthDecorationServiceImpl.class, StringRedisTemplate.class, SellerService.class, UserRepository.class,
        DefaultMQProducer.class, RocketMqQueueHelper.class, AuthService.class, SubscribeUserMessageEventHandler.class})
public class OAuthDecorationServiceImplTest {

    @MockitoSpyBean
    private OAuthDecorationServiceImpl oAuthDecorationService;

    @MockitoBean
    private SubscribeUserMessageEventHandler subscribeUserMessageEventHandler;

    @MockitoBean
    private SellerService sellerService;
    @MockitoBean
    private UserRepository userRepository;

    @MockitoBean
    private DefaultMQProducer producer;
    @MockitoBean
    private RocketMqQueueHelper rocketMqQueueHelper;
    @MockitoBean
    private StringRedisTemplate stringRedisTemplate;
    @MockitoBean
    private AuthService authService;
    @MockitoBean
    private UserManageRedisRepositoryHashRedisRepository userManageRedisRepository;

    @org.junit.jupiter.api.Test
    public void authCodeAndRefreshUserTest1() throws Exception {
        doReturn(getUserBo()).when(oAuthDecorationService).getAuthCodeResult(getAuthBo());
        when(sellerService.getUserInfoByDb(any(), any(), any())).thenReturn(null);
        CallbackResponse ret = oAuthDecorationService.authCodeAndRefreshUser(getAuthBo());
        Assertions.assertNull(ret);
    }

    @org.junit.jupiter.api.Test
    public void authCodeAndRefreshUserTest2() throws Exception {
        AuthBo authBo = getAuthBo();
        authBo.setPlatformId("PDD");
        doReturn(getUserBo()).when(oAuthDecorationService).getAuthCodeResult(authBo);
        when(sellerService.getUserInfoByDb(any(), any(), any())).thenReturn(null);
        doNothing().when(oAuthDecorationService).eventHandle(any());
        CallbackResponse ret = oAuthDecorationService.authCodeAndRefreshUser(authBo);
        verify(sellerService, times(1)).saveUserInfoCacheAndTable(getUserBo(), "TAO", "trade");
        assertTrue(ret.isSuccess());
    }

    @org.junit.jupiter.api.Test
    public void authCodeAndRefreshUserTest3() throws Exception {
        AuthBo authBo = getAuthBo();
        doReturn(getUserBo()).when(oAuthDecorationService).getAuthCodeResult(authBo);
        when(sellerService.getUserInfoByDb(any(), any(), any())).thenReturn(getUserProductInfo());
        doNothing().when(oAuthDecorationService).eventHandle(any());
        CallbackResponse ret = oAuthDecorationService.authCodeAndRefreshUser(authBo);
        verify(sellerService, times(1)).refreshUserInfoCacheAndTable(getUserBo(), "TAO", "trade");
        assertTrue(ret.isSuccess());
    }

    @org.junit.jupiter.api.Test
    public void authCodeAndRefreshUserTest4() throws Exception {
        AuthBo authBo = getAuthBo();
        doReturn(getUserBo()).when(oAuthDecorationService).getAuthCodeResult(authBo);
        when(sellerService.getUserInfoByDb(any(), any(), any())).thenReturn(getUserProductInfo());
        doNothing().when(oAuthDecorationService).eventHandle(any());
        doThrow(DbWriteException.class).when(sellerService).refreshUserInfoCacheAndTable(getUserBo(), "TAO", "trade");
        CallbackResponse ret = oAuthDecorationService.authCodeAndRefreshUser(authBo);
        assertNull(ret);
    }

    @org.junit.jupiter.api.Test
    public void authCodeAndRefreshUserTest5() throws Exception {
        AuthBo authBo = getAuthBo();
        doReturn(getUserBo()).when(oAuthDecorationService).getAuthCodeResult(authBo);
        when(sellerService.getUserInfoByDb(any(), any(), any())).thenReturn(getUserProductInfo());
        doNothing().when(oAuthDecorationService).eventHandle(any());
        doThrow(CacheWriteException.class).when(sellerService).refreshUserInfoCacheAndTable(getUserBo(), "TAO",
            "trade");
        CallbackResponse ret = oAuthDecorationService.authCodeAndRefreshUser(authBo);
        assertTrue(ret.isSuccess());
    }

    @org.junit.jupiter.api.Test
    public void authCodeAndRefreshUserTest6() throws Exception {
        AuthBo authBo = getAuthBo();
        doThrow(Exception.class).when(oAuthDecorationService).getAuthCodeResult(authBo);
        CallbackResponse ret = oAuthDecorationService.authCodeAndRefreshUser(authBo);
        assertNull(ret);
    }

    /**
     * 刷新刷新token成功
     *
     * @throws Exception
     */
    @org.junit.jupiter.api.Test
    public void testRefreshAccessToken1() throws Exception {
        UserInfoBo userInfoBo = createUserInfoBo();
        RefreshTokenCallbackResult refreshTokenCallbackResult = createRefreshTokenCallbackResult();
        String refreshToken = "22222222222222";
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        CallbackResponse callbackResponse = new CallbackResponse();
        callbackResponse.setSellerNick(refreshTokenCallbackResult.getSellerNick());
        callbackResponse.setAccessToken(refreshTokenCallbackResult.getDecryptAccessToken());

        when(authService.decryptToken(eq(refreshToken), eq(platformId), eq(appName)))
            .thenReturn("44444444444444444444");
        when(authService.refreshToken(eq(userInfoBo), eq("44444444444444444444"), eq(platformId), eq(appName)))
            .thenReturn(refreshTokenCallbackResult);
        CallbackResponse response =
            oAuthDecorationService.refreshAccessToken(userInfoBo, refreshToken, platformId, appName);

        assertEquals(callbackResponse, response);
    }

    /**
     * 刷新token失败（请求平台刷新token接口失败）
     *
     * @throws Exception
     */
    @org.junit.jupiter.api.Test
    public void testRefreshAccessToken2() throws Exception {
        UserInfoBo userInfoBo = createUserInfoBo();
        String refreshToken = "22222222222222";
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;

        when(authService.decryptToken(eq(refreshToken), eq(platformId), eq(appName)))
            .thenReturn("44444444444444444444");
        when(authService.refreshToken(eq(userInfoBo), eq("44444444444444444444"), eq(platformId), eq(appName)))
            .thenReturn(null);
        CallbackResponse response =
            oAuthDecorationService.refreshAccessToken(userInfoBo, refreshToken, platformId, appName);
        assertNull(response);
    }

    /**
     * 刷新token失败（请求平台刷新token接口失败）
     *
     * @throws Exception
     */
    @org.junit.jupiter.api.Test
    public void testRefreshAccessToken3() throws Exception {
        UserInfoBo userInfoBo = createUserInfoBo();
        String refreshToken = "22222222222222";
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        RefreshTokenCallbackResult refreshTokenCallbackResult = createRefreshTokenCallbackResult();
        UserProductInfo userProductInfo = UserProductInfo.of(refreshTokenCallbackResult.getSellerNick(),
            refreshTokenCallbackResult.getSellerId(), UserProductInfo.NO_NEED_AUTH);
        userProductInfo.initDefault();
        userProductInfo.setToprefreshkey(refreshTokenCallbackResult.getRefreshToken());
        userProductInfo.setTopsessionkey(refreshTokenCallbackResult.getAccessToken());
        userProductInfo.setW1Deadline(DateUtil.parseDate(new Date(refreshTokenCallbackResult.getExpiresIn())));

        UserBo userBo = new UserBo(platformId, appName, refreshTokenCallbackResult.getSellerNick(), userProductInfo);
        userBo.setDecryptAccessToken(refreshTokenCallbackResult.getDecryptAccessToken());

        when(authService.decryptToken(eq(refreshToken), eq(platformId), eq(appName)))
            .thenReturn("44444444444444444444");
        when(authService.refreshToken(eq(userInfoBo), eq("44444444444444444444"), eq(platformId), eq(appName)))
            .thenReturn(refreshTokenCallbackResult);
        doThrow(new DbWriteException(110, "数据库入库异常")).when(sellerService).refreshUserInfoCacheAndTable(any(),
            eq(platformId), eq(appName));
        CallbackResponse response =
            oAuthDecorationService.refreshAccessToken(userInfoBo, refreshToken, platformId, appName);
        assertNull(response);
    }

    /**
     * 刷新token成功写入缓存异常
     *
     * @throws Exception
     */
    @org.junit.jupiter.api.Test
    public void testRefreshAccessToken4() throws Exception {
        UserInfoBo userInfoBo = createUserInfoBo();
        String refreshToken = "22222222222222";
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        RefreshTokenCallbackResult refreshTokenCallbackResult = createRefreshTokenCallbackResult();
        UserProductInfo userProductInfo = UserProductInfo.of(refreshTokenCallbackResult.getSellerNick(),
            refreshTokenCallbackResult.getSellerId(), UserProductInfo.NO_NEED_AUTH);
        userProductInfo.initDefault();
        userProductInfo.setToprefreshkey(refreshTokenCallbackResult.getRefreshToken());
        userProductInfo.setTopsessionkey(refreshTokenCallbackResult.getAccessToken());
        userProductInfo.setW1Deadline(DateUtil.parseDate(new Date(refreshTokenCallbackResult.getExpiresIn())));

        UserBo userBo = new UserBo(platformId, appName, refreshTokenCallbackResult.getSellerNick(), userProductInfo);
        userBo.setDecryptAccessToken(refreshTokenCallbackResult.getDecryptAccessToken());

        when(authService.decryptToken(eq(refreshToken), eq(platformId), eq(appName)))
            .thenReturn("44444444444444444444");
        when(authService.refreshToken(eq(userInfoBo), eq("44444444444444444444"), eq(platformId), eq(appName)))
            .thenReturn(refreshTokenCallbackResult);
        doThrow(new CacheWriteException(112, "缓存写入异常")).when(sellerService).refreshUserInfoCacheAndTable(any(),
            eq(platformId), eq(appName));
        CallbackResponse response =
            oAuthDecorationService.refreshAccessToken(userInfoBo, refreshToken, platformId, appName);

        CallbackResponse callbackResponse = CallbackResponse.of(refreshTokenCallbackResult.getSellerNick(),
            refreshTokenCallbackResult.getDecryptAccessToken());
        assertEquals(callbackResponse, response);
    }

    /**
     * 刷新token失败（意料之外的异常情况）
     *
     * @throws Exception
     */
    @org.junit.jupiter.api.Test
    public void testRefreshAccessToken5() throws Exception {
        UserInfoBo userInfoBo = createUserInfoBo();
        String refreshToken = "22222222222222";
        String platformId = CommonPlatformConstants.PLATFORM_WXVIDEOSHOP;
        String appName = CommonAppConstants.APP_DISTRIBUTE;
        RefreshTokenCallbackResult refreshTokenCallbackResult = createRefreshTokenCallbackResult();
        UserProductInfo userProductInfo = UserProductInfo.of(refreshTokenCallbackResult.getSellerNick(),
            refreshTokenCallbackResult.getSellerId(), UserProductInfo.NO_NEED_AUTH);
        userProductInfo.initDefault();
        userProductInfo.setToprefreshkey(refreshTokenCallbackResult.getRefreshToken());
        userProductInfo.setTopsessionkey(refreshTokenCallbackResult.getAccessToken());
        userProductInfo.setW1Deadline(DateUtil.parseDate(new Date(refreshTokenCallbackResult.getExpiresIn())));

        UserBo userBo = new UserBo(platformId, appName, refreshTokenCallbackResult.getSellerNick(), userProductInfo);
        userBo.setDecryptAccessToken(refreshTokenCallbackResult.getDecryptAccessToken());

        when(authService.decryptToken(eq(refreshToken), eq(platformId), eq(appName)))
            .thenReturn("44444444444444444444");
        when(authService.refreshToken(eq(userInfoBo), eq("44444444444444444444"), eq(platformId), eq(appName)))
            .thenReturn(refreshTokenCallbackResult);
        doThrow(new RuntimeException()).when(sellerService).refreshUserInfoCacheAndTable(any(), eq(platformId),
            eq(appName));
        CallbackResponse response =
            oAuthDecorationService.refreshAccessToken(userInfoBo, refreshToken, platformId, appName);
        assertNull(response);
    }

    private UserProductInfo getUserProductInfo() {
        UserProductInfo userProductInfo = new UserProductInfo();
        userProductInfo.setNick("赵东昊的测试店铺");
        return userProductInfo;
    }

    private AuthBo getAuthBo() {
        AuthBo authBo = new AuthBo();
        authBo.setCode("xxx");
        authBo.setPlatformId("TAO");
        authBo.setAppType("trade");
        return authBo;
    }

    private UserBo getUserBo() {
        UserBo userBo = new UserBo();
        userBo.setHasReadTag(Boolean.TRUE);
        userBo.setPlatformId("TAO");
        userBo.setAppType("trade");
        userBo.setSellerNick("赵东昊的测试店铺");
        userBo.setDecryptAccessToken("xxx");
        return userBo;
    }

    private UserInfoBo createUserInfoBo() {
        UserInfoBo userInfoBo = new UserInfoBo();
        userInfoBo.setAccessToken("1111111111111111111");
        userInfoBo.setRefreshToken("222222222222222222");
        userInfoBo.setOrderCycleEnd(LocalDateTime.now().plusDays(2));
        userInfoBo.setAuthDeadLine(LocalDateTime.now().plusDays(2));
        userInfoBo.setSellerNick("gh_4ebc6c1c889a");
        userInfoBo.setSellerId("wxc37ce2922b49c0b8");
        userInfoBo.setAppType(CommonAppConstants.APP_DISTRIBUTE);
        userInfoBo.setPlatformId(CommonPlatformConstants.PLATFORM_WXVIDEOSHOP);
        return userInfoBo;
    }

    private RefreshTokenCallbackResult createRefreshTokenCallbackResult() {
        RefreshTokenCallbackResult result = new RefreshTokenCallbackResult();
        result.setDecryptRefreshToken("4444444444444444444444444444");
        result.setDecryptAccessToken("3333333333333333333");
        result.setRefreshToken("222222222222222222222222");
        result.setAccessToken("111111111111111111111111111");
        result.setSellerId("wxc37ce2922b49c0b8");
        result.setSellerNick("gh_4ebc6c1c889a");
        result.setExpiresIn(new Date().getTime());
        return result;
    }
}
