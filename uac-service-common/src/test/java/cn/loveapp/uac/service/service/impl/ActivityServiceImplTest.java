package cn.loveapp.uac.service.service.impl;

import cn.loveapp.uac.common.bo.CalculateBo;
import cn.loveapp.uac.common.entity.PromotionActivity;
import cn.loveapp.uac.common.utils.DateUtil;
import cn.loveapp.uac.db.common.dao.dream.ReturnMoneyInfoDao;
import cn.loveapp.uac.db.common.dao.dream.ReturnMoneyProjectInfoDao;
import cn.loveapp.uac.db.common.dao.dream.UserAlipayAccountInfoDao;
import cn.loveapp.uac.db.common.repository.PromotionActivityRepository;
import cn.loveapp.uac.service.config.ActivityConfig;
import cn.loveapp.uac.service.service.CalculateService;
import cn.loveapp.uac.service.service.impl.CalculateServiceImpl.SellerLevelCycleEndTimeTemporary;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * @Created by: IntelliJ IDEA.
 * @description:
 * @authr: jason
 * @date: 2020/3/24
 * @time: 4:17 PM
 */
@SpringBootTest(webEnvironment = WebEnvironment.NONE,
	classes = {ActivityServiceImpl.class, StringRedisTemplate.class,
		UserAlipayAccountInfoDao.class, ReturnMoneyInfoDao.class, ReturnMoneyProjectInfoDao.class, PromotionActivityRepository.class, CalculateService.class,
		})
public class ActivityServiceImplTest {

	@MockitoSpyBean
	private ActivityServiceImpl activityService;

	@MockitoSpyBean
	private UserAlipayAccountInfoDao userAlipayAccountInfoDao;
	@MockitoSpyBean
	private ReturnMoneyInfoDao returnMoneyInfoDao;
	@MockitoSpyBean
	private ReturnMoneyProjectInfoDao returnMoneyProjectInfoDao;
	@MockitoSpyBean
	private PromotionActivityRepository promotionActivityRepository;
	@MockitoSpyBean
	private StringRedisTemplate stringRedisTemplate;
	@MockitoSpyBean
	private CalculateService calculateService;

	@MockitoSpyBean
	private ActivityConfig activityConfig;

	private final String platformId = "TAO";
	private final String appName = "trade";

	@org.junit.jupiter.api.Test
	public void calculatePromotionActivityLevelAndCycleEndTimeTest1() {
		when(promotionActivityRepository.aggregationActCycleAndOptimeBySellerNickAndUnused("赵东昊的测试店铺",
			PromotionActivity.UNUSED, platformId, appName)).thenReturn(null);
		Pair<Integer, LocalDateTime> pair = activityService.calculatePromotionActivityLevelAndCycleEndTime("赵东昊的测试店铺", 1,
			DateUtil.parseString("2020-03-24 16:22:45"), platformId, appName);
		Assertions.assertEquals(Integer.valueOf(1), pair.getLeft());
	}

	@org.junit.jupiter.api.Test
	public void calculatePromotionActivityLevelAndCycleEndTimeTest3() {
		when(promotionActivityRepository.aggregationActCycleAndOptimeBySellerNickAndUnused("赵东昊的测试店铺",
			PromotionActivity.UNUSED, platformId, appName)).thenReturn(getPromotionActivityList());
		CalculateBo calculateBo = new CalculateBo();
		calculateBo.setPromotionActivity(getPromotionActivity());
		calculateBo.setSellerNick("赵东昊的测试店铺");
		calculateBo.setLevel(1);
		calculateBo.setCycleEndTime(DateUtil.parseString("2020-03-24 16:22:45"));
		calculateBo.setPromotionItemCode("FW_GOODS-1827490-v2");
		calculateBo.setCurrentDateTime(DateUtil.parseString("2020-02-24 16:22:45"));
		calculateBo.setHasGivePresent(Boolean.FALSE);
		SellerLevelCycleEndTimeTemporary sellerLevelCycleEndTimeTemporary = new SellerLevelCycleEndTimeTemporary();
		sellerLevelCycleEndTimeTemporary.setLevel(1);
		sellerLevelCycleEndTimeTemporary.setOrderCycleEndTime(DateUtil.parseString("2020-03-24 16:22:45"));
		when(calculateService.calculateAdvancedSeller(any(), eq(platformId), eq(appName))).thenReturn(sellerLevelCycleEndTimeTemporary);
		doNothing().when(activityService).recalculateLevelCycleEndTime("赵东昊的测试店铺",
				sellerLevelCycleEndTimeTemporary, DateUtil.parseString("2020-03-24 16:22:45"), platformId, appName);
		Pair<Integer, LocalDateTime> pair = activityService.calculatePromotionActivityLevelAndCycleEndTime("赵东昊的测试店铺",
			1,
			DateUtil.parseString("2020-03-24 16:22:45"), platformId, appName);
		verify(activityService).recalculateLevelCycleEndTime("赵东昊的测试店铺",
			sellerLevelCycleEndTimeTemporary, DateUtil.parseString("2020-03-24 16:22:45"), platformId, appName);
	}

	@org.junit.jupiter.api.Test
	public void recalculateLevelCycleEndTimeTest1() {
		SellerLevelCycleEndTimeTemporary sellerLevelCycleEndTimeTemporary = new SellerLevelCycleEndTimeTemporary();
		sellerLevelCycleEndTimeTemporary.setLevel(1);
		sellerLevelCycleEndTimeTemporary.setIsAllUsed(Boolean.TRUE);
		sellerLevelCycleEndTimeTemporary.setOrderCycleEndTime(DateUtil.parseString("2020-03-24 16:22:45"));
		when(promotionActivityRepository.updateIsUsedBySellerNick(PromotionActivity.USED, "赵东昊的测试店铺", platformId, appName)).thenReturn(1);
		activityService.recalculateLevelCycleEndTime("赵东昊的测试店铺", sellerLevelCycleEndTimeTemporary,
				DateUtil.parseString("2020-03-24 16:22:45"), platformId, appName);
		verify(promotionActivityRepository, times(1)).updateIsUsedBySellerNick(PromotionActivity.USED, "赵东昊的测试店铺", platformId, appName);
	}

	@org.junit.jupiter.api.Test
	public void recalculateLevelCycleEndTimeTest2() {
		SellerLevelCycleEndTimeTemporary sellerLevelCycleEndTimeTemporary = new SellerLevelCycleEndTimeTemporary();
		sellerLevelCycleEndTimeTemporary.setLevel(1);
		sellerLevelCycleEndTimeTemporary.setIsAllUsed(Boolean.FALSE);
		sellerLevelCycleEndTimeTemporary.setReAggregationCalculate(Boolean.TRUE);
		sellerLevelCycleEndTimeTemporary.setUseUpIds(Arrays.asList(1,2,3,4));
		sellerLevelCycleEndTimeTemporary.setLastUseActCycle(15);
		sellerLevelCycleEndTimeTemporary.setLastUseId(1);
		sellerLevelCycleEndTimeTemporary.setNewSplitActivity(getPromotionActivity());
		sellerLevelCycleEndTimeTemporary.setOrderCycleEndTime(DateUtil.parseString("2020-03-24 16:22:45"));
		when(promotionActivityRepository.updateIsUsedOrActCycleByPkIds(
			any(), any(), any(), eq(platformId), eq(appName))).thenReturn(1);
		when(promotionActivityRepository.insert(
			any(), eq(platformId), eq(appName))).thenReturn(1);
		when(promotionActivityRepository.aggregationActCycleAndOptimeBySellerNickAndUnused(eq("赵东昊的测试店铺"),
			eq(PromotionActivity.UNUSED), eq(platformId), eq(appName))).thenReturn(getPromotionActivityList());
		activityService.recalculateLevelCycleEndTime("赵东昊的测试店铺",
			sellerLevelCycleEndTimeTemporary, DateUtil.parseString("2020-03-24 16:22:45"), platformId, appName);
		verify(promotionActivityRepository, times(1)).aggregationActCycleAndOptimeBySellerNickAndUnused(
			"赵东昊的测试店铺", PromotionActivity.UNUSED, platformId, appName);
	}

	@org.junit.jupiter.api.Test
	public void recalculateLevelCycleEndTimeTest3() {
		SellerLevelCycleEndTimeTemporary sellerLevelCycleEndTimeTemporary = new SellerLevelCycleEndTimeTemporary();
		sellerLevelCycleEndTimeTemporary.setLevel(1);
		sellerLevelCycleEndTimeTemporary.setIsAllUsed(Boolean.FALSE);
		sellerLevelCycleEndTimeTemporary.setReAggregationCalculate(Boolean.TRUE);
		sellerLevelCycleEndTimeTemporary.setUseUpIds(Arrays.asList(1,2,3,4));
		sellerLevelCycleEndTimeTemporary.setLastUseActCycle(15);
		sellerLevelCycleEndTimeTemporary.setLastUseId(1);
		sellerLevelCycleEndTimeTemporary.setNewSplitActivity(getPromotionActivity());
		sellerLevelCycleEndTimeTemporary.setOrderCycleEndTime(DateUtil.parseString("2020-03-24 16:22:45"));
		when(promotionActivityRepository.updateIsUsedOrActCycleByPkIds(
			PromotionActivity.USED, null, Arrays.asList(1,2,3,4), platformId, appName))
			.thenThrow(new RuntimeException());
		activityService.recalculateLevelCycleEndTime("赵东昊的测试店铺",
			sellerLevelCycleEndTimeTemporary, DateUtil.parseString("2020-03-24 16:22:45"), platformId, appName);
		verify(promotionActivityRepository, never()).aggregationActCycleAndOptimeBySellerNickAndUnused(
			"赵东昊的测试店铺", PromotionActivity.UNUSED, platformId, appName);
	}

	private PromotionActivity getPromotionActivity() {
		PromotionActivity promotionActivity = new PromotionActivity();
		promotionActivity.setActCycle(10);
		promotionActivity.setOptime(DateUtil.parseString("2020-03-24 16:22:45"));
		return promotionActivity;
	}

	private List<PromotionActivity> getPromotionActivityList() {
		List<PromotionActivity> list = Lists.newArrayList();
		PromotionActivity promotionActivity = new PromotionActivity();
		promotionActivity.setActCycle(10);
		promotionActivity.setOptime(DateUtil.parseString("2020-03-24 16:22:45"));

		list.add(promotionActivity);
		return list;
	}

}
