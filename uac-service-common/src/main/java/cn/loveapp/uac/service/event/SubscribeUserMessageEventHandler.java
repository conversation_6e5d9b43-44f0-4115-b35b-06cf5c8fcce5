package cn.loveapp.uac.service.event;

import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.proto.event.UserChangedEvent;

/**
 * @program: uac-service-group
 * @description: SubscribeUserMessageEventHandler
 * @author: <PERSON>
 * @create: 2020-10-16 15:39
 **/
public interface SubscribeUserMessageEventHandler {

	/**
	 * 统一处理subscribe方法
	 * @param userBo
	 * @param changeStatus
	 * @param type
	 */
	void eventHandle(UserInfoBo userBo, String changeStatus, String type);

    /**
     * 用户信息变更消息通知
     * @param userInfoBo
     * @param event
     */
    void userChangedEventHandle(UserInfoBo userInfoBo, UserChangedEvent event);
}
