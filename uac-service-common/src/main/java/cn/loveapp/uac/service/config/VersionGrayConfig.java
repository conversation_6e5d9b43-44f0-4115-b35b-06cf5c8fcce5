package cn.loveapp.uac.service.config;

import com.google.common.collect.Lists;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 版本灰度控制相关配置
 *
 * <AUTHOR>
 */
@Configuration
@Data
public class VersionGrayConfig {

    public static final String CONFIGURATION_PREFIX = "uac.version.gray";

    /**
     * 是否开启灰度
     */
    @Value("${uac.version.gray.enable:false}")
    private boolean enable = false;

    /**
     * 灰度版本内的用户Nick
     */
    @Value("${uac.version.gray.users:}")
    private List<String> users = Lists.newArrayList();

    /**
     * 灰度的平台
     */
    @Value("${uac.version.gray.platforms:}")
    private List<String> platforms = Lists.newArrayList();

    /**
     * 灰度服务的host
     */
    @Value("${uac.version.gray.serviceHost:}")
    private String serviceHost;
}
