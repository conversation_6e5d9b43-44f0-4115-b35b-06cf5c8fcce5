package cn.loveapp.uac.service.service;

import cn.loveapp.uac.common.code.taobao.ApiCodeConstant.CodeEnum;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.proto.event.UserChangedEvent;

/**
 * @program: uac-service-group
 * @description: OperationService
 * @author: <PERSON>
 * @create: 2020-03-13 11:11
 **/
public interface OperationService {
	/**
	 * 计算最终用户时间和等级
	 * @param userInfoBo
	 * @return
	 */
    UserChangedEvent calculateOrderCycleEndAndLevel(UserInfoBo userInfoBo, String platformId, String appName);

	/**
	 * 判断用户是自动续费 - 调用平台接口
	 * @param sellerNick
	 * @param articleCode
	 * @param fuwuItemCode
	 * @return
	 */
	CodeEnum hasAutoRenew(String sellerNick, String articleCode, String fuwuItemCode, Boolean useCache, String platformId, String appName);

	/**
	 * 判断是否有连续包月标识
	 * @param sellerNick
	 * @return
	 */
	boolean hasAutoMonth(String sellerNick, String platformId, String appName);

    /**
     * eventHandle
     * @param userInfoBo
     * @param event
     */
    void eventHandle(UserInfoBo userInfoBo, UserChangedEvent event);

}
