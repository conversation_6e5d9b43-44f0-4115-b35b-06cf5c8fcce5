package cn.loveapp.uac.service.platform.biz.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.bo.LoginUserInfoBo;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.common.exception.DbWriteException;
import cn.loveapp.uac.response.UserCacheInfoResponse;
import cn.loveapp.uac.service.service.SellerService;
import cn.loveapp.uac.exception.UserException;
import cn.loveapp.uac.response.UserInfoResponse;
import cn.loveapp.uac.service.platform.biz.UserPlatformHandleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: uac-service-group
 * @description: TaoUserPlatformHandleServiceImpl
 * @author: Jason
 * @create: 2021-04-19 17:23
 **/
@Service
public class UserPlatformHandleServiceImpl implements UserPlatformHandleService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(UserPlatformHandleServiceImpl.class);

	@Value("${uac.user.reminder.auth.daydiff:15}")
	private Long reminderAuthDayDiff;

	/**
	 * 用户订购周期到期最大天数
	 * 默认两年，365*2=730
	 */
	@Value("${uac.user.order.cycle.max.end.days:730}")
	private long orderCycleMaxEndDays;

	@Autowired
	private SellerService sellerService;

	@Override
	public UserInfoResponse login(UserInfoBo userInfoBo, String platformId, String appName)
		throws UserException {
		UserInfoResponse login = sellerService.login(userInfoBo, platformId, appName);
		if (login.getOrderCycleEnd() != null && login.getOrderCycleEnd().isAfter(LocalDateTime.now().plusDays(orderCycleMaxEndDays))) {
			LOGGER.logInfo(userInfoBo.getSellerNick(), "", "该用户订购到期时间可能存在异常：" + login.getOrderCycleEnd());
		}
		return login;
	}

	@Override
	public UserInfoResponse quickLogin(LoginUserInfoBo loginUserInfoBo, String platformId,
		String appName) throws DbWriteException, UserException {
		loginUserInfoBo.setReminderAuthDayDiff(reminderAuthDayDiff);
		return sellerService.quickLogin(loginUserInfoBo, platformId, appName);
	}

	@Override
	public UserInfoResponse getUserInfo(UserInfoBo userInfoBo, String platformId,
		String appName) throws UserException {
		return sellerService.getUserInfo(userInfoBo, platformId, appName);
	}

	@Override
	public UserInfoResponse getUserInfo(UserInfoBo userInfoBo, String memberId,
		String platformId, String appName) throws UserException {
		return sellerService.getUserInfo(userInfoBo, memberId, platformId, appName);
	}

	@Override
	public UserProductInfo getUserFullInfo(UserInfoBo userInfoBo, boolean isCheckAndRefreshToken, String platformId,
										   String appName) throws Exception {
		return sellerService.getUserFullInfo(userInfoBo, isCheckAndRefreshToken, platformId, appName);
	}

	@Override
	public Boolean refreshUserInfo(UserInfoBo userInfoBo, String platformId, String appName) {
		return sellerService.refreshUserInfo(userInfoBo, platformId, appName);
	}

	@Override
	public UserInfoResponse getAccessToken(UserInfoBo userInfoBo, String platformId,
		String appName) throws Exception {
		return sellerService.getAccessToken(userInfoBo, platformId, appName);
	}

	@Override
	public UserInfoResponse getAccessToken(UserInfoBo userInfoBo, String memberId,
		String platformId, String appName) throws Exception {
		return sellerService.getAccessToken(userInfoBo, memberId, platformId, appName);
	}

	@Override
	public String refreshAccessToken(UserInfoBo userInfoBo, String refreshToken,
		String platformId, String appName) {
		return sellerService.refreshAccessToken(userInfoBo, refreshToken, platformId, appName);
	}

	@Override
	public UserInfoResponse rebuildUserInfo(UserInfoBo userInfoBo, String platformId,
		String appName) throws UserException {
		return sellerService.rebuildUserInfo(userInfoBo, platformId, appName);
	}

	@Override
	public int batchUpdateUserCacheInfo(List<UserInfoBo> userInfoBoList, String hKey, String value) {
		return sellerService.batchUpdateUserCacheInfo(userInfoBoList, hKey, value);
	}

	@Override
	public List<UserCacheInfoResponse> batchGetUserCacheInfo(List<UserInfoBo> userInfoBoList, String hKey) {
		return sellerService.batchGetUserCacheInfo(userInfoBoList, hKey);
	}
}
