package cn.loveapp.uac.service.platform.biz.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.uac.common.bo.AuthBo;
import cn.loveapp.uac.response.CallbackResponse;
import cn.loveapp.uac.service.platform.biz.CallbackPlatformHandleService;
import org.springframework.stereotype.Service;

/**
 * 抖店授权接口
 *
 * <AUTHOR>
 * @date 2021/7/7
 */
@Service
public class DoudianCallbackPlatformHandleServiceImpl implements CallbackPlatformHandleService {

	@Override
	public CallbackResponse authCallback(AuthBo authBo, String platformId, String appType) {
		return null;
	}

	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_DOUDIAN;
	}
}
