package cn.loveapp.uac.service.config;

import cn.loveapp.common.constant.CommonAppConstants;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 运营活动相关配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
public class ActivityConfig {
	/**
	 * 交易自动续费projectCode
	 */
	@Value("${activity.trade.autorenew.project.code:trade_auto_renew}")
	private String tradeAutoRenewProjectCode;

	/**
	 * 商品自动续费projectCode
	 */
	@Value("${activity.item.autorenew.project.code:item_auto_renew}")
	private String itemAutoRenewProjectCode;

	/**
	 * 需要计算自动续费 & 赠送信息的vipflag
	 */
	@Value("${activity.need.normally.calculate.levels:0,1,2,3,4,6,8}")
	private List<Integer> needNormallyCalculateLevels;

	/**
	 * 获取返现的projectCode
	 * @param appType 应用类型
	 * @return
	 */
	public String getRefundMoneyProjectCode(String appType) {
		switch (appType) {
			case CommonAppConstants.APP_TRADE:
				return tradeAutoRenewProjectCode;
			case CommonAppConstants.APP_ITEM:
				return itemAutoRenewProjectCode;
			default:
				return null;
		}
	}
}
