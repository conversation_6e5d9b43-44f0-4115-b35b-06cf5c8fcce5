package cn.loveapp.uac.service.service.impl;

import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.api.domain.SellerArticleUserSubscribe;
import cn.loveapp.uac.common.api.request.SellerVasOrderSearchRequest;
import cn.loveapp.uac.common.api.request.SellerVasSubscribeGetRequest;
import cn.loveapp.uac.common.api.response.SellerVasOrderSearchResponse;
import cn.loveapp.uac.common.api.response.SellerVasSubscribeGetResponse;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.code.taobao.ApiCodeConstant.CodeEnum;
import cn.loveapp.uac.common.config.app.ArticleCodeConfig;
import cn.loveapp.uac.common.config.cache.VasGetResult;
import cn.loveapp.uac.common.platform.api.AppStoreService;
import cn.loveapp.uac.common.service.PlatformFuwuItemCodeService;
import cn.loveapp.uac.db.common.entity.OrderSearch;
import cn.loveapp.uac.db.common.repository.OrderSearchRepository;
import cn.loveapp.uac.service.service.SellerOrderSearchService;
import com.alibaba.fastjson2.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * @program: uac-service-group
 * @description: SellerOrderSearchServiceImpl
 * @author: Jason
 * @create: 2020-09-14 13:46
 **/
@Service
public class SellerOrderSearchServiceImpl implements SellerOrderSearchService {

	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(SellerOrderSearchServiceImpl.class);

	@Autowired
	private ArticleCodeConfig articleCodeConfig;

	@Autowired
	private PlatformFuwuItemCodeService platformFuwuItemCodeService;

	@Autowired
	private AppStoreService appstoreService;

	@Autowired
	private OrderSearchRepository orderSearchRepository;

    @Override
    public VasGetResult getVasGetResult(UserInfoBo userInfoBo, String platformId, String appName) {
        return getAppStoreServiceOrderSearch(userInfoBo.getSellerNick(), userInfoBo.getOrderCycleEnd(),
            userInfoBo.getLevel(), platformId, appName);
    }

	@Override
	public SellerVasOrderSearchResponse getVasOrderSearchResult(UserInfoBo userInfoBo, String platformId,LocalDateTime orderCycleEnd, String appName) {
		return getAppStoreVasOrderSearch(userInfoBo.getSellerNick(),userInfoBo.getOrderCycleEnd(),platformId,appName);
	}

	/**
	 * 调用 vas.subscribe.get 接口获取用户的订购信息
	 *
	 * @param sellerNick 用户Nick
	 * @param defaultOrderCycleEnd 默认订购到期时间
	 * @param defaultVipFlag 默认用户等级
	 * @param platformId 平台id
	 * @param appName 应用名
	 * @return
	 */
    private VasGetResult getAppStoreServiceOrderSearch(String sellerNick, LocalDateTime defaultOrderCycleEnd,
        Integer defaultVipFlag, String platformId, String appName) {
		if(LOGGER.isDebugEnabled()) {
		    LOGGER.logDebug(sellerNick, "-", "beginning getVasGetApi");
		}

		// 使用数据库中值作为默认vas结果，当平台返回成功时且与库中数据不一致时，取平台接口返回
        VasGetResult vasGetResult = VasGetResult.of(defaultVipFlag, defaultOrderCycleEnd);

		SellerVasSubscribeGetResponse sellerVasSubscribeGetResponse = vasSubscribeGet(sellerNick, articleCodeConfig.getArticleCode(platformId, appName), platformId, appName);
		CodeEnum apiCode = sellerVasSubscribeGetResponse.hasSuccess();
		if (LOGGER.isDebugEnabled()) {
			LOGGER.logDebug(sellerNick, "-", "hasSuccess返回结果为:" + apiCode);
		}

		vasGetResult.setApiCode(apiCode);
		if (apiCode.equals(CodeEnum.SUCCESS) && CollectionUtils.isNotEmpty(sellerVasSubscribeGetResponse.getArticleUserSubscribes())) {
			SellerArticleUserSubscribe articleUserSubscribe = sellerVasSubscribeGetResponse.getArticleUserSubscribes().get(0);
			Integer vipFlag = platformFuwuItemCodeService.obtainVipFlag(articleUserSubscribe.getItemCode(), platformId, appName);
			if (vipFlag == null) {
				LOGGER.logWarn(sellerNick, platformId, "platformFuwuItemCodeConfig 无对应vipFlag配置, appName => "
						+ appName + ", itemCode => " + articleUserSubscribe.getItemCode());
				return vasGetResult;
			}
			vasGetResult.setLevel(vipFlag);
			vasGetResult.setOrderCycleEnd(articleUserSubscribe.getDeadline());
			vasGetResult.setArticleUserSubscribe(articleUserSubscribe);
		} else {
			//因为用户订购到期后无法查询到订购关系，会导致使用数据库库中的到期时间，若此时有赠送时间，则到期时候会累计，然后入库更新到期时间。下次进入时，依然存在赠送时间，时间继续累积。
			//解决以上问题需要，取用户订购关系表中最后一条数据，将此设置为默认值，如果没有订购关系，则再使用用户表中的到期时间
			LOGGER.logInfo(sellerNick, "", "调用api查询订购关系结果为空");
			OrderSearch previousOrderSearch = orderSearchRepository.queryBySellerNickAndItemCodeAndIdSortIdDesc(sellerNick, null, null, platformId, appName);
			if (previousOrderSearch != null) {
				Integer vipFlag = platformFuwuItemCodeService.obtainVipFlag(previousOrderSearch.getItemCode(), platformId, appName);
				if (vipFlag == null) {
					LOGGER.logWarn(sellerNick, platformId, "platformFuwuItemCodeConfig 无对应vipFlag配置, appName => "
							+ appName + ", itemCode => " + previousOrderSearch.getItemCode());
					return vasGetResult;
				}
				vasGetResult = VasGetResult.of(vipFlag, previousOrderSearch.getOrderCycleEnd());
				vasGetResult.setApiCode(apiCode);
			}
		}
		return vasGetResult;
	}

	private SellerVasSubscribeGetResponse vasSubscribeGet(String sellerNick, String articleCode, String platformId, String appName){
		SellerVasSubscribeGetRequest sellerVasSubscribeGetRequest = new SellerVasSubscribeGetRequest();
		sellerVasSubscribeGetRequest.setSellerNick(sellerNick);
		sellerVasSubscribeGetRequest.setArticleCode(articleCode);
		return appstoreService
				.vasSubscribeGet(sellerVasSubscribeGetRequest, platformId, appName);
	}

	/**
	 * 调用 vas.order.serach 接口获取用户的订单记录
	 * 错误时返回 new SellerVasOrderSearchResponse
	 *
	 * @param sellerNick           用户Nick
	 * @param defaultOrderCycleEnd 默认订购到期时间
	 * @param platformId           平台id
	 * @param appName              应用名
	 * @return
	 */
	private SellerVasOrderSearchResponse getAppStoreVasOrderSearch(String sellerNick, LocalDateTime defaultOrderCycleEnd, String platformId, String appName) {
		String articleCode = articleCodeConfig.getArticleCode(platformId, appName);
		SellerVasOrderSearchResponse response =
				vasOrderSearch(sellerNick, articleCode, defaultOrderCycleEnd, platformId, appName);
		CodeEnum successFlag = response.hasSuccess();
		if (LOGGER.isDebugEnabled()) {
			LOGGER.logDebug(sellerNick, "-", "hasSuccess返回结果为:" + successFlag);
		}
		if (CodeEnum.SUCCESS.equals(successFlag)) {
			if (CollectionUtils.isNotEmpty(response.getArticleBizOrders()) && response.getArticleBizOrders().size() > 1) {
				LOGGER.logInfo("查询vas订单记录结果多条，入参:[nick:{"+sellerNick+"},articledCode:{"+articleCode+"},cycleEndDate:{"+DateUtil.parseDateTimeFormat(defaultOrderCycleEnd,DateUtil.FORMATTER_DATETIME)+"}]");
			}
			return response;
		} else {
			LOGGER.logInfo("查询vas订单记录失败，出参：" + JSON.toJSONString(response));
			return new SellerVasOrderSearchResponse();
		}
	}

	private SellerVasOrderSearchResponse vasOrderSearch(String sellerNick, String articleCode,
														LocalDateTime defaultOrderCycleEnd, String platformId, String appName){
		SellerVasOrderSearchRequest vasOrderSearchRequest = new SellerVasOrderSearchRequest();
		vasOrderSearchRequest.setArticleCode(articleCode);
		vasOrderSearchRequest.setNick(sellerNick);
		vasOrderSearchRequest.setEndCreated(DateUtil.convertLocalDateTimetoDate(defaultOrderCycleEnd));
		return appstoreService
				.vasOrderSearch(vasOrderSearchRequest, platformId, appName);
	}
}
