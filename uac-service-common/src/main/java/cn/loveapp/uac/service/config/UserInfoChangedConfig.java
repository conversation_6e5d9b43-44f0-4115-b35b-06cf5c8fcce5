package cn.loveapp.uac.service.config;

import cn.loveapp.uac.proto.event.EventTypeEnum;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.google.common.collect.Maps;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

/**
 * 用户信息变更消息通知配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "uac.event.subscribe")
public class UserInfoChangedConfig extends AbstractConfig {

    /**
     * 消息转发配置
     */
    protected Map<EventTypeEnum, List<Config>> configs = Maps.newHashMap();

    /**
     * 消息转发配置
     */
    @Data
    public static class Config {
        /**
         * 转发的目标消息topic
         */
        private String topic;

        /**
         * 转发的目标消息tag
         */
        private String tag = "*";
    }

    @ApolloConfigChangeListener(value = {"uac-pretest", "uac-service", "application"})
    protected void configChangeListener(ConfigChangeEvent changeEvent) {
        super.configChangeListener(changeEvent);
    }
}
