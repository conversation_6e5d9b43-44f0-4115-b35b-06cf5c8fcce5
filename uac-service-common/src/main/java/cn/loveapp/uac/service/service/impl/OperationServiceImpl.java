package cn.loveapp.uac.service.service.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.code.taobao.ApiCodeConstant.CodeEnum;
import cn.loveapp.uac.common.config.app.ArticleCodeConfig;
import cn.loveapp.uac.common.config.cache.VasGetResult;
import cn.loveapp.uac.common.constant.PromotionActFlag;
import cn.loveapp.uac.common.dao.redis.repository.OperationManageRedisRepository;
import cn.loveapp.uac.common.entity.PromotionActivity;
import cn.loveapp.uac.common.platform.api.AppStoreService;
import cn.loveapp.uac.common.service.PlatformFuwuItemCodeService;
import cn.loveapp.uac.common.utils.DateUtil;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.db.common.repository.PromotionActivityRepository;
import cn.loveapp.uac.entity.LevelCycleEndTime;
import cn.loveapp.uac.proto.SubscribeUserMessageRequestProto;
import cn.loveapp.uac.proto.event.UserChangedEvent;
import cn.loveapp.uac.service.base.BaseOperationService;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.service.config.ActivityConfig;
import cn.loveapp.uac.service.event.SubscribeUserMessageEventHandler;
import cn.loveapp.uac.service.service.ActivityService;
import cn.loveapp.uac.service.service.OperationService;
import cn.loveapp.uac.service.service.SellerOrderSearchService;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: uac-service-group
 * @description: OperationServiceImpl
 * @author: Jason
 * @create: 2020-03-13 11:11
 **/
@Service
public class OperationServiceImpl extends BaseOperationService implements OperationService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OperationServiceImpl.class);

	@Autowired
	private ArticleCodeConfig articleCodeConfig;

	@Autowired
	private ActivityConfig activityConfig;

	@Autowired
	private PlatformFuwuItemCodeService platformFuwuItemCodeService;

	@Autowired
	private SubscribeUserMessageEventHandler subscribeUserMessageEventHandler;

	@Autowired
	private ActivityService activityService;
	@Autowired
	private SellerOrderSearchService sellerOrderSearchService;
	@Autowired
	private AppStoreService appStoreFuWuService;
	@Autowired
	private OperationManageRedisRepository operationManageRedisRepository;

    @Autowired
    private PromotionActivityRepository promotionActivityRepository;


	/**
	 * 计算最终用户时间和等级
	 */
	@Override
	public UserChangedEvent calculateOrderCycleEndAndLevel(UserInfoBo userInfoBo, String platformId, String appName) {
		return super.calculateOrderCycleEndAndLevel(userInfoBo, platformId, appName);
	}

	/**
	 * 判断是否是自动续费用户
	 *
	 * 默认不使用cache
	 *
	 * @param sellerNick
	 * @param articleCode
	 * @param fuwuItemCode
	 * @return
	 */
	@Override
	public CodeEnum hasAutoRenew(String sellerNick, String articleCode, String fuwuItemCode, Boolean userCache, String platformId, String appName) {
		return hasAutoRenew(sellerNick, articleCode, fuwuItemCode, appStoreFuWuService, userCache, platformId, appName);
	}

	@Override
	public boolean hasAutoMonth(String sellerNick, String platformId, String appName) {
		return super.hasAutoMonth(sellerNick, operationManageRedisRepository, platformId, appName);
	}

	@Override
	public void calculateLevel(UserInfoBo userInfoBo, OperationUserInfo operationUserInfo, String platformId, String appName) {
		String sellerNick = userInfoBo.getSellerNick();

		VasGetResult vasGetResult = sellerOrderSearchService.getVasGetResult(userInfoBo, platformId, appName);
		userInfoBo.setArticleUserSubscribe(vasGetResult.getArticleUserSubscribe());
		Integer level = vasGetResult.getLevel();
		LocalDateTime orderCycleEnd = vasGetResult.getOrderCycleEnd();
		userInfoBo.setLevel(level);
		operationUserInfo.setSellerNick(sellerNick);
		operationUserInfo.setLevel(level);
		operationUserInfo.setOrderCycleEnd(orderCycleEnd);
		//将查询出来的订购关系结束时间带到sellerService去 做订购订单查询条件
		userInfoBo.setVasSubscibeGetDeadline(orderCycleEnd);
		/*
		 * 如果发现订单的订购时间为空,取服务市场的到期时间
		 */
		if (Objects.isNull(userInfoBo.getOriginalIdentityOrderCycleEnd()) && CodeEnum.SUCCESS.equals(vasGetResult.getApiCode())) {
			userInfoBo.setOriginalIdentityOrderCycleEnd(orderCycleEnd);
		}
		if (CodeEnum.SUCCESS.equals(vasGetResult.getApiCode())) {
			userInfoBo.setFuwuOrderCycleEnd(operationUserInfo.getOrderCycleEnd());
			userInfoBo.setFuwuVipFlag(operationUserInfo.getLevel());
		}
		if(LOGGER.isDebugEnabled()) {
		    LOGGER.logDebug(sellerNick, "-", "从vas处得取结果," + operationUserInfo.toString());
		}

		if (activityConfig.getNeedNormallyCalculateLevels().contains(operationUserInfo.getLevel())) {
			// 指定的等级的用户需要计算 自动续费&赠送 信息
			normallyCalculate(userInfoBo, operationUserInfo, sellerNick, platformId, appName);

            //剩余的小于当前版本的其他版本的（已经拆分后的赠送信息）
            List<LevelCycleEndTime> levelCycleEndTimeList = lessCurrentLevelPromotionActivity(sellerNick, operationUserInfo.getLevel(), platformId, appName);
			// 当前赠送的版本比用户订购高，并且用户订购的高级版
			if (!Objects.equals(vasGetResult.getLevel(), operationUserInfo.getLevel())
                && vasGetResult.getLevel() > 0 && vasGetResult.getOrderCycleEnd() != null) {
                LocalDateTime now = DateUtil.toDateTimeZeroHourMinusSecond(LocalDateTime.now());
                LocalDateTime vasEnd = vasGetResult.getOrderCycleEnd().withHour(0).withMinute(0).withSecond(0).withNano(0);
                long vasDays = ChronoUnit.DAYS.between(now, vasEnd);
				if (vasDays > 0) {
					LevelCycleEndTime levelCycleEndTime = new LevelCycleEndTime();
					levelCycleEndTime.setLevel(vasGetResult.getLevel());
					levelCycleEndTime.setCycleDays((int) vasDays);
					levelCycleEndTimeList.add(levelCycleEndTime);
				}
			}

            // 将相同vip等级的天数聚合相加
            List<LevelCycleEndTime> agglevelCycleEndTimeList = levelCycleEndTimeList.stream()
                .collect(Collectors.groupingBy(LevelCycleEndTime::getLevel,
                    Collectors.summingInt(LevelCycleEndTime::getCycleDays)))
                .entrySet().stream()
                .map(entry -> new LevelCycleEndTime(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());

            userInfoBo.setLowerSentLevelList(agglevelCycleEndTimeList);

        }
		if(LOGGER.isDebugEnabled()) {
		    LOGGER.logDebug(sellerNick, "-", "最终operationUserInfo结果信息为:<"+operationUserInfo.toString()+">");
		}
	}

    /**
     * 小于当前版本的赠送数据
     * 大于等于当前版本的，都会计算到当前版本身上，所以只需要返回给前端小于当前版本的数据
     * @param sellerNick
     * @param platformId
     * @param appName
     */
    private List<LevelCycleEndTime> lessCurrentLevelPromotionActivity(String sellerNick, Integer currentLevel, String platformId, String appName) {
        //因为存在版本6大于版本8的情况，所以不能直接利用sql db.level < currentLevel查询,需要全部查询出来，在代码中执行compareLevel逻辑得出比当前小的赠送记录,和计算拆分时逻辑保持一致
        List<PromotionActivity> promotionActivityTrades = promotionActivityRepository.aggregationActCycleAndOptimeBySellerNickAndUnused(sellerNick, PromotionActivity.UNUSED, platformId, appName);
        List<LevelCycleEndTime> levelCycleEndTimeList = new ArrayList<>();
        for (PromotionActivity activity : promotionActivityTrades) {
            int activityLevel = PromotionActFlag.of(activity.getActflag()).level;
            if (activityService.compareLevel(activityLevel, currentLevel) < 0) {
                //小于当前版本
                levelCycleEndTimeList.add(new LevelCycleEndTime(activityLevel, activity.getActCycle()));
            }
        }
        return levelCycleEndTimeList;
    }


	/**
	 * 非特殊形态订购的计算逻辑
	 * @param userInfoBo
	 * @param operationUserInfo
	 * @param sellerNick
	 * @param platformId
	 * @param appName
	 */
	protected void normallyCalculate(UserInfoBo userInfoBo, OperationUserInfo operationUserInfo, String sellerNick, String platformId, String appName) {
		// 自动续费
		autoRenewCalculate(userInfoBo, operationUserInfo, sellerNick, platformId, appName);

		LocalDateTime orderCycleEndZero = DateUtil.toDateTimeZeroHourMinusSecond(operationUserInfo.getOrderCycleEnd());
		operationUserInfo.setOrderCycleEnd(orderCycleEndZero);
		if(LOGGER.isDebugEnabled()) {
		    LOGGER.logDebug(sellerNick, "-", "operationUserInfo输入信息为:<"+ operationUserInfo.toString()+">");
		}

		Pair<Integer, LocalDateTime> calculateResult = activityService.calculatePromotionActivityLevelAndCycleEndTime(
			sellerNick, operationUserInfo.getLevel(), operationUserInfo.getOrderCycleEnd(), platformId, appName);
		operationUserInfo.setLevel(calculateResult.getLeft());
		operationUserInfo.setOrderCycleEnd(calculateResult.getRight());
	}

	/**
	 * 自动续费相关计算
	 *
	 * @param userInfoBo
	 * @param operationUserInfo
	 * @param sellerNick
	 * @param platformId
	 * @param appName
	 * @return
	 */
	private String autoRenewCalculate(UserInfoBo userInfoBo, OperationUserInfo operationUserInfo, String sellerNick,
		String platformId, String appName) {
		if (operationUserInfo.getLevel().equals(UserProductInfo.LEVEL_ONE)
			&& userInfoBo.getOriginalIdentityLevel().equals(UserProductInfo.LEVEL_THREE)) {
			// 这里特殊处理一下自动续费的Level
			operationUserInfo.setLevel(userInfoBo.getOriginalIdentityLevel());
		}

		//判断自动续费用户是否手动关闭了自动续费服务
		List<Integer> levelList = Arrays.asList(userInfoBo.getLevel(), operationUserInfo.getLevel());
		boolean hasAutoSeller = levelList.contains(UserProductInfo.LEVEL_THREE);
		boolean hasAutoActivitySeller = hasAutoMonth(sellerNick, platformId, appName) && userInfoBo.getLevel().equals(UserProductInfo.LEVEL_ONE);
		if(LOGGER.isDebugEnabled()) {
		    LOGGER.logDebug(sellerNick, "-", "hasAutoSeller结果为:<"+hasAutoSeller+">");
		}
		if(LOGGER.isDebugEnabled()) {
		    LOGGER.logDebug(sellerNick, "-", "hasAutoActivitySeller结果为:<"+hasAutoActivitySeller+">");
		}
		String itemCode = platformFuwuItemCodeService.obtainAutoRenewItemCode(platformId, appName);
		if (hasAutoSeller) {
			CodeEnum autoRenewCode = hasAutoRenew(sellerNick, articleCodeConfig.getArticleCode(platformId, appName), itemCode, true,
				platformId, appName);
			if (CodeEnum.SUCCESS.equals(autoRenewCode)) {
				operationUserInfo.setLevel(UserProductInfo.LEVEL_THREE);
			}
			if (CodeEnum.ERROR.equals(autoRenewCode)) {
				operationUserInfo.setLevel(UserProductInfo.LEVEL_ONE);
			}

		}
		if (hasAutoActivitySeller) {
			CodeEnum autoRenewCode = hasAutoRenew(sellerNick, articleCodeConfig.getArticleCode(platformId, appName), itemCode,
				false, platformId, appName);
			if (CodeEnum.SUCCESS.equals(autoRenewCode)) {
				operationUserInfo.setLevel(UserProductInfo.LEVEL_THREE);
				if(LOGGER.isDebugEnabled()) {
				    LOGGER.logDebug(sellerNick, appName, "waiting write lend table");
				}
				//调活动去
				activityService.waitLendDone(sellerNick, appName);
				if(LOGGER.isDebugEnabled()) {
				    LOGGER.logDebug(sellerNick, appName, "write lend table success");
				}
			}
		}
		return itemCode;
	}

	@Override
    public void eventHandle(UserInfoBo userInfoBo, UserChangedEvent event) {
        try {
            subscribeUserMessageEventHandler.eventHandle(userInfoBo, "identityChanged", SubscribeUserMessageRequestProto.IDENTITY_TYPE);
            if (event != null) {
                subscribeUserMessageEventHandler.userChangedEventHandle(userInfoBo, event);
            }
        } catch (Exception e) {
            LOGGER.logError(userInfoBo.getSellerNick(), "-", "用户变更事件处理异常", e);
        }
    }

}
