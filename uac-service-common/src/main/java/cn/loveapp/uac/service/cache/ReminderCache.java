package cn.loveapp.uac.service.cache;

import cn.loveapp.common.utils.LoggerHelper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import org.springframework.stereotype.Component;

/**
 * @program: uac-service-group
 * @description: ReminderCache
 * @author: Jason
 * @create: 2020-05-28 12:00
 **/
@Component
public class ReminderCache {
	private static LoggerHelper LOGGER = LoggerHelper.getLogger(ReminderCache.class);

	private Cache<String, Integer> cache = CacheBuilder.newBuilder().maximumSize(200000L).expireAfterWrite(1, TimeUnit.DAYS).build();

	/**
	 * 获取cache值，如果获取不到返回NEED_REMINDER
	 * @param sellerNick
	 * @return
	 */
	public int getReminderValue(String sellerNick) {
		String k = initKey(sellerNick);
		try {
			return cache.get(k, this::loadReminderValue);
		} catch (ExecutionException e) {
			return NEED_REMINDER;
		}
	}

	/**
	 * 写入对应的缓存值
	 * @param sellerNick
	 * @param value
	 */
	public void putReminderValue(String sellerNick, Integer value) {
		String k = initKey(sellerNick);
		cache.put(k, value);
	}

	public int loadReminderValue() {
		return NEED_REMINDER;
	}

	/**
	 * 初始化key
	 * @param sellerNick
	 * @return
	 */
	public String initKey(String sellerNick) {
		return sellerNick;
	}

	/**
	 * 需要提醒
	 */
	public static final Integer NEED_REMINDER = 1;

	/**
	 * 当天不需要提醒
	 */
	public static final Integer NO_NEED_REMINDER_ON_DAY = 2;

	/**
	 * 不需要提醒
	 */
	public static final Integer NO_NEED_REMINDER = 0;

}
