package cn.loveapp.uac.service.service;

import cn.loveapp.uac.common.api.response.SellerVasOrderSearchResponse;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.config.cache.VasGetResult;

import java.time.LocalDateTime;

/**
 * @program: uac-service-group
 * @description: SellerOrderSearchService
 * @author: <PERSON>
 * @create: 2020-09-14 13:46
 **/
public interface SellerOrderSearchService {

	VasGetResult getVasGetResult(UserInfoBo userInfoBo, String platformId, String appName);


	/*
	 * 获取 vas订单详情
	 * <AUTHOR>
	 * @date 2021/10/9 14:03
	 */
	SellerVasOrderSearchResponse getVasOrderSearchResult(UserInfoBo userInfoBo, String platformId, LocalDateTime orderCycleEnd, String appName);

}
