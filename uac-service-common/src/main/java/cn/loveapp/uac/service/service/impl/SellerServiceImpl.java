package cn.loveapp.uac.service.service.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.code.ApiCode;
import cn.loveapp.uac.common.api.domain.SellerArticleBizOrder;
import cn.loveapp.uac.common.api.domain.SellerArticleSub;
import cn.loveapp.uac.common.api.request.SellerVasSubscSearchRequest;
import cn.loveapp.uac.common.api.response.SellerVasOrderSearchResponse;
import cn.loveapp.uac.common.api.response.SellerVasSubscSearchResponse;
import cn.loveapp.uac.common.bo.LoginUserInfoBo;
import cn.loveapp.uac.common.bo.UserAutoRenewBo;
import cn.loveapp.uac.common.bo.UserBo;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.bo.UserInfoBo.IdentityProperty;
import cn.loveapp.uac.common.config.TradeConfig;
import cn.loveapp.uac.common.config.app.ArticleCodeConfig;
import cn.loveapp.uac.common.dao.redis.repository.UserManageRedisRepositoryHashRedisRepository;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.common.entity.redis.UserRedisEntity;
import cn.loveapp.uac.common.exception.CacheWriteException;
import cn.loveapp.uac.common.exception.DbWriteException;
import cn.loveapp.uac.common.platform.api.AppStoreService;
import cn.loveapp.uac.common.platform.api.AuthService;
import cn.loveapp.uac.common.utils.DateUtil;
import cn.loveapp.uac.db.common.entity.OrderSearch;
import cn.loveapp.uac.db.common.repository.OrderSearchRepository;
import cn.loveapp.uac.db.common.repository.UserRepository;
import cn.loveapp.uac.exception.UserException;
import cn.loveapp.uac.proto.event.UserChangedEvent;
import cn.loveapp.uac.response.CallbackResponse;
import cn.loveapp.uac.response.UserCacheInfoResponse;
import cn.loveapp.uac.response.UserInfoResponse;
import cn.loveapp.uac.service.base.BaseSellerService;
import cn.loveapp.uac.service.cache.ReminderCache;
import cn.loveapp.uac.service.service.OAuthDecorationService;
import cn.loveapp.uac.service.service.OperationService;
import cn.loveapp.uac.service.service.SellerOrderSearchService;
import cn.loveapp.uac.service.service.SellerService;
import cn.loveapp.uac.utils.UserCacheUtils;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: uac-service-group
 * @description: SellerServiceImpl
 * @author: Jason
 * @create: 2020-03-13 11:01
 **/
@Service
public class SellerServiceImpl extends BaseSellerService implements SellerService {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(SellerServiceImpl.class);

    @Value("${uac.white.list:}")
	private List<String> whiteList;
	@Value("${uac.test.user.level:}")
	private Integer testUserLevel;
	@Value("${uac.test.user.cycle.end:}")
	private String testUserCycleEnd;

	@Autowired
	private UserRepository userRepository;
	@Autowired
	private UserManageRedisRepositoryHashRedisRepository userManageRedisRepository;

	@Autowired
	@Lazy
	private OAuthDecorationService oAuthDecorationService;
	@Autowired
	private AppStoreService appStoreFuWuService;
	@Autowired
	private OperationService operationService;
	@Autowired
	private OrderSearchRepository orderSearchRepository;
	@Autowired
	private SellerOrderSearchService sellerOrderSearchService;
	@Autowired
	private ArticleCodeConfig articleCodeConfig;

    @Autowired
    private TradeConfig tradeConfig;



	public SellerServiceImpl(ReminderCache reminderCache, AuthService authService) {
		super(reminderCache, authService);
	}

	/**
	 * 获取用户属性
	 */
	@Override
	public UserInfoResponse login(UserInfoBo userInfoBo, String platformId, String appName) throws UserException{
		boolean checkUserInfoResult = checkUserInfo(userInfoBo, Boolean.TRUE, userManageRedisRepository, userRepository);
		if (!checkUserInfoResult) {
			LOGGER.logWarn("用户不存在: " + JSON.toJSONString(userInfoBo));
			userInfoBo.setHasExist(Boolean.FALSE);
			userInfoBo.setLevel(UserProductInfo.LEVEL_ZERO);
			userInfoBo.setOriginalIdentityLevel(UserProductInfo.LEVEL_ZERO);
			IdentityProperty identityProperty = new IdentityProperty();
			identityProperty.setHasNeedAuth(true);
			userInfoBo.setIdentityProperty(identityProperty);
			return createUserInfo(userInfoBo);
		}
		/**
		 * 获取最终的role+h - version
		 */
        UserChangedEvent userChangedEvent = operationService.calculateOrderCycleEndAndLevel(userInfoBo, platformId, appName);
        whiteListValidator(userInfoBo, whiteList, testUserLevel, testUserCycleEnd);

		//TODO 保存商家服务购买记录  只有淘宝商家需要进行保存
//		if (CommonPlatformConstants.PLATFORM_TAO.equals(platformId)) {
//			insertOrUpdateOrderSearch(userInfoBo, platformId, appName);
//		}
		IdentityProperty identityProperty = userInfoBo.getIdentityProperty();

		UserProductInfo userProductinfo = new UserProductInfo();
		userProductinfo.initDefault();
		userProductinfo.setRoleid(userInfoBo.getSellerRole());
		userProductinfo.setNick(userInfoBo.getSellerNick());
		userProductinfo.setVipflag(userInfoBo.getLevel());
		userProductinfo.setOrderCycleEnd(userInfoBo.getOrderCycleEnd());
		userProductinfo.setUserId(userInfoBo.getSellerId());
		userProductinfo.setUserIdStr(userInfoBo.getSellerId());
		userProductinfo.setIsNeedauth(BooleanUtils.toInteger(identityProperty.getHasNeedAuth()));

        if (tradeConfig.getProfessionalVipFlagList().contains(userInfoBo.getLevel())) {
            userProductinfo.setProfessionalOrderCycleEnd(userInfoBo.getOrderCycleEnd());
        }

		UserBo userBo = new UserBo(platformId, appName, userInfoBo.getSellerNick(), userProductinfo);
		try {
			refreshUserInfoCacheAndTable(userBo, userManageRedisRepository, userRepository);
		} catch (DbWriteException | CacheWriteException e) {
			LOGGER.logError(e.getMessage(), e);
		} finally {
            operationService.eventHandle(userInfoBo, userChangedEvent);
        }
		return createUserInfo(userInfoBo);
	}

	/**
	 * 新增或更新用户的订购记录
	 * @param userInfoBo
	 * @param platformId
	 * @param appName
	 */
	@Override
	public void insertOrUpdateOrderSearch(UserInfoBo userInfoBo, String platformId, String appName){
		try {
			//根据到期时间查询订单列表
			//先根据nick+item_code+order_cycle_end查询库中是否有记录，有则不查询淘宝api
			String articleCode = articleCodeConfig.getArticleCode(platformId, appName);
			OrderSearch orderSearch = orderSearchRepository.
					queryBySellerNickAndArticleCodeAndItemCodeAndOrderCycleEnd(userInfoBo.getSellerNick(),
							articleCode, userInfoBo.getArticleUserSubscribe() == null ? null : userInfoBo.getArticleUserSubscribe().getItemCode(), userInfoBo.getVasSubscibeGetDeadline(), platformId, appName);
			LOGGER.logInfo("查询数据库用户订购记录出参：" + JSON.toJSONString(orderSearch));
			if (orderSearch == null) {
				SellerVasOrderSearchResponse response = sellerOrderSearchService.
						getVasOrderSearchResult(userInfoBo, platformId, userInfoBo.getVasSubscibeGetDeadline(), appName);
				if (CollectionUtils.isNotEmpty(response.getArticleBizOrders())) {
					SellerArticleBizOrder responseOrder = response.getArticleBizOrders().get(0);
					//新增订购记录
					orderSearch = new OrderSearch();
					BeanUtils.copyProperties(responseOrder, orderSearch);
					orderSearchRepository.insert(orderSearch, platformId, appName);
				}
			}
		} catch (Exception e) {
			LOGGER.logInfo("插入用户订购记录时发生错误："+e.getMessage());
		}
	}

	@Override
	public int batchUpdateUserCacheInfo(List<UserInfoBo> userInfoBoList, String hKey, String value) {
		int updateNum = 0;
		for (UserInfoBo userInfoBo : userInfoBoList) {
			boolean checkUserInfoResult = checkUserInfo(userInfoBo, false, userManageRedisRepository, userRepository);
			if (!checkUserInfoResult) {
				LOGGER.logError(userInfoBo.getSellerNick(), userInfoBo.getSellerId(), "用户不存在, 跳过");
				continue;
			}
			String collection = userManageRedisRepository.initCollection(userInfoBo.getSellerNick(), userInfoBo.getSellerId(), userInfoBo.getPlatformId(), userInfoBo.getAppType());
			if (StringUtils.isNotEmpty(collection)) {
				value = StringUtils.trimToEmpty(value);
				userManageRedisRepository.put(collection, hKey, value, userInfoBo.getAppType());
				updateNum++;
			} else {
				LOGGER.logError(userInfoBo.getSellerNick(), userInfoBo.getSellerId(), "找不到缓存key, 跳过");
			}
		}
		return updateNum;
	}

	@Override
	public List<UserCacheInfoResponse> batchGetUserCacheInfo(List<UserInfoBo> userInfoBoList, String hKey) {
		List<UserCacheInfoResponse> cacheInfoResponseList = Lists.newArrayList();
		for (UserInfoBo userInfoBo : userInfoBoList) {
			String collection = userManageRedisRepository.initCollection(userInfoBo.getSellerNick(), userInfoBo.getSellerId(), userInfoBo.getPlatformId(), userInfoBo.getAppType());
			if(StringUtils.isEmpty(collection) && StringUtils.isAnyEmpty(userInfoBo.getSellerNick(), userInfoBo.getSellerId())) {
				UserProductInfo userProductInfo = getUserInfoByDb(new UserBo(userInfoBo), userRepository);
				if(userProductInfo == null) {
					LOGGER.logError(userInfoBo.getSellerNick(), userInfoBo.getSellerId(), "用户不存在, 跳过");
					continue;
				}
				userInfoBo.setSellerNick(userProductInfo.getNick());
				userInfoBo.setSellerId(userProductInfo.getUserId());
				collection = userManageRedisRepository.initCollection(userInfoBo.getSellerNick(), userInfoBo.getSellerId(), userInfoBo.getPlatformId(), userInfoBo.getAppType());
			}
			if (StringUtils.isNotEmpty(collection)) {
				String value;
                // TODO: 暂时兼容获取 shops_tag 时追加 ayMultiTags 的内容, 后面逐步废弃 shops_tag
                // {@link cn.loveapp.uac.utils.UacRpcUtils#batchGetUserCacheInfo}
                if(UserCacheUtils.HKEY_SHOPS_TAG.equals(hKey)) {
                    List<String> values = userManageRedisRepository.find(collection, Lists.newArrayList(hKey, UserCacheUtils.HKEY_AY_MULTI_TAGS), userInfoBo.getAppType());
                    value = StringUtils.defaultIfEmpty(values.stream().filter(Objects::nonNull).collect(Collectors.joining(",")), null);
                } else {
                    value = userManageRedisRepository.find(collection, hKey, userInfoBo.getAppType());
                }
				UserCacheInfoResponse cacheInfoResponse = new UserCacheInfoResponse();
				cacheInfoResponse.setSellerNick(userInfoBo.getSellerNick());
				cacheInfoResponse.setSellerId(userInfoBo.getSellerId());
				cacheInfoResponse.setPlatformId(userInfoBo.getPlatformId());
				cacheInfoResponse.setAppName(userInfoBo.getAppType());
				cacheInfoResponse.setCacheValue(value);
				cacheInfoResponseList.add(cacheInfoResponse);
			} else {
				LOGGER.logError(userInfoBo.getSellerNick(), userInfoBo.getSellerId(), "找不到缓存key, 跳过");
			}
		}
		return cacheInfoResponseList;
	}

	/**
	 * 重建用户关键信息
	 */
	@Override
	public UserInfoResponse rebuildUserInfo(UserInfoBo userInfoBo, String platformId, String appName) throws UserException {
		String sellerNick = userInfoBo.getSellerNick();
		UserBo userBo = new UserBo(userInfoBo);
		userBo.setHasReadTag(Boolean.TRUE);
        UserProductInfo userProductInfo = super.getUserInfoByDb(userBo, userRepository);
		if (Objects.isNull(userProductInfo)) {
			throw new UserException(ApiCode.NO_EXIST_USER.code(), ApiCode.NO_EXIST_USER.message());
		}
		if (StringUtils.isEmpty(sellerNick)) {
			sellerNick = userProductInfo.getNick();
			userBo.setSellerNick(userProductInfo.getNick());
		}
		userBo.setUserProductInfo(userProductInfo);
		try {
			refreshUserInfoCache(userBo, userManageRedisRepository);
			userInfoBo.setSellerNick(sellerNick);
			userInfoBo.setLevel(userProductInfo.getVipflag());
			userInfoBo.setAuthDeadLine(userProductInfo.getW1Deadline());
			userInfoBo.setAccessToken(userProductInfo.getTopsessionkey());
			userInfoBo.setOrderCycleEnd(userProductInfo.getOrderCycleEnd());
			userInfoBo.setCorpId(userProductInfo.getCorpId());
			userInfoBo.setTag(userProductInfo.getTag());
			userInfoBo.setAyMultiTags(userProductInfo.getAyMultiTags());
			IdentityProperty identityProperty = new IdentityProperty();
			identityProperty.setHasNeedAuth(BooleanUtils.toBooleanObject(userProductInfo.getIsNeedauth()));
			userInfoBo.setIdentityProperty(identityProperty);
			return createUserInfo(userInfoBo);
		} catch (CacheWriteException cache) {
			LOGGER.logError(sellerNick, "-", "当前sellerNick is " + sellerNick + ", 缓存写入失败" + cache.getMessage(), cache);
		} catch (Exception ioe) {
			LOGGER.logError(sellerNick, "-", "当前sellerNick is " + sellerNick + ", 请求失败, 原因为: " + ioe.getMessage(), ioe);
		}
		return null;
	}

	/**
	 * 登录用户信息
	 */
	@Override
	public UserInfoResponse quickLogin(LoginUserInfoBo loginUserInfoBo, String platformId, String appName)
		throws DbWriteException {
		return super.login(loginUserInfoBo, userManageRedisRepository, userRepository);
	}

	/**
	 * 获取用户属性
	 */
	@Override
	public UserInfoResponse getUserInfo(UserInfoBo userInfoBo, String platformId, String appName)
		throws UserException {
		return super.getUserInfo(userInfoBo, userManageRedisRepository, userRepository);
	}

	@Override
	public UserInfoResponse getUserInfo(UserInfoBo userInfoBo, String memberId, String platformId, String appName) throws UserException {
        userInfoBo.setMemberId(memberId);
		return getUserInfo(userInfoBo, platformId, appName);
	}

	@Override
	public UserProductInfo getUserFullInfo(UserInfoBo userInfoBo, boolean isCheckAndRefreshToken, String platformId, String appName)
		throws Exception {
        UserBo userBo = new UserBo(userInfoBo);
		UserProductInfo userProductInfo = getUserInfoByDb(userBo, userRepository);

		if (null == userProductInfo) {
			throw new UserException(ApiCode.NO_EXIST_USER.code(), ApiCode.NO_EXIST_USER.message());
		}

		IdentityProperty identityProperty = new IdentityProperty();
		converUserProductInfo2UserInfoBo(userInfoBo, userProductInfo, identityProperty);
		userInfoBo.setIdentityProperty(identityProperty);

		if (isCheckAndRefreshToken) {
			// 校验授权时间，过期则自动刷新
			boolean isRefresh = checkAndRefreshToken(userInfoBo, platformId, appName);
			if (isRefresh) {
				// 刷新成功，重新获取用户信息
				userProductInfo = getUserInfoByDb(userBo, userRepository);
			} else {
				userProductInfo.setIsNeedauth(BooleanUtils.toInteger(userInfoBo.getOriginalHasNeedAuth()));
			}

			String accessToken = userProductInfo.getTopsessionkey();
			String refreshToken = userProductInfo.getToprefreshkey();
			accessToken = authService.decryptToken(accessToken, platformId, appName);
			try {
				//忽略refreshToken错误
				refreshToken = authService.decryptToken(refreshToken, platformId, appName);
			} catch (Exception e) {
				LOGGER.logError(userInfoBo.getSellerNick(), "", "解密refreshToken错误: " + e.getMessage(), e);
			}
			userProductInfo.setTopsessionkey(accessToken);
			userProductInfo.setToprefreshkey(refreshToken);
		}

		// 获取上次订购授权信息
		UserRedisEntity userRedisEntity = userManageRedisRepository.getLastAuthInfo(userInfoBo.getSellerNick(), userInfoBo.getSellerId(), platformId, appName);
		if (userRedisEntity != null) {
			userProductInfo.setLastOrderCycleEnd(userRedisEntity.getlastOrderCycleEnd());
			if (CommonPlatformConstants.PLATFORM_TAO.equals(platformId) && CommonAppConstants.APP_TRADE.equals(appName)) {
				userProductInfo.setLastW1Deadline(userRedisEntity.getLastW1Deadline());
			} else {
				userProductInfo.setLastAuthDeadLine(userRedisEntity.getlastAuthDeadline());
			}
		} else {
			LOGGER.logInfo(userInfoBo.getSellerNick(), null, "获取上一次订购信息为空");
		}
		return userProductInfo;
	}

	/**
	 * 刷新用户信息
	 */
	@Override
	public Boolean refreshUserInfo(UserInfoBo newUserInfoBo, String platformId, String appName) {
		String sellerNick = newUserInfoBo.getSellerNick();
		UserInfoBo oldUserInfoBo = new UserInfoBo(platformId, appName, sellerNick);
		boolean checkUserInfo = checkUserInfoDb(oldUserInfoBo, Boolean.FALSE, userRepository);
		if (!checkUserInfo) {
			return false;
		}
		Integer level = newUserInfoBo.getLevel();
		/**
		 * 比对新旧userinfo里的值
		 * 1. vipflag
		 * 2. orderCycleEndTime
		 * 3. ipAddress
		 * 4. role
		 * 5. point
		 * */
		if (Objects.nonNull(newUserInfoBo.getOrderCycleEnd()) && !newUserInfoBo.getOrderCycleEnd().equals(oldUserInfoBo.getOrderCycleEnd())) {
			if (newUserInfoBo.getOrderCycleEnd().isBefore(oldUserInfoBo.getOrderCycleEnd())) {
				LOGGER.logWarn(sellerNick, "-", "用户新旧订购时间异常, "
					+ "oldUserInfoBo is " + oldUserInfoBo + ", newUserInfoBo is " + newUserInfoBo);
				return false;
			}
		}

		if (Objects.nonNull(newUserInfoBo.getLevel()) && !newUserInfoBo.getLevel().equals(oldUserInfoBo.getLevel())) {
			LOGGER.logInfo(sellerNick, "-", "用户新旧vipfalg等级变更, 需要检查一下是否存在问题, "
				+ "oldUserInfoBo is " + oldUserInfoBo + ", newUserInfoBo is " + newUserInfoBo);
		}

		UserProductInfo userProductinfo = new UserProductInfo();
		userProductinfo.setRoleid(newUserInfoBo.getSellerRole());
		userProductinfo.setNick(sellerNick);
		userProductinfo.setVipflag(newUserInfoBo.getLevel());
		userProductinfo.setLastipaddress(newUserInfoBo.getIpAddress());
		userProductinfo.setLastactivedt(newUserInfoBo.getLastActiveDateTime());
		userProductinfo.setOrderCycleEnd(newUserInfoBo.getOrderCycleEnd());
		if (BooleanUtils.isTrue(newUserInfoBo.getHasSilent())) {
			userProductinfo.setIsSilent(Boolean.TRUE);
			userProductinfo.setRevivalDate(DateUtil.currentDate());
		}
		userProductinfo.setLastPaidTime(newUserInfoBo.getLastPaidTime());
		userProductinfo.setLogincountPc(oldUserInfoBo.getCountPc());
		userProductinfo.setLogincountWw(oldUserInfoBo.getCountWw());
		userProductinfo.setLogincountMp(oldUserInfoBo.getCountMp());
		userProductinfo.putLastActivePlatform(newUserInfoBo.getLastActivePoint());
		userProductinfo.setUserId(oldUserInfoBo.getSellerId());
		userProductinfo.setUserIdStr(oldUserInfoBo.getSellerId());
		if(LOGGER.isDebugEnabled()){
			LOGGER.logDebug(sellerNick, "-", "用户重新写入db数据为:" + JSON.toJSONString(userProductinfo));
			LOGGER.logDebug(sellerNick, "-", "用户重新写入cache数据为:" + JSON.toJSONString(userProductinfo.toUserRedisEntity()));
		}
		UserBo userBo = new UserBo(platformId, appName, sellerNick, userProductinfo);

		try {
			refreshUserInfoCacheAndTable(userBo, userManageRedisRepository, userRepository);
		} catch (DbWriteException db) {
			LOGGER.logError(sellerNick, "-", "数据库写入失败: " + db.getMessage(), db);
		} catch (CacheWriteException cache) {
			LOGGER.logError(sellerNick, "-", "缓存写入失败: " + cache.getMessage(), cache);
			return Boolean.TRUE;
		}
		return true;
	}

	/**
	 * 获取token
	 */
	@Override
	public UserInfoResponse getAccessToken(UserInfoBo userInfoBo, String platformId, String appName) throws Exception {
		UserInfoResponse userInfo = new UserInfoResponse();
		boolean checkUserInfo = checkUserInfo(userInfoBo, Boolean.FALSE, userManageRedisRepository, userRepository);
		if (!checkUserInfo) {
			throw new UserException(ApiCode.NO_EXIST_USER.code(), ApiCode.NO_EXIST_USER.message());
		}
		// 刷新授权判断
		checkAndRefreshToken(userInfoBo, platformId, appName);
		userInfo.setSellerId(userInfoBo.getSellerId());
		userInfo.setSellerNick(userInfoBo.getSellerNick());
		userInfo.setHasNeedAuth(userInfoBo.getOriginalHasNeedAuth());
		userInfo.setPlatformId(platformId);
		userInfo.setAppName(appName);
		userInfo.setTopSession(userInfoBo.getAccessToken());
		userInfo.setSellerAppId(userInfoBo.getSellerAppId());
		userInfo.setSellerAppSecret(userInfoBo.getSellerAppSecret());
		userInfo.setAyMultiTags(userInfoBo.getAyMultiTags());
        userInfo.setShopId(userInfoBo.getShopId());
        userInfo.setShopCipher(userInfoBo.getShopCipher());
        userInfo.setIsAuthExcept(userInfoBo.getIsAuthExcept());
		return userInfo;
	}

	/**
	 * 判断是否需要刷新并重新授权
	 * @param userInfoBo
	 * @param platformId
	 * @param appName
	 * @return
	 * @throws Exception
	 */
	private boolean checkAndRefreshToken(UserInfoBo userInfoBo, String platformId, String appName) throws Exception{
		String sellerNick = userInfoBo.getSellerNick();
		String oldAccessToken = userInfoBo.getAccessToken();
		String refreshToken = userInfoBo.getRefreshToken();
		boolean isExpire = accessTokenExpireValidator(userInfoBo);
		boolean hasNeedAuth = false;
		// 是否刷新
		boolean isRefresh = false;
		String newAccessToken = null;

		// 先解密AppSecret
		if (StringUtils.isNotEmpty(userInfoBo.getSellerAppSecret())) {
			try {
				String sellerAppSecret = authService.decryptToken(userInfoBo.getSellerAppSecret(), platformId, appName);
				userInfoBo.setSellerAppSecret(sellerAppSecret);
			} catch (Exception e) {
				LOGGER.logError("appSecret 解密失败: " + e.getMessage(), e);
			}
		}

		if (isExpire || StringUtils.isBlank(oldAccessToken)) {
			if (StringUtils.isNotEmpty(refreshToken) || StringUtils.isNotEmpty(userInfoBo.getSellerAppSecret())) {
				LOGGER.logInfo(sellerNick, "-", "授权过期, 尝试使用refresh_token刷新");
				newAccessToken = refreshAccessToken(userInfoBo, refreshToken, platformId, appName);
				if (StringUtils.isBlank(newAccessToken)) {
					// 刷新失败, 需要重新授权
					hasNeedAuth = true;
				}else{
					// 刷新成功
					isRefresh = true;}
			} else {
				hasNeedAuth = true;
			}
		}

        if (StringUtils.isNotEmpty(oldAccessToken) && StringUtils.isEmpty(newAccessToken)) {
			if(!hasNeedAuth) {
				// 授权未过期
				newAccessToken = authService.decryptToken(oldAccessToken, platformId, appName);
			} else if (userInfoBo.getAuthDeadLine() == null && userInfoBo.getOrderCycleEnd() != null && LocalDateTime.now().isBefore(userInfoBo.getOrderCycleEnd())) {
				// 授权时候为空, 并且订购时间未过期, 兼容一下
				LOGGER.logInfo(sellerNick, "-", "授权时间为空, 并且订购时间未过期, 兼容使用老的accessToken");
				newAccessToken = authService.decryptToken(oldAccessToken, platformId, appName);
			}
		}

		userInfoBo.setAccessToken(newAccessToken);
		userInfoBo.setOriginalHasNeedAuth(hasNeedAuth);
		return isRefresh;
	}

	@Override
	public UserInfoResponse getAccessToken(UserInfoBo userInfoBo, String memberId, String platformId, String appName) throws Exception {
		UserProductInfo userProductInfo = userRepository.queryByMemberId(memberId, userInfoBo.getPlatformId(),
			userInfoBo.getAppType());
		userInfoBo.setSellerId(userProductInfo.getUserIdStr());
		userInfoBo.setSellerNick(userProductInfo.getNick());
		return getAccessToken(userInfoBo, platformId, appName);
	}

	/**
	 * refresh-access token
	 * @param userInfoBo
	 * @param refreshToken
	 * @return
	 * @throws DbWriteException
	 * @throws CacheWriteException
	 */
	@Override
	public String refreshAccessToken(UserInfoBo userInfoBo, String refreshToken, String platformId, String appName) {
		CallbackResponse callbackResponse = oAuthDecorationService.refreshAccessToken(userInfoBo ,refreshToken, platformId, appName);
		return null != callbackResponse ? callbackResponse.getAccessToken() : null;
	}

	@Override
	public boolean validatorUserAutoRenewAndUpdating(UserAutoRenewBo userAutoRenewBo, String platformId, String appName) throws InterruptedException {
		ErrorVasSearch errorVasSearch = new ErrorVasSearch();
		String articleCode = userAutoRenewBo.getArticleCode();
		String itemCode = userAutoRenewBo.getFuwuItemCode();
		Long pageSize = userAutoRenewBo.getPageSize();
		int pageNo = 1;
		SellerVasSubscSearchResponse sellerVasSubscSearchResponse = loopVasSearch(articleCode, itemCode,
			userAutoRenewBo.getMaxRetryCount(), pageSize, pageNo, platformId, appName);
		if (Objects.isNull(sellerVasSubscSearchResponse)) {
			LOGGER.logError("vas.search接口异常不能获取到订购记录, 不能刷新数据");
			return false;
		}
		Long totalItem = sellerVasSubscSearchResponse.getTotalItem();
		List<SellerArticleSub> sellerArticleSubs = new ArrayList<>(sellerVasSubscSearchResponse.getArticleSubs());
		int pageCount = (int) Math.ceil((double)totalItem / pageSize);
		++pageNo;
		for (int i = pageNo; i <= pageCount; i++) {
			sellerVasSubscSearchResponse = loopVasSearch(articleCode, itemCode, userAutoRenewBo.getMaxRetryCount(), pageSize, i, platformId, appName);
			if (Objects.isNull(sellerVasSubscSearchResponse)) {
				errorVasSearch.getPageNos().add(i);
				LOGGER.logError("vas.search接口异常不能获取到订购记录, 不能刷新数据");
				continue;
			}
			List<SellerArticleSub> tmpSellerArticleSub = new ArrayList<>(sellerVasSubscSearchResponse.getArticleSubs());
			sellerArticleSubs = ListUtils.union(sellerArticleSubs, tmpSellerArticleSub);
			if (sellerArticleSubs.size() > 1000) {
				List<String> sellerNickList = sellerArticleSubs.stream().map(SellerArticleSub::getNick).collect(
					Collectors.toList());
				userAutoRenewBo.getExecutor().execute(()->{
					refreshUserLevel(sellerNickList, userAutoRenewBo.getPlatformId(), userAutoRenewBo.getAppType());
				});
				sellerArticleSubs = new ArrayList<>();
			}
		}
		return true;
	}

	public void refreshUserLevel(List<String> sellerNickList, String platformId, String appName) {
		List<UserProductInfo> userProductInfoList = getUserInfoListByDb(sellerNickList, platformId, appName, userRepository);
		if (Objects.isNull(userProductInfoList) || userProductInfoList.isEmpty()) {
			LOGGER.logError("-", "-", "sellerNick用户异常, nickList为:" + JSON.toJSONString(sellerNickList));
			return;
		}
		List<String> sellerNickToDbList = userProductInfoList.stream().map(UserProductInfo::getNick).collect(Collectors.toList());
		//做一次交集
		List<String> upSellerNick = sellerNickList.stream()
			.distinct()
			.filter(sellerNickToDbList::contains)
			.collect(Collectors.toList());
		userRepository.updateBatchLevelBySellerNickCollection(upSellerNick, UserProductInfo.LEVEL_THREE, platformId, appName);
		userManageRedisRepository.hmSetLevelByKeys(upSellerNick, UserProductInfo.LEVEL_THREE.toString(), platformId, appName);
	}


	public SellerVasSubscSearchResponse loopVasSearch(String articleCode, String fuwuItemCode, Integer maxRetryCount, Long pageSize, long pageNo, String platformId, String appName) throws InterruptedException {
		SellerVasSubscSearchRequest sellerVasSubscSearchRequest = new SellerVasSubscSearchRequest();
		sellerVasSubscSearchRequest.setArticleCode(articleCode);
		sellerVasSubscSearchRequest.setPageSize(pageSize);
		sellerVasSubscSearchRequest.setPageNo(pageNo);
		sellerVasSubscSearchRequest.setItemCode(fuwuItemCode);
		sellerVasSubscSearchRequest.setStatus(1L);
		sellerVasSubscSearchRequest.setAutosub(true);
		SellerVasSubscSearchResponse sellerVasSubscSearchResponse = null;
		int retryCount = 0;
		while (true) {
			retryCount++;
			if (retryCount>maxRetryCount) {
				break;
			}
			sellerVasSubscSearchResponse = appStoreFuWuService.vasSubscribeSearch(sellerVasSubscSearchRequest, platformId, appName);
			if (sellerVasSubscSearchResponse.isSuccess()) {
				break;
			} else {
				Thread.sleep(retryCount * 1000);
			}
		}
		return sellerVasSubscSearchResponse;
	}

	@Override
	public void refreshUserInfoCacheAndTable(UserBo userBo, String platformId, String appName) throws DbWriteException, CacheWriteException {
		super.refreshUserInfoCacheAndTable(userBo, userManageRedisRepository, userRepository);
	}

	@Override
	public void saveUserInfoCacheAndTable(UserBo userBo, String platformId, String appName)
		throws DbWriteException {
		super.saveUserInfoCacheAndTable(userBo, userManageRedisRepository, userRepository);
	}

	@Override
    public UserProductInfo getUserInfoByDb(UserBo userBo, String platformId, String appName) {
        return super.getUserInfoByDb(userBo, userRepository);
    }

	@Data
	public static class ErrorVasSearch {
		private List<Integer> pageNos;
	}
}
