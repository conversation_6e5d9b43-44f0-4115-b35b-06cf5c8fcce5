package cn.loveapp.uac.service.event;

import cn.loveapp.common.thread.VirtualThreadExecutorAdapter;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.utils.RocketMqQueueHelper;
import cn.loveapp.uac.proto.SubscribeUserMessageRequestProto;
import cn.loveapp.uac.proto.UserChangedRequestProto;
import cn.loveapp.uac.proto.event.UserChangedEvent;
import cn.loveapp.uac.service.config.UserInfoChangedConfig;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @program: uac-service-group
 * @description: SubscribeUserMessageEventHandler
 * @author: Jason
 * @create: 2020-10-16 15:38
 **/
@Service
public class SubscribeUserMessageEventHandlerImpl implements SubscribeUserMessageEventHandler {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(SubscribeUserMessageEventHandlerImpl.class);

	@Value("${uac.event.subscribe.topics:}")
	private List<String> subscribeTopics;
	@Value("${uac.event.subscribe.retry:1}")
	private Integer retryCount;

	@Autowired
	private DefaultMQProducer producer;
	@Autowired
	private RocketMqQueueHelper rocketMqQueueHelper;

    @Autowired
    private UserInfoChangedConfig userChangedMqConfig;

	private final ThreadPoolExecutor poolExecutor;

	public SubscribeUserMessageEventHandlerImpl() {
		poolExecutor = VirtualThreadExecutorAdapter.create("subscribe-pool-");
	}

	@Override
	public void eventHandle(UserInfoBo userInfoBo, String changeStatus, String type) {
		if (!CollectionUtils.isEmpty(subscribeTopics)) {
			for (String oauthTopic : subscribeTopics) {
				SubscribeUserMessageRequestProto subscribeUserMessageRequestProto = new SubscribeUserMessageRequestProto();
				subscribeUserMessageRequestProto.setSellerNick(userInfoBo.getSellerNick());
				subscribeUserMessageRequestProto.setSubSellerNick(userInfoBo.getSubSellerNick());
				subscribeUserMessageRequestProto.setSellerId(userInfoBo.getSellerId());
				subscribeUserMessageRequestProto.setPlatformId(userInfoBo.getPlatformId());
				subscribeUserMessageRequestProto.setAppName(userInfoBo.getAppType());
				subscribeUserMessageRequestProto.setChangeStatus(changeStatus);
				subscribeUserMessageRequestProto.setType(type);
				poolExecutor.execute(() ->
					rocketMqQueueHelper.push(oauthTopic, "*", subscribeUserMessageRequestProto, producer, retryCount)
				);
			}
		}
	}

    @Override
    public void userChangedEventHandle(UserInfoBo userInfoBo, UserChangedEvent event) {
        if (userInfoBo == null || event == null || event.getType() == null) {
            return;
        }

        UserChangedRequestProto userChangedRequestProto = new UserChangedRequestProto();
        userChangedRequestProto.setSellerId(userInfoBo.getSellerId());
        userChangedRequestProto.setSellerNick(userInfoBo.getSellerNick());
        userChangedRequestProto.setAppName(userInfoBo.getAppType());
        userChangedRequestProto.setPlatformId(userInfoBo.getPlatformId());
        userChangedRequestProto.setEvent(event);

        List<UserInfoChangedConfig.Config> configs = userChangedMqConfig.getConfigs().get(event.getType());
        if (CollectionUtils.isEmpty(configs)) {
            return;
        }
        for (UserInfoChangedConfig.Config config : configs) {
            poolExecutor.execute(() ->
                rocketMqQueueHelper.push(config.getTopic(), config.getTag(), userChangedRequestProto, producer, retryCount)
            );
        }
    }

}
