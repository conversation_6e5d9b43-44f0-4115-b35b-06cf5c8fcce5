package cn.loveapp.uac.service.base;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.api.request.SellerVasSubscSearchRequest;
import cn.loveapp.uac.common.api.response.SellerVasSubscSearchResponse;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.bo.UserInfoBo.IdentityProperty;
import cn.loveapp.uac.common.code.taobao.ApiCodeConstant.CodeEnum;
import cn.loveapp.uac.common.dao.redis.repository.OperationManageRedisRepository;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.common.platform.api.AppStoreService;
import cn.loveapp.uac.common.utils.DateUtil;
import cn.loveapp.uac.proto.event.EventTypeEnum;
import cn.loveapp.uac.proto.event.UserChangedEvent;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * @program: uac-service-group
 * @description: BaseOperationService
 * @author: Jason
 * @create: 2020-03-11 19:07
 **/
abstract public class BaseOperationService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(BaseOperationService.class);

	private Cache<String, CodeEnum> vasSearchCache = CacheBuilder.newBuilder().maximumSize(50000L)
		.expireAfterWrite(1, TimeUnit.HOURS)
		.build();

	/**
	 * 计算最终用户时间和等级
	 */
	public UserChangedEvent calculateOrderCycleEndAndLevel(UserInfoBo userInfoBo, String platformId, String appName) {
		OperationUserInfo operationUserInfo = new OperationUserInfo();
		calculateLevel(userInfoBo, operationUserInfo, platformId, appName);
		return compareSellerIdentityChanged(userInfoBo, operationUserInfo);
	}

	/**
	 * 判断是否有连续包月的标志
	 * @param sellerNick
	 * @return
	 */
	public boolean hasAutoMonth(String sellerNick, OperationManageRedisRepository operationManageRedisRepository, String platformId, String appName) {
		return !StringUtils.isEmpty(operationManageRedisRepository.find(sellerNick, platformId, appName));
	}

	/**
	 * 判断是否是自动续费用户
	 * @param sellerNick
	 * @param articleCode
	 * @param fuwuItemCode
	 * @param appStoreFuWuService
	 * @param useCache 是否使用cache
	 * @return
	 */
	public CodeEnum hasAutoRenew(String sellerNick, String articleCode, String fuwuItemCode, AppStoreService appStoreFuWuService, boolean useCache, String platformId, String appName) {
		CodeEnum code;
		try {
			code = useCache ?
					vasSearchCache.get(sellerNick + articleCode + fuwuItemCode,
							() -> getVasSearchApiResult(sellerNick, articleCode, fuwuItemCode, appStoreFuWuService,
								platformId, appName))
					: getVasSearchApiResult(sellerNick, articleCode, fuwuItemCode, appStoreFuWuService,
						platformId, appName);
		} catch (ExecutionException e) {
			LOGGER.logError(sellerNick, "-", "获取vas.search缓存属性异常");
			code = CodeEnum.ERROR;
		}
		return code;
	}

	/**
	 * 获取vassearch接口返回结果
	 * @param sellerNick
	 * @param articleCode
	 * @param fuwuItemCode
	 * @param appStoreFuWuService
	 * @return
	 */
	@NotNull
	private CodeEnum getVasSearchApiResult(String sellerNick, String articleCode, String fuwuItemCode,
		AppStoreService appStoreFuWuService, String platformId, String appName) {
		CodeEnum code;
		SellerVasSubscSearchRequest sellerVasSubscSearchRequest = new SellerVasSubscSearchRequest();
		sellerVasSubscSearchRequest.setArticleCode(articleCode);
		sellerVasSubscSearchRequest.setItemCode(fuwuItemCode);
		sellerVasSubscSearchRequest.setStatus(1L);
		sellerVasSubscSearchRequest.setAutosub(true);
		sellerVasSubscSearchRequest.setSellerNick(sellerNick);
		SellerVasSubscSearchResponse sellerVasSubscSearchResponse = appStoreFuWuService.vasSubscribeSearch(sellerVasSubscSearchRequest, platformId, appName);
		code = sellerVasSubscSearchResponse.hasSuccess();
		if (code.equals(CodeEnum.SUCCESS)) {
			if (sellerVasSubscSearchResponse.getTotalItem() <= 0) {
				code = CodeEnum.ERROR;
			}
		}
		LOGGER.logInfo(sellerNick, articleCode + "-" + fuwuItemCode, "through vas search api get result => " + code.name());
		return code;
	}

	/**
	 * 比对身份是否有变更
	 * @param userInfoBo
	 * @param operationUserInfo
	 */
	protected UserChangedEvent compareSellerIdentityChanged(UserInfoBo userInfoBo, OperationUserInfo operationUserInfo) {
		//原始身份
		Integer identityOriginalLevel = userInfoBo.getOriginalIdentityLevel();
		//计算后得出的身份
		Integer rawLevel = operationUserInfo.getLevel();
		Integer identityAfterCalculateLevel = operationUserInfo.getLevel();
		if(userInfoBo.getOriginalIdentityOrderCycleEnd() == null){
			LOGGER.logWarn(userInfoBo.getSellerNick(), "", "用户缺少授权时间, 跳过买赠计算");
			return null;
		}
		//原始身份到期时间
		LocalDateTime identityOriginalCycleEnd = userInfoBo.getOriginalIdentityOrderCycleEnd().withHour(0).withMinute(0).withSecond(0).withNano(0);
		//计算后身份到期时间
		LocalDateTime identityAfterCalculateCycleEnd = operationUserInfo.getOrderCycleEnd().withHour(0).withMinute(0).withSecond(0).withNano(0);
		if(LOGGER.isDebugEnabled()){
			LOGGER.logDebug(userInfoBo.getSellerNick(), "-", "原始身份为<" + identityOriginalLevel
				+ ">, 计算后得出的身份为<" + identityAfterCalculateLevel
				+ ">, 原始身份到期时间为<" + DateUtil.parseLocalDateTime(identityOriginalCycleEnd)
				+ ">,计算后得出的新身份的到期时间为<" + DateUtil.parseLocalDateTime(identityAfterCalculateCycleEnd) + ">");
		}
		IdentityProperty identityProperty = new IdentityProperty();
		identityProperty.setHasChanged(Boolean.FALSE);
		identityProperty.setProperty(UserProductInfo.IDENTITY_UNCHANGED);
		identityProperty.setHasNeedAuth(userInfoBo.getOriginalHasNeedAuth());
		/**
		 * 校验身份是否有变化
		 */
		Integer property = UserProductInfo.IDENTITY_UNCHANGED;
		//1 判断level有没有变更
		//2 判断订购时长有没有变更
        UserChangedEvent userLevelChangedEvent = null;
		if (!identityOriginalLevel.equals(identityAfterCalculateLevel)) {
			LOGGER.logInfo(userInfoBo.getSellerNick(), "-", "用户身份变更, 原始身份为<"
				+ identityOriginalLevel
				+ ">, 变更后的身份为<"
				+ identityAfterCalculateLevel
				+ ">");
			if (identityAfterCalculateLevel > 0) {
				property = UserProductInfo.IDENTITY_PRIMARY_TO_HIGH;
			} else {
				property = UserProductInfo.IDENTITY_HIGH_TO_PRIMARY;
			}
			identityProperty.setHasChanged(Boolean.TRUE);
            userLevelChangedEvent = new UserChangedEvent();
            userLevelChangedEvent.setOldVipFlag(identityOriginalLevel);
            userLevelChangedEvent.setNewVipFlag(identityAfterCalculateLevel);
            userLevelChangedEvent.setType(EventTypeEnum.USER_LEVEL_CHANGED);
		} else {
			if (! identityAfterCalculateCycleEnd.isEqual(identityOriginalCycleEnd)) {
				LOGGER.logInfo(userInfoBo.getSellerNick(), "-", "用户身份到期时间变更, 原始身份到期时间变更为<"
					+ DateUtil.parseDateTimeFormat(identityOriginalCycleEnd, DateUtil.FORMATTER_DATETIME)
					+ ">, 变更后身份到期时间变更为<"
					+ DateUtil.parseDateTimeFormat(identityAfterCalculateCycleEnd, DateUtil.FORMATTER_DATETIME)
					+ ">");
				identityProperty.setHasChanged(Boolean.TRUE);
				if (identityAfterCalculateLevel > 0) {
					property = UserProductInfo.IDENTITY_HIGH_TO_HIGH;
				} else {
					property = UserProductInfo.IDENTITY_PRIMARY_TO_PRIMARY;
				}
			}
		}
		identityProperty.setProperty(property);
		if (BooleanUtils.isTrue(identityProperty.getHasChanged())) {
			LOGGER.logInfo(userInfoBo.getSellerNick(), "-", "检测到用户身份发生变更, 需要重新授权");
			identityProperty.setHasNeedAuth(BooleanUtils.toBooleanObject(UserProductInfo.NEED_AUTH));
		} else {
			/**
			 * 如果发现用户授权到期时间为空或者accessToken为空，强制用户授权
			 */
			if (null == userInfoBo.getAuthDeadLine() || StringUtils.isEmpty(userInfoBo.getAccessToken())) {
				identityProperty.setHasNeedAuth(BooleanUtils.toBooleanObject(UserProductInfo.NEED_AUTH));
			}
			/**
			 * 如果authdeadline 为0000-00-00，强制重新授权
			 */
			/**
			 * 判断一下如果身份都没变更,但hasNeedAuth为1,比较一下授权到期时间和订购到期时间的差值
			 */
			if (null != userInfoBo.getAuthDeadLine() && null != userInfoBo.getFuwuOrderCycleEnd()) {
				if (userInfoBo.getAuthDeadLine().isBefore(LocalDateTime.now())) {
					// auth_dead_line 在当前时间之前, 需要重新授权
					identityProperty.setHasNeedAuth(BooleanUtils.toBooleanObject(UserProductInfo.NEED_AUTH));
				} else {
					identityProperty.setHasNeedAuth(BooleanUtils.toBooleanObject(UserProductInfo.NO_NEED_AUTH));
				}
			}
		}
		userInfoBo.setIdentityProperty(identityProperty);
		userInfoBo.setLevel(rawLevel);
		userInfoBo.setOrderCycleEnd(identityAfterCalculateCycleEnd);
		if(LOGGER.isDebugEnabled()){
			LOGGER.logDebug(userInfoBo.getSellerNick(), "-", "最终userInfoBo结果为:" + userInfoBo.toString());
		}
		return userLevelChangedEvent;
	}

	/**
	 * calculateLevel
	 * @param userInfoBo
	 * @param operationUserInfo
	 * @param platformId
	 * @param appName
	 */
	abstract public void calculateLevel(UserInfoBo userInfoBo, OperationUserInfo operationUserInfo, String platformId, String appName);



	@Data
	@ToString
	public static class OperationUserInfo{
		private String sellerNick;
		private Integer level;
		private LocalDateTime orderCycleEnd;
	}

}
