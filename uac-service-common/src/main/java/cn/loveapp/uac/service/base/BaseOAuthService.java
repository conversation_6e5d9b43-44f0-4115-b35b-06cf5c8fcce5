package cn.loveapp.uac.service.base;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.platform.api.AuthService;
import cn.loveapp.uac.common.platform.api.domain.RefreshTokenCallbackResult;
import cn.loveapp.uac.common.utils.DateUtil;
import cn.loveapp.uac.common.bo.UserBo;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.common.bo.AuthBo;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @program: uac-service-group
 * @description: BaseAuthCallback
 * @author: Jason
 * @create: 2020-03-14 11:15
 **/
abstract public class BaseOAuthService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(BaseOAuthService.class);

	public UserBo authCode(AuthBo authBo, AuthService authService) throws Exception {
		String code = authBo.getCode();
		String platformId = authBo.getPlatformId();
		String appName = authBo.getAppType();
		RefreshTokenCallbackResult refreshTokenCallbackResult = authService.getCallbackResultByCode(code, platformId, appName);
		if (Objects.isNull(refreshTokenCallbackResult)) {
			throw new IllegalArgumentException("since callbackResult is null");
		}
        LocalDateTime w1DeadLine = refreshTokenCallbackResult.getAuthDeadLine();
		LOGGER.logInfo("-", code, "calculate authDeadLine value is => " + DateUtil.parseLocalDateTime(w1DeadLine));
		UserProductInfo userProductInfo = UserProductInfo.of(refreshTokenCallbackResult.getSellerNick(), refreshTokenCallbackResult.getSellerId(), UserProductInfo.NO_NEED_AUTH);
		userProductInfo.initDefault();
		userProductInfo.setToprefreshkey(refreshTokenCallbackResult.getRefreshToken());
		userProductInfo.setTopsessionkey(refreshTokenCallbackResult.getAccessToken());
		userProductInfo.setW1Deadline(w1DeadLine);
		String sellerNick = refreshTokenCallbackResult.getSellerNick();
		UserBo userBo =new UserBo(platformId, appName, sellerNick, userProductInfo);
		userBo.setDecryptAccessToken(refreshTokenCallbackResult.getDecryptAccessToken());
		return userBo;
	}
}
