package cn.loveapp.uac.service.base;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.code.ApiCode;
import cn.loveapp.uac.common.bo.LoginUserInfoBo;
import cn.loveapp.uac.common.bo.UserBo;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.bo.UserInfoBo.IdentityProperty;
import cn.loveapp.uac.common.code.ErrorCode.BaseCode;
import cn.loveapp.uac.common.dao.redis.repository.UserManageRedisRepositoryHashRedisRepository;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.common.entity.redis.UserRedisEntity;
import cn.loveapp.uac.common.exception.CacheWriteException;
import cn.loveapp.uac.common.exception.DbWriteException;
import cn.loveapp.uac.common.platform.api.AuthService;
import cn.loveapp.uac.common.utils.DateUtil;
import cn.loveapp.uac.db.common.dao.dream.AyMultiUserTagDao;
import cn.loveapp.uac.db.common.entity.AyMultiUserTag;
import cn.loveapp.uac.db.common.entity.UserShopInfoMapping;
import cn.loveapp.uac.db.common.repository.UserRepository;
import cn.loveapp.uac.db.common.service.PlatformUserProductInfoService;
import cn.loveapp.uac.exception.UserException;
import cn.loveapp.uac.response.UserInfoResponse;
import cn.loveapp.uac.service.cache.ReminderCache;
import com.alibaba.fastjson2.JSON;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;

/**
 * @program: uac-service-group
 * @description: BaseSellerService
 * @author: Jason
 * @create: 2020-03-05 10:29
 **/
abstract public class BaseSellerService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(BaseSellerService.class);

	private ReminderCache reminderCache;

    protected AuthService authService;

	public BaseSellerService(ReminderCache reminderCache, AuthService authService) {
		this.reminderCache = reminderCache;
		this.authService = authService;
	}

	@Autowired
	private PlatformUserProductInfoService platformUserProductInfoService;

	@Autowired
	private AyMultiUserTagDao ayMultiUserTagDao;

	/**
	 * 从缓存里获取用户属性
	 */
    public UserRedisEntity getUserInfoByCache(String redisKey, String sellerNick, String sellerId, UserManageRedisRepositoryHashRedisRepository userManageRedisDao, String platformId, String appName) {
        return userManageRedisDao.entries(redisKey, sellerNick, sellerId, platformId, appName);
    }

	/**
	 * 直接从db里读用户
	 * @param sellerNicks
	 * @param userRepository
	 * @return
	 */
	public List<UserProductInfo> getUserInfoListByDb(List<String> sellerNicks, String platformId, String appType, UserRepository userRepository) {
		return userRepository.queryUserProductInfoListBySellerNickCollection(sellerNicks, platformId, appType);
	}

    /**
	 * 直接从db里读用户
	 * @param userBo
	 * @param userRepository
	 * @return
	 */
	protected UserProductInfo getUserInfoByDb(UserBo userBo, UserRepository userRepository) {
		UserProductInfo userInfo = platformUserProductInfoService.getUserInfo(userBo, userRepository, userBo.getPlatformId(),
				userBo.getAppType());

		if (userInfo == null) {
			return null;
		}
		userInfo.setPlatformId(userBo.getPlatformId());
		userInfo.setAppName(userBo.getAppType());
		try {
			// 获取tag
			AyMultiUserTag ayMultiUserTag = ayMultiUserTagDao.queryTag(StringUtils.firstNonBlank(userInfo.getUserId(), userInfo.getUserIdStr()), userBo.getPlatformId(), StringUtils.firstNonBlank(userBo.getAppType(), CommonAppConstants.APP_TRADE));
			if (ayMultiUserTag != null && StringUtils.isNotEmpty(ayMultiUserTag.getTags())) {
				userInfo.setAyMultiTags(ayMultiUserTag.getTags());
			} else {
				userInfo.setAyMultiTags(UserProductInfo.SEPARATOR);
			}
		} catch (Exception e) {
			LOGGER.logError("获取用户tag异常" + e.getMessage(), e);
		}

		return userInfo;
	}

	/**
	 * checkUserInfo
	 * @param userInfoBo userInfoBo
	 * @param hasReadTag hasReadTag
	 * @param userManageRedisRepository userManageRedisRepository
	 * @param userRepository userRepository
	 * @return boolean
	 */
	public boolean checkUserInfo(UserInfoBo userInfoBo, Boolean hasReadTag, UserManageRedisRepositoryHashRedisRepository userManageRedisRepository, UserRepository userRepository) {
		String sellerNick = userInfoBo.getSellerNick();
        String sellerId = userInfoBo.getSellerId();

        String redisKey = userManageRedisRepository.initCollection(sellerNick, sellerId, userInfoBo.getPlatformId(), userInfoBo.getAppType());
		if (StringUtils.isBlank(redisKey)) {
			//无法获取对应的redis key
			IdentityProperty identityProperty = new IdentityProperty();
			UserBo userBo = new UserBo(userInfoBo);
			userBo.setHasReadTag(hasReadTag);
            UserProductInfo userProductInfo = getUserInfoByDb(userBo, userRepository);
			if(LOGGER.isDebugEnabled()){
				LOGGER.logDebug(sellerNick, "-", "用户id " + sellerId + ", 尝试读取数据库结果: " + userProductInfo);
			}else{
				LOGGER.logInfo(sellerNick, "-", "用户id " + sellerId + ", 尝试读取数据库结果: " + !Objects.isNull(userProductInfo));
			}
			if (Objects.isNull(userProductInfo)) {
				return false;
			} else {
				converUserProductInfo2UserInfoBo(userInfoBo, userProductInfo, identityProperty);
				userInfoBo.setIdentityProperty(identityProperty);
			}
		}else{
			//使用sellerNick 查 先从redis中查找 如果没有找到的话 就去 数据库中查
			UserRedisEntity userRedisEntity = getUserInfoByCache(redisKey, sellerNick, sellerId, userManageRedisRepository,
				userInfoBo.getPlatformId(), userInfoBo.getAppType());
			String platformId = userInfoBo.getPlatformId();
			String appType = userInfoBo.getAppType();
			IdentityProperty identityProperty = new IdentityProperty();

			if (Objects.isNull(userRedisEntity) || userRedisEntity.isBlank(userInfoBo.getPlatformId() ,userInfoBo.getAppType())) {
				UserBo userBo = new UserBo(userInfoBo);
				userBo.setHasReadTag(hasReadTag);
                UserProductInfo userProductInfo = getUserInfoByDb(userBo, userRepository);
				if(LOGGER.isDebugEnabled()) {
				    LOGGER.logDebug(sellerNick, "-", "从读取数据库的结果: " + userProductInfo);
				}else{
					LOGGER.logInfo(sellerNick, "-", "从读取数据库的结果:" + !Objects.isNull(userProductInfo));
				}

                //1 将库里取出来的数据做二次封装
				if (Objects.isNull(userProductInfo)) {
					return false;
				} else {
                    if(StringUtils.isEmpty(sellerNick)){
                        sellerNick = userProductInfo.getNick();
                    }
					if (StringUtils.isEmpty(sellerId)){
						if (StringUtils.isNotEmpty(userProductInfo.getUserIdStr())) {
                            sellerId = userProductInfo.getUserIdStr();
                        }else if (userProductInfo.getUserId() != null){
							sellerId = userProductInfo.getUserId().toString();
						}
					}
					if (StringUtils.isEmpty(sellerId)){
						LOGGER.logError(sellerNick, "", "数据库中的sellerId为空!");
					}

					converUserProductInfo2UserInfoBo(userInfoBo, userProductInfo, identityProperty);
					userBo.setUserProductInfo(userProductInfo);
					try {
						if(!userBo.getUserRedisEntity().isBlank(platformId, appType)){
							refreshUserInfoCache(userBo, userManageRedisRepository);
							LOGGER.logError(sellerNick, "", "刷新cache");
						}
					} catch (Exception e) {
						LOGGER.logError(sellerNick, "", "刷新cache失败: " + e.getMessage(), e);
					}
				}
			} else {
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.logDebug(sellerNick, "-", "从读取cache到的结果: " + userRedisEntity);
                }
				authService.convertUserRedisEntity2UserInfoBo(userInfoBo, userRedisEntity, platformId, appType);
				if (Objects.isNull(userRedisEntity.getIsNeedauth())) {
					userInfoBo.setOriginalHasNeedAuth(Boolean.FALSE);
					identityProperty.setHasNeedAuth(Boolean.FALSE);
				} else {
					userInfoBo.setOriginalHasNeedAuth(userRedisEntity.getIsNeedauth());
					identityProperty.setHasNeedAuth(BooleanUtils.toBoolean(userRedisEntity.getIsNeedauth()));
				}
                userInfoBo.setCreateDateTime(userRedisEntity.getCreateDateTime());
                userInfoBo.setTag(userRedisEntity.getTag());
                userInfoBo.setSupplierId(userRedisEntity.getSupplier_id());
                userInfoBo.setSupplierNick(userRedisEntity.getSupplier_name());
                userInfoBo.setSellerAppId(userRedisEntity.getApp_id());
                userInfoBo.setSellerAppSecret(userRedisEntity.getApp_secret());
				userInfoBo.setAyMultiTags(userRedisEntity.generalAyMultiTagList());

                if (StringUtils.isNotEmpty(userInfoBo.getShopId())) {
                    // todo 并发提升后需要优化查询
                    UserShopInfoMapping userShopInfo =
                        platformUserProductInfoService.getUserShopInfo(userInfoBo.getShopId(), platformId, appType);
                    if (userShopInfo != null) {
                        userInfoBo.setShopCipher(userShopInfo.getShopCipher());
                    }
                }
            }

			userInfoBo.setIdentityProperty(identityProperty);
			if(LOGGER.isDebugEnabled()){
				LOGGER.logDebug(sellerNick, "-", "需要返回的userInfoBo, " + userInfoBo.toString());
			}
		}
		return true;

	}

	/**
	 * 设置爱用代发nick（淘宝、必要除外，代发表没存nick，直接是用id做为nick）
	 *
	 * @param userInfoBo
	 */
	private void setDistributeSellerNickBySellerId(UserInfoBo userInfoBo) {
		if (CommonAppConstants.APP_DISTRIBUTE.equals(userInfoBo.getAppType()) && !CommonPlatformConstants.PLATFORM_BIYAO.equals(userInfoBo.getPlatformId())
				&& !CommonPlatformConstants.PLATFORM_TAO.equals(userInfoBo.getPlatformId())) {
			userInfoBo.setSellerNick(userInfoBo.getShopName());
		}
	}


	protected void converUserProductInfo2UserInfoBo(UserInfoBo userInfoBo,UserProductInfo userProductInfo,IdentityProperty identityProperty){
        if(StringUtils.isNotEmpty(userProductInfo.getUserIdStr())){
            userInfoBo.setSellerId(userProductInfo.getUserIdStr());
        }else if (StringUtils.isNotEmpty(userProductInfo.getUserId())) {
            userInfoBo.setSellerId(userProductInfo.getUserId());
        }
		userInfoBo.setShopName(userProductInfo.getShopName());
		if (StringUtils.isNotEmpty(userProductInfo.getNick())) {
			userInfoBo.setSellerNick(userProductInfo.getNick());
		} else {
			setDistributeSellerNickBySellerId(userInfoBo);
		}
		userInfoBo.setSellerRole(userProductInfo.getRoleid());
		userInfoBo.setCorpId(userProductInfo.getCorpId());
		userInfoBo.setLevel(userProductInfo.getVipflag());
		userInfoBo.setTag(userProductInfo.getTag());
		userInfoBo.setOrderCycleEnd(userProductInfo.getOrderCycleEnd());
		userInfoBo.setAuthDeadLine(userProductInfo.getW1Deadline());
		userInfoBo.setW2Deadline(userProductInfo.getW2Deadline());
		userInfoBo.setLastPaidTime(userProductInfo.getLastPaidTime());
		userInfoBo.setOriginalIdentityLevel(userProductInfo.getVipflag());
		userInfoBo.setOriginalIdentityOrderCycleEnd(userProductInfo.getOrderCycleEnd());
		if (Objects.isNull(userProductInfo.getIsNeedauth())) {
			userInfoBo.setOriginalHasNeedAuth(Boolean.FALSE);
			identityProperty.setHasNeedAuth(Boolean.FALSE);
		} else {
			userInfoBo.setOriginalHasNeedAuth(BooleanUtils.toBooleanObject(userProductInfo.getIsNeedauth()));
			identityProperty.setHasNeedAuth(BooleanUtils.toBoolean(userProductInfo.getIsNeedauth()));
		}
		userInfoBo.setCreateDateTime(userProductInfo.getCreateDate());
		userInfoBo.setAccessToken(userProductInfo.getTopsessionkey());
		userInfoBo.setRefreshToken(userProductInfo.getToprefreshkey());
		userInfoBo.setMallName(userProductInfo.getMallName());
		userInfoBo.setSupplierId(userProductInfo.getSupplierId());
		userInfoBo.setSupplierNick(userProductInfo.getSupplierNick());
        userInfoBo.setMemberId(userProductInfo.getMemberId());
        userInfoBo.setSellerAppId(userProductInfo.getAppId());
        userInfoBo.setSellerAppSecret(userProductInfo.getAppSecret());
        userInfoBo.setProfessionalOrderCycleEnd(userProductInfo.getProfessionalOrderCycleEnd());
        userInfoBo.setIsAuthExcept(userProductInfo.getIsAuthExcept());

        // 多店tag
		userInfoBo.setAyMultiTags(userProductInfo.getAyMultiTags());

        // tiktok 店铺信息
        userInfoBo.setShopId(userProductInfo.getShopId());
        userInfoBo.setShopCipher(userProductInfo.getShopCipher());
        userInfoBo.setIsAuthExcept(userProductInfo.getIsAuthExcept());
	}

	/**
	 * checkUserInfo
	 * @param userInfoBo userInfoBo
	 * @param hasReadTag hasReadTag
	 * @param userRepository userRepository
	 * @return boolean
	 */
	public boolean checkUserInfoDb(UserInfoBo userInfoBo, Boolean hasReadTag, UserRepository userRepository) {
		String sellerNick = userInfoBo.getSellerNick();
		UserBo userBo = new UserBo(userInfoBo);
		userBo.setHasReadTag(hasReadTag);
        UserProductInfo userProductInfo = getUserInfoByDb(userBo, userRepository);
		if(LOGGER.isDebugEnabled()) {
		    LOGGER.logDebug(sellerNick, "-", "尝试读取数据库结果: " + userProductInfo);
		}else{
			LOGGER.logInfo(sellerNick, "-", "尝试读取数据库结果: " + !Objects.isNull(userProductInfo));
		}
		if (Objects.nonNull(userProductInfo)) {
			//1 将库里取出来的数据做二次封装
			userInfoBo.setSellerId(userProductInfo.getUserId().toString());
			userInfoBo.setSellerRole(userProductInfo.getRoleid());
			userInfoBo.setCorpId(userProductInfo.getCorpId());
			userInfoBo.setLevel(userProductInfo.getVipflag());
			userInfoBo.setTag(userProductInfo.getTag());
			userInfoBo.setOrderCycleEnd(userProductInfo.getOrderCycleEnd());
			userInfoBo.setAuthDeadLine(userProductInfo.getW1Deadline());
			userInfoBo.setLastPaidTime(userProductInfo.getLastPaidTime());
			userInfoBo.setOriginalIdentityLevel(userProductInfo.getVipflag());
			userInfoBo.setOriginalIdentityOrderCycleEnd(userProductInfo.getOrderCycleEnd());
			userInfoBo.setCreateDateTime(userProductInfo.getCreateDate());
			userInfoBo.setAccessToken(userProductInfo.getTopsessionkey());
			userInfoBo.setRefreshToken(userProductInfo.getToprefreshkey());
			userInfoBo.setCountMp(userProductInfo.getLogincountMp());
			userInfoBo.setCountPc(userProductInfo.getLogincountPc());
			userInfoBo.setCountWw(userProductInfo.getLogincountWw());
			userInfoBo.setAyMultiTags(userProductInfo.getAyMultiTags());
			return true;
		}
		return false;
	}

	/**
	 * 登录用户信息
	 */
	public UserInfoResponse login(LoginUserInfoBo loginUserInfoBo, UserManageRedisRepositoryHashRedisRepository userManageRedisRepository, UserRepository userRepository)
		throws DbWriteException {
		String sellerNick = loginUserInfoBo.getSellerNick();
		String platformId = loginUserInfoBo.getPlatformId();
		String appType = loginUserInfoBo.getAppType();
		long reminderAuthDayDiff = loginUserInfoBo.getReminderAuthDayDiff();
		//先初始化一波
		UserInfoResponse userInfo = loginUserInfoBo.toUserInfo();
		UserInfoBo userInfoBoCache = new UserInfoBo(platformId, appType, sellerNick, loginUserInfoBo.getSellerId());
		boolean checkUserInfoResult = checkUserInfo(userInfoBoCache, Boolean.TRUE, userManageRedisRepository, userRepository);
		UserProductInfo userProductInfo = loginUserInfoBo.toUserProductInfo();
		userProductInfo.setLastactivedt(DateUtil.currentDate());
		userProductInfo.setLastupdatetime(DateUtil.currentDate());
		Boolean hasNeedAuth = userInfoBoCache.getOriginalHasNeedAuth();;
		if (!checkUserInfoResult) {
			// 做个兼容当发现sellernick为空的时候不处理存储
			if (!StringUtils.isBlank(userInfoBoCache.getSellerNick())) {
				if(LOGGER.isDebugEnabled()) {
				    LOGGER.logDebug(sellerNick, "-", "用户名称 " + sellerNick + "不存在需要新建，建立的属性有" + userProductInfo);
				} else {
					LOGGER.logInfo(sellerNick, "-", "用户名称 " + sellerNick + "不存在需要新建");
				}
				userProductInfo.setCreateDate(DateUtil.currentDate(true));
				userProductInfo.setSubdatetime(DateUtil.currentDate(true));
				userProductInfo.setIsNeedauth(UserProductInfo.NEED_AUTH);
				saveUserInfoCacheAndTable(new UserBo(platformId, appType, sellerNick, userProductInfo), userManageRedisRepository, userRepository);
			}
			//新用户要标记一下重新授权
			userInfo.setHasNeedAuth(true);
		} else {
			if (Objects.isNull(userInfoBoCache.getAuthDeadLine())) {
				LOGGER.logWarn(sellerNick, "-", "原授权时间异常需要检查");
				hasNeedAuth = true;
			} else {
				LocalDateTime now = DateUtil.currentDate(true);
				LocalDateTime authDeadLine = userInfoBoCache.getAuthDeadLine().withHour(0).withSecond(0).withMinute(0).withNano(0);
				if (authDeadLine.isBefore(DateUtil.currentDate(true)) || authDeadLine.isEqual(now)) {
					LOGGER.logWarn(sellerNick, "-", "原授权时间即将过期,需要重新授权");
					hasNeedAuth = true;
				} else {
					long diffDay = now.until(authDeadLine, ChronoUnit.DAYS);
					if (diffDay <= reminderAuthDayDiff) {
						if (ReminderCache.NEED_REMINDER.equals(reminderCache.getReminderValue(sellerNick))) {
							LOGGER.logWarn(sellerNick, "-", "时间在阀值内需要重新授权并且提醒缓存失效");
							hasNeedAuth = true;
							reminderCache.putReminderValue(sellerNick, ReminderCache.NO_NEED_REMINDER_ON_DAY);
						}
					}
				}
				userInfo.setAuthDeadLine(authDeadLine);
			}
			if (Objects.isNull(userInfoBoCache.getOrderCycleEnd())) {
				LOGGER.logWarn(sellerNick, "-", "用户订购最终时间异常需要检查");
			} else {
				userInfo.setOrderCycleEnd(userInfoBoCache.getOrderCycleEnd());
			}
			userInfo.setHasNeedAuth(hasNeedAuth);
			userInfo.setTag(userInfoBoCache.getTag());
			userInfo.setVipflag(userInfoBoCache.getLevel());
			userInfo.setSellerId(userInfoBoCache.getSellerId());
			userInfo.setRoleId(userInfoBoCache.getSellerRole());
			userInfo.setCreateDate(userInfoBoCache.getCreateDateTime());
			userInfo.setW2DeadLine(userInfoBoCache.getW2Deadline());
		}
		return userInfo;
	}


	protected UserInfoResponse createUserInfo(UserInfoBo userInfoBo) {
		IdentityProperty identityProperty = userInfoBo.getIdentityProperty();
		UserInfoResponse userInfo = new UserInfoResponse();
		userInfo.setAppName(userInfoBo.getAppType());
		userInfo.setPlatformId(userInfoBo.getPlatformId());
		userInfo.setSellerNick(userInfoBo.getSellerNick());
		userInfo.setSellerId(userInfoBo.getSellerId());
		userInfo.setW2DeadLine(userInfoBo.getW2Deadline());
		userInfo.setAuthDeadLine(userInfoBo.getAuthDeadLine());
		userInfo.setOrderCycleEnd(userInfoBo.getOrderCycleEnd());
		userInfo.setVipflag(userInfoBo.getLevel());
		userInfo.setCorpId(userInfoBo.getCorpId());
		userInfo.setRoleId(userInfoBo.getSellerRole());
		userInfo.setHasNeedAuth(identityProperty.getHasNeedAuth());
		userInfo.setHVersion(userInfoBo.getHVersion());
		userInfo.setTag(userInfoBo.getTag());
		userInfo.setCreateDate(userInfoBo.getCreateDateTime());
		userInfo.setFuwuMarketOrderCycleEnd(userInfoBo.getFuwuOrderCycleEnd());
		userInfo.setFuwuMarketVipflag(userInfoBo.getFuwuVipFlag());
		userInfo.setHasExist(userInfoBo.getHasExist());
		userInfo.setMallName(userInfoBo.getMallName());
        userInfo.setSellerAppId(userInfoBo.getSellerAppId());
        userInfo.setSellerAppSecret(userInfoBo.getSellerAppSecret());
        userInfo.setMemberId(userInfoBo.getMemberId());
        // 先解密AppSecret
        if (StringUtils.isNotEmpty(userInfoBo.getSellerAppSecret())) {
            userInfo.setSellerAppId(userInfoBo.getSellerAppId());
            try {
                String sellerAppSecret = authService.decryptToken(userInfoBo.getSellerAppSecret(), userInfo.getPlatformId(), userInfo.getAppName());
                userInfo.setSellerAppSecret(sellerAppSecret);
                userInfoBo.setSellerAppSecret(sellerAppSecret);
            } catch (Exception e) {
                LOGGER.logError("appSecret 解密失败: " + e.getMessage(), e);
                userInfo.setSellerAppSecret(userInfoBo.getSellerAppSecret());
            }
        }

		userInfo.setAyMultiTags(userInfoBo.getAyMultiTags());
        userInfo.setLowerSentLevelList(userInfoBo.getLowerSentLevelList());
        userInfo.setIsAuthExcept(userInfoBo.getIsAuthExcept());
		return userInfo;
	}

	public void whiteListValidator(UserInfoBo userInfoBo, List<String> whiteList, Integer testUserLevel, String testUserCycleEnd) {
		String sellerNick = userInfoBo.getSellerNick();
		boolean whiteListBol = StringUtils.containsAny(sellerNick, whiteList.toArray(new String[0]));
		if (whiteListBol && StringUtils.isNotEmpty(testUserCycleEnd)) {
			IdentityProperty identityProperty = new IdentityProperty();
			identityProperty.setHasNeedAuth(BooleanUtils.toBooleanObject(UserProductInfo.NO_NEED_AUTH));
			userInfoBo.setIdentityProperty(identityProperty);
			userInfoBo.setLevel(testUserLevel);
			userInfoBo.setOrderCycleEnd(DateUtil.parseString(testUserCycleEnd));
		}
	}

	protected void refreshUserInfoCacheAndTable(UserBo userBo, UserManageRedisRepositoryHashRedisRepository userManageRedisRepository, UserRepository userRepository)
		throws DbWriteException, CacheWriteException {
        LOGGER.logInfo(userBo.getSellerNick(), "-", "更新数据库: " + JSON.toJSONString(userBo.getUserProductInfo()));
		boolean upBol = false;
		try {
			userRepository.update(userBo.getUserProductInfo(), userBo.getPlatformId(), userBo.getAppType());
			upBol = true;
		} catch (Exception e) {
			throw new DbWriteException(BaseCode.SYS_DB_ERR.getCode(), "变更用户主表信息失败: " + e.getMessage(), e);
		}
		try {
            LOGGER.logInfo(userBo.getSellerNick(), "-", "更新redis: " + JSON.toJSONString(userBo.getUserRedisEntity()));
			userManageRedisRepository.putUserData(userBo);
		} catch (Exception e) {
			upBol = false;
			throw new CacheWriteException(BaseCode.SYS_CACHE_ERR.getCode(), "变更用户缓存信息失败: " + e.getMessage(), e);
		} finally {
			if (!upBol) {
				userManageRedisRepository.putUserData(userBo);
			}
		}
	}

	public void refreshUserInfoCache(UserBo userBo, UserManageRedisRepositoryHashRedisRepository userManageRedisRepository)
		throws CacheWriteException {
		boolean upBol = true;
		try {
			userManageRedisRepository.putUserData(userBo);
		} catch (Exception e) {
			upBol = false;
			throw new CacheWriteException(BaseCode.SYS_CACHE_ERR.getCode(), "变更用户缓存信息失败");
		} finally {
			if (!upBol) {
				userManageRedisRepository.putUserData(userBo);
			}
		}
	}

	public void saveUserInfoCacheAndTable(UserBo userBo, UserManageRedisRepositoryHashRedisRepository userManageRedisRepository, UserRepository userRepository)
		throws DbWriteException {
		boolean upBol = false;
		try {
			userRepository.insert(userBo.getUserProductInfo(), userBo.getPlatformId(), userBo.getAppType());
			upBol = true;
		} catch (Exception e) {
			throw new DbWriteException(BaseCode.SYS_DB_ERR.getCode(), "新增用户主表信息失败");
		}
		try {
			userManageRedisRepository.putUserData(userBo);
		} catch (Exception e) {
			upBol = false;
		} finally {
			if (!upBol) {
				userManageRedisRepository.putUserData(userBo);
			}
		}
	}

	protected void toUserInfo(UserProductInfo userProductInfo, UserInfoResponse userInfo) {
		LocalDateTime authDeadLine = userProductInfo.getW1Deadline();
		boolean hasNeedAuth = false;
		if (authDeadLine.isBefore(DateUtil.currentDate(true))) {
			hasNeedAuth = true;
		}
		userInfo.setAuthDeadLine(userProductInfo.getW1Deadline());
		userInfo.setSellerNick(userProductInfo.getNick());
		userInfo.setSellerId(
			StringUtils.isBlank(userProductInfo.getUserIdStr()) ? userProductInfo.getUserId().toString() : userProductInfo.getUserIdStr());
		userInfo.setVipflag(UserProductInfo.LEVEL_ONE);
		userInfo.setHasNeedAuth(hasNeedAuth);
	}

	/**
	 * 获取用户属性
	 */
	protected UserInfoResponse getUserInfo(UserInfoBo userInfoBo, UserManageRedisRepositoryHashRedisRepository userManageRedisRepository, UserRepository userRepository)
		throws UserException {
		boolean checkUserInfo = checkUserInfo(userInfoBo, Boolean.FALSE, userManageRedisRepository, userRepository);
		if (checkUserInfo) {
			return createUserInfo(userInfoBo);
		}
		throw new UserException(ApiCode.NO_EXIST_USER.code(), ApiCode.NO_EXIST_USER.message());
	}


	/**
	 * 校验用户accessToken是否过期
	 */
	public boolean accessTokenExpireValidator(UserInfoBo userInfoBo) {
		String platformId = userInfoBo.getPlatformId();
		String appName = userInfoBo.getAppType();
		boolean isTaobaoItemUser =
				CommonPlatformConstants.PLATFORM_TAO.equals(platformId) && CommonAppConstants.APP_ITEM.equals(appName);
		// 淘宝爱用商品用户存的w1授权到期时间不准或为空，需要跳过判空校验和过期校验，改用订购到期时间判断是否过期
		if (isTaobaoItemUser) {
			// cycle_date 订购校验
			if(userInfoBo.getOrderCycleEnd() != null && LocalDateTime.now().isAfter(userInfoBo.getOrderCycleEnd())){
				return true;
			}
		} else {
			try {
				Assert.notNull(userInfoBo.getAuthDeadLine(), "授权时间为空");
                Assert.isInstanceOf(LocalDateTime.class, userInfoBo.getAuthDeadLine(), "授权时间格式有误");
			} catch (IllegalArgumentException e) {
				LOGGER.logError(userInfoBo.getSellerNick(), "-", "授权时间有误, 原因为:" + e.getMessage(), e);
				return true;
			}
			// cycle_date 订购校验. 快手因为订购没有推送, 导致订购时间可能不准, 不需要检验
			if(!CommonPlatformConstants.PLATFORM_KWAISHOP.equals(userInfoBo.getPlatformId())
					&& userInfoBo.getOrderCycleEnd() != null && LocalDateTime.now().isAfter(userInfoBo.getOrderCycleEnd())){
				return true;
			}
			// 淘宝的授权到期时间统一为0点（当天授权还是有效的）需要特殊处理
			LocalDateTime now = DateUtil.currentDate(CommonPlatformConstants.PLATFORM_TAO.equals(userInfoBo.getPlatformId()));
			LocalDateTime authDeadLine = DateUtil.toDateTimeZeroSecond(userInfoBo.getAuthDeadLine());
			if (now.isAfter(authDeadLine) || now.isEqual(authDeadLine)) {
				return true;
			}
		}

		return false;
	}

}
