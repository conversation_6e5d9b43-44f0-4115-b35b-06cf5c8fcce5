package cn.loveapp.uac.service.platform.biz;

import cn.loveapp.uac.common.exception.DbWriteException;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.exception.UserException;
import cn.loveapp.uac.response.UserCacheInfoResponse;
import cn.loveapp.uac.response.UserInfoResponse;
import cn.loveapp.uac.common.bo.LoginUserInfoBo;
import cn.loveapp.uac.common.bo.UserInfoBo;

import java.util.List;

/**
 * @program: uac-service-group
 * @description: UserPlatformHandleService
 * @author: Jason
 * @create: 2021-04-19 17:22
 **/
public interface UserPlatformHandleService {
	/**
	 * 用户登陆并发生一系列的复杂计算逻辑
	 * @param userInfoBo
	 * @return
	 */
	UserInfoResponse login(UserInfoBo userInfoBo, String platformId, String appName) throws UserException;

	/**
	 * 快速登录
	 * @param loginUserInfoBo
	 * @return
	 */
	UserInfoResponse quickLogin(LoginUserInfoBo loginUserInfoBo, String platformId, String appName) throws DbWriteException, UserException;

	/**
	 * 获取用户属性
	 * @param userInfoBo
	 * @return
	 */
	UserInfoResponse getUserInfo(UserInfoBo userInfoBo, String platformId, String appName) throws UserException;

	UserInfoResponse getUserInfo(UserInfoBo userInfoBo, String memberId, String platformId, String appName) throws UserException;

	/**
	 * 获取用户全部属性
	 * @param userInfoBo
	 * @param isCheckAndRefreshToken
	 * @return
	 * @throws UserException
	 */
	UserProductInfo getUserFullInfo(UserInfoBo userInfoBo, boolean isCheckAndRefreshToken, String platformId, String appName) throws Exception;

	/**
	 * 刷新用户信息
	 * @param userInfoBo
	 * @return
	 */
	Boolean refreshUserInfo(UserInfoBo userInfoBo, String platformId, String appName);

	/**
	 * 获取token
	 * @param userInfoBo
	 * @return
	 */
	UserInfoResponse getAccessToken(UserInfoBo userInfoBo, String platformId, String appName) throws Exception;

	UserInfoResponse getAccessToken(UserInfoBo userInfoBo, String memberId, String platformId, String appName) throws Exception;

	/**
	 * refreshToken
	 * @param userInfoBo
	 * @param refreshToken
	 * @return
	 * @throws Exception
	 */
	String refreshAccessToken(UserInfoBo userInfoBo, String refreshToken, String platformId, String appName) throws Exception;

	/**
	 * 重建用户信息
	 * @param userInfoBo
	 * @return
	 * @throws UserException
	 */
	UserInfoResponse rebuildUserInfo(UserInfoBo userInfoBo, String platformId, String appName) throws UserException;

	/**
	 * 更新用户缓存指定key的值
	 * @param userInfoBoList
	 * @param hKey
	 * @param value
	 * @return
	 */
	int batchUpdateUserCacheInfo(List<UserInfoBo> userInfoBoList, String hKey, String value);

	/**
	 * 获取用户缓存指定key的值
	 * @param userInfoBoList
	 * @param hKey
	 * @return
	 */
	List<UserCacheInfoResponse> batchGetUserCacheInfo(List<UserInfoBo> userInfoBoList, String hKey);
}
