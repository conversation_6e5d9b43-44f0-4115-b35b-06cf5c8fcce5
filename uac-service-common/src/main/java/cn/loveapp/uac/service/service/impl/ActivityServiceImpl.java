package cn.loveapp.uac.service.service.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.bo.CalculateBo;
import cn.loveapp.uac.common.constant.PromotionActFlag;
import cn.loveapp.uac.common.entity.PromotionActivity;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.common.service.PlatformFuwuItemCodeService;
import cn.loveapp.uac.common.utils.DateUtil;
import cn.loveapp.uac.db.common.dao.dream.ReturnMoneyInfoDao;
import cn.loveapp.uac.db.common.dao.dream.ReturnMoneyProjectInfoDao;
import cn.loveapp.uac.db.common.dao.dream.UserAlipayAccountInfoDao;
import cn.loveapp.uac.db.common.entity.ReturnMoneyInfo;
import cn.loveapp.uac.db.common.entity.ReturnMoneyProjectInfo;
import cn.loveapp.uac.db.common.entity.UserAlipayAccountInfo;
import cn.loveapp.uac.db.common.repository.PromotionActivityRepository;
import cn.loveapp.uac.service.config.ActivityConfig;
import cn.loveapp.uac.service.service.ActivityService;
import cn.loveapp.uac.service.service.CalculateService;
import cn.loveapp.uac.service.service.impl.CalculateServiceImpl.SellerLevelCycleEndTimeTemporary;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: uac-service-group
 * @description: ActivityServiceImpl
 * @author: Jason
 * @create: 2020-03-11 18:55
 **/
@Service
public class ActivityServiceImpl implements ActivityService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ActivityServiceImpl.class);

	@Autowired
	private UserAlipayAccountInfoDao userAlipayAccountInfoDao;
	@Autowired
	private ReturnMoneyInfoDao returnMoneyInfoDao;
	@Autowired
	private ReturnMoneyProjectInfoDao returnMoneyProjectInfoDao;
	@Autowired
	private PromotionActivityRepository promotionActivityRepository;

	@Autowired
	private CalculateService calculateService;

	@Autowired
	private PlatformFuwuItemCodeService fuwuItemCodeService;

	@Autowired
	private ActivityConfig activityConfig;

	/**
	 * 等待返现
	 */
	@Override
	public void waitLendDone(String sellerNick, String appType) {
		UserAlipayAccountInfo userAlipayAccountInfo = userAlipayAccountInfoDao.queryUserAlipayAccountInfoBySellerNickAndStatus(sellerNick, UserAlipayAccountInfo.LEND_INIT, appType);
		if (Objects.nonNull(userAlipayAccountInfo)) {
			String projectCode = activityConfig.getRefundMoneyProjectCode(appType);
			if(LOGGER.isDebugEnabled()) {
			    LOGGER.logDebug(sellerNick, "-", "读取UserAlipayAccountInfo表信息, " + userAlipayAccountInfo.toString());
			}
			int returnMoneyInfoCount = returnMoneyInfoDao.countByProjectCodeAndSellerNick(projectCode, sellerNick);
			LOGGER.logInfo(sellerNick, "-", "读取returnMoneyInfo表信息总数为:" + returnMoneyInfoCount);
			if (returnMoneyInfoCount == 0) {
				ReturnMoneyProjectInfo returnMoneyProjectInfo = returnMoneyProjectInfoDao.queryReturnMoneyProjectInfoByProjectCode(projectCode);
				if (Objects.nonNull(returnMoneyProjectInfo)) {
					if(LOGGER.isDebugEnabled()) {
					    LOGGER.logDebug(sellerNick, "-", "读取ReturnMoneyProjectInfo表信息, " + returnMoneyProjectInfo.toString());
					}
					int money = returnMoneyProjectInfo.getReturnMoney();
					ReturnMoneyInfo returnMoneyInfo = new ReturnMoneyInfo();
					returnMoneyInfo.setUserNick(sellerNick);
					returnMoneyInfo.setUserAlipayRealName(userAlipayAccountInfo.getSellerName());
					returnMoneyInfo.setUserAlipayAccount(userAlipayAccountInfo.getSellerAlipayAccount());
					returnMoneyInfo.setReturnMoney(money);
					returnMoneyInfo.setProjectCode(projectCode);
					returnMoneyInfo.setRegisterTime(userAlipayAccountInfo.getAddTime());
					returnMoneyInfo.setGmtCreate(LocalDateTime.now());
					returnMoneyInfo.setGmtModified(LocalDateTime.now());
					int returnId = 0;
					try{
						returnId = returnMoneyInfoDao.insert(returnMoneyInfo);
					} catch (Exception e) {
						LOGGER.logError(sellerNick, "-", "返现记录写入失败,原因是: " + e.getMessage(), e);
					} finally {
						if (returnId > 0) {
							UserAlipayAccountInfo alipayAccountInfo = new UserAlipayAccountInfo();
							alipayAccountInfo.setPushedReturn(true);
							alipayAccountInfo.setId(userAlipayAccountInfo.getId());
							userAlipayAccountInfoDao.update(alipayAccountInfo, appType);
						}
					}
				}
			}
		}
	}

	/**
	 * calculate promotion activity cycle end time and level
	 *
	 * @param sellerNick 用户昵称
	 * @param level 身份级别
	 * @param cycleEndTime 到达时间
	 * @param platformId
	 * @param appName
	 * @return left is level, right is order cycle end time
	 */
	@Override
	public Pair<Integer, LocalDateTime> calculatePromotionActivityLevelAndCycleEndTime(String sellerNick, Integer level, LocalDateTime cycleEndTime,
	                                                                                   String platformId, String appName) {
		// 聚合出来活动的剩余周期
		List<PromotionActivity> promotionActivityTrades = promotionActivityRepository.queryByIsUsedAndSellerNickSortOptime(sellerNick, PromotionActivity.UNUSED, platformId, appName);
		// 1. 如果没有赠送 传过来的就是正确的身份信息 如果没有买赠，万事大吉
		if (CollectionUtils.isEmpty(promotionActivityTrades)) {
			LOGGER.logInfo(sellerNick, "-", "查询到用户聚合后的活动剩余周期和时间为空");
			return Pair.of(level, cycleEndTime);
		}
		if(LOGGER.isDebugEnabled()) {
		    LOGGER.logDebug(sellerNick, "-", "查询到用户聚合后的活动剩余周期和时间: " + promotionActivityTrades);
		}
		// 2. 如果有赠送要进行下面一系列的判断
		// 是否新手村赠送
		boolean hasGivePresent = promotionActivityTrades.size() == 1 && PromotionActFlag.ZS == PromotionActFlag.of(promotionActivityTrades.get(0).getActflag());

		// 3. 合并排序赠送信息
		// 返回 左<赠送集合的id列表> ， 右<赠送的详细聚合信息>
		List<Pair<List<Integer>, PromotionActivity>> promotionActivityTradesPair = promotionActivityTrades.stream()
				.collect(Collectors.groupingBy(a -> PromotionActFlag.of(a.getActflag()).priority))
				.entrySet().stream()
				.flatMap(entry -> {
					List<Integer> ids = entry.getValue().stream()
							.map(PromotionActivity::getId)
							.collect(Collectors.toList());
					PromotionActivity mergedActivity = mergePromotionActivity(entry.getValue());
					return Lists.newArrayList(Pair.of(ids, mergedActivity)).stream();
				})
				.sorted(Comparator.comparingInt((Pair<List<Integer>, PromotionActivity> pair) -> PromotionActFlag.of(pair.getRight().getActflag()).priority).reversed())
				.collect(Collectors.toList());

		// 4. 按优先级处理赠送
		SellerLevelCycleEndTimeTemporary sellerLevelCycleEndTimeTemporary = null;
		for(Pair<List<Integer>, PromotionActivity> promotionActivityPair : promotionActivityTradesPair){
			PromotionActivity promotionActivity = promotionActivityPair.getRight();
			if (promotionActivity == null) {
				continue;
			}

			CalculateBo calculateBo = new CalculateBo();
			calculateBo.setPromotionActivity(promotionActivity);
			calculateBo.setSellerNick(sellerNick);
			calculateBo.setCycleEndTime(cycleEndTime);
			calculateBo.setCurrentDateTime(DateUtil.currentDate());
			calculateBo.setHasGivePresent(hasGivePresent);
			calculateBo.setLevel(level);
			calculateBo.setCurrentIds(promotionActivityPair.getLeft());
			// 获取赠送的vipFlag对应的itemCode
			PromotionActFlag promotionActFlag = PromotionActFlag.of(promotionActivity.getActflag());
			int promotionVipFlag = promotionActFlag.level;
			String promotionItemCode = fuwuItemCodeService.obtainItemCode(promotionVipFlag, platformId, appName);
			calculateBo.setPromotionItemCode(promotionItemCode);
			if(StringUtils.isEmpty(promotionItemCode)){
				LOGGER.logError(sellerNick, "", "无法获取到对应的 promotionItemCode : " + promotionVipFlag);
				continue;
			}

			int levelCompareResult = compareLevel(promotionVipFlag, level);
			int targetLevel = levelCompareResult > 0 ? promotionVipFlag : level;
			calculateBo.setTargetLevel(targetLevel);
			if (level > 0) {
				if(levelCompareResult >= 0) {
					sellerLevelCycleEndTimeTemporary = calculateService.calculateAdvancedSeller(calculateBo, platformId, appName);
				}else{
					// 忽略低级向高级的赠送
					continue;
				}
			} else {
				sellerLevelCycleEndTimeTemporary = calculateService.calculatePrimarySeller(calculateBo, platformId, appName);
			}
			if(LOGGER.isDebugEnabled()) {
				LOGGER.logDebug(sellerNick, "-", "进行重新计算前得出的用户属性参数<" + sellerLevelCycleEndTimeTemporary + ">");
			}
			recalculateLevelCycleEndTime(sellerNick, sellerLevelCycleEndTimeTemporary, cycleEndTime, platformId, appName);
			if(LOGGER.isDebugEnabled()) {
				LOGGER.logDebug(sellerNick, "-", "进行重新计算后得出的用户最终属性参数<" + sellerLevelCycleEndTimeTemporary + ">");
			}
			if(!sellerLevelCycleEndTimeTemporary.getIsAllUsed()){
				break;
			}
		}
		if(sellerLevelCycleEndTimeTemporary == null){
			return Pair.of(level, cycleEndTime);
		}
		return Pair.of(sellerLevelCycleEndTimeTemporary.getLevel(), sellerLevelCycleEndTimeTemporary.getOrderCycleEndTime());
	}

	/**
	 * 需要重新计算一次
	 * @param sellerNick
	 * @param sellerLevelCycleEndTimeTemporary
	 * @param cycleEndTime
	 */
	protected void recalculateLevelCycleEndTime(String sellerNick, SellerLevelCycleEndTimeTemporary sellerLevelCycleEndTimeTemporary, LocalDateTime cycleEndTime, String platformId, String appName) {
		//1 全部用完了
		if (BooleanUtils.isTrue(sellerLevelCycleEndTimeTemporary.getIsAllUsed())){
			LOGGER.logInfo(sellerNick, "-", "用户赠送耗尽, 将所有赠送记录更改为已使用");
			promotionActivityRepository.updateIsUsedOrActCycleByPkIds(PromotionActivity.USED, null, sellerLevelCycleEndTimeTemporary.getAllIds(), platformId, appName);
			return;
		}
		//2 先判断一下是否重新需要聚合
		if (!BooleanUtils.isTrue(sellerLevelCycleEndTimeTemporary.getReAggregationCalculate())) {
			return;
		}
		LOGGER.logInfo(sellerNick, "-", "拆分赠送记录");
		boolean successUp = false;
		List<Integer> upIds = sellerLevelCycleEndTimeTemporary.getUseUpIds();
		int lastUseActCycle = Objects.isNull(sellerLevelCycleEndTimeTemporary.getLastUseActCycle()) ? 0 : sellerLevelCycleEndTimeTemporary.getLastUseActCycle();
		int lastUseId = Objects.isNull(sellerLevelCycleEndTimeTemporary.getLastUseId()) ? 0 : sellerLevelCycleEndTimeTemporary.getLastUseId();
		PromotionActivity newSplitActivity = sellerLevelCycleEndTimeTemporary.getNewSplitActivity();
		List<PromotionActivity> needRefreshOpttimeActivityList = sellerLevelCycleEndTimeTemporary.getNeedRefreshOpttimeActivityList();
		try {
			if (!upIds.isEmpty()) {
				if(LOGGER.isDebugEnabled()) {
					LOGGER.logDebug(sellerNick, "-", "处理过期的赠送记录" + JSON.toJSONString(upIds));
				}
				promotionActivityRepository.updateIsUsedOrActCycleByPkIds(PromotionActivity.USED, null, upIds, platformId, appName);
			}
			if (lastUseId > 0) {
				if(LOGGER.isDebugEnabled()) {
					LOGGER.logDebug(sellerNick, "-", "处理拆分后原始的赠送记录变更为过期" + lastUseId);
				}
				promotionActivityRepository.updateIsUsedOrActCycleByPkIds(PromotionActivity.USED, lastUseActCycle,
					Collections.singletonList(lastUseId), platformId, appName);
			}
			if (!Objects.isNull(newSplitActivity) && newSplitActivity.getActCycle() != null && newSplitActivity.getActCycle() > 0) {
				if(LOGGER.isDebugEnabled()) {
					LOGGER.logDebug(sellerNick, "-", "处理拆分后新赠送记录写入" + JSON.toJSONString(newSplitActivity));
				}
				promotionActivityRepository.insert(newSplitActivity, platformId, appName);
			}
			//赠送重叠，上一次的赠送还未使用完毕,这一次的赠送记录变更赠送时间
			if (CollectionUtils.isNotEmpty(needRefreshOpttimeActivityList)) {
				if(LOGGER.isDebugEnabled()) {
					LOGGER.logDebug(sellerNick, "-", "处理拆分后未使用的赠送记录写入" + JSON.toJSONString(needRefreshOpttimeActivityList));
				}
				promotionActivityRepository.insertBatch(needRefreshOpttimeActivityList, platformId, appName);
			}
			//写入新的记录
			successUp = true;
		} catch (Exception e) {
			LOGGER.logError(sellerNick, "-", "拆分时写入数据库失败: " + e.getMessage(), e);
		} finally {
			if (successUp) {
				LOGGER.logInfo(sellerNick, "-", "拆分处理成功, 重新聚合计算");
				List<PromotionActivity> promotionActivityTrade = promotionActivityRepository
					.aggregationActCycleAndOptimeBySellerNickAndUnused(sellerNick, PromotionActivity.UNUSED, platformId, appName);
				// 过滤匹配当前赠送版本的活动
				PromotionActFlag promotionActFlag = PromotionActFlag.of(newSplitActivity.getActflag());
				promotionActivityTrade = promotionActivityTrade.stream()
					.filter(a -> PromotionActFlag.of(a.getActflag()) == promotionActFlag).collect(Collectors.toList());
				if (promotionActivityTrade.size() > 0) {
					PromotionActivity promotionActivity = mergePromotionActivity(promotionActivityTrade);
					sellerLevelCycleEndTimeTemporary
						.setOrderCycleEndTime(cycleEndTime.plusDays(promotionActivity.getActCycle()));
				} else {
					sellerLevelCycleEndTimeTemporary.setOrderCycleEndTime(cycleEndTime);
				}
			}
		}
	}

	/**
	 * 校验是否能赠送
	 *
	 * @param promotionVipFlag
	 * @param level
	 * @return
	 */
    @Override
	public int compareLevel(int promotionVipFlag, int level) {
		if (promotionVipFlag == UserProductInfo.LEVEL_ONE) {
			if (level == UserProductInfo.LEVEL_TWO) {
				return -1;
			} else if (level == UserProductInfo.LEVEL_THREE || level == UserProductInfo.LEVEL_FOUR
				|| level == UserProductInfo.LEVEL_FIVE) {
				return 0;
			}
		} else if (promotionVipFlag == UserProductInfo.LEVEL_TWO || promotionVipFlag == UserProductInfo.LEVEL_FOUR
			|| promotionVipFlag == UserProductInfo.LEVEL_FIVE) {
			if (level == UserProductInfo.LEVEL_ONE || level == UserProductInfo.LEVEL_THREE) {
				return -1;
			}
        } else if (promotionVipFlag == UserProductInfo.LEVEL_SIX && level == UserProductInfo.LEVEL_EIGHT) {
            //版本6 大于 版本8
            return 1;
        } else if (promotionVipFlag == UserProductInfo.LEVEL_EIGHT && level == UserProductInfo.LEVEL_SIX) {
            //版本6 大于 版本8
            return -1;
        }
        return promotionVipFlag - level;
	}

	private PromotionActivity mergePromotionActivity(List<PromotionActivity> list) {
		if (list.size() == 1) {
			return list.get(0);
		}
		// mz有很多Actflag, 统一为mz
		PromotionActivity activity = new PromotionActivity();
		// 合并actCycle
		activity.setActCycle(list.stream().mapToInt(PromotionActivity::getActCycle).sum());
		// 取最小Optime
		activity.setOptime(list.stream().map(PromotionActivity::getOptime).min(LocalDateTime::compareTo).orElse(null));
		activity.setActflag(PromotionActFlag.of(list.get(0).getActflag()).name().toLowerCase());
		return activity;
	}

}
