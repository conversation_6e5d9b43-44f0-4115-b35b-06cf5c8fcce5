package cn.loveapp.uac.service.platform.biz.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.uac.response.CallbackResponse;
import cn.loveapp.uac.common.bo.AuthBo;
import cn.loveapp.uac.service.platform.biz.CallbackPlatformHandleService;
import cn.loveapp.uac.service.service.OAuthDecorationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: uac-service-group
 * @description:
 * @author: Jason
 * @create: 2021-04-19 17:27
 **/
@Service
public class PddCallbackPlatformHandleServiceImpl implements CallbackPlatformHandleService {

	@Autowired
	private OAuthDecorationService oAuthDecorationService;

	@Override
	public CallbackResponse authCallback(AuthBo authBo, String platformId, String appName) {
		return oAuthDecorationService.authCodeAndRefreshUser(authBo);
	}

	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_PDD;
	}
}
