package cn.loveapp.uac.service.service;

import cn.loveapp.uac.common.entity.PromotionActivity;
import cn.loveapp.uac.service.base.BaseOperationService.OperationUserInfo;
import cn.loveapp.uac.common.bo.UserInfoBo;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.commons.lang3.tuple.Pair;

/**
 * @program: uac-service-group
 * @description: ActivityService
 * @author: Jason
 * @create: 2020-03-11 16:07
 **/
public interface ActivityService {

	/**
	 * 等待返现
	 * @param sellerNick 用户昵称
	 * @param appType 应用类型
	 */
	void waitLendDone(String sellerNick, String appType);

	/**
	 * calculate promotion activity cycle end time and level 计算赠送活动周期结束时间和级别
	 * @param sellerNick 用户昵称
	 * @param level 身份级别
	 * @param cycleEndTime 到达时间
	 * @return left is level, right is order cycle end time
	 */
	Pair<Integer, LocalDateTime> calculatePromotionActivityLevelAndCycleEndTime(String sellerNick, Integer level, LocalDateTime cycleEndTime,
	                                                                            String platformId, String appName);

    /**
     * 校验是否能赠送
     *
     * @param promotionVipFlag
     * @param level
     * @return
     */
    int compareLevel(int promotionVipFlag, int level);
}
