package cn.loveapp.uac.service.service;

import cn.loveapp.uac.common.bo.CalculateBo;
import cn.loveapp.uac.service.service.impl.CalculateServiceImpl.SellerLevelCycleEndTimeTemporary;

/**
 * @program: uac-service-group
 * @description: CalculateService
 * @author: <PERSON>
 * @create: 2020-03-09 12:04
 **/
public interface CalculateService {

	/**
	 * 计算高级用户
	 * @param calculateBo
	 * @return
	 */
	SellerLevelCycleEndTimeTemporary calculateAdvancedSeller(CalculateBo calculateBo, String platformId, String appName);

	/**
	 * 计算普通用户
	 * @param calculateBo
	 * @return
	 */
	SellerLevelCycleEndTimeTemporary calculatePrimarySeller(CalculateBo calculateBo, String platformId, String appName);

}
