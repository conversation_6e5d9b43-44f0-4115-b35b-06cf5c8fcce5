package cn.loveapp.uac.service.service.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.exception.CacheWriteException;
import cn.loveapp.uac.common.exception.DbWriteException;
import cn.loveapp.uac.common.platform.api.AuthService;
import cn.loveapp.uac.common.platform.api.domain.RefreshTokenCallbackResult;
import cn.loveapp.uac.common.bo.UserBo;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.proto.SubscribeUserMessageRequestProto;
import cn.loveapp.uac.response.CallbackResponse;
import cn.loveapp.uac.service.base.BaseOAuthService;
import cn.loveapp.uac.common.bo.AuthBo;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.service.event.SubscribeUserMessageEventHandler;
import cn.loveapp.uac.service.service.OAuthDecorationService;
import cn.loveapp.uac.service.service.SellerService;

import java.time.LocalDateTime;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: uac-service-group
 * @description: OAuthServiceImpl
 * @author: Jason
 * @create: 2020-03-14 11:31
 **/
@Service
public class OAuthDecorationServiceImpl extends BaseOAuthService implements OAuthDecorationService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OAuthDecorationServiceImpl.class);

	@Autowired
	private SellerService sellerService;

	@Autowired
	private SubscribeUserMessageEventHandler subscribeUserMessageEventHandler;
	@Autowired
	private AuthService authService;


	@Override
	public CallbackResponse authCodeAndRefreshUser(AuthBo authBo) {
		String platformId = authBo.getPlatformId();
		String code = authBo.getCode();
		String sellerNick = "";
		UserBo userBo = new UserBo();
		try {
			userBo = getAuthCodeResult(authBo);
			sellerNick = userBo.getSellerNick();
			UserProductInfo userProductInfo = sellerService.getUserInfoByDb(userBo, platformId, authBo.getAppType());
			if (Objects.isNull(userProductInfo) && platformId.equals(CommonPlatformConstants.PLATFORM_TAO)) {
				LOGGER.logError(sellerNick, code, "当前code is " + code + ", 数据库查询失败, 没有找到对应的用户");
				return null;
			}
			if (Objects.isNull(userProductInfo) && platformId.equals(CommonPlatformConstants.PLATFORM_PDD)) {
				sellerService.saveUserInfoCacheAndTable(userBo, platformId, authBo.getAppType());
			} else {
				sellerService.refreshUserInfoCacheAndTable(userBo, platformId, authBo.getAppType());
			}
			//通知业务用户授权成功
			eventHandle(userBo);
			//开通TMC消息
//			 messageCenterService.subscibeUserMessageService(userBo.toMessageRequest(null), platformId);
			return userBo.toCallbackResponse();
		} catch (DbWriteException db) {
			LOGGER.logError(sellerNick, code, "当前code is " + code + ", 数据库写入失败: " + db.getMessage(), db);
		} catch (CacheWriteException cache) {
			LOGGER.logError(sellerNick, code, "当前code is " + code + ", 缓存写入失败: " + cache.getMessage(), cache);
			return userBo.toCallbackResponse();
		} catch (Exception ioe) {
			LOGGER.logError(sellerNick, code, "当前code is " + code + ", 请求失败, 原因为: " + ioe.getMessage(), ioe);
		}
		return null;
	}

	@Override
	public UserBo getAuthCodeResult(AuthBo authBo) throws Exception {
		return super.authCode(authBo, authService);
	}

	/**
	 * 刷新token
	 */
	@Override
	public CallbackResponse refreshAccessToken(UserInfoBo userInfoBo, String refreshToken, String platformId, String appName) {
		String decryptAccessToken = "";
		UserBo userBo;
		String sellerNick = userInfoBo.getSellerNick();
		try {
			String decryptRefreshToken = authService.decryptToken(refreshToken, platformId, appName);
			RefreshTokenCallbackResult refreshTokenCallbackResult = authService.refreshToken(userInfoBo, decryptRefreshToken, platformId, appName);
			if (refreshTokenCallbackResult == null) {
				return null;
			}
			decryptAccessToken = refreshTokenCallbackResult.getDecryptAccessToken();
			LocalDateTime w1DeadLine = refreshTokenCallbackResult.getAuthDeadLine();
			UserProductInfo userProductInfo = UserProductInfo.of(refreshTokenCallbackResult.getSellerNick(), refreshTokenCallbackResult.getSellerId(), UserProductInfo.NO_NEED_AUTH);
			userProductInfo.initDefault();
			userProductInfo.setToprefreshkey(refreshTokenCallbackResult.getRefreshToken());
			userProductInfo.setTopsessionkey(refreshTokenCallbackResult.getAccessToken());
			userProductInfo.setW1Deadline(w1DeadLine);
			userBo = new UserBo(platformId, appName, refreshTokenCallbackResult.getSellerNick(), userProductInfo);
			userBo.setDecryptAccessToken(refreshTokenCallbackResult.getDecryptAccessToken());
			sellerService.refreshUserInfoCacheAndTable(userBo, platformId, appName);
			return userBo.toCallbackResponse();
		} catch (DbWriteException db) {
			LOGGER.logError(sellerNick, refreshToken, "当前code is " + refreshToken + ", 数据库写入失败: " + db.getMessage(), db);
		} catch (CacheWriteException cache) {
			LOGGER.logError(sellerNick, refreshToken, "当前code is " + refreshToken + ", 缓存写入失败: " + cache.getMessage(), cache);
			return CallbackResponse.of(sellerNick, decryptAccessToken);
		} catch (Exception ioe) {
			LOGGER.logError(sellerNick, refreshToken, "当前code is " + refreshToken + ", 请求失败, 原因为: " + ioe.getMessage(), ioe);
		}
		return null;
	}

	public void eventHandle(Object o) {
		UserBo userBo = (UserBo) o;
		UserInfoBo userInfoBo = new UserInfoBo();
		userInfoBo.setSellerNick(userBo.getSellerNick());
		userInfoBo.setSubSellerNick(userBo.getSubSellerNick());
		userInfoBo.setPlatformId(userBo.getPlatformId());
		userInfoBo.setAppType(userBo.getAppType());
		userInfoBo.setSellerId(userBo.getSellerId());
		subscribeUserMessageEventHandler.eventHandle(userInfoBo, "oauthSuccess", SubscribeUserMessageRequestProto.OAUTH_TYPE);
	}

}
