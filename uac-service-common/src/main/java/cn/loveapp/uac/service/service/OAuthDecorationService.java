package cn.loveapp.uac.service.service;

import cn.loveapp.uac.common.bo.UserBo;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.response.CallbackResponse;
import cn.loveapp.uac.common.bo.AuthBo;

/**
 * @program: uac-service-group
 * @description: OAuthService
 * @author: Jason
 * @create: 2020-03-14 11:30
 **/
public interface OAuthDecorationService {

	/**
	 * authCodeAndRefreshUser
	 * @param authBo
	 * @return
	 */
	CallbackResponse authCodeAndRefreshUser(AuthBo authBo);

	/**
	 * 通过code获取token
	 */
	UserBo getAuthCodeResult(AuthBo authBo) throws Exception;

	/**
	 * 刷新token
	 * @param userInfoBo
	 * @param refreshToken
	 * @param platformId
	 * @param appName
	 * @return
	 * @throws Exception
	 */
	CallbackResponse refreshAccessToken(UserInfoBo userInfoBo, String refreshToken, String platformId, String appName);
}
