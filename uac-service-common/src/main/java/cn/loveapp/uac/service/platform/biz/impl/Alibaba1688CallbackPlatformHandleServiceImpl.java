package cn.loveapp.uac.service.platform.biz.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.uac.response.CallbackResponse;
import cn.loveapp.uac.common.bo.AuthBo;
import cn.loveapp.uac.service.platform.biz.CallbackPlatformHandleService;
import org.springframework.stereotype.Service;

/**
 * @program: uac-service-group
 * @description:
 * @author: <PERSON>
 * @create: 2021-04-19 17:27
 **/
@Service
public class Alibaba1688CallbackPlatformHandleServiceImpl implements CallbackPlatformHandleService {

	@Override
	public CallbackResponse authCallback(AuthBo authBo, String platformId, String appType) {
		return null;
	}

	@Override
	public String getPlatformId() {
		return CommonPlatformConstants.PLATFORM_1688;
	}
}
