package cn.loveapp.uac.service.service.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.constant.PromotionActFlag;
import cn.loveapp.uac.common.utils.DateUtil;
import cn.loveapp.uac.common.entity.PromotionActivity;
import cn.loveapp.uac.db.common.entity.OrderSearch;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.db.common.repository.PromotionActivityRepository;
import cn.loveapp.uac.db.common.repository.OrderSearchRepository;
import cn.loveapp.uac.common.bo.CalculateBo;
import cn.loveapp.uac.service.service.CalculateService;
import com.alibaba.fastjson2.JSON;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import com.google.common.collect.ImmutableList;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: uac-service-group
 * @description: CalculateServiceImpl
 * @author: Jason
 * @create: 2020-03-09 12:04
 **/
@Service
public class CalculateServiceImpl implements CalculateService {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(CalculateServiceImpl.class);

	@Autowired
	private OrderSearchRepository orderSearchRepository;
	@Autowired
	private PromotionActivityRepository promotionActivityRepository;

	/**
	 * 计算高级版本用户逻辑
	 *
	 * @see cn.loveapp.uac.service.service.impl.ActivityServiceImpl#recalculateLevelCycleEndTime
	 *
	 * @return
	 */
	@Override
	public SellerLevelCycleEndTimeTemporary calculateAdvancedSeller(CalculateBo calculateBo, String platformId, String appName) {
		String sellerNick = calculateBo.getSellerNick();
		String promotionItemCode = calculateBo.getPromotionItemCode();
		PromotionActivity promotionActivity = calculateBo.getPromotionActivity();
		LocalDateTime cycleEndTime = calculateBo.getCycleEndTime();
		LocalDateTime currentDateTime = calculateBo.getCurrentDateTime();
		PromotionActFlag promotionActFlag = PromotionActFlag.of(promotionActivity.getActflag());
		int targetLevel = calculateBo.getTargetLevel();
		int level = calculateBo.getLevel();
		boolean levelChanged = targetLevel != level;
		String levelChangLog = level + ">" + targetLevel;
		/**
		 * 初始化一些参数
		 */
		SellerLevelCycleEndTimeTemporary sellerLevelCycleEndTimeTemporary = new SellerLevelCycleEndTimeTemporary(
			Boolean.FALSE, Boolean.FALSE, level, cycleEndTime, calculateBo.getCurrentIds()
		);

        //在本次订购期内订购的相同版本的订购数据，订购数据累加
		OrderSearch orderSearchTrade = orderSearchRepository.queryBySellerNickAndOrderCycleStartAndOrderCycleEndAndItemCode(sellerNick,
			currentDateTime, currentDateTime, ImmutableList.of(promotionItemCode), OrderSearch.ASC_SORT_BY, platformId, appName);
		if(LOGGER.isDebugEnabled()) {
		    LOGGER.logDebug(sellerNick, levelChangLog, "查询到用户当前版本订购记录的记录集为:" + JSON.toJSONString(orderSearchTrade));
		}
		LocalDateTime opTime = promotionActivity.getOptime();
		Integer actCycle = promotionActivity.getActCycle();
		if (Objects.isNull(orderSearchTrade)) {
			// 当前没有相同版本的订购
			if(!levelChanged){
				// 相同版本赠送
				LOGGER.logInfo(sellerNick, levelChangLog, "未订购当前版本, 直接追加赠送时长");
				//场景：当前时间不在订购期内（没有订购），但是赠送可能大于订购结束期，此时计算剩余时长应该使用赠送开始时间
				if (opTime.isAfter(cycleEndTime)) {
					LOGGER.logInfo(sellerNick, "", "存在订购到期，但是赠送开始时间晚于订购到期时间的数据，将计算到期时间" + cycleEndTime + "修改为赠送时间" + opTime);
					cycleEndTime = opTime;
				}
				LocalDateTime realCycleEndTime = DateUtil.calculateCustomDay(cycleEndTime, Long.valueOf(actCycle));
				sellerLevelCycleEndTimeTemporary.setOrderCycleEndTime(realCycleEndTime);
				sellerLevelCycleEndTimeTemporary.setLevel(targetLevel);
			}else{
				// 不同版本赠送
				LocalDateTime now = DateUtil.toDateTimeZeroHourMinusSecond(calculateBo.getCurrentDateTime());
				LocalDateTime lastActCycle = opTime.plusDays(actCycle).withHour(0).withMinute(0).withSecond(0).withNano(0);
				if(lastActCycle.isAfter(now)) {
					long diffDay = ChronoUnit.DAYS.between(now, lastActCycle);
					LOGGER.logInfo(calculateBo.getSellerNick(), levelChangLog, "未订购当前版本, 剩余赠送时长 " + diffDay);
					sellerLevelCycleEndTimeTemporary.setLevel(targetLevel);
					sellerLevelCycleEndTimeTemporary.setOrderCycleEndTime(lastActCycle);
				} else {
					LOGGER.logInfo(calculateBo.getSellerNick(), levelChangLog, "未订购当前版本, 赠送时长已耗尽");
					sellerLevelCycleEndTimeTemporary.setIsAllUsed(Boolean.TRUE);
				}
			}
		} else {
			// 当前有相同版本的订购
			// 是否在订单有效期内
			boolean inOrderTime = DateUtil.compareToTwoDateTimeByDateTime(opTime,
				Pair.of(orderSearchTrade.getOrderCycleStart(), orderSearchTrade.getOrderCycleEnd()));
			if (inOrderTime) {
				// 订购时间内
				LOGGER.logInfo(sellerNick, levelChangLog, "已订购当前版本, 订购期间赠送, 直接追加赠送时长");
				LocalDateTime realCycleEndTime = DateUtil.calculateCustomDay(cycleEndTime, Long.valueOf(actCycle));
				sellerLevelCycleEndTimeTemporary.setOrderCycleEndTime(realCycleEndTime);
			} else {
				// 上次订购此版本的记录
				OrderSearch previousOrderSearch = orderSearchRepository.
					queryBySellerNickAndItemCodeAndIdSortIdDesc(sellerNick, promotionItemCode, orderSearchTrade.getId(), platformId, appName);
				if(LOGGER.isDebugEnabled()) {
				    LOGGER.logDebug(sellerNick, levelChangLog, "查询用户之前是否有购买过版本订单, previousOrderSearch => " + JSON.toJSONString(previousOrderSearch));
				}
				//计算差异时间
				//开始拆单 因为有没有耗完的, 之后需要重新聚合计算 OrderCycleEndTime
				LOGGER.logInfo(sellerNick, levelChangLog, "已订购当前版本, 赠送时长还有剩余, 需要计算并拆单处理");
				List<PromotionActivity> promotionActivityTrades = promotionActivityRepository.queryByIsUsedAndSellerNickSortOptime(sellerNick, PromotionActivity.UNUSED, platformId, appName);
				if(LOGGER.isDebugEnabled()) {
					LOGGER.logDebug(sellerNick, levelChangLog, "查询用户未使用完的优惠信息记录, queryByIsUsedAndSellerNickSortOptime => " + JSON.toJSONString(promotionActivityTrades));
				}
				// 过滤出当前赠送版本的记录
				promotionActivityTrades = promotionActivityTrades.stream()
					.filter(a -> PromotionActFlag.of(a.getActflag()) == promotionActFlag).collect(Collectors.toList());
				List<Integer> useUpIds = new ArrayList<>(Collections.emptyList());
				//上一个赠送剩余到期时间（连续赠送后订购，此时赠送时间应该是累积的，而非两条记录都做时间减法）
				PromotionActivity previousActivity = null;
				int handleActivityCount = 0;
				for (PromotionActivity activityTrade : promotionActivityTrades) {
					handleActivityCount++;
					int promotionActivityId = activityTrade.getId();
					Integer sourceActCycle = activityTrade.getActCycle();
					activityTrade = getCalcActivity(previousActivity,activityTrade, previousOrderSearch, orderSearchTrade);
					if (activityTrade.getIsused()) {
						useUpIds.add(promotionActivityId);
						previousActivity = activityTrade;
						continue;
					}

					//赠送未使用完
					//从这里开始就要拆了
					//标记已经使用完的id
					sellerLevelCycleEndTimeTemporary.setLastUseId(promotionActivityId);
					//标记寿命
					sellerLevelCycleEndTimeTemporary.setLastUseActCycle((sourceActCycle - activityTrade.getActCycle()));
					//接下来处理拆下来的信息记录以及剩余寿命, 先算出来剩余寿命
					PromotionActivity splitActivity = new PromotionActivity();
					splitActivity.setSellernick(sellerNick);
					splitActivity.setActCycle(activityTrade.getActCycle());
					splitActivity.setOptime(DateUtil.currentDate(true));
					splitActivity.setActflag(promotionActFlag.name().toLowerCase());
					splitActivity.setSender("SYS-赠送拆分");
					splitActivity.setPromotionCode("FROM ID: " + promotionActivityId);
					splitActivity.setIsused(Boolean.FALSE);
					sellerLevelCycleEndTimeTemporary.setNewSplitActivity(splitActivity);
					break;
				}

				long usedActivityCount = promotionActivityTrades.stream().filter(f -> f.getIsused()).count();
				if (usedActivityCount == promotionActivityTrades.size()) {
					LOGGER.logInfo(sellerNick, levelChangLog, "已订购当前版本, 赠送时长已耗尽");
					sellerLevelCycleEndTimeTemporary.setIsAllUsed(Boolean.TRUE);
					sellerLevelCycleEndTimeTemporary.setOrderCycleEndTime(cycleEndTime);
					return sellerLevelCycleEndTimeTemporary;
				}

				//将未处理的赠送记录修改为已使用，新增一条记录将时间修改为今天 ,不然下次刷新会对此记录重新计算，会导致重叠赠送记录条件下重复扣除天数
				if (handleActivityCount != promotionActivityTrades.size() && CollectionUtils.isNotEmpty(promotionActivityTrades)) {
					List<PromotionActivity> unHandleActivityList = new ArrayList<>();
					if (handleActivityCount == 0) {
						unHandleActivityList = promotionActivityTrades;
					} else{
						unHandleActivityList = promotionActivityTrades.subList(handleActivityCount, promotionActivityTrades.size());
					}
					LocalDateTime tempCurrentDateTime = DateUtil.currentDate(true);
					LocalDate tempCurrentDate = tempCurrentDateTime.toLocalDate();
					List<PromotionActivity> finalUnHandleActivityList = new ArrayList<>();
					for (PromotionActivity activity : unHandleActivityList) {
						LocalDate originOpttimeDate = activity.getOptime().toLocalDate();
						//如果赠送时间小于当前时间，那么需要将老数据设置为已使用，新增赠送记录时间重写
						if (originOpttimeDate != null && originOpttimeDate.isBefore(tempCurrentDate)) {
							useUpIds.add(activity.getId());
							PromotionActivity splitActivity = new PromotionActivity();
							splitActivity.setSellernick(sellerNick);
							splitActivity.setActCycle(activity.getActCycle());
							splitActivity.setOptime(DateUtil.currentDate(true));
							splitActivity.setActflag(activity.getActflag());
							splitActivity.setSender("SYS-赠送重叠拆分");
							splitActivity.setPromotionCode("FROM ID: " + activity.getId());
							splitActivity.setIsused(Boolean.FALSE);
							finalUnHandleActivityList.add(splitActivity);
						}
					}
					sellerLevelCycleEndTimeTemporary.setNeedRefreshOpttimeActivityList(finalUnHandleActivityList);
				}


				/*
				  会在这里处理
				  @see cn.loveapp.uac.service.service.impl.ActivityServiceImpl#recalculateLevelCycleEndTime
				 */
				sellerLevelCycleEndTimeTemporary.setReAggregationCalculate(Boolean.TRUE);
				sellerLevelCycleEndTimeTemporary.setUseUpIds(useUpIds);
			}
		}
		return sellerLevelCycleEndTimeTemporary;
	}

	/**
	 * 获取计算后的赠送数据
	 * @param activity
	 * @param previousOrderSearch
	 * @param orderSearchTrade
	 * @return
	 */
	private PromotionActivity getCalcActivity(PromotionActivity previousActivity, PromotionActivity activity, OrderSearch previousOrderSearch, OrderSearch orderSearchTrade) {
		//如果上次订购不为空，因为上面已经判断过实在订购期内直接增加了时间，所以这里必定是上次订购到期了的
		if (previousActivity == null) {
			previousActivity = new PromotionActivity();
			BeanUtils.copyProperties(activity, previousActivity);
		} else if (previousActivity.getOptime() != null && previousActivity.getActCycle() != null) {
			LocalDateTime cycleEndTime = previousActivity.getOptime().plusDays(previousActivity.getActCycle());
			//上次赠送结束时间不为空,且存在于当前赠送开始结束时间内，则说明出现赠送重叠，将重叠的天数累积到下一个赠送的cycle里
			if (DateUtil.compareToTwoDateTimeByDateTime(cycleEndTime, Pair.of(activity.getOptime(), activity.getOptime().plusDays(activity.getActCycle())))) {
				long activitySurplusDay = ChronoUnit.DAYS.between(activity.getOptime(), cycleEndTime);
				activity.setActCycle((int) (activity.getActCycle() + activitySurplusDay));
			}
			activity.setOptime(cycleEndTime);
		}
		if (previousOrderSearch != null) {
			activity = calcActivitySurplusDays(activity, previousOrderSearch.getOrderCycleStart(), previousOrderSearch.getOrderCycleEnd());
		}
		if (!activity.getIsused()) {
			activity = calcActivitySurplusDays(activity, orderSearchTrade.getOrderCycleStart(), orderSearchTrade.getOrderCycleEnd());
		}
		return activity;
	}

	/**
	 * 计算赠送剩余时间
	 * @param activity
	 * @param orderCycleStart
	 * @param orderCycleEnd
	 * @return
	 */
	private PromotionActivity calcActivitySurplusDays(PromotionActivity activity, LocalDateTime orderCycleStart, LocalDateTime orderCycleEnd){
		LocalDateTime optime = activity.getOptime();
		LocalDateTime giveFinalEndTime = optime.plusDays(activity.getActCycle());
		boolean inOrderTime = DateUtil.compareToTwoDateTimeByDateTime(giveFinalEndTime,
				Pair.of(orderCycleStart, orderCycleEnd));
		//计算本赠送记录所剩余时间
		if (inOrderTime) {
			//如果在期间内，说明赠送时间用不完，反之说明赠送时间已全部被消费
			//订购开始时间1号，结束10号，赠送到期时间为6号，
			//1:对赠送开始时间 和 订购开始时间 比较，前者大于后者说明赠送完全未被使用（结束时间在订购期内的前提下），前者小于后者说明订购开始时间到赠送结束时间内未被消费
			LocalDateTime useCompareStart = orderCycleStart;
			if (optime.isAfter(orderCycleStart)) {
				useCompareStart = optime;
			}
			long activitySurplusDay = ChronoUnit.DAYS.between(useCompareStart, giveFinalEndTime);
			if (activitySurplusDay >= 0) {
				activity.setActCycle((int) activitySurplusDay);
				optime.plusDays(activitySurplusDay);
				activity.setOptime(orderCycleEnd);
			}
		} else if (giveFinalEndTime.isAfter(orderCycleEnd)) {
			//赠送结束时间大于订购结束时间 也说明说明赠送时间用不完。订购开始时间1号，结束10号，赠送到期时间为15号，则完全不需要消耗赠送时长
			//但可能存在赠送开始时间大于订购开始时间，会消耗一定的赠送天数
			if (optime.isBefore(orderCycleStart)) {
				long activitySurplusDay = ChronoUnit.DAYS.between(optime, orderCycleStart);
				if (activitySurplusDay > 0) {
					long diffDays = activity.getActCycle() - activitySurplusDay;
					activity.setActCycle((int) diffDays);
					optime.plusDays(diffDays);
					activity.setOptime(orderCycleEnd);
				}
			} else if (DateUtil.compareToTwoDateTimeByDateTime(optime, Pair.of(orderCycleStart, orderCycleEnd))) {
				//如果赠送结束时间大于订购结束时间 并且 赠送开始时间处于订购期内，则说明订购期内的赠送时间完全没有被消费,修改赠送开始时间为订购结束时间
				activity.setOptime(orderCycleEnd);
			}
		} else {
			activity.setIsused(true);
		}
		return activity;
	}

	/**
	 * 计算初级版本用户逻辑
	 * @return
	 */
	@Override
	public SellerLevelCycleEndTimeTemporary calculatePrimarySeller(CalculateBo calculateBo, String platformId, String appName) {
		SellerLevelCycleEndTimeTemporary sellerLevelCycleEndTimeTemporary = new SellerLevelCycleEndTimeTemporary(
			Boolean.FALSE, Boolean.FALSE, calculateBo.getLevel(), calculateBo.getCycleEndTime(), calculateBo.getCurrentIds()
		);
		int targetLevel = calculateBo.getTargetLevel();
		String levelChangLog = calculateBo.getLevel() + ">" + targetLevel;

		OrderSearch orderSearchTrade = orderSearchRepository.queryBySellerNickAndOrderCycleStartAndOrderCycleEndAndItemCode(
				calculateBo.getSellerNick(),
				null, null, Arrays.asList(calculateBo.getPromotionItemCode()), OrderSearch.DESC_SORT_BY, platformId, appName);
		if(LOGGER.isDebugEnabled()) {
		    LOGGER.logDebug(calculateBo.getSellerNick(), levelChangLog, "查询到用户是否订购过该版本, 订购记录的记录集为:" + JSON.toJSONString(orderSearchTrade));
		}
		LocalDateTime opTime = calculateBo.getPromotionActivity().getOptime();
		Integer actCycle = calculateBo.getPromotionActivity().getActCycle();
		LocalDateTime now = DateUtil.toDateTimeZeroHourMinusSecond(calculateBo.getCurrentDateTime());

		// 初级版新手赠送 (新手村用户与初级公用itemCode, 没有专用的itemCode, 不需要判断订购记录)
		boolean isNewVillageUser = UserProductInfo.LEVEL_ZERO.equals(calculateBo.getLevel()) && UserProductInfo.LEVEL_FOUR.equals(targetLevel);

		if (Objects.isNull(orderSearchTrade) || isNewVillageUser) {
			//从来没买过对应版本 或 初级版赠送的新手村
			LocalDateTime lastActCycle = opTime.plusDays(actCycle).withHour(0).withMinute(0).withSecond(0).withNano(0);
			if(lastActCycle.isAfter(now)) {
				long diffDay = ChronoUnit.DAYS.between(now, lastActCycle);
				LOGGER.logInfo(calculateBo.getSellerNick(), levelChangLog, "未订购当前版本 或 是新手村赠送用户, 剩余赠送时长 " + diffDay);
				if (calculateBo.getHasGivePresent().equals(Boolean.TRUE)) {
					LOGGER.logInfo(calculateBo.getSellerNick(), levelChangLog, "用户为新手村赠送标识, 需要变更vipFlag=4");
					sellerLevelCycleEndTimeTemporary.setLevel(UserProductInfo.LEVEL_FOUR);
				}else{
					sellerLevelCycleEndTimeTemporary.setLevel(targetLevel);
				}
				sellerLevelCycleEndTimeTemporary.setOrderCycleEndTime(lastActCycle);
			} else {
				LOGGER.logInfo(calculateBo.getSellerNick(), levelChangLog, "未订购当前版本 或 是新手村赠送用户, 赠送时长已耗尽");
				sellerLevelCycleEndTimeTemporary.setIsAllUsed(Boolean.TRUE);
			}
		} else {
			// 购买过对应版本
			opTime = opTime.withHour(0).withMinute(0).withSecond(0).withNano(0);
			long diffDay;
			if (!orderSearchTrade.getOrderCycleEnd().isBefore(opTime)) {
				diffDay = ChronoUnit.DAYS.between(orderSearchTrade.getOrderCycleEnd(), now);
			}else{
				diffDay = ChronoUnit.DAYS.between(opTime, now);
			}
			if (diffDay < actCycle) {
				long remaining = actCycle-diffDay;
				LOGGER.logInfo(calculateBo.getSellerNick(), levelChangLog, "订购过当前版本, 剩余赠送时长 " + remaining);
				if (calculateBo.getHasGivePresent().equals(Boolean.TRUE)) {
					LOGGER.logInfo(calculateBo.getSellerNick(), levelChangLog, "用户为新手村赠送标识, 需要变更vipFlag=4");
					sellerLevelCycleEndTimeTemporary.setLevel(UserProductInfo.LEVEL_FOUR);
				} else {
					sellerLevelCycleEndTimeTemporary.setLevel(targetLevel);
				}
				sellerLevelCycleEndTimeTemporary.setOrderCycleEndTime(now.plusDays(remaining));
			} else {
				LOGGER.logInfo(calculateBo.getSellerNick(), levelChangLog, "订购过当前版本, 赠送时长已耗尽");
				sellerLevelCycleEndTimeTemporary.setIsAllUsed(Boolean.TRUE);
			}
		}
		return sellerLevelCycleEndTimeTemporary;
	}


	@Data
	@NoArgsConstructor
	@ToString
	public static class SellerLevelCycleEndTimeTemporary{
		/**
		 * 实际最终需要的二个值
		 */
		private Integer level;
		private LocalDateTime orderCycleEndTime;
		/**
		 * 是否全部耗尽
		 */
		private Boolean isAllUsed;

		/**
		 * 当前处理的赠送记录
		 */
		private List<Integer> allIds;
		//------------------ 拆分后更新用的
		/**
		 * 耗尽的id集合
		 */
		private List<Integer> useUpIds;
		/**
		 * 拆分时新生成的赠送信息
		 */
		private PromotionActivity newSplitActivity;
		private Integer lastUseActCycle;
		private Integer lastUseId;
		/**
		 * 重新聚合一次
		 */
		private Boolean reAggregationCalculate;

		/**
		 * 需要刷新opttime的活动列表集合
		 */
		private List<PromotionActivity> needRefreshOpttimeActivityList;

		public SellerLevelCycleEndTimeTemporary(Boolean isAllUsed, Boolean reAggregationCalculate, Integer level, LocalDateTime orderCycleEndTime, List<Integer> allIds){
			this.level = level;
			this.orderCycleEndTime = orderCycleEndTime;
			this.isAllUsed = isAllUsed;
			this.reAggregationCalculate = reAggregationCalculate;
			this.allIds = allIds;
		}
	}
}
