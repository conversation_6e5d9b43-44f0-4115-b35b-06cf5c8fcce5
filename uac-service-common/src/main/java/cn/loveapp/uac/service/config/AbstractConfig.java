package cn.loveapp.uac.service.config;

import cn.loveapp.common.utils.LoggerHelper;
import com.alibaba.fastjson.annotation.JSONField;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import lombok.Data;
import org.springframework.beans.BeansException;
import org.springframework.cloud.context.environment.EnvironmentChangeEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

/**
 * 开放平台消息接入及转发配置
 *
 * <AUTHOR>
 * @date 2021/3/24
 */
@Data
public abstract class AbstractConfig implements ApplicationContextAware {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(AbstractConfig.class);

    @JSONField(serialize = false, deserialize = false)
    protected ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    protected void configChangeListener(ConfigChangeEvent changeEvent) {
        this.applicationContext.publishEvent(new EnvironmentChangeEvent(changeEvent.changedKeys()));
        LOGGER.logInfo("Refreshing properties: " + this);
    }
}
