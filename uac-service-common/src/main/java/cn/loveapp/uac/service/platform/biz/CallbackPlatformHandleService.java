package cn.loveapp.uac.service.platform.biz;

import cn.loveapp.common.autoconfigure.platform.CommonPlatformHandler;
import cn.loveapp.uac.response.CallbackResponse;
import cn.loveapp.uac.common.bo.AuthBo;

/**
 * @program: uac-service-group
 * @description: CallbackPlatformHandleService
 * @author: <PERSON>
 * @create: 2021-04-19 17:26
 **/
public interface CallbackPlatformHandleService extends CommonPlatformHandler {
	/**
	 * authCallback
	 * @param authBo
	 * @param platformId
	 * @param appName
	 * @return
	 */
	CallbackResponse authCallback(AuthBo authBo, String platformId, String appName);
}
