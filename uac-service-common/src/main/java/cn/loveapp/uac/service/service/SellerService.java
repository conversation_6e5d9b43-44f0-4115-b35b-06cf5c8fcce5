package cn.loveapp.uac.service.service;

import cn.loveapp.uac.common.bo.LoginUserInfoBo;
import cn.loveapp.uac.common.bo.UserAutoRenewBo;
import cn.loveapp.uac.common.bo.UserBo;
import cn.loveapp.uac.common.bo.UserInfoBo;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.common.exception.CacheWriteException;
import cn.loveapp.uac.common.exception.DbWriteException;
import cn.loveapp.uac.exception.UserException;
import cn.loveapp.uac.response.UserCacheInfoResponse;
import cn.loveapp.uac.response.UserInfoResponse;

import java.util.List;

/**
 * @program: uac-service-group
 * @description: SellerService
 * @author: Jason
 * @create: 2020-03-13 11:00
 **/
public interface SellerService {
	/**
	 * 用户登陆并发生一系列的复杂计算逻辑
	 * @param userInfoBo
	 * @return
	 */
	UserInfoResponse login(UserInfoBo userInfoBo, String platformId, String appName) throws UserException;

	/**
	 * 快速登录
	 * @param loginUserInfoBo
	 * @return
	 */
	UserInfoResponse quickLogin(LoginUserInfoBo loginUserInfoBo, String platformId, String appName)
		throws DbWriteException;

	/**
	 * 获取用户属性 根据sellerNick 或者 SellerId(PDD)
	 * @param userInfoBo
	 * @return
	 */
	UserInfoResponse getUserInfo(UserInfoBo userInfoBo, String platformId, String appName)
		throws UserException;

	UserInfoResponse getUserInfo(UserInfoBo userInfoBo, String memberId, String platformId, String appName)
		throws UserException;

	UserProductInfo getUserFullInfo(UserInfoBo sellerNick, boolean isCheckAndRefreshToken, String platformId, String appName)
		throws Exception;

	/**
	 * 刷新用户信息
	 * @return
	 */
	Boolean refreshUserInfo(UserInfoBo newUserInfoBo, String platformId, String appName);

	/**
	 * 获取token
	 * @param userInfoBo
	 * @return
	 */
	UserInfoResponse getAccessToken(UserInfoBo userInfoBo, String platformId, String appName) throws Exception;

	UserInfoResponse getAccessToken(UserInfoBo userInfoBo, String memberId, String platformId, String appName) throws Exception;

	/**
	 * 刷新token
	 * @param userInfoBo
	 * @param refreshToken
	 * @return
	 * @throws DbWriteException
	 * @throws CacheWriteException
	 */
	String refreshAccessToken(UserInfoBo userInfoBo,
		String refreshToken, String platformId, String appName);

	/**
	 * 重建用户信息
	 * @param userInfoBo
	 * @return
	 * @throws UserException
	 */
	UserInfoResponse rebuildUserInfo(UserInfoBo userInfoBo, String platformId, String appName) throws UserException;

	/**
	 * refreshUserInfoCacheAndTable
	 * @param userBo
	 */
	void refreshUserInfoCacheAndTable(
      UserBo userBo, String platformId, String appName) throws DbWriteException, CacheWriteException;

	/**
	 * saveUserInfoCacheAndTable
	 * @param userBo
	 * @throws DbWriteException
	 * @throws CacheWriteException
	 */
	void saveUserInfoCacheAndTable(UserBo userBo, String platformId, String appName) throws DbWriteException;

	/**
	 * 直接通过db获取用户信息
	 * @param userBo
	 * @return
	 */
	UserProductInfo getUserInfoByDb(UserBo userBo, String platformId, String appName);

	/**
	 * validatorUserAutoRenewAndUpdating
	 * @param userAutoRenewBo
	 * @throws InterruptedException
	 */
	boolean validatorUserAutoRenewAndUpdating(UserAutoRenewBo userAutoRenewBo, String platformId, String appName) throws InterruptedException;

	/**
	 * 新增或更新用户的订购记录
	 * @param userInfoBo
	 * @param platformId
	 * @param appName
	 */
	void insertOrUpdateOrderSearch(UserInfoBo userInfoBo, String platformId, String appName);


	/**
	 * 更新用户缓存指定key的值
	 * @param userInfoBoList
	 * @param hKey
	 * @param value
	 * @return
	 */
	int batchUpdateUserCacheInfo(List<UserInfoBo> userInfoBoList, String hKey, String value);

	/**
	 * 获取用户缓存指定key的值
	 * @param userInfoBoList
	 * @param hKey
	 * @return
	 */
	List<UserCacheInfoResponse> batchGetUserCacheInfo(List<UserInfoBo> userInfoBoList, String hKey);
}
