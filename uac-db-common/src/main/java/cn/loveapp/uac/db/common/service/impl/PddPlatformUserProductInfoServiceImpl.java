package cn.loveapp.uac.db.common.service.impl;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.bo.UserBo;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.db.common.repository.UserRepository;

/**
 * <AUTHOR>
 * @date 2022-11-21 12:23
 * @Description: 拼多多用户数据库信息接口实现类
 */
@Service
public class PddPlatformUserProductInfoServiceImpl extends DefaultPlatformUserProductInfoService {

    public static final LoggerHelper LOGGER = LoggerHelper.getLogger(PddPlatformUserProductInfoServiceImpl.class);

    @Override
    public UserProductInfo getUserInfo(UserBo userBo, UserRepository userRepository, String platformId,
        String appName) {
        if (StringUtils.isNotEmpty(userBo.getSellerNick())) {
            return userRepository.queryBySellerNick(userBo.getSellerNick(), BooleanUtils.isTrue(userBo.getHasReadTag()),
                userBo.getPlatformId(), userBo.getAppType());
        } else if (StringUtils.isNotEmpty(userBo.getSellerId())) {
            return userRepository.queryBySellerIdStr(userBo.getSellerId(), BooleanUtils.isTrue(userBo.getHasReadTag()),
                userBo.getPlatformId(), userBo.getAppType());
        } else if (StringUtils.isNotEmpty(userBo.getSellerAppId())) {
            return userRepository.queryByAppId(userBo.getSellerAppId(), userBo.getPlatformId(), userBo.getAppType());
        } else if (StringUtils.isNotEmpty(userBo.getMemberId())) {
            return userRepository.queryByMemberId(userBo.getMemberId(), userBo.getPlatformId(), userBo.getAppType());
        }
        return null;
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_PDD;
    }
}
