package cn.loveapp.uac.db.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 卖家店铺群(AyTradeShops)实体类
 *
 * <AUTHOR>
 * @since 2020-04-18 11:48:02
 */
@Data
public class AyTradeShops implements Serializable {
    private static final long serialVersionUID = -50599681225533271L;

    private Integer id;
    //店铺群id
    private String shopsId;
    //卖家昵称
    private String sellerNick;
    //卖家店铺群角色 1:店铺群管理员 0:店铺群成员
    private Boolean role;
    //卖家店铺群设置, json格式
    private String settings;
    //平台id
    private String storeId;
    //添加时间
    private LocalDateTime createdTime;
    //创建时间
    private LocalDateTime gmtCreate;
    //更新时间
    private LocalDateTime gmtModified;

}
