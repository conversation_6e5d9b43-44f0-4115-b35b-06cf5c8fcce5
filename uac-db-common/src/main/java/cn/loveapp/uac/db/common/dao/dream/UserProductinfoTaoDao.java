package cn.loveapp.uac.db.common.dao.dream;

import cn.loveapp.uac.common.entity.UserProductInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 淘宝平台 user_productinfo表 数据访问层
 *
 * <AUTHOR>
 */
public interface UserProductinfoTaoDao extends BasePlatformUserProductinfoDao {
    /**
     * 新增数据
     *
     * @param userProductinfo 实例对象
     * @param tableName       各平台真实的表名
     * @return 影响行数
     */
    @Override
    int insert(UserProductInfo userProductinfo, String tableName);

    /**
     * 更新dbId
     * @param nick
     * @param db
     * @param tableName
     * @return
     */
    int updateSaveDataDB(@Param("nick") String nick, @Param("db") String db, @Param("tableName") String tableName);
}
