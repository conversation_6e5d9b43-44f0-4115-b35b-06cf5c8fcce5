package cn.loveapp.uac.db.common.dao.dream;

import cn.loveapp.uac.db.common.entity.ReturnMoneyInfo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 返现记录(ReturnMoneyInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-03-06 11:29:34
 */
public interface ReturnMoneyInfoDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ReturnMoneyInfo queryById(Long id);

    /**
     * 查询指定行数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    List<ReturnMoneyInfo> queryAllByLimit(@Param("offset") int offset, @Param("limit") int limit);

	/**
	 * 通过projectCode和sellerNick进行统计
	 * @param projectCode
	 * @param sellerNick
	 * @return
	 */
    int countByProjectCodeAndSellerNick(@Param("projectCode") String projectCode, @Param("sellerNick") String sellerNick);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param returnMoneyInfo 实例对象
     * @return 对象列表
     */
    List<ReturnMoneyInfo> queryAll(ReturnMoneyInfo returnMoneyInfo);

    /**
     * 新增数据
     *
     * @param returnMoneyInfo 实例对象
     * @return 影响行数
     */
    int insert(ReturnMoneyInfo returnMoneyInfo);

    /**
     * 修改数据
     *
     * @param returnMoneyInfo 实例对象
     * @return 影响行数
     */
    int update(ReturnMoneyInfo returnMoneyInfo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

}
