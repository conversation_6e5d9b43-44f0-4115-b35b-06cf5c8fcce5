package cn.loveapp.uac.db.common.repository.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.config.redis.CacheTimeoutConfig;
import cn.loveapp.uac.common.dto.QueryUserSettingsParam;
import cn.loveapp.uac.db.common.cache.DefaultSettingLocalCache;
import cn.loveapp.uac.db.common.dao.dream.UserSettingsDao;
import cn.loveapp.uac.db.common.entity.UserSettings;
import cn.loveapp.uac.db.common.repository.UserSettingsRepository;
import cn.loveapp.uac.domain.UserSettingDTO;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Author: zhongzijie
 * @Date: 2023/7/12 12:26
 * @Description: 用户设置 - repository - 实现类
 */
@Repository
public class UserSettingsRepositoryImpl implements UserSettingsRepository {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(UserSettingsRepositoryImpl.class);

    private static final String USER_SETTING_DEFAULT = "default";

    /**
     * redis 用户设置空标记
     */
    private static final String SETTING_EMPTY = "@EMPTY_USER_SETTING";

    /**
     * 用户设置key前缀
     */
    private static final String USER_SETTINGS = "user:settings:";

    @Autowired
    private UserSettingsDao userSettingsDao;

    @Autowired
    @Qualifier("stringTradeRedisTemplate")
    private StringRedisTemplate stringTradeRedisTemplate;

    @Autowired
    private CacheTimeoutConfig cacheTimeoutConfig;

    @Autowired
    private DefaultSettingLocalCache defaultSettingLocalCache;

    @Override
    public List<UserSettings> queryAllSettings(String userId, String platformId, String appName) {
        return userSettingsDao.queryAllSettings(userId, platformId, appName);
    }

    @Override
    public List<UserSettingDTO> batchQueryUserSetting(QueryUserSettingsParam userSettingsParam) {
        String userId = userSettingsParam.getUserId();
        boolean isDefaultSettingQuery = USER_SETTING_DEFAULT.equals(userId);
        String platformId = userSettingsParam.getPlatformId();
        String appName = userSettingsParam.getAppName();
        List<String> settingKeys = userSettingsParam.getSettingKeys();
        List<UserSettingDTO> userSettingDTOList = Lists.newArrayList();
        List<String> keys = Lists.newArrayList();
        List<String> redisKeys = Lists.newArrayList();
        ValueOperations<String, String> opsForValue = stringTradeRedisTemplate.opsForValue();

        for (String settingKey : settingKeys) {
            String userSettingKey = initUserSettingKey(userId, settingKey, platformId, appName);
            redisKeys.add(userSettingKey);
            keys.add(settingKey);
        }
        List<String> cacheResults = opsForValue.multiGet(redisKeys);
        // 缓存查询不到的设置
        List<String> keysNotFoundByCache = Lists.newArrayList();
        // 被缓存标记为空的设置
        List<String> keysEmptyByCache = Lists.newArrayList();
        for (int i = 0; i < cacheResults.size(); i++) {
            String key = keys.get(i);
            String redisKey = redisKeys.get(i);
            String result = cacheResults.get(i);
            UserSettings userSettings;
            // 先判断redis有没有，没有的记录一下，稍后批量查库
            if (result == null) {
                keysNotFoundByCache.add(key);
            } else {
                // 缓存续期
                stringTradeRedisTemplate.expire(redisKey, cacheTimeoutConfig.getUserSettingsCacheTimeout(), TimeUnit.SECONDS);

                if (!SETTING_EMPTY.equals(result)) {
                    // 缓存查到的直接放结果集里
                    userSettings = JSON.parseObject(result, UserSettings.class);
                    UserSettingDTO userSettingDTO = new UserSettingDTO();
                    userSettingDTO.setKey(userSettings.getSettingKey());
                    userSettingDTO.setValue(userSettings.getSettingValue());
                    userSettingDTOList.add(userSettingDTO);
                } else {
                    keysEmptyByCache.add(key);
                }
            }
        }
        // 批量查库
        if (CollectionUtils.isNotEmpty(keysNotFoundByCache)) {
            userSettingsParam.setSettingKeys(keysNotFoundByCache);
            List<UserSettings> dbUserSettings = userSettingsDao.batchQueryUserSetting(userSettingsParam);
            for (UserSettings dbUserSetting : dbUserSettings) {
                UserSettingDTO userSettingDTO = new UserSettingDTO();
                userSettingDTO.setKey(dbUserSetting.getSettingKey());
                userSettingDTO.setValue(dbUserSetting.getSettingValue());
                userSettingDTOList.add(userSettingDTO);
                // 查库结果存redis
                String cacheKey = initUserSettingKey(userId, dbUserSetting.getSettingKey(), platformId, appName);
                String cacheValue = JSON.toJSONString(dbUserSetting);
                opsForValue.set(cacheKey, cacheValue, cacheTimeoutConfig.getUserSettingsCacheTimeout(), TimeUnit.SECONDS);
            }
            if (!isDefaultSettingQuery) {
                // 收集一下库里没有查到的记录，等会去查默认设置
                List<String> keysNotFoundByDb = Lists.newArrayList(keysNotFoundByCache);
                for (UserSettings dbUserSetting : dbUserSettings) {
                    keysNotFoundByDb.remove(dbUserSetting.getSettingKey());
                }
                for (String keyNotFoundByDb : keysNotFoundByDb) {
                    // 数据库不存在的记录去redis打空标记
                    String redisSettingKey = initUserSettingKey(userId, keyNotFoundByDb, platformId, appName);
                    if (StringUtils.isEmpty(redisSettingKey)) {
                        continue;
                    }

                    opsForValue.set(redisSettingKey, SETTING_EMPTY, cacheTimeoutConfig.getUserSettingsCacheTimeout(), TimeUnit.SECONDS);
                    if (BooleanUtils.isTrue(userSettingsParam.getLoadDefaultSetting())) {
                        // 查默认设置只查本地缓存，没有查到就不返回
                        UserSettingDTO userSettingDTO = defaultSettingLocalCache.get(keyNotFoundByDb, appName);
                        if (userSettingDTO != null) {
                            userSettingDTOList.add(userSettingDTO);
                        }
                    }
                }
            }
        }

        if (BooleanUtils.isTrue(userSettingsParam.getLoadDefaultSetting())) {
            // 缓存标记为空的再去查一下默认设置
            for (String key : keysEmptyByCache) {
                UserSettingDTO userSettingDTO = defaultSettingLocalCache.get(key, appName);
                if (userSettingDTO != null) {
                    userSettingDTOList.add(userSettingDTO);
                }
            }
        }

        return userSettingDTOList;
    }

    @Override
    public int batchUpsertUserSetting(List<UserSettings> userSettings) {
        int result = userSettingsDao.batchUpsertUserSetting(userSettings);
        if (result > 0) {
            List<String> deleteRedisKeys = Lists.newArrayList();
            for (UserSettings userSetting : userSettings) {
                String userId = userSetting.getUserId();
                String settingKey = userSetting.getSettingKey();
                String platformId = userSetting.getPlatformId();
                String appName = userSetting.getAppName();
                String userSettingKey = initUserSettingKey(userId, settingKey, platformId, appName);
                deleteRedisKeys.add(userSettingKey);
            }
            stringTradeRedisTemplate.delete(deleteRedisKeys);
        }
        return result;
    }

    /**
     * 生成Redis用户设置key
     *
     * @param sellerId
     * @param settingKey
     * @param platformId
     * @param appName
     * @return
     */
    public static String initUserSettingKey(String sellerId, String settingKey, String platformId, String appName) {
        if (StringUtils.isAnyEmpty(sellerId, settingKey, platformId, appName)) {
            LOGGER.logError(sellerId, platformId, "缺少必要入参，无法生成UserSettingKey");
            return null;
        }
        return USER_SETTINGS + platformId + ":" + appName + ":" + sellerId + ":" + settingKey;
    }
}
