package cn.loveapp.uac.db.common.repository;

import cn.loveapp.uac.common.dto.QueryUserSettingsParam;
import cn.loveapp.uac.db.common.entity.UserSettings;
import cn.loveapp.uac.domain.UserSettingDTO;

import java.util.List;

/**
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/7/12 12:23
 * @Description: 用户设置 - repository
 */
public interface UserSettingsRepository {

    List<UserSettings> queryAllSettings(String userId, String platformId, String appName);

    /**
     * 批量查询用户设置
     *
     * @param userSettingsParam
     * @return
     */
    List<UserSettingDTO> batchQueryUserSetting(QueryUserSettingsParam userSettingsParam);

    /**
     * 批量新增用户设置，如果设置已存在，则更新用户设置
     *
     * @param userSettings
     * @return
     */
    int batchUpsertUserSetting(List<UserSettings> userSettings);
}
