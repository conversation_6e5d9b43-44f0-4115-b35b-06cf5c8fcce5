package cn.loveapp.uac.db.common.dao.dream;

import cn.loveapp.uac.db.common.entity.UserAlipayAccountInfo;
import org.apache.ibatis.annotations.Param;

/**
 * (UserAlipayAccountInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-03-06 11:29:07
 */
public interface UserAlipayAccountInfoDao {

	/**
	 * 通过sellernick和status查询
	 * @param sellerNick
	 * @param status
	 * @param appType
	 * @return
	 */
    UserAlipayAccountInfo queryUserAlipayAccountInfoBySellerNickAndStatus(@Param("sellerNick") String sellerNick, @Param("status") Integer status, @Param("appType") String appType);

    /**
     * 修改数据
     *
     * @param userAlipayAccountInfo 实例对象
     * @return 影响行数
     */
    int update(@Param("user") UserAlipayAccountInfo userAlipayAccountInfo, @Param("appType") String appType);

}
