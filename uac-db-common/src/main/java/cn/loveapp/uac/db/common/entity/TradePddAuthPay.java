package cn.loveapp.uac.db.common.entity;

import java.io.Serializable;
import java.time.LocalDate;
import lombok.Data;

/**
 * (TradePddAuthPay)实体类
 *
 * <AUTHOR>
 * @since 2020-03-04 16:54:26
 */
@Data
public class TradePddAuthPay implements Serializable {
    private static final long serialVersionUID = -18891023098517972L;

    private Integer id;
    //拼多多的id
    private Long pddId;
    //拼多多nick
    private String pddName;
    //交易店铺
    private String nickTrade;
    //创建的时间
    private LocalDate createTime;
    //备注
    private String remark;

}
