package cn.loveapp.uac.db.common.entity;

import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;

/**
 * 交易-营销活动赠送主表(PromotionActivityTrade)实体类
 *
 * <AUTHOR>
 * @since 2020-03-04 16:51:45
 */
@Data
public class PromotionActivityTrade implements Serializable {
    private static final long serialVersionUID = -72273055548993389L;

    private Integer id;
    //卖家昵称
    private String sellernick;
    //赠送的时长，单位天
    private Integer actCycle;
    //何时给用户赠送运营时长的时间
    private LocalDateTime optime;
    //活动类型
    private String actflag;
    //赠送的客服名字
    private String sender;
    //活动Code
    private String promotionCode;
    //是否已经使用
    private Boolean isused;

}
