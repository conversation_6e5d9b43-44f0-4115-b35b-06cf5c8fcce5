package cn.loveapp.uac.db.common.dao.dream;

import cn.loveapp.uac.db.common.entity.TradePddAuth;
import cn.loveapp.uac.db.common.entity.TradePddAuthPay;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * (TradePddAuthPay)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-03-04 16:54:26
 */
public interface TradePddAuthPayDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    TradePddAuthPay queryById(Integer id);

	/**
	 * 查询nick
	 * @return
	 */
	List<String> queryDistinctSellerNick();

    /**
     * 查询指定行数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    List<TradePddAuthPay> queryAllByLimit(@Param("offset") int offset, @Param("limit") int limit);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param tradePddAuthPay 实例对象
     * @return 对象列表
     */
    List<TradePddAuthPay> queryAll(TradePddAuthPay tradePddAuthPay);

	List<TradePddAuthPay> queryAllBySellerNick(@Param("sellerNick") String sellerNick);

	/**
     * 新增数据
     *
     * @param tradePddAuthPay 实例对象
     * @return 影响行数
     */
    int insert(TradePddAuthPay tradePddAuthPay);

    /**
     * 修改数据
     *
     * @param tradePddAuthPay 实例对象
     * @return 影响行数
     */
    int update(TradePddAuthPay tradePddAuthPay);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

}
