package cn.loveapp.uac.db.common.dao.dream;

import cn.loveapp.uac.db.common.entity.AyBusinessOpenUserLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * (AyOpenUserLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2019-09-26 14:28:03
 */
@Component
public interface BaseAyBusinessOpenUserLogDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AyBusinessOpenUserLog queryById(@Param("id") Integer id, @Param("tableName") String tableName);

    /**
     * 新增数据
     *
     * @param ayBusinessOpenUserLog 实例对象
     * @return 影响行数
     */
    int insert(AyBusinessOpenUserLog ayBusinessOpenUserLog, String tableName);

    /**
     * 修改数据
     *
     * @param ayBusinessOpenUserLog 实例对象
     * @return 影响行数
     */
    int update(AyBusinessOpenUserLog ayBusinessOpenUserLog, String tableName);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(@Param("id") Integer id, @Param("tableName") String tableName);

}
