package cn.loveapp.uac.db.common.entity;

import java.time.LocalDateTime;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 返现记录(ReturnMoneyInfo)实体类
 *
 * <AUTHOR>
 * @since 2020-03-06 11:29:33
 */
@Data
public class ReturnMoneyInfo implements Serializable {
    private static final long serialVersionUID = -25403791879712994L;

    private Long id;
    //用户淘宝会员名
    private String userNick;
    //用户支付宝实名
    private String userAlipayRealName;
    //用户支付宝账号
    private String userAlipayAccount;
    //订单来源 iOS android PC
    private String platform;
    //返现金额
    private Integer returnMoney;
    //返现项目编号
    private String projectCode;
    //是否推送到第三方进行操作
    private Boolean pushedThird;
    //第三方订单号
    private String thirdOrderId;
    //第三方状态 4-成功 5-失败
    private Integer thirdStatus;
    //第三方操作失败返回的消息
    private String thirdMsg;
    //人工操作
    private Boolean manualOperate;
    //手动操作客服名
    private String operateUser;
    //登记时间
    private LocalDateTime registerTime;

    private LocalDateTime gmtCreate;

    private LocalDateTime gmtModified;

}
