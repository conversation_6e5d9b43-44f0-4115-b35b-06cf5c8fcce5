package cn.loveapp.uac.db.common.service.impl;

import java.util.List;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.uac.common.bo.UserBo;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.db.common.entity.UserShopInfoMapping;
import cn.loveapp.uac.db.common.repository.UserRepository;
import cn.loveapp.uac.db.common.service.PlatformUserProductInfoService;

/**
 * <AUTHOR>
 * @date 2022-11-21 18:36
 * @Description: 用户数据库信息接口默认实现类
 */
@Service
public class DefaultPlatformUserProductInfoService implements PlatformUserProductInfoService {
    @Override
    public UserProductInfo getUserInfo(UserBo userBo, UserRepository userRepository, String platformId,
        String appName) {
        if (CommonAppConstants.APP_DISTRIBUTE.equals(appName)) {
            if (StringUtils.isNotEmpty(userBo.getSellerId())) {
                return userRepository.queryBySellerIdStr(userBo.getSellerId(),
                    BooleanUtils.isTrue(userBo.getHasReadTag()), userBo.getPlatformId(), userBo.getAppType());
            } else if (StringUtils.isNotEmpty(userBo.getSellerNick())) {
                return userRepository.queryByShopName(userBo.getSellerNick(), userBo.getPlatformId(),
                    userBo.getAppType());
            } else if (StringUtils.isNotEmpty(userBo.getSellerAppId())) {
                return userRepository.queryByAppId(userBo.getSellerAppId(), userBo.getPlatformId(),
                    userBo.getAppType());
            } else if (StringUtils.isNotEmpty(userBo.getMemberId())) {
                return userRepository.queryByMemberId(userBo.getMemberId(), userBo.getPlatformId(),
                    userBo.getAppType());
            }
        } else {
            if (StringUtils.isNotEmpty(userBo.getSellerNick())) {
                return userRepository.queryBySellerNick(userBo.getSellerNick(),
                    BooleanUtils.isTrue(userBo.getHasReadTag()), userBo.getPlatformId(), userBo.getAppType());
            } else if (StringUtils.isNotEmpty(userBo.getSellerId())) {
                return userRepository.queryBySellerIdStr(userBo.getSellerId(),
                    BooleanUtils.isTrue(userBo.getHasReadTag()), userBo.getPlatformId(), userBo.getAppType());
            } else if (StringUtils.isNotEmpty(userBo.getSellerAppId())) {
                return userRepository.queryByAppId(userBo.getSellerAppId(), userBo.getPlatformId(),
                    userBo.getAppType());
            } else if (StringUtils.isNotEmpty(userBo.getMemberId())) {
                return userRepository.queryByMemberId(userBo.getMemberId(), userBo.getPlatformId(),
                    userBo.getAppType());
            }
        }
        return null;
    }

    @Override
    public UserShopInfoMapping getUserShopInfo(String shopId, String platformId, String appName) {
        return null;
    }

    @Override
    public List<UserShopInfoMapping> getUserShopInfo(String sellerId, String sellerNick, String platformId, String appName) {
        return null;
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_DEFAULT;
    }
}
