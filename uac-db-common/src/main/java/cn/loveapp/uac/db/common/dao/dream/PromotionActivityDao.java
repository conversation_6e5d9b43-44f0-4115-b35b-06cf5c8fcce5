package cn.loveapp.uac.db.common.dao.dream;

import cn.loveapp.uac.common.entity.PromotionActivity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 营销活动赠送主表(promotion_activity_**)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-03-04 16:51:45
 */
public interface PromotionActivityDao {

	/**
	 * 聚合查询用户的剩余周期和最小操作时间
	 * @param sellerNick
	 * @param unUsed
	 * @return
	 */
	List<PromotionActivity> aggregationActCycleAndOptimeBySellerNickAndUnused(@Param("sellerNick") String sellerNick, @Param("unUsed") Boolean unUsed, @Param("tableName") String tableName);

	/**
	 * 通过sellerNick + isUsed查询
	 * @param sellerNick
	 * @param unUsed
	 * @return
	 */
	List<PromotionActivity> queryByIsUsedAndSellerNickSortOptime(@Param("sellerNick") String sellerNick, @Param("unUsed") Boolean unUsed, @Param("tableName") String tableName);

    /**
     * 新增数据
     *
     * @param promotionActivity 实例对象
     * @return 影响行数
     */
    int insert(@Param("promotionActivity") PromotionActivity promotionActivity, @Param("tableName") String tableName);


	/**
	 * 新增数据
	 *
	 * @param promotionActivityList 实例对象
	 * @return 影响行数
	 */
	int insertBatch(@Param("promotionActivityList") List<PromotionActivity> promotionActivityList, @Param("tableName") String tableName);

	/**
	 * 更新use状态&actcycle
	 * @param isused
	 * @param actCycle
	 * @param pkIds
	 * @return
	 */
	int updateIsUsedOrActCycleByPkIds(@Param("isused") Boolean isused, @Param("actCycle") Integer actCycle, @Param("pkIds") List<Integer> pkIds, @Param("tableName") String tableName);

	/**
	 * 更新用户所有信息
	 * @param isused
	 * @param sellerNick
	 * @return
	 */
	int updateIsUsedBySellerNick(@Param("isused") Boolean isused, @Param("sellerNick") String sellerNick, @Param("tableName") String tableName);

}
