package cn.loveapp.uac.db.common.entity;

import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 爱用多店用户tag实体 （ay_multi_user_tags）
 *
 * <AUTHOR>
 * @Date 2023/10/16 11:24 AM
 */
@Data
public class AyMultiUserTag {

    public static final String SEPARATOR = ",";

    /**
     * 主键自增
     */
    private Long id;

    /**
     * 用户id
     */
    private String sellerId;

    /**
     * 平台
     */
    private String storeId;

    /**
     * 应用
     */
    private String appName;

    /**
     * 标识
     */
    private String tags;

    /**
     * 类型 {@link cn.loveapp.uac.contant.AyMultiTagType}
     */
    private Integer type;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    public void addTags(Set<String> tags) {
        if (CollectionUtils.isEmpty(tags)) {
            return;
        }

        if (StringUtils.isNotEmpty(this.tags)) {
            String[] split = StringUtils.split(this.tags, SEPARATOR);
            tags.addAll(Arrays.asList(split));
        }

        this.tags = StringUtils.join(tags, SEPARATOR);
    }

    public void deleteTags(Set<String> tags) {
        if (CollectionUtils.isEmpty(tags) || StringUtils.isEmpty(this.tags)) {
            return;
        }
        Set<String> newTags = new HashSet<>();

        String[] split = StringUtils.split(this.tags, SEPARATOR);
        for (String s : split) {
            if (tags.contains(s)) {
                continue;
            }
            newTags.add(s);
        }

        if (newTags.isEmpty()) {
            this.tags = StringUtils.EMPTY;
        } else {
            this.tags = StringUtils.join(newTags, SEPARATOR);
        }

    }
}
