package cn.loveapp.uac.db.common.service;

import cn.loveapp.common.autoconfigure.platform.CommonPlatformHandler;
import cn.loveapp.uac.common.bo.UserBo;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.db.common.entity.UserShopInfoMapping;
import cn.loveapp.uac.db.common.repository.UserRepository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-11-21 10:44
 * @Description: 多平台用户数据库信息接口
 */
public interface PlatformUserProductInfoService extends CommonPlatformHandler {

    /**
     * 获取用户信息
     *
     * @param userBo
     * @param userRepository
     * @param platformId
     * @param appName
     * @return
     */
    UserProductInfo getUserInfo(UserBo userBo, UserRepository userRepository, String platformId, String appName);

    /**
     * 获取店铺信息
     *
     * @param shopId
     * @param platformId
     * @param appName
     * @return
     */
    UserShopInfoMapping getUserShopInfo(String shopId, String platformId, String appName);

    /**
     * 获取店铺信息
     *
     * @param sellerId
     * @param sellerNick
     * @param platformId
     * @param appName
     * @return
     */
    List<UserShopInfoMapping> getUserShopInfo(String sellerId, String sellerNick, String platformId, String appName);
}
