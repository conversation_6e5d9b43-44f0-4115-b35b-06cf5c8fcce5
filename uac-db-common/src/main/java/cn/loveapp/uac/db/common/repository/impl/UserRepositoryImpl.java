package cn.loveapp.uac.db.common.repository.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.common.config.DistributeConfig;
import cn.loveapp.uac.db.common.config.UserProductinfoTableConfig;
import cn.loveapp.uac.db.common.dao.dream.*;
import cn.loveapp.uac.db.common.entity.UserTag;
import cn.loveapp.uac.db.common.repository.UserRepository;
import com.alibaba.fastjson2.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.UncategorizedSQLException;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: uac-service-group
 * @description: UserRepositoryImpl
 * @author: Jason
 * @create: 2020-03-09 14:35
 **/
@Repository
public class UserRepositoryImpl implements UserRepository {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(UserRepositoryImpl.class);

    private static final String USER_PRODUCT_INFO_TABLE_NAME = "user_productinfo";

    private UserTagDao userTagDao;

    private UserProductinfoTableConfig userProductinfoTableConfig;

    private final Map<String, BasePlatformUserProductinfoDao> platformUserProductinfoDaoMap;

    public UserRepositoryImpl(UserProductinfoTableConfig userProductinfoTableConfig, UserTagDao userTagDao,
        UserProductinfoTaoDao userProductinfoTaoDao, UserProductinfoPddDao userProductinfoPddDao,
        UserProductinfo1688Dao userProductinfo1688Dao, UserProductinfoKwaishopDao userProductinfoKwaishopDao,
        UserProductinfoDoudianDao userProductinfoDoudianDao, UserProductinfoWxshopDao userProductinfoWxshopDao,
        UserProductinfoWxvideoshopDao userProductinfoWxvideoshopDao, UserProductinfoBiyaoDao userProductinfoBiyaoDao,
        UserProductinfoYouzanDao userProductinfoYouzanDao, UserProductinfoAiyongDao userProductinfoAiyongDao,
        UserProductinfoXhsDao userProductinfoXhsDao, UserProductinfoTikTokDao userProductinfoTikTokDao,
        UserProductinfoOfflineShopDao userProductinfoOfflineShopDao, UserProductinfoJdDao userProductinfoJdDao,
        UserProductinfoTgcDao userProductinfoTgcDao) {

        this.userProductinfoTableConfig = userProductinfoTableConfig;
        this.userTagDao = userTagDao;
        this.platformUserProductinfoDaoMap = new HashMap<>();
        this.platformUserProductinfoDaoMap.put(CommonPlatformConstants.PLATFORM_TAO, userProductinfoTaoDao);
        this.platformUserProductinfoDaoMap.put(CommonPlatformConstants.PLATFORM_PDD, userProductinfoPddDao);
        this.platformUserProductinfoDaoMap.put(CommonPlatformConstants.PLATFORM_1688, userProductinfo1688Dao);
        this.platformUserProductinfoDaoMap.put(CommonPlatformConstants.PLATFORM_KWAISHOP, userProductinfoKwaishopDao);
        this.platformUserProductinfoDaoMap.put(CommonPlatformConstants.PLATFORM_DOUDIAN, userProductinfoDoudianDao);
        this.platformUserProductinfoDaoMap.put(CommonPlatformConstants.PLATFORM_WXSHOP, userProductinfoWxshopDao);
        this.platformUserProductinfoDaoMap.put(CommonPlatformConstants.PLATFORM_WXVIDEOSHOP,
            userProductinfoWxvideoshopDao);
        this.platformUserProductinfoDaoMap.put(CommonPlatformConstants.PLATFORM_BIYAO, userProductinfoBiyaoDao);
        this.platformUserProductinfoDaoMap.put(CommonPlatformConstants.PLATFORM_YOUZAN, userProductinfoYouzanDao);
        this.platformUserProductinfoDaoMap.put(CommonPlatformConstants.PLATFORM_AIYONG, userProductinfoAiyongDao);
        this.platformUserProductinfoDaoMap.put(CommonPlatformConstants.PLATFORM_XHS, userProductinfoXhsDao);
        this.platformUserProductinfoDaoMap.put(CommonPlatformConstants.PLATFORM_TIKTOK, userProductinfoTikTokDao);
        this.platformUserProductinfoDaoMap.put(CommonPlatformConstants.PLATFORM_OFFLINESTORE,
            userProductinfoOfflineShopDao);
        this.platformUserProductinfoDaoMap.put(CommonPlatformConstants.PLATFORM_JD, userProductinfoJdDao);
        this.platformUserProductinfoDaoMap.put(CommonPlatformConstants.PLATFORM_TGC, userProductinfoTgcDao);
    }

    @Autowired
    private DistributeConfig distributeConfig;

    @Override
    public UserProductInfo queryBySellerNick(String sellerNick, boolean needTag, String platformId, String appName) {
        UserProductInfo userProductInfo = platformUserProductinfoDaoMap.get(platformId).queryBySellerNick(sellerNick,
            getTableName(platformId, appName), getExtensionFields(platformId, appName));
        if (Objects.nonNull(userProductInfo) && needTag) {
            String tag = toTags(sellerNick, appName);
            userProductInfo.setTag(tag);
        }
        return userProductInfo;
    }

    @Override
    public UserProductInfo queryBySellerId(String sellerId, boolean needTag, String platformId, String appName) {

        UserProductInfo userProductInfo = platformUserProductinfoDaoMap.get(platformId).queryByUserId(sellerId, getTableName(platformId, appName),
            getExtensionFields(platformId, appName));

        if (Objects.nonNull(userProductInfo) && needTag) {
            String tag = toTags(userProductInfo.getNick(), appName);
            userProductInfo.setTag(tag);
        }
        return userProductInfo;
    }

    @Override
    public UserProductInfo queryBySellerIdStr(String sellerId, boolean needTag, String platformId, String appName) {

        UserProductInfo userProductInfo = platformUserProductinfoDaoMap.get(platformId).queryByUserIdStr(sellerId, getTableName(platformId, appName),
            getExtensionFields(platformId, appName));

        if (Objects.nonNull(userProductInfo) && needTag) {
            String tag = toTags(userProductInfo.getNick(), appName);
            userProductInfo.setTag(tag);
        }
        return userProductInfo;
    }

    @Override
    public List<UserProductInfo> queryByLevel(Integer level, Boolean isNeedAuth, Integer maxId, Integer offset, Integer limit, String platformId, String appName) {
        return platformUserProductinfoDaoMap.get(platformId).queryByLevel(level, isNeedAuth, maxId, offset, limit, getTableName(platformId, appName), getExtensionFields(platformId, appName));
    }

    @Override
    public UserProductInfo queryByMemberId(String memberId, String platformId, String appName) {
        // 平台: 1688、淘工厂
        return platformUserProductinfoDaoMap.get(platformId).queryByMemberId(memberId,
            getTableName(platformId, appName), getExtensionFields(platformId, appName));
    }

    @Override
    public UserProductInfo queryByAppId(String sellerAppId, String platformId, String appName) {
        return platformUserProductinfoDaoMap.get(platformId).queryByAppId(sellerAppId, getTableName(platformId, appName),
            getExtensionFields(platformId, appName));
    }


    @Override
    public UserProductInfo queryByShopName(String shopName, String platformId, String appName) {
        return platformUserProductinfoDaoMap.get(platformId).queryByShopName(shopName, getTableName(platformId, appName),
            getExtensionFields(platformId, appName));
    }

    @Override
    public UserProductInfo queryByMallName(String mallName, String platformId, String appName) {
        if (CommonPlatformConstants.PLATFORM_TAO.equals(platformId)) {
            return platformUserProductinfoDaoMap.get(platformId).queryBySellerNick(mallName, getTableName(platformId, appName),
                    getExtensionFields(platformId, appName));
        }
        return platformUserProductinfoDaoMap.get(platformId).queryByMallName(mallName, getTableName(platformId, appName),
                getExtensionFields(platformId, appName));
    }

    @Override
    public List<UserProductInfo> queryNickListByvipFlagList(List<Integer> vipFlagList, Integer maxId, Integer limit, String platformId,
        String appName) {
        return platformUserProductinfoDaoMap.get(platformId).queryNickListByVipFlagList(vipFlagList, maxId, limit,
            getTableName(platformId, appName));
    }

    @Override
    public List<UserProductInfo> queryNickListByProfessionalOrderCycleEnd(LocalDateTime professionalOrderCycleEnd,
        Integer maxId, Integer limit, String platformId, String appName) {
        return platformUserProductinfoDaoMap.get(platformId).queryNickListByProfessionalOrderCycleEnd(
            professionalOrderCycleEnd, maxId, limit, getTableName(platformId, appName));
    }

    @Override
    public List<UserProductInfo> queryByLevelAndW1DeadLine(Integer level, LocalDateTime deadLine, Integer maxId, Integer offset, Integer limit, String platform, String appName) {
        return platformUserProductinfoDaoMap.get(platform).queryByLevelAndW1DeadLine(level, deadLine, maxId, offset, limit, getTableName(platform, appName), getExtensionFields(platform, appName));
    }

    @Override
    public List<UserProductInfo> queryByW1DeadLineAndOrderCycleEnd(LocalDateTime w1DeadLineStart, LocalDateTime w1DeadLineEnd,
        LocalDateTime orderCycleEnd, Integer maxId, Integer offset, Integer limit, String platform, String appName) {
        return platformUserProductinfoDaoMap.get(platform).queryByW1DeadLineAndOrderCycleEnd(w1DeadLineStart, w1DeadLineEnd,
            orderCycleEnd, maxId, offset, limit, getTableName(platform, appName), getExtensionFields(platform, appName));
    }

    @Override
    public List<UserProductInfo> queryUserProductInfoListBySellerNickCollection(List<String> sellerNicks, String platformId, String appName) {
        return platformUserProductinfoDaoMap.get(platformId).queryUserProductInfoListBySellerNickCollection(sellerNicks, getTableName(platformId, appName), getExtensionFields(platformId, appName));
    }

    @Override
    public int update(UserProductInfo userProductInfo, String platformId, String appName) {
        boolean useUserId = CommonAppConstants.APP_DISTRIBUTE.equals(appName);
        boolean isDistribute1688 = useUserId && CommonPlatformConstants.PLATFORM_1688.equals(platformId);
        return platformUserProductinfoDaoMap.get(platformId).update(userProductInfo, getTableName(platformId, appName),
            useUserId, isDistribute1688);
    }

    @Override
    public int updateBatchLevelBySellerNickCollection(List<String> sellerNicks, Integer level, String platformId, String appName) {
        return platformUserProductinfoDaoMap.get(platformId).updateBatchLevelBySellerNickCollection(sellerNicks, level, getTableName(platformId, appName));
    }

    @Override
    public int insert(UserProductInfo userProductInfo, String platformId, String appName) {
        return platformUserProductinfoDaoMap.get(platformId).insert(userProductInfo, getTableName(platformId, appName));
    }

    @Override
    public int updateSaveDataDB(String nick, String db, String platformId, String appName) {
        BasePlatformUserProductinfoDao basePlatformUserProductinfoDao = platformUserProductinfoDaoMap.get(platformId);
        if (basePlatformUserProductinfoDao instanceof UserProductinfoTaoDao) {
            UserProductinfoTaoDao userProductinfoTaoDao = (UserProductinfoTaoDao) basePlatformUserProductinfoDao;
            return userProductinfoTaoDao.updateSaveDataDB(nick, db, getTableName(platformId, appName));
        }
        return 0;
    }

    @Override
    public List<UserProductInfo> queryByW1DeadlineBeforeWithPage(LocalDateTime w1DeadlineBefore,
                                                                  LocalDateTime lastW1Deadline,
                                                                  Integer limit,
                                                                  String platformId,
                                                                  String appName) {
        return platformUserProductinfoDaoMap.get(platformId).queryByW1DeadlineBeforeWithPage(
                w1DeadlineBefore, lastW1Deadline, limit, getTableName(platformId, appName), getExtensionFields(platformId, appName));
    }

    private String toTags(String sellerNick, String appType) {
        try {
            List<UserTag> userTags = userTagDao.queryUserTagBySellerNick(sellerNick, appType);
            if(LOGGER.isDebugEnabled()){
                LOGGER.logDebug(sellerNick, "-", "尝试读取usertag属性结果" + JSON.toJSONString(userTags));
            }
            if (CollectionUtils.isNotEmpty(userTags)) {
                return userTags.stream().map(UserTag::getTag).collect(Collectors.joining(","));
            }
        } catch (UncategorizedSQLException e) {
            LOGGER.logError("查询user_tag异常，异常原因：" + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 根据平台和应用获取表名
     *
     * @param platformId
     * @param appName
     * @return
     */
    private String getTableName(String platformId, String appName) {
        if (CommonPlatformConstants.PLATFORM_TAO.equals(platformId)
            && (CommonAppConstants.APP_TRADE.equals(appName)
                || CommonAppConstants.APP_ITEM.equals(appName)
                || CommonAppConstants.APP_SHOP_HELPER.equals(appName))) {
            return USER_PRODUCT_INFO_TABLE_NAME + "_" + appName.toLowerCase();
        }

        if (CommonAppConstants.APP_DISTRIBUTE.equals(appName)
            && distributeConfig.getDistributeSpecialTableSuffixMap().containsKey(platformId)) {
            return USER_PRODUCT_INFO_TABLE_NAME + "_"
                + distributeConfig.getDistributeSpecialTableSuffixMap().get(platformId).toLowerCase();
        }

        return USER_PRODUCT_INFO_TABLE_NAME + "_" + platformId.toLowerCase() + "_" + appName.toLowerCase();
    }

    /**
     * 获取各平台额外的字段信息
     *
     * @param platformId
     * @param appName
     * @return
     */
    private String getExtensionFields(String platformId, String appName) {
        UserProductinfoTableConfig.PlatformConfig config;
        if (CommonPlatformConstants.PLATFORM_TAO.equals(platformId)
            && CommonAppConstants.APP_TRADE_SUPPLIER.equals(appName)) {
            config = userProductinfoTableConfig.getPlatforms().get(appName.toLowerCase());
        } else {
            config =
                userProductinfoTableConfig.getPlatforms().get(platformId.toLowerCase() + "_" + appName.toLowerCase());
            if (config == null) {
                config = userProductinfoTableConfig.getPlatforms().get(platformId.toLowerCase());
            }
        }

        if (config == null) {
            return null;
        }
        return config.getExtensionFields();
    }
}
