package cn.loveapp.uac.db.common.dao.dream;

import cn.loveapp.uac.db.common.entity.UserTag;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * (UserTag)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-03-04 16:53:23
 */
public interface UserTagDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    UserTag queryById(Integer id);

    /**
     * 查询指定行数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    List<UserTag> queryAllByLimit(@Param("offset") int offset, @Param("limit") int limit);

	/**
	 * 通过sellerNick获取tag列表
	 * @param sellerNick
	 * @param appType
	 * @return
	 */
	List<UserTag> queryUserTagBySellerNick(@Param("sellerNick") String sellerNick, @Param("appType") String appType);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param userTag 实例对象
     * @return 对象列表
     */
    List<UserTag> queryAll(UserTag userTag);

    /**
     * 新增数据
     *
     * @param userTag 实例对象
     * @return 影响行数
     */
    int insert(UserTag userTag);

    /**
     * 修改数据
     *
     * @param userTag 实例对象
     * @return 影响行数
     */
    int update(UserTag userTag);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

}
