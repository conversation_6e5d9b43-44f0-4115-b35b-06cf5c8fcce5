package cn.loveapp.uac.db.common.repository.impl;

import cn.loveapp.common.constant.CommonBusinessConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.db.common.dao.dream.BaseAyBusinessOpenUserLogDao;
import cn.loveapp.uac.db.common.entity.AyBusinessOpenUserLog;
import cn.loveapp.uac.db.common.repository.AyBusinessOpenUserLogRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: zhong<PERSON><PERSON>e
 * @Date: 2023/1/11 21:04
 * @Description: 开户操作日志repository实现类
 */
@Repository
public class AyBusinessOpenUserLogRepositoryImpl implements AyBusinessOpenUserLogRepository {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(AyBusinessOpenUserLogRepositoryImpl.class);

    private final Map<String, BaseAyBusinessOpenUserLogDao> businessAyOpenUserLogDaoMap;

    public AyBusinessOpenUserLogRepositoryImpl(BaseAyBusinessOpenUserLogDao baseAyBusinessOpenUserLogDao) {
        this.businessAyOpenUserLogDaoMap = new HashMap<>();
        this.businessAyOpenUserLogDaoMap.put(CommonBusinessConstants.BUSINESS_DEFAULT, baseAyBusinessOpenUserLogDao);
    }

    /**
     * 根据业务id获取表名
     * @param businessId
     * @return
     */
    private String getTableName(String businessId) {
        if (StringUtils.isEmpty(businessId) || !CommonBusinessConstants.getBusiness().contains(businessId)) {
            throw new RuntimeException("businessId不正确，无法组成正确的表名: " + businessId);
        } else {
            return "ay_" + businessId + "_open_user_log";
        }
    }

    /**
     * 根据业务id获取对应的dao
     * @param businessId
     * @return
     */
    private BaseAyBusinessOpenUserLogDao getAyOpenUserLogDao (String businessId) {
        if (StringUtils.isEmpty(businessId) || !CommonBusinessConstants.getBusiness().contains(businessId)) {
            return null;
        } else {
            BaseAyBusinessOpenUserLogDao baseAyBusinessOpenUserLogDao = businessAyOpenUserLogDaoMap.get(businessId);
            if (baseAyBusinessOpenUserLogDao == null) {
                return businessAyOpenUserLogDaoMap.get(CommonBusinessConstants.BUSINESS_DEFAULT);
            } else {
                return baseAyBusinessOpenUserLogDao;
            }
        }
    }

    @Override
    public AyBusinessOpenUserLog queryById(Integer id, String businessId) {
        return getAyOpenUserLogDao(businessId).queryById(id, getTableName(businessId));
    }

    @Override
    public int insert(AyBusinessOpenUserLog ayBusinessOpenUserLog, String businessId) {
        return getAyOpenUserLogDao(businessId).insert(ayBusinessOpenUserLog, getTableName(businessId));
    }

    @Override
    public int update(AyBusinessOpenUserLog ayBusinessOpenUserLog, String businessId) {
        return getAyOpenUserLogDao(businessId).update(ayBusinessOpenUserLog, getTableName(businessId));
    }

    @Override
    public int deleteById(Integer id, String businessId) {
        return getAyOpenUserLogDao(businessId).deleteById(id, getTableName(businessId));
    }
}
