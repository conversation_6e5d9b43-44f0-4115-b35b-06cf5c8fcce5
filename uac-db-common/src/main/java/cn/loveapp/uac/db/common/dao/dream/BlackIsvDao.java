package cn.loveapp.uac.db.common.dao.dream;

import cn.loveapp.uac.db.common.entity.BlackIsv;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * isv所有nick(BlackIsv)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-03-05 15:56:59
 */
public interface BlackIsvDao {

    /**
     * 通过ID查询单条数据
     *
     * @param isvnick 主键
     * @return 实例对象
     */
    BlackIsv queryBlackIsvByIsvnick(String isvnick);

    /**
     * 查询指定行数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    List<BlackIsv> queryAllByLimit(@Param("offset") int offset, @Param("limit") int limit);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param blackIsv 实例对象
     * @return 对象列表
     */
    List<BlackIsv> queryAll(BlackIsv blackIsv);

    /**
     * 新增数据
     *
     * @param blackIsv 实例对象
     * @return 影响行数
     */
    int insert(BlackIsv blackIsv);

    /**
     * 修改数据
     *
     * @param blackIsv 实例对象
     * @return 影响行数
     */
    int update(BlackIsv blackIsv);

    /**
     * 通过主键删除数据
     *
     * @param isvnick 主键
     * @return 影响行数
     */
    int deleteById(String isvnick);

}
