package cn.loveapp.uac.db.common.dao.dream;

import cn.loveapp.uac.db.common.entity.AyBusinessOpenUser;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/21 18:56
 * @Description: 新用户开通（ay_${businessId}_open_user）表 数据访问层
 */
@Component
public interface BaseAyBusinessOpenUserDao {

    /**
     * 主键查询
     *
     * @param id
     * @param tableName
     * @return
     */
    AyBusinessOpenUser queryById(@Param("id") Integer id, @Param("tableName") String tableName);

    /**
     * 修改数据
     *
     * @param ayBusinessOpenUser 实例对象
     * @param tableName
     * @return 影响行数
     */
    int updateByStatus(AyBusinessOpenUser ayBusinessOpenUser, String tableName);


    /**
     * 通过sellerId和平台查询
     *
     * @param sellerId 卖家id
     * @param platId 卖家平台
     * @param appName
     * @param tableName
     * @return 实例对象
     */
    AyBusinessOpenUser queryBySellerIdAndPlatId(@Param("sellerId") String sellerId,
                                                @Param("platId") String platId,
                                                @Param("appName") String appName,
                                                @Param("tableName") String tableName);

    /**
     * 新增数据
     *
     * @param ayBusinessOpenUser 实例对象
     * @return 影响行数
     */
    int insert(AyBusinessOpenUser ayBusinessOpenUser, String tableName);


    /**
     * 更新状态和重试次数
     *
     * @param ayBusinessOpenUser 实例对象
     * @param tableName
     * @return 影响行数
     */
    int updateByStatusAndRetryCount(AyBusinessOpenUser ayBusinessOpenUser, String tableName);

    /**
     * 根据状态查询
     *
     * @param status 状态
     * @param maxId 当前最大id
     * @param offset 偏移量
     * @param limit 返回条数限制
     * @param tableName
     * @return 某一状态的用户
     */
    Set<AyBusinessOpenUser> queryByStatus(@Param("status") List<Integer> status,
                                          @Param("maxId") Integer maxId,
                                          @Param("offset") Integer offset,
                                          @Param("limit") Integer limit,
                                          @Param("tableName") String tableName);


    /**
     * 通过状态和修改时间查询
     * @param status 状态
     * @param modify 修改时间
     * @param tableName
     * @return
     */
    Set<AyBusinessOpenUser> queryByStatusAndModify(@Param("status")int status,
                                                   @Param("gmtModify") LocalDateTime modify,
                                                   @Param("tableName") String tableName);
}
