package cn.loveapp.uac.db.common.repository.impl;

import cn.loveapp.common.constant.CommonBusinessConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.utils.RedisUtil;
import cn.loveapp.uac.db.common.dao.dream.CommonUserProductionInfoExtDao;
import cn.loveapp.uac.common.dao.redis.repository.UserManageRedisRepositoryHashRedisRepository;
import cn.loveapp.uac.entity.UserProductInfoBusinessExt;
import cn.loveapp.uac.db.common.repository.UserProductionInfoExtRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: zhongzijie
 * @Date: 2023/1/6 16:24
 * @Description: 用户业务状态 Repository实现类
 */
@Repository
public class UserProductionInfoExtRepositoryImpl implements UserProductionInfoExtRepository {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(UserProductionInfoExtRepositoryImpl.class);

    private final Map<String, CommonUserProductionInfoExtDao> businessUserProductionInfoExtDaoMap;

    public UserProductionInfoExtRepositoryImpl(CommonUserProductionInfoExtDao commonUserProductionInfoExtDao) {
        this.businessUserProductionInfoExtDaoMap = new HashMap<>();
        this.businessUserProductionInfoExtDaoMap.put(CommonBusinessConstants.BUSINESS_DEFAULT, commonUserProductionInfoExtDao);
    }

    @Autowired
    @Qualifier("stringTradeRedisTemplate")
    private StringRedisTemplate stringTradeRedisTemplate;

    @Autowired
    private UserManageRedisRepositoryHashRedisRepository userManageRedisRepositoryHashRedisRepository;

    @Value("${user.task.limit:1000}")
    private int limit;
    @Value("${user.task.offset:0}")
    private int offset;

    /**
     * redis userext缓存超时时间（秒）
     * 默认一天
     */
    @Value("${uac.redis.userext.timeout:86400}")
    private long userExtTimeout;

    /**
     * 根据业务id获取表名
     * @param businessId
     * @return
     */
    private String getTableName(String businessId) {
        if (StringUtils.isEmpty(businessId) || !CommonBusinessConstants.getBusiness().contains(businessId)) {
            throw new RuntimeException("businessId不正确，无法组成正确的表名: " + businessId);
        } else {
            return "user_productinfo_" + businessId + "_ext";
        }
    }

    /**
     * 根据业务id获取对应的dao
     * @param businessId
     * @return
     */
    private CommonUserProductionInfoExtDao getUserProductionInfoExtDao(String businessId) {
        if (StringUtils.isEmpty(businessId) || !CommonBusinessConstants.getBusiness().contains(businessId)) {
            return null;
        } else {
            CommonUserProductionInfoExtDao commonUserProductionInfoExtDao = businessUserProductionInfoExtDaoMap.get(businessId);
            if (commonUserProductionInfoExtDao == null) {
                return businessUserProductionInfoExtDaoMap.get(CommonBusinessConstants.BUSINESS_DEFAULT);
            } else {
                return commonUserProductionInfoExtDao;
            }
        }
    }

    @Override
    public List<UserProductInfoBusinessExt> queryAllByLimit(int pullStatus, Boolean pullEndPoint, String storeId, LocalDateTime diffTime, int maxId, String businessId) {
        List<UserProductInfoBusinessExt> userProductInfoBusinessExtList = getUserProductionInfoExtDao(businessId).queryAllByMaxId(pullStatus,
                pullEndPoint, storeId, diffTime, maxId, offset, limit, getTableName(businessId));
        if (CollectionUtils.isEmpty(userProductInfoBusinessExtList)) {
            return null;
        }
        return userProductInfoBusinessExtList;
    }

    @Override
    public List<UserProductInfoBusinessExt> queryByPullStatusAndStoreIdAndTopStatusLimit(Integer pullStatus, String storeId, String topStatus, int offset, int limit, String businessId) {
        return getUserProductionInfoExtDao(businessId).queryByPullStatusAndStoreIdAndTopStatusLimit(pullStatus, storeId, topStatus, offset, limit, getTableName(businessId));
    }

    @Override
    public List<UserProductInfoBusinessExt> queryByTopStatusLimit(String topStatus, long offset, int limit, String businessId) {
        return getUserProductionInfoExtDao(businessId).queryByTopStatusLimit(topStatus, offset, limit, getTableName(businessId));
    }

    @Override
    public Integer queryCountBySellerId(String sellerId, String storeId, String appName, String businessId) {
        return getUserProductionInfoExtDao(businessId).queryCountBySellerId(sellerId, storeId, appName, getTableName(businessId));
    }

    @Override
    public UserProductInfoBusinessExt querySingleBySellerId(String sellerId, String storeId, String appName, String businessId) {
        if (userExtTimeout == 0) {
            return getUserProductionInfoExtDao(businessId).querySingleBySellerId(sellerId, storeId, appName, getTableName(businessId));
        } else {
            String userExtKey = userManageRedisRepositoryHashRedisRepository.initUserExtCollection(sellerId, storeId, appName, businessId);
            return RedisUtil.getCacheWithEmptyIfNotFound(stringTradeRedisTemplate,
                    () -> getUserProductionInfoExtDao(businessId).querySingleBySellerId(sellerId, storeId, appName, getTableName(businessId)),
                    UserProductInfoBusinessExt.class,
                    userExtTimeout,
                    userExtKey);
        }
    }

    @Override
    public UserProductInfoBusinessExt querySingleByMemberId(String memberId, String storeId, String appName, String businessId) {
        return getUserProductionInfoExtDao(businessId).querySingleByMemberId(memberId, storeId, appName, getTableName(businessId));
    }

    @Override
    public List<UserProductInfoBusinessExt> getDbidValidOrderSum(String businessId) {
        return getUserProductionInfoExtDao(businessId).getDbidValidOrderSum(getTableName(businessId));
    }

    @Override
    public int insert(UserProductInfoBusinessExt userProductInfoBusinessExt, String businessId) {
        String userExtKey = userManageRedisRepositoryHashRedisRepository.initUserExtCollection(
                userProductInfoBusinessExt.getSellerId(), userProductInfoBusinessExt.getStoreId(), userProductInfoBusinessExt.getAppName(), businessId);
        int rows = getUserProductionInfoExtDao(businessId).insert(userProductInfoBusinessExt, getTableName(businessId));
        if (rows > 0) {
            stringTradeRedisTemplate.delete(userExtKey);
        }
        return rows;
    }

    @Override
    public int update(UserProductInfoBusinessExt userProductInfoBusinessExt, String businessId) {
        String userExtKey = userManageRedisRepositoryHashRedisRepository.initUserExtCollection(
                userProductInfoBusinessExt.getSellerId(), userProductInfoBusinessExt.getStoreId(), userProductInfoBusinessExt.getAppName(), businessId);
        int rows = getUserProductionInfoExtDao(businessId).update(userProductInfoBusinessExt, getTableName(businessId));
        if (rows > 0) {
            stringTradeRedisTemplate.delete(userExtKey);
        }
        return rows;
    }

    @Override
    public int updateApiStatus(String sellerId, Integer apiStatus, String storeId, String appName, String businessId) {
        String userExtKey = userManageRedisRepositoryHashRedisRepository.initUserExtCollection(sellerId, storeId, appName, businessId);
        int rows = getUserProductionInfoExtDao(businessId).updateApiStatus(sellerId, apiStatus, storeId, appName, getTableName(businessId));
        if (rows > 0) {
            stringTradeRedisTemplate.delete(userExtKey);
        }
        return rows;
    }

    @Override
    public int batchUpdatePullStatusForSellerId(List<String> list, int pullStatus, String storeId, String appName, String businessId) {
        int rows = getUserProductionInfoExtDao(businessId).batchUpdatePullStatusForSellerId(list, pullStatus, storeId, appName, getTableName(businessId));
        for (String sellerId : list) {
            String userExtKey = userManageRedisRepositoryHashRedisRepository.initUserExtCollection(sellerId, storeId, appName, businessId);
            stringTradeRedisTemplate.delete(userExtKey);
        }
        return rows;
    }

    @Override
    public List<UserProductInfoBusinessExt> queryAllByLimitBetweenPullEndTime(int pullStatus, Boolean pullEndPoint, String storeId, LocalDateTime startDateTime, LocalDateTime endDateTime, int maxId, String businessId) {
        List<UserProductInfoBusinessExt> userProductInfoBusinessExtList = getUserProductionInfoExtDao(businessId).queryAllByLimitBetweenPullEndDateTime(pullStatus,
                pullEndPoint, storeId, startDateTime, endDateTime, maxId, offset, limit, getTableName(businessId));
        if (CollectionUtils.isEmpty(userProductInfoBusinessExtList)) {
            return null;
        }
        return userProductInfoBusinessExtList;
    }

    @Override
    public List<UserProductInfoBusinessExt> queryAllWithOutTimeByLimit(int pullStatus, Boolean pullEndPoint, String storeId, int maxId, String businessId) {
        List<UserProductInfoBusinessExt> userProductInfoBusinessExtList = getUserProductionInfoExtDao(businessId).queryAllWithOutTimeByLimit(pullStatus,
                pullEndPoint, storeId, maxId, offset, limit, getTableName(businessId));
        if (CollectionUtils.isEmpty(userProductInfoBusinessExtList)) {
            return null;
        }
        return userProductInfoBusinessExtList;
    }

}
