package cn.loveapp.uac.db.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户信息表(user_productinfo_**_**) 相关的配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "uac.userproductinfo.table")
public class UserProductinfoTableConfig {

    private Map<String, PlatformConfig> platforms = new HashMap<>();


    @Data
    public static class PlatformConfig {
        /**
         * 各平台用户表额外的字段
         */
        private String extensionFields;
    }

}
