package cn.loveapp.uac.db.common.dao.dream;

import cn.loveapp.uac.entity.UserProductInfoBusinessExt;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户业务状态表（user_productinfo_${businessId}_ext）表 数据访问层
 *
 * <AUTHOR>
 * @since 2018-12-24 15:57:33
 */
@Component
public interface CommonUserProductionInfoExtDao {

	/**
	 * 查询指定行数据
	 * @param pullStatus
	 * @param pullEndPoint
	 * @param storeId
	 * @param diffTime
	 * @param maxId
	 * @param offset
	 * @param limit
	 * @return
	 */
	List<UserProductInfoBusinessExt> queryAllByMaxId(@Param("pullStatus") int pullStatus, @Param("pullEndPoint") Boolean pullEndPoint,
													 @Param("storeId") String storeId, @Param("diffTime") LocalDateTime diffTime, @Param("maxId") int maxId,
													 @Param("offset") int offset, @Param("limit") int limit, @Param("tableName") String tableName);

	/**
	 * 依据pullStatus、storeId、topStatus分页查询
	 *
	 * @param pullStatus
	 * @param storeId
	 * @param topStatus
	 * @param offset
	 * @param limit
	 * @return
	 */
	List<UserProductInfoBusinessExt> queryByPullStatusAndStoreIdAndTopStatusLimit(@Param("pullStatus") Integer pullStatus,
																				  @Param("storeId") String storeId,
																				  @Param("topStatus") String topStatus,
																				  @Param("offset") int offset,
																				  @Param("limit") int limit,
																				  @Param("tableName") String tableName);

	/**
	 * 依据topStatus分页查询
	 *
	 * @param topStatus top状态
	 * @param offset 偏移量
	 * @param limit 分页数量
	 * @return 影响行数
	 */
	List<UserProductInfoBusinessExt> queryByTopStatusLimit(@Param("topStatus") String topStatus,
														   @Param("offset") long offset,
														   @Param("limit") int limit,
														   @Param("tableName") String tableName);


	/**
	 * 通过sellerId查询用户数量
	 * @param sellerId sellerId
	 * @return 用户量
	 */
	Integer queryCountBySellerId(@Param("sellerId") String sellerId, @Param("storeId") String storeId, @Param("appName") String appName, @Param("tableName") String tableName);

	/**
	 * 通过sellerId、storeId、appName查询用户信息
	 * @param sellerId
	 * @param appName
	 * @return
	 */
	UserProductInfoBusinessExt querySingleBySellerId(@Param("sellerId") String sellerId, @Param("storeId") String storeId, @Param("appName") String appName, @Param("tableName") String tableName);

	/**
	 * 通过memberId查询用户信息
	 *
	 * @param memberId
	 * @param storeId
	 * @param appName
	 * @param tableName
	 * @return
	 */
    UserProductInfoBusinessExt querySingleByMemberId(@Param("memberId") String memberId,
        @Param("storeId") String storeId, @Param("appName") String appName, @Param("tableName") String tableName);

	/**
	 * 	根据查询各dbId订单总和
	 * @return
	 */
	List<UserProductInfoBusinessExt> getDbidValidOrderSum(@Param("tableName") String tableName);

	/**
	 *
	 *
	 * 新增数据
	 *
	 * @param userProductInfoBusinessExt 实例对象
	 * @return 影响行数
	 */
	int insert(UserProductInfoBusinessExt userProductInfoBusinessExt, String tableName);

	/**
	 * 修改数据
	 *
	 * @param userProductInfoBusinessExt 实例对象
	 * @return 影响行数
	 */
	int update(UserProductInfoBusinessExt userProductInfoBusinessExt, String tableName);

	/**
	 * 更新apiStatus
	 * @param sellerId
	 * @param apiStatus
	 * @param appName
	 * @return
	 */
	int updateApiStatus(@Param("sellerId") String sellerId, @Param("apiStatus") Integer apiStatus, @Param("storeId") String storeId, @Param("appName") String appName, @Param("tableName") String tableName);

	/**
	 * 	根据nick的集合,批量更新pull_status状态
	 * @param sellerIds
	 * @param pullStatus
	 * @return
	 */
	int batchUpdatePullStatusForSellerId(@Param("sellerIds") List<String> sellerIds, @Param("pullStatus")int pullStatus,
										 @Param("storeId") String storeId, @Param("appName") String appName, @Param("tableName") String tableName);

	/**
	 * 查询不同时间区间内的数据
	 * @param pullStatus
	 * @param pullEndPoint
	 * @param storeId
	 * @param startDateTime
	 * @param endDateTime
	 * @param maxId
	 * @param offset
	 * @param limit
	 * @return
	 */
	List<UserProductInfoBusinessExt> queryAllByLimitBetweenPullEndDateTime(@Param("pullStatus") int pullStatus, @Param("pullEndPoint") Boolean pullEndPoint,
																		   @Param("storeId") String storeId, @Param("startDateTime") LocalDateTime startDateTime, @Param("endDateTime") LocalDateTime endDateTime, @Param("maxId") int maxId,
																		   @Param("offset") int offset, @Param("limit") int limit, @Param("tableName") String tableName);

	/**
	 * 不带时间范围的查询
	 * @param pullStatus
	 * @param pullEndPoint
	 * @param storeId
	 * @param maxId
	 * @param offset
	 * @param limit
	 * @return
	 */
	List<UserProductInfoBusinessExt> queryAllWithOutTimeByLimit(@Param("pullStatus") int pullStatus, @Param("pullEndPoint") Boolean pullEndPoint,
																@Param("storeId") String storeId, @Param("maxId") int maxId,
																@Param("offset") int offset, @Param("limit") int limit, @Param("tableName") String tableName);

}
