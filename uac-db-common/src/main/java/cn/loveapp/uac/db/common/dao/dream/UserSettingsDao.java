package cn.loveapp.uac.db.common.dao.dream;

import cn.loveapp.uac.common.dto.QueryUserSettingsParam;
import cn.loveapp.uac.db.common.entity.UserSettings;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/7/12 11:21
 * @Description: 用户设置-dao
 */
public interface UserSettingsDao {

    /**
     * 查询单个用户所有的设置
     *
     * @param userId
     * @param platformId
     * @param appName
     * @return
     */
    List<UserSettings> queryAllSettings(@Param("userId") String userId,
                                          @Param("platformId") String platformId,
                                          @Param("appName") String appName);

    /**
     * 批量查询单个用户的设置
     *
     * @param userSettingsParam
     * @return
     */
    List<UserSettings> batchQueryUserSetting(@Param("userSettingsParam") QueryUserSettingsParam userSettingsParam);

    /**
     * 查询默认设置
     *
     * @param offset
     * @param limit
     * @return
     */
    List<UserSettings> queryDefaultSetting(@Param("offset") int offset, @Param("limit") int limit);

    /**
     * 批量新增用户设置，如果设置已存在，则更新用户设置
     *
     * @param userSettings
     * @return
     */
    int batchUpsertUserSetting(@Param("userSettings") List<UserSettings> userSettings);
}
