package cn.loveapp.uac.db.common.entity;

import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;

/**
 * 爱用卖家基本信息(UserTaobaoSellerinfo)实体类
 *
 * <AUTHOR>
 * @since 2020-03-04 16:53:08
 */
@Data
public class UserTaobaoSellerinfo implements Serializable {
    private static final long serialVersionUID = -15100525658441392L;

    private Integer id;
    //用户数字ID
    private Long userId;
    //用户昵称
    private String nick;
    //用户联系电话
    private String phone;
    //用户Email
    private String email;
    //用户所在省份
    private String province;
    //用户所在城市
    private String city;
    //用户详细地址不含省、市
    private String address;
    //性别。可选值:m(男),f(女)
    private String sex;
    //信用等级（是根据score生成的），信用等级：淘宝会员在淘宝网上的信用度，分为20个级别，级别如：level = 1 时，表示一心；level = 2 时，表示二心
    private Integer sellerCreditLevel;
    //信用总分（“好评”加一分，“中评”不加分，“差评”扣一分。分越高，等级越高）
    private Integer sellerCreditScore;
    //收到的评价总条数。
    private Integer sellerCreditTotalNum;
    //收到的好评总条数。
    private Integer sellerCreditGoodNum;
    //用户类型。可选值:B(B商家),C(C商家)
    private String type;
    //状态。可选值:normal(正常),inactive(未激活),delete(删除),reeze(冻结),supervise(监管)
    private String status;
    //用户是否是金牌卖家
    private Boolean isGoldenSeller;
    //第一次使用爱用产品时间
    private LocalDateTime createdate;
    //最后一次活动时间，含所有产品，用触发器更新
    private LocalDateTime lastactivedt;
    //用户基本信息最后更新时间
    private LocalDateTime lastupdatetime;
    //是否是退货地址的手机号：1是0否
    private Integer refundPhone;

}
