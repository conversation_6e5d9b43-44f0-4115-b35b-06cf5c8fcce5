package cn.loveapp.uac.db.common.entity;

import lombok.Data;

import java.util.Date;

/**
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/7/12 11:16
 * @Description: user_settings 用户设置表 - 实体类
 */
@Data
public class UserSettings {

    /**
     * 主键
     */
    private Long id;

    /**
     * 设置名
     */
    private String settingKey;

    /**
     * 设置值
     */
    private String settingValue;

    /**
     * 设置描述
     */
    private String description;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 渠道id
     */
    private String channelId;

    /**
     * 平台id
     */
    private String platformId;

    /**
     * 应用
     */
    private String appName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
