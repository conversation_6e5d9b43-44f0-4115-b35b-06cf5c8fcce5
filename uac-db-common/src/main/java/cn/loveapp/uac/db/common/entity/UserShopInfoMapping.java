package cn.loveapp.uac.db.common.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-04-12 18:18
 * @description: 用户所对应店铺(UserShopInfoMapping)实体类
 */
public class UserShopInfoMapping implements Serializable {
    private static final long serialVersionUID = 528086894976288771L;
    /**
     * 主键
     */
    private Integer id;
    /**
     * 卖家id
     */
    private String sellerId;
    /**
     * 用户昵称
     */
    private String sellerNick;
    /**
     * 店铺id
     */
    private String shopId;
    /**
     * 店铺密钥
     */
    private String shopCipher;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 地区
     */
    private String region;
    /**
     * 用户类型
     */
    private String sellerType;
    /**
     * 店铺code
     */
    private String code;
    /**
     * 店铺类型
     */
    private Integer shopsType;
    /**
     * 应用
     */
    private String appName;
    /**
     * 平台id
     */
    private String storeId;
    /**
     * 创建时间
     */
    private Date gmtCreated;
    /**
     * 修改时间
     */
    private Date gmtModify;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSellerId() {
        return sellerId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public String getSellerNick() {
        return sellerNick;
    }

    public void setSellerNick(String sellerNick) {
        this.sellerNick = sellerNick;
    }

    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    public String getShopCipher() {
        return shopCipher;
    }

    public void setShopCipher(String shopCipher) {
        this.shopCipher = shopCipher;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getSellerType() {
        return sellerType;
    }

    public void setSellerType(String sellerType) {
        this.sellerType = sellerType;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getShopsType() {
        return shopsType;
    }

    public void setShopsType(Integer shopsType) {
        this.shopsType = shopsType;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public Date getGmtCreated() {
        return gmtCreated;
    }

    public void setGmtCreated(Date gmtCreated) {
        this.gmtCreated = gmtCreated;
    }

    public Date getGmtModify() {
        return gmtModify;
    }

    public void setGmtModify(Date gmtModify) {
        this.gmtModify = gmtModify;
    }

}
