package cn.loveapp.uac.db.common.dao.dream;

import cn.loveapp.uac.db.common.entity.UserTaobaoSellerinfo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 爱用卖家基本信息(UserTaobaoSellerinfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-03-04 16:53:08
 */
public interface UserTaobaoSellerinfoDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    UserTaobaoSellerinfo queryById(Integer id);

    /**
     * 查询指定行数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    List<UserTaobaoSellerinfo> queryAllByLimit(@Param("offset") int offset, @Param("limit") int limit);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param userTaobaoSellerinfo 实例对象
     * @return 对象列表
     */
    List<UserTaobaoSellerinfo> queryAll(UserTaobaoSellerinfo userTaobaoSellerinfo);

    /**
     * 新增数据
     *
     * @param userTaobaoSellerinfo 实例对象
     * @return 影响行数
     */
    int insert(UserTaobaoSellerinfo userTaobaoSellerinfo);

    /**
     * 修改数据
     *
     * @param userTaobaoSellerinfo 实例对象
     * @return 影响行数
     */
    int update(UserTaobaoSellerinfo userTaobaoSellerinfo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

}
