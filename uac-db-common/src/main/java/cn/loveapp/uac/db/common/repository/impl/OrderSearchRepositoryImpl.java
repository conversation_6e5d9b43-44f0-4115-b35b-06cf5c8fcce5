package cn.loveapp.uac.db.common.repository.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.dto.OrderSearchQueryDTO;
import cn.loveapp.uac.db.common.dao.dream.OrderSearchDao;
import cn.loveapp.uac.db.common.entity.OrderSearch;
import cn.loveapp.uac.db.common.repository.OrderSearchRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * @program: uac-service-group
 * @description: TaobaoOrderSearchRepositoryImpl
 * @author: Jason
 * @create: 2020-03-09 13:39
 **/
@Repository
public class OrderSearchRepositoryImpl implements OrderSearchRepository {

	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OrderSearchRepositoryImpl.class);

	private static final String ORDER_SEARCH_TABLE_NAME = "order_search";
	private static final String TAOBAO_PREFIX = "taobao";

	@Autowired
	private OrderSearchDao orderSearchDao;

	@Override
	public OrderSearch queryBySellerNickAndOrderCycleStartAndOrderCycleEndAndItemCode(
			String sellerNick, LocalDateTime orderCycleStart, LocalDateTime orderCycleEnd, List<String> itemCodes, String sortBy,
			String platformId, String appName) {
		return orderSearchDao.queryBySellerNickAndOrderCycleStartAndOrderCycleEndAndItemCode(sellerNick, orderCycleStart, orderCycleEnd,
				itemCodes, sortBy, getTableName(platformId, appName));
	}

	@Override
	public OrderSearch queryBySellerNickAndItemCodeAndIdSortIdDesc(String sellerNick, String itemCode, Integer id,
	                                                               String platformId, String appName) {
		return orderSearchDao.queryBySellerNickAndItemCodeAndIdSortIdDesc(sellerNick, itemCode, id, getTableName(platformId, appName));
	}

	@Override
	public int insert(OrderSearch save, String platformId, String appName) {
		String tableName = getTableName(platformId, appName);
		if (save == null) {
			return 0;
		}
		return orderSearchDao.insert(save, tableName);
	}

	@Override
	public OrderSearch queryBySellerNickAndArticleCodeAndItemCodeAndOrderCycleEnd(String sellerNick, String articleCode, String itemCode,
																		  LocalDateTime orderCycleEnd, String platformId, String appName) {
		OrderSearch queryOrder = new OrderSearch();
		queryOrder.setNick(sellerNick);
		queryOrder.setArticleCode(articleCode);
		queryOrder.setItemCode(itemCode);
		queryOrder.setOrderCycleEnd(orderCycleEnd);
		return orderSearchDao.queryBySellerNickAndArticleCodeAndItemCodeAndOrderCycleEnd(queryOrder,getTableName(platformId, appName));
	}

    @Override
    public List<OrderSearch> queryOrderSearchList(OrderSearchQueryDTO queryDTO, String platformId, String appName) {
        if (queryDTO == null || queryDTO.getSellerNick() == null) {
            return Collections.emptyList();
        }
        return orderSearchDao.queryOrderSearchList(queryDTO,getTableName(platformId, appName));
    }

	/**
	 * 根据平台和应用获取表名
	 *
	 * @param platformId
	 * @param appName
	 * @return
	 */
	private String getTableName(String platformId, String appName) {
		if (CommonPlatformConstants.PLATFORM_TAO.equals(platformId)) {
			return TAOBAO_PREFIX + "_" + ORDER_SEARCH_TABLE_NAME + "_" + appName.toLowerCase();
		}
		return platformId.toLowerCase() + "_" + ORDER_SEARCH_TABLE_NAME + "_" + appName.toLowerCase();
	}
}
