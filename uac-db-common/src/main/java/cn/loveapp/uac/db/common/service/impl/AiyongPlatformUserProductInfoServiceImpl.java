package cn.loveapp.uac.db.common.service.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.bo.UserBo;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.db.common.repository.UserRepository;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022-11-21 14:05
 * @Description: 爱用平台用户数据库信息接口实现类
 */
@Service
public class AiyongPlatformUserProductInfoServiceImpl extends DefaultPlatformUserProductInfoService {

    public static final LoggerHelper LOGGER =
        LoggerHelper.getLogger(AiyongPlatformUserProductInfoServiceImpl.class);

    @Override
    public UserProductInfo getUserInfo(UserBo userBo, UserRepository userRepository, String platformId,
        String appName) {
            return super.getUserInfo(userBo, userRepository, platformId, appName);
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_AIYONG;
    }
}
