package cn.loveapp.uac.db.common.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>e
 * @Date: 2022/12/21 18:41
 * @Description: 新用户开通（ay_${businessId}_open_user）表实体类
 */
@Data
public class AyBusinessOpenUser implements Serializable {
    private static final long serialVersionUID = -92418965890425077L;

    private Integer id;

    /**
     * 用户id
     */
    private String sellerId;

    /**
     * 卖家主昵称
     */
    private String sellerNick;

    /**
     * 状态：101:待开通,102:正在开通,10:已开通,-101:开通失败
     */
    private Integer status;

    /**
     * 平台id
     * {@link cn.loveapp.common.constant.CommonPlatformConstants}
     */
    private String platId;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 开通规则,1:全开通，2:根据条件开通
     */
    private Integer ruleId;

    /**
     * 扩展字段
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModify;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 最新一条log_id
     */
    private Integer userLogId;

    /**
     * 1：准备开通
     */
    public static final int WAIT_OPEN = 101;

    /**
     * 2：开通中
     */
    public static final int OPENING = 102;

    /**
     * 3:已开通
     */
    public static final int DONE = 10;

    /**
     * 0:开通失败
     */
    public static final int FAILED_INVALID_TOKEN = -103;

    /**
     * 0:开通失败
     */
    public static final int FAILED = -101;

    /**
     * 可以重试的失败
     */
    public static final int WAIT_RETRY = -102;


    /**
     * 使用默认规则开通
     */
    public static final int RULE_DEFAULT = 1;

    /**
     * 新开通
     */
    public static final int OPEN_LOG_TYPE_NEW_OPEN = 1;

    /**
     * 重新开通
     */
    public static final int OPEN_LOG_TYPE_RE_OPEN = 2;

    /**
     * 开通新应用
     */
    public static final int OPEN_LOG_TYPE_NEW_APP = 3;

    public Boolean isOpening() {
        return OPENING == status;
    }
}
