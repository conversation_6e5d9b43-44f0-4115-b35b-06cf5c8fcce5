package cn.loveapp.uac.db.common.entity;

import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;

/**
 * 拼多多用户表(PddUserInfo)实体类
 *
 * <AUTHOR>
 * @since 2020-03-04 16:53:51
 */
@Data
public class PddUserInfo implements Serializable {
    private static final long serialVersionUID = -33429235545716173L;

    private Integer id;
    //拼多多id
    private String ownerId;
    //拼多多name
    private String ownerName;
    //session key
    private String accessToken;
    //刷新key
    private String refreshToken;
    //创建时间
    private LocalDateTime createTime;
    //本记录最后编辑的时间
    private LocalDateTime updateTime;
    //refresh_token的更新时间
    private LocalDateTime lastTime;
    //店铺类型
    private String shopType;
    //授权到期时间
    private LocalDateTime authDeadline;

    private String sellerId;


}
