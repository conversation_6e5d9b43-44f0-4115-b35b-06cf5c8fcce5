package cn.loveapp.uac.db.common.cache;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.db.common.dao.dream.UserSettingsDao;
import cn.loveapp.uac.db.common.entity.UserSettings;
import cn.loveapp.uac.domain.UserSettingDTO;
import com.google.common.collect.Lists;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: zhongzijie
 * @Date: 2023/7/13 17:28
 * @Description: 用户默认设置本地缓存
 */
@Component
public class DefaultSettingLocalCache {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(DefaultSettingLocalCache.class);

    private static ConcurrentHashMap<String, UserSettingDTO> cacheMap = new ConcurrentHashMap<>();

    private static final String USER_SETTING_DEFAULT = "default";

    /**
     * 定时任务开关
     */
    @Value("${uac.task.refreshDefaultSetting.enable:false}")
    private boolean taskEnable;

    /**
     * 缓存个数限制（超出限制仅打印报错日志）
     */
    @Value("${uac.cache.defaultSetting.limit:10000}")
    private int cacheLimit;

    public UserSettingDTO get(String key, String appName) {
        return cacheMap.get(getDefaultKey(key, appName));
    }

    @Autowired
    private UserSettingsDao userSettingsDao;

    @Bean
    public CacheLifeCycleManager cacheLifeCycleManager() {
        return new CacheLifeCycleManager();
    }

    /**
     * 缓存生命周期管理
     */
    public static class CacheLifeCycleManager implements CommandLineRunner {

        @Autowired
        private DefaultSettingLocalCache defaultSettingLocalCache;

        @Override
        public void run(String... args) throws Exception {
            defaultSettingLocalCache.refreshDefaultSetting();
        }
    }

    @Scheduled(fixedDelayString = "${uac.task.refreshDefaultSetting.delay:#{5 * 60 * 1000}}")
    private void refreshDefaultSettingTask() {
        if (taskEnable) {
            LOGGER.logInfo("开始执行refreshDefaultSetting任务");
            refreshDefaultSetting();
            LOGGER.logInfo("结束执行refreshDefaultSetting任务");
        }
    }

    /**
     * 刷新默认设置
     */
    private void refreshDefaultSetting() {
        List<UserSettings> userSettingsList = pageQueryDefaultSettings();
        ConcurrentHashMap<String, UserSettingDTO> newCacheMap = new ConcurrentHashMap<>();
        for (UserSettings userSettings : userSettingsList) {
            UserSettingDTO userSettingDTO = new UserSettingDTO();
            userSettingDTO.setKey(userSettings.getSettingKey());
            userSettingDTO.setValue(userSettings.getSettingValue());
            // 给不同应用设置各自的默认key
            newCacheMap.put(userSettings.getAppName() + userSettings.getSettingKey(), userSettingDTO);
        }
        if (newCacheMap.size() > cacheLimit) {
            LOGGER.logError("默认设置本地缓存大小超过" + cacheLimit);
        }
        cacheMap = newCacheMap;
    }

    /**
     * 分页查询所有默认设置
     *
     * @return
     */
    private List<UserSettings> pageQueryDefaultSettings() {
        int pageNo = 0;
        final int pageSize = 1000;
        List<UserSettings> userSettingsList = Lists.newArrayList();
        List<UserSettings> currentUserSettingsList;
        do {
            int offset = pageNo * pageSize;
            int limit = pageSize;
            currentUserSettingsList = userSettingsDao.queryDefaultSetting(offset, limit);
            userSettingsList.addAll(currentUserSettingsList);
            pageNo++;
        } while (currentUserSettingsList.size() == pageSize);
        return userSettingsList;
    }


    private static String getDefaultKey(String keyNotFoundByDb, String appName) {
        String key;
        if (Objects.equals(CommonAppConstants.APP_TRADE, appName)
            || Objects.equals(CommonAppConstants.APP_GUANDIAN, appName)) {
            // 兼容老数据 老数据库里appName和storeId都为 "default"
            key = USER_SETTING_DEFAULT + keyNotFoundByDb;
        } else {
            key = appName + keyNotFoundByDb;
        }
        return key;
    }
}
