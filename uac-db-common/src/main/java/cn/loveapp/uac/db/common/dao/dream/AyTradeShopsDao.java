package cn.loveapp.uac.db.common.dao.dream;

import cn.loveapp.uac.db.common.entity.AyTradeShops;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 卖家店铺群(AyTradeShops)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-04-18 11:48:02
 */
public interface AyTradeShopsDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AyTradeShops queryById(Integer id);

	/**
	 * 通过sellernick查询单条数据
	 *
	 * @param sellerNick 主键
	 * @return 实例对象
	 */
	AyTradeShops queryBySellerNick(@Param("sellerNick") String sellerNick);

    /**
     * 查询指定行数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    List<AyTradeShops> queryAllByLimit(@Param("offset") int offset, @Param("limit") int limit);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param ayTradeShops 实例对象
     * @return 对象列表
     */
    List<AyTradeShops> queryAll(AyTradeShops ayTradeShops);

    /**
     * 新增数据
     *
     * @param ayTradeShops 实例对象
     * @return 影响行数
     */
    int insert(AyTradeShops ayTradeShops);

	int batchInsert(@Param("tradeShops") List<AyTradeShops> ayTradeShops);

    /**
     * 修改数据
     *
     * @param ayTradeShops 实例对象
     * @return 影响行数
     */
    int update(AyTradeShops ayTradeShops);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

}
