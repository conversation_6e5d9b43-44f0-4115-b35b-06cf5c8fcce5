package cn.loveapp.uac.db.common.entity;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/21 18:41
 * @Description: 开户操作日志表（ay_${businessId}_open_user_log）实体类
 */
@Data
@ToString
public class AyBusinessOpenUserLog implements Serializable {
    private static final long serialVersionUID = -53431858067496096L;
    private Integer id;

    private Integer openUserId;

    /**
     * 卖家主nick
     */
    private String sellerNick;

    /**
     * 10:已开通,-101:授权失败,-102:已到期
     */
    private Integer status;

    /**
     * 开通类型,1:新开通,2:重新开通
     */
    private Integer type;

    /**
     * 开通规则,1:全开通，2:根据条件开通
     */
    private Integer ruleId;

    /**
     * 原因
     */
    private String reason;

    /**
     * 备注
     */
    private String remark;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModify;


}
