package cn.loveapp.uac.db.common.dao.dream;

import cn.loveapp.uac.db.common.entity.ReturnMoneyProjectInfo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * (ReturnMoneyProjectInfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-03-06 11:29:55
 */
public interface ReturnMoneyProjectInfoDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    ReturnMoneyProjectInfo queryById(Integer id);

    /**
     * 查询指定行数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    List<ReturnMoneyProjectInfo> queryAllByLimit(@Param("offset") int offset, @Param("limit") int limit);

	/**
	 * 通过projectcode查询
	 * @param projectCode
	 * @return
	 */
	ReturnMoneyProjectInfo queryReturnMoneyProjectInfoByProjectCode(@Param("projectCode") String projectCode);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param returnMoneyProjectInfo 实例对象
     * @return 对象列表
     */
    List<ReturnMoneyProjectInfo> queryAll(ReturnMoneyProjectInfo returnMoneyProjectInfo);

    /**
     * 新增数据
     *
     * @param returnMoneyProjectInfo 实例对象
     * @return 影响行数
     */
    int insert(ReturnMoneyProjectInfo returnMoneyProjectInfo);

    /**
     * 修改数据
     *
     * @param returnMoneyProjectInfo 实例对象
     * @return 影响行数
     */
    int update(ReturnMoneyProjectInfo returnMoneyProjectInfo);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

}
