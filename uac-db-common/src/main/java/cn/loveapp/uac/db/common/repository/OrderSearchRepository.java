package cn.loveapp.uac.db.common.repository;

import cn.loveapp.uac.common.dto.OrderSearchQueryDTO;
import cn.loveapp.uac.db.common.entity.OrderSearch;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: uac-service-group
 * @description: TaobaoOrderSearchRepository
 * @author: <PERSON>
 * @create: 2020-03-09 13:39
 **/
public interface OrderSearchRepository {

	/**
	 * 通过用户昵称 + orderCycleStart=<now()<=OrderCycleEnd + itemCode = 'FW_GOODS-1827490-v2' 查询用户单条信息
	 * @param sellerNick
	 * @param orderCycleStart
	 * @param orderCycleEnd
	 * @param itemCodes
	 * @param sortBy
	 * @param platformId
	 * @param appName
	 * @return
	 */
	OrderSearch queryBySellerNickAndOrderCycleStartAndOrderCycleEndAndItemCode(
      String sellerNick,
      LocalDateTime orderCycleStart, LocalDateTime orderCycleEnd, List<String> itemCodes, String sortBy, String platformId, String appName);

	/**
	 * 通过用户昵称 + itemCode = 'FW_GOODS-1827490-v2' + id < #{id} order by id desc 查询用户单条信息
	 * @param sellerNick
	 * @param itemCode
	 * @param id
	 * @param platformId
	 * @param appName
	 * @return
	 */
	OrderSearch queryBySellerNickAndItemCodeAndIdSortIdDesc(String sellerNick, String itemCode,
                                                            Integer id, String platformId, String appName);

	/**
	 * 保存用户订购记录
	 * @param save
	 * @param platformId
	 * @param appName
	 * @return
	 */
	int insert(OrderSearch save, String platformId, String appName);


	/**
	 *根据nick+item_code+order_cycle_end查询
	 * @return
	 */
	OrderSearch queryBySellerNickAndArticleCodeAndItemCodeAndOrderCycleEnd(String sellerNick,String articleCode,
															   String itemCode,LocalDateTime orderCycleEnd,String platformId, String appName);

    /**
     * 查询订购列表
     * @param queryDTO
     * @return
     */
    List<OrderSearch> queryOrderSearchList(OrderSearchQueryDTO queryDTO, String platformId, String appName);
}
