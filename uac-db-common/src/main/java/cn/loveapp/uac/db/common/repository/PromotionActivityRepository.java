package cn.loveapp.uac.db.common.repository;

import cn.loveapp.uac.common.entity.PromotionActivity;

import java.util.List;

/**
 * @program: uac-service-group
 * @description: PromotionActivityRepository
 * @author: <PERSON>
 * @create: 2020-03-09 12:18
 **/
public interface PromotionActivityRepository {

	/**
	 * 聚合查询用户的剩余周期和最小操作时间
	 * @param sellerNick
	 * @param unUsed
	 * @return
	 */
	List<PromotionActivity> aggregationActCycleAndOptimeBySellerNickAndUnused(String sellerNick, Boolean unUsed, String platformId, String appName);

	/**
	 * 通过sellerNick + isUsed查询
	 * @param sellerNick
	 * @param unUsed
	 * @return
	 */
	List<PromotionActivity> queryByIsUsedAndSellerNickSortOptime(String sellerNick, Boolean unUsed, String platformId, String appName);

	/**
	 * 新增数据
	 *
	 * @param promotionActivity 实例对象
	 * @return 影响行数
	 */
	int insert(PromotionActivity promotionActivity, String platformId, String appName);


	/**
	 * 新增数据
	 *
	 * @param promotionActivityList 实例对象
	 * @return 影响行数
	 */
	int insertBatch(List<PromotionActivity> promotionActivityList, String platformId, String appName);

	/**
	 * 更新use状态&actcycle
	 * @param isused
	 * @param actCycle
	 * @param pkIds
	 * @return
	 */
	int updateIsUsedOrActCycleByPkIds(Boolean isused, Integer actCycle, List<Integer> pkIds, String platformId, String appName);

	/**
	 * 更新用户所有信息
	 * @param isused
	 * @param sellerNick
	 * @return
	 */
	int updateIsUsedBySellerNick(Boolean isused, String sellerNick, String platformId, String appName);

}
