package cn.loveapp.uac.db.common.dao.dream;

import cn.loveapp.uac.common.dto.OrderSearchQueryDTO;
import cn.loveapp.uac.db.common.entity.OrderSearch;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订购记录表（[platformId]_order_search_[appName]）表数据库访问层
 *
 * <AUTHOR>
 */
public interface OrderSearchDao {
    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    OrderSearch queryById(@Param("id") Integer id, @Param("tableName") String tableName);

    /**
     * 查询指定行数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    List<OrderSearch> queryAllByLimit(@Param("offset") int offset, @Param("limit") int limit, @Param("tableName") String tableName);



    /**
     * 通过用户昵称 + orderCycleStart=<now()<=OrderCycleEnd + itemCode = 'FW_GOODS-1827490-v2' 查询用户单条信息
     * @param sellerNick
     * @param orderCycleStart
     * @param orderCycleEnd
     * @param itemCodes
     * @return
     */
    OrderSearch queryBySellerNickAndOrderCycleStartAndOrderCycleEndAndItemCode(@Param("sellerNick") String sellerNick, @Param("orderCycleStart")
            LocalDateTime orderCycleStart, @Param("orderCycleEnd") LocalDateTime orderCycleEnd, @Param("itemCodes") List<String> itemCodes, @Param("sortBy") String sortBy, @Param("tableName") String tableName);

    /**
     * 通过用户昵称 + itemCode = 'FW_GOODS-1827490-v2' + id < #{id} order by id desc 查询用户单条信息
     * @param sellerNick
     * @param itemCode
     * @param id
     * @return
     */
    OrderSearch queryBySellerNickAndItemCodeAndIdSortIdDesc(@Param("sellerNick") String sellerNick, @Param("itemCode") String itemCode, @Param("id") Integer id, @Param("tableName") String tableName);

    /**
     * 新增数据
     *
     * @param orderSearch 实例对象
     * @return 影响行数
     */
    int insert(@Param("orderSearch") OrderSearch orderSearch, @Param("tableName") String tableName);

    /**
     * 修改数据
     *
     * @param orderSearch 实例对象
     * @return 影响行数
     */
    int update(@Param("orderSearch") OrderSearch orderSearch, @Param("tableName") String tableName);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(@Param("id") Integer id, @Param("tableName") String tableName);

    /**
     * 根据nick+item_code+order_cycle_end查询
     * @param orderSearch
     * @param tableName
     * @return
     */
    OrderSearch queryBySellerNickAndArticleCodeAndItemCodeAndOrderCycleEnd(@Param("orderSearch") OrderSearch orderSearch, @Param("tableName") String tableName);

    /**
     * 查询订购记录列表
     *
     * @param queryDTO
     * @param tableName
     * @return
     */
    List<OrderSearch> queryOrderSearchList(@Param("queryDTO") OrderSearchQueryDTO queryDTO, @Param("tableName") String tableName);
}
