package cn.loveapp.uac.db.common.dao.dream;

import cn.loveapp.uac.common.entity.UserProductInfo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * user_productinfo_**_** 表 （数据库访问层）
 *
 * <AUTHOR>
 */
public interface BasePlatformUserProductinfoDao {

    /**
     * 通过 user_id 字段查询单条记录
     *
     * @param sellerId        用户id
     * @param tableName       各平台真实的表名
     * @param extensionFields 各平台额外的字段
     * @return
     */
    UserProductInfo queryByUserId(@Param("sellerId") String sellerId, @Param("tableName") String tableName, @Param("extensionFields") String extensionFields);

    /**
     * 通过 user_id_str 字段查询单条记录
     *
     * @param sellerId        用户id
     * @param tableName       各平台真实的表名
     * @param extensionFields 各平台额外的字段
     * @return
     */
    UserProductInfo queryByUserIdStr(@Param("sellerId") String sellerId, @Param("tableName") String tableName, @Param("extensionFields") String extensionFields);

    /**
     * 通过 memberid 字段查询单条记录
     *
     * @param memberId        1688平台额外的memberId字段
     * @param tableName       各平台真实的表名
     * @param extensionFields 各平台额外的字段
     * @return
     */
    UserProductInfo queryByMemberId(@Param("memberId") String memberId, @Param("tableName") String tableName, @Param("extensionFields") String extensionFields);

    /**
     * 通过 appId 字段查询单条记录
     *
     * @param sellerAppId     必要用户的appId
     * @param tableName       各平台真实的表名
     * @param extensionFields 各平台额外的字段
     * @return
     */
    UserProductInfo queryByAppId(@Param("appId") String sellerAppId, @Param("tableName") String tableName, @Param("extensionFields") String extensionFields);

    /**
     * 通过sellerNick查询数据
     *
     * @param sellerNick      用户昵称
     * @param tableName       各平台真实的表名
     * @param extensionFields 各平台额外的字段
     * @return
     */
    UserProductInfo queryBySellerNick(@Param("sellerNick") String sellerNick, @Param("tableName") String tableName, @Param("extensionFields") String extensionFields);

    /**
     * 通过shopName查询数据
     *
     * @param shopName      用户昵称
     * @param extensionFields 各平台额外的字段
     * @return
     */
    UserProductInfo queryByShopName(@Param("shopName") String shopName, @Param("tableName") String tableName,
        @Param("extensionFields") String extensionFields);

    /**
     * 通过vipFlagList获取用户nick列表
     *
     * @param vipFlagList
     * @param maxId
     * @param tableName
     * @return
     */
    List<UserProductInfo> queryNickListByVipFlagList(@Param("vipFlagList") List<Integer> vipFlagList,
        @Param("maxId") Integer maxId, @Param("limit") Integer limit, @Param("tableName") String tableName);

    /**
     * 通过vipFlagList获取用户nick列表
     *
     * @param professionalOrderCycleEnd
     * @param maxId
     * @param tableName
     * @return
     */
    List<UserProductInfo> queryNickListByProfessionalOrderCycleEnd(
        @Param("professionalOrderCycleEnd") LocalDateTime professionalOrderCycleEnd, @Param("maxId") Integer maxId,
        @Param("limit") Integer limit, @Param("tableName") String tableName);

    /**
     * 通过多个sellerNick查询指定的记录
     *
     * @param sellerNicks     用户昵称列表
     * @param tableName       各平台真实的表名
     * @param extensionFields 各平台额外的字段
     * @return
     */
    List<UserProductInfo> queryUserProductInfoListBySellerNickCollection(@Param("sellerNicks") List<String> sellerNicks,
                                                                         @Param("tableName") String tableName, @Param("extensionFields") String extensionFields);

    /**
     * 通过用户level信息查询记录
     *
     * @param level
     * @param isNeedAuth
     * @param maxId
     * @param offset
     * @param limit
     * @return
     */
    List<UserProductInfo> queryByLevel(@Param("vipflag") Integer level, @Param("isNeedAuth") Boolean isNeedAuth,
                                       @Param("maxId") Integer maxId, @Param("offset") Integer offset, @Param("limit") Integer limit,
                                       @Param("tableName") String tableName, @Param("extensionFields") String extensionFields);

    /**
     * 根据用户level和w1_dead_line 信息查询记录
     * @param level
     * @param authDeadLine
     * @param maxId
     * @param offset
     * @param limit
     * @return
     */
    List<UserProductInfo> queryByLevelAndW1DeadLine(@Param("vipflag") Integer level, @Param("authDeadLine") LocalDateTime authDeadLine,
                                                    @Param("maxId") Integer maxId, @Param("offset") Integer offset, @Param("limit") Integer limit,
                                                    @Param("tableName") String tableName, @Param("extensionFields") String extensionFields);

    /**
     * 根据用户w1_dead_line 和 order_cycle_end 信息查询vip用户记录
     * @param w1DeadLineStart
     * @param orderCycleEnd
     * @param maxId
     * @param offset
     * @param limit
     * @param tableName
     * @param extensionFields
     * @return
     */
    List<UserProductInfo> queryByW1DeadLineAndOrderCycleEnd(@Param("w1DeadLineStart") LocalDateTime w1DeadLineStart,
                                                    @Param("w1DeadLineEnd") LocalDateTime w1DeadLineEnd,
                                                    @Param("orderCycleEnd") LocalDateTime orderCycleEnd,
                                                    @Param("maxId") Integer maxId, @Param("offset") Integer offset, @Param("limit") Integer limit,
                                                    @Param("tableName") String tableName, @Param("extensionFields") String extensionFields);

    /**
     * 批量更新指定sellerNick记录的level字段
     *
     * @param sellerNicks 用户昵称列表
     * @param level       更新后的level字段的值
     * @param tableName   各平台真实的表名
     * @return
     */
    int updateBatchLevelBySellerNickCollection(@Param("sellerNicks") List<String> sellerNicks, @Param("level") Integer level, @Param("tableName") String tableName);

    /**
     * 新增数据
     *
     * @param userProductinfo 实例对象
     * @param tableName       各平台真实的表名
     * @return 影响行数
     */
    int insert(@Param("userProductinfo") UserProductInfo userProductinfo, @Param("tableName") String tableName);

    /**
     * 修改数据
     *
     * @param userProductinfo 实例对象
     * @param tableName       各平台真实的表名
     * @param useUserId       是否使用UserId更新
     * @param isDistribute1688     是否是1688代发用户
     * @return 影响行数
     */
    int update(@Param("userProductinfo") UserProductInfo userProductinfo, @Param("tableName") String tableName,
        @Param("useUserId") boolean useUserId, @Param("isDistribute1688") boolean isDistribute1688);

    /**
     * 通过 mallName 字段查询单条记录
     *
     * @param mallName
     * @param tableName
     * @param extensionFields
     * @return
     */
    UserProductInfo queryByMallName(@Param("mallName") String mallName, @Param("tableName") String tableName, @Param("extensionFields") String extensionFields);

    /**
     * 分页查询w1_deadline在指定时间之前的用户数据
     *
     * @param w1DeadlineBefore w1_deadline在此时间之前的数据
     * @param lastW1Deadline 上一次查询的最后一个w1_deadline值，用于滚动分页
     * @param limit 每页查询的条数
     * @param tableName 表名
     * @param extensionFields 扩展字段
     * @return
     */
    List<UserProductInfo> queryByW1DeadlineBeforeWithPage(@Param("w1DeadlineBefore") LocalDateTime w1DeadlineBefore,
                                                          @Param("lastW1Deadline") LocalDateTime lastW1Deadline,
                                                          @Param("limit") Integer limit,
                                                          @Param("tableName") String tableName,
                                                          @Param("extensionFields") String extensionFields);
}
