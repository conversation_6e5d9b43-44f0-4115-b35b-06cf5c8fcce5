package cn.loveapp.uac.db.common.repository;

import cn.loveapp.uac.db.common.entity.AyBusinessOpenUserLog;

/**
 * @Author: z<PERSON><PERSON><PERSON>e
 * @Date: 2023/1/11 20:36
 * @Description: 开户操作日志repository
 */
public interface AyBusinessOpenUserLogRepository {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AyBusinessOpenUserLog queryById(Integer id, String businessId);

    /**
     * 新增数据
     *
     * @param ayBusinessOpenUserLog 实例对象
     * @return 影响行数
     */
    int insert(AyBusinessOpenUserLog ayBusinessOpenUserLog, String businessId);

    /**
     * 修改数据
     *
     * @param ayBusinessOpenUserLog 实例对象
     * @return 影响行数
     */
    int update(AyBusinessOpenUserLog ayBusinessOpenUserLog, String businessId);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id, String businessId);
}
