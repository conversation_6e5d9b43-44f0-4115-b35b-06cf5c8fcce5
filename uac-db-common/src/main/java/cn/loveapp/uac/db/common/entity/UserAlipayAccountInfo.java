package cn.loveapp.uac.db.common.entity;

import java.time.LocalDateTime;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.ToString;

/**
 * (UserAlipayAccountInfo)实体类
 *
 * <AUTHOR>
 * @since 2020-03-06 11:29:07
 */
@Data
@ToString
public class UserAlipayAccountInfo implements Serializable {
    private static final long serialVersionUID = -50639420352151599L;
    //id
    private Integer id;
    //用户nick
    private String sellerNick;
    //用户实名
    private String sellerName;
    //用户支付宝账号
    private String sellerAlipayAccount;
    //添加时间
    private LocalDateTime addTime;
    //是否返现 0未返现 1待返现 2已返现

    private Integer status;
    //用户初高级版信息
    private Integer vipFlag;
    //用户订购信息 到期时间
    private LocalDateTime vipTime;
    //操作人姓名
    private String optionnick;
    //是否自动返现 1-是 0-否 2-手动操作之前的数据
    private Integer isAutoReturn;
    //返现金额 默认-0
    private Integer returnMoney;
    //推送到返现表
    private Boolean pushedReturn;
    //订购时间
    private LocalDateTime payDate;
    //是否推送机器人
    private Boolean isPushed;
    //登记信息的设备来源
    private String platform;

	public static final int LEND_INIT = 0;
	public static final int LEND_WAIT = 1;
	public static final int LEND_DONE = 2;

}
