package cn.loveapp.uac.db.common.repository.impl;

import cn.loveapp.common.constant.CommonBusinessConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.db.common.dao.dream.BaseAyBusinessOpenUserDao;
import cn.loveapp.uac.db.common.entity.AyBusinessOpenUser;
import cn.loveapp.uac.db.common.repository.AyBusinessOpenUserRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Author: zhongzijie
 * @Date: 2022/12/22 14:31
 * @Description: 新用户开通Repository实现类
 */
@Repository
public class AyBusinessOpenUserRepositoryImpl implements AyBusinessOpenUserRepository {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(AyBusinessOpenUserRepositoryImpl.class);

    private final Map<String, BaseAyBusinessOpenUserDao> businessAyOpenUserDaoMap;

    public AyBusinessOpenUserRepositoryImpl(BaseAyBusinessOpenUserDao baseAyBusinessOpenUserDao) {
        this.businessAyOpenUserDaoMap = new HashMap<>();
        this.businessAyOpenUserDaoMap.put(CommonBusinessConstants.BUSINESS_DEFAULT, baseAyBusinessOpenUserDao);
    }

    /**
     * 根据业务id获取表名
     * @param businessId
     * @return
     */
    private String getTableName(String businessId) {
        if (StringUtils.isEmpty(businessId) || !CommonBusinessConstants.getBusiness().contains(businessId)) {
            throw new RuntimeException("businessId不正确，无法组成正确的表名: " + businessId);
        } else {
            return "ay_" + businessId + "_open_user";
        }
    }

    /**
     * 根据业务id获取对应的dao
     * @param businessId
     * @return
     */
    private BaseAyBusinessOpenUserDao getAyOpenUserDao (String businessId) {
        if (StringUtils.isEmpty(businessId) || !CommonBusinessConstants.getBusiness().contains(businessId)) {
            return null;
        } else {
            BaseAyBusinessOpenUserDao baseAyBusinessOpenUserDao = businessAyOpenUserDaoMap.get(businessId);
            if (baseAyBusinessOpenUserDao == null) {
                return businessAyOpenUserDaoMap.get(CommonBusinessConstants.BUSINESS_DEFAULT);
            } else {
                return baseAyBusinessOpenUserDao;
            }
        }
    }

    @Override
    public AyBusinessOpenUser queryById(Integer id, String businessId) {
        return getAyOpenUserDao(businessId).queryById(id, getTableName(businessId));
    }

    @Override
    public int updateByStatus(AyBusinessOpenUser ayBusinessOpenUser, String businessId) {
        return getAyOpenUserDao(businessId).updateByStatus(ayBusinessOpenUser, getTableName(businessId));
    }

    @Override
    public AyBusinessOpenUser queryBySellerIdAndPlatId(String sellerId, String platId, String appName, String businessId) {
        return getAyOpenUserDao(businessId).queryBySellerIdAndPlatId(sellerId, platId, appName, getTableName(businessId));
    }

    @Override
    public int insert(AyBusinessOpenUser ayBusinessOpenUser, String businessId) {
        return getAyOpenUserDao(businessId).insert(ayBusinessOpenUser, getTableName(businessId));
    }

    @Override
    public int updateByStatusAndRetryCount(AyBusinessOpenUser ayBusinessOpenUser, String businessId) {
        return getAyOpenUserDao(businessId).updateByStatusAndRetryCount(ayBusinessOpenUser, getTableName(businessId));
    }

    @Override
    public Set<AyBusinessOpenUser> queryByStatus(List<Integer> status, Integer maxId, Integer offset, Integer limit, String businessId) {
        return getAyOpenUserDao(businessId).queryByStatus(status, maxId, offset, limit, getTableName(businessId));
    }

    @Override
    public Set<AyBusinessOpenUser> queryByStatusAndModify(int status, LocalDateTime modify, String businessId) {
        return getAyOpenUserDao(businessId).queryByStatusAndModify(status, modify, getTableName(businessId));
    }
}
