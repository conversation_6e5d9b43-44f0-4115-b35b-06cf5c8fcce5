package cn.loveapp.uac.db.common.dao.dream;

import cn.loveapp.uac.db.common.entity.AyMultiUserTag;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 爱用多店用户tag实体(AyMultiUserTag)表数据库访问层
 *
 * <AUTHOR>
 * @Date 2023/10/16 11:28 AM
 */
public interface AyMultiUserTagDao {

    /**
     * 更新数据
     *
     * @param ayMultiUserTag
     * @return
     */
    int insertOrUpdate(AyMultiUserTag ayMultiUserTag);

    /**
     * 更新数据
     *
     * @param ayMultiUserTagList
     * @return
     */
    int batchInsertOrUpdate(@Param("ayMultiUserTagList") List<AyMultiUserTag> ayMultiUserTagList);


    /**
     * 查询tag
     *
     * @param sellerId
     * @param appName
     * @param storeId
     * @return
     */
    AyMultiUserTag queryTag(@Param("sellerId") String sellerId, @Param("storeId") String storeId, @Param("appName") String appName);


    /**
     * 查询tag
     *
     * @param sellerId
     * @param appName
     * @param storeId
     * @return
     */
    AyMultiUserTag queryTagByType(@Param("sellerId") String sellerId, @Param("storeId") String storeId, @Param("appName") String appName, @Param("type") Integer type);


}
