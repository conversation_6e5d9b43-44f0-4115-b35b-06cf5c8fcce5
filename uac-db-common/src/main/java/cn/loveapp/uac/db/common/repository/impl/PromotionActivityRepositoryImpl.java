package cn.loveapp.uac.db.common.repository.impl;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.uac.common.entity.PromotionActivity;
import cn.loveapp.uac.db.common.dao.dream.PromotionActivityDao;
import cn.loveapp.uac.db.common.repository.PromotionActivityRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @program: uac-service-group
 * @description: PromotionActivityRepositoryImpl
 * @author: Jason
 * @create: 2020-03-09 12:19
 **/
@Repository
public class PromotionActivityRepositoryImpl implements PromotionActivityRepository {
	private static final LoggerHelper LOGGER = LoggerHelper.getLogger(PromotionActivityRepositoryImpl.class);

	private static final String PROMOTION_ACTIVITY_TABLE_NAME = "promotion_activity";

	@Autowired
	private PromotionActivityDao promotionActivityDao;

	@Override
	public List<PromotionActivity> aggregationActCycleAndOptimeBySellerNickAndUnused(String sellerNick, Boolean unUsed, String platformId, String appName) {
		return promotionActivityDao.aggregationActCycleAndOptimeBySellerNickAndUnused(sellerNick, unUsed, getTableName(platformId, appName));
	}

	@Override
	public List<PromotionActivity> queryByIsUsedAndSellerNickSortOptime(String sellerNick, Boolean unUsed, String platformId, String appName) {
		return promotionActivityDao.queryByIsUsedAndSellerNickSortOptime(sellerNick, unUsed, getTableName(platformId, appName));
	}

	@Override
	public int insert(PromotionActivity promotionActivity, String platformId, String appName) {
		return promotionActivityDao.insert(promotionActivity, getTableName(platformId, appName));
	}

	@Override
	public int insertBatch(List<PromotionActivity> promotionActivityList, String platformId, String appName) {
		return promotionActivityDao.insertBatch(promotionActivityList,getTableName(platformId, appName));
	}

	@Override
	public int updateIsUsedOrActCycleByPkIds(Boolean isused, Integer actCycle, List<Integer> pkIds, String platformId, String appName) {
		return promotionActivityDao.updateIsUsedOrActCycleByPkIds(isused, actCycle, pkIds, getTableName(platformId, appName));
	}

	@Override
	public int updateIsUsedBySellerNick(Boolean isused, String sellerNick, String platformId, String appName) {
		return promotionActivityDao.updateIsUsedBySellerNick(isused, sellerNick, getTableName(platformId, appName));
	}

	/**
	 * 根据平台和应用获取表名
	 *
	 * @param platformId
	 * @param appName
	 * @return
	 */
	private String getTableName(String platformId, String appName) {
		if (CommonPlatformConstants.PLATFORM_TAO.equals(platformId)) {
			return PROMOTION_ACTIVITY_TABLE_NAME + "_" + appName.toLowerCase();
		}
		return PROMOTION_ACTIVITY_TABLE_NAME + "_" + platformId.toLowerCase() + "_" + appName.toLowerCase();
	}

}
