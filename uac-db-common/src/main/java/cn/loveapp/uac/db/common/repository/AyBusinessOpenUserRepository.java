package cn.loveapp.uac.db.common.repository;

import cn.loveapp.uac.db.common.entity.AyBusinessOpenUser;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/12/22 14:30
 * @Description: 新用户开通Repository
 */
public interface AyBusinessOpenUserRepository {

    /**
     * 主键查询
     *
     * @param id
     * @param businessId
     * @return
     */
    AyBusinessOpenUser queryById(Integer id, String businessId);

    /**
     * 修改数据
     *
     * @param ayBusinessOpenUser 实例对象
     * @param businessId
     * @return 影响行数
     */
    int updateByStatus(AyBusinessOpenUser ayBusinessOpenUser, String businessId);


    /**
     * 通过nick和平台查询
     *
     * @param sellerId 卖家id
     * @param platId 卖家平台
     * @param appName
     * @param businessId
     * @return 实例对象
     */
    AyBusinessOpenUser queryBySellerIdAndPlatId(String sellerId, String platId, String appName, String businessId);

    /**
     * 新增数据
     *
     * @param ayBusinessOpenUser 实例对象
     * @param businessId
     * @return 影响行数
     */
    int insert(AyBusinessOpenUser ayBusinessOpenUser, String businessId);


    /**
     * 更新状态和重试次数
     *
     * @param ayBusinessOpenUser 实例对象
     * @param businessId
     * @return 影响行数
     */
    int updateByStatusAndRetryCount(AyBusinessOpenUser ayBusinessOpenUser, String businessId);

    /**
     * 根据状态查询
     *
     * @param status 状态
     * @param maxId 当前最大id
     * @param offset 偏移量
     * @param limit 返回条数限制
     * @param businessId
     * @return 某一状态的用户
     */
    Set<AyBusinessOpenUser> queryByStatus(List<Integer> status, Integer maxId, Integer offset, Integer limit, String businessId);


    /**
     * 通过状态和修改时间查询
     * @param status 状态
     * @param modify 修改时间
     * @param businessId
     * @return
     */
    Set<AyBusinessOpenUser> queryByStatusAndModify(int status, LocalDateTime modify, String businessId);
}
