package cn.loveapp.uac.db.common.repository;

import cn.loveapp.uac.entity.UserProductInfoBusinessExt;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: zhong<PERSON><PERSON>e
 * @Date: 2023/1/6 16:23
 * @Description: 用户业务状态 Repository
 */
public interface UserProductionInfoExtRepository {

    /**
     * 查询所有信息
     * @param pullStatus
     * @param pullEndPoint
     * @param storeId
     * @param diffTime
     * @param maxId
     * @return
     */
    List<UserProductInfoBusinessExt> queryAllByLimit(int pullStatus, Boolean pullEndPoint, String storeId, LocalDateTime diffTime, int maxId, String businessId);

    /**
     * 依据pullStatus、storeId、topStatus分页查询
     *
     * @param pullStatus
     * @param storeId
     * @param topStatus
     * @param offset
     * @param limit
     * @return
     */
    List<UserProductInfoBusinessExt> queryByPullStatusAndStoreIdAndTopStatusLimit(Integer pullStatus, String storeId, String topStatus, int offset, int limit, String businessId);

    /**
     * 依据topStatus分页查询
     *
     * @param topStatus top状态
     * @param offset 偏移量
     * @param limit 分页数量
     * @return 影响行数
     */
    List<UserProductInfoBusinessExt> queryByTopStatusLimit(String topStatus, long offset, int limit, String businessId);

    /**
     * 根据sellerId查询user production信息
     *
     * @param sellerId 卖家id
     * @param storeId 平台信息
     * @param appName
     * @return 用户信息
     */
    UserProductInfoBusinessExt querySingleBySellerId(String sellerId, String storeId, String appName, String businessId);

    /**
     * 通过sellerId查询用户数量
     * @param sellerId sellerId
     * @param appName
     * @return 用户量
     */
    Integer queryCountBySellerId(String sellerId, String storeId, String appName, String businessId);

    /**
     * 通过memberId查询用户信息，可能有多个
     * @param memberId
     * @param appName
     * @return
     */
    UserProductInfoBusinessExt querySingleByMemberId(String memberId, String storeId, String appName, String businessId);

    /**
     * 	根据查询各dbId订单总和
     * @return
     */
    List<UserProductInfoBusinessExt> getDbidValidOrderSum(String businessId);

    /**
     *
     *
     * 新增数据
     *
     * @param userProductInfoBusinessExt 实例对象
     * @return 影响行数
     */
    int insert(UserProductInfoBusinessExt userProductInfoBusinessExt, String businessId);

    /**
     * 修改数据
     *
     * @param userProductInfoBusinessExt 实例对象
     * @return 影响行数
     */
    int update(UserProductInfoBusinessExt userProductInfoBusinessExt, String businessId);

    /**
     * 更新apiStatus
     * @param sellerId
     * @param apiStatus
     * @param appName
     * @return
     */
    int updateApiStatus(String sellerId, Integer apiStatus, String storeId, String appName, String businessId);

    /**
     * 	根据sellerId的集合,批量更新pull_status状态
     * @param list
     * @param pullStatus
     * @param storeId
     * @param appName
     * @return
     */
    int batchUpdatePullStatusForSellerId(List<String> list, int pullStatus, String storeId, String appName, String businessId);

    /**
     * 查询指定时间区间内的信息
     * @param pullStatus
     * @param pullEndPoint
     * @param storeId
     * @param startDateTime
     * @param endDateTime
     * @param maxId
     * @param businessId
     * @return
     */
    List<UserProductInfoBusinessExt> queryAllByLimitBetweenPullEndTime(int pullStatus, Boolean pullEndPoint, String storeId,
                                                                       LocalDateTime startDateTime, LocalDateTime endDateTime, int maxId, String businessId);

    /**
     * 查询指定用户信息
     * @param pullStatus
     * @param pullEndPoint
     * @param storeId
     * @param maxId
     * @return
     */
    List<UserProductInfoBusinessExt> queryAllWithOutTimeByLimit(int pullStatus, Boolean pullEndPoint, String storeId, int maxId, String businessId);

}



