package cn.loveapp.uac.db.common.entity;

import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;

/**
 * 数据落库状态记录表(UserProductinfoTradeExt)实体类
 *
 * <AUTHOR>
 * @since 2020-03-04 16:52:06
 */
@Data
public class UserProductinfoTradeExt implements Serializable {
    private static final long serialVersionUID = 714948380591155039L;

    private Integer id;
    //商家id
    private String sellerId;
    //用户nick
    private String sellerNick;
    //店铺id
    private String storeId;

    private String corpId;
    //存单状态
    private Integer dbStatus;
    //拉单状态
    private Integer pullStatus;
    //session状态
    private Integer sessionStatus;
    //自有接口状态
    private Integer apiStatus;
    //第三方接口状态
    private String topStatus;
    //入库时,淘宝近3个月订单数量
    private Integer topTradeCount;
    //降级标
    private String downgradeTag;
    //数据库ID
    private Integer dbId;
    //搜索库id
    private Integer searchdbId;
    //表id
    private Integer listId;
    //灰度标识
    private Integer grayLevel;

    private LocalDateTime pullStartDatetime;

    private LocalDateTime pullEndDatetime;
    //最后修改时间
    private LocalDateTime gmtModified;
    //创建时间
    private LocalDateTime gmtCreate;

}
