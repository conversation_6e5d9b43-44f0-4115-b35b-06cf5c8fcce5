package cn.loveapp.uac.db.common.dao.dream;

import cn.loveapp.uac.db.common.entity.PddUserInfo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 拼多多用户表(PddUserInfoTrade)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-03-04 16:53:51
 */
public interface PddUserInfoTradeDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
	PddUserInfo queryById(Integer id);

    /**
     * 查询指定行数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    List<PddUserInfo> queryAllByLimit(@Param("id") int id, @Param("offset") int offset, @Param("limit") int limit);

	/**
	 * 通过sellerNick查询
	 * @param sellerNick
	 * @return
	 */
	PddUserInfo queryBySellerNick(@Param("sellerNick") String sellerNick);


	/**
	 * queryUserProductInfoListBySellerNickCollection
	 * @param sellerNicks
	 * @return
	 */
	List<PddUserInfo> queryUserProductInfoListBySellerNickCollection(List<String> sellerNicks);

    /**
     * 通过实体作为筛选条件查询
     *
     * @param pddUserInfoTrade 实例对象
     * @return 对象列表
     */
    List<PddUserInfo> queryAll(PddUserInfo pddUserInfoTrade);

    /**
     * 新增数据
     *
     * @param pddUserInfoTrade 实例对象
     * @return 影响行数
     */
    int insert(PddUserInfo pddUserInfoTrade);

    /**
     * 修改数据
     *
     * @param pddUserInfoTrade 实例对象
     * @return 影响行数
     */
    int update(PddUserInfo pddUserInfoTrade);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

}
