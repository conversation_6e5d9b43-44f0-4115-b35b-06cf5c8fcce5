package cn.loveapp.uac.db.common.dao.dream;

import cn.loveapp.uac.db.common.entity.BlackIp;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 黑名单IP(BlackIp)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-03-05 15:56:51
 */
public interface BlackIpDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    BlackIp queryById(Integer id);

    /**
     * 查询指定行数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    List<BlackIp> queryAllByLimit(@Param("offset") int offset, @Param("limit") int limit);

	/**
	 * 通过Ip查询数据
	 * @param ipaddress
	 * @return
	 */
	BlackIp queryBlackIpByIpaddress(@Param("ipaddress") String ipaddress);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param blackIp 实例对象
     * @return 对象列表
     */
    List<BlackIp> queryAll(BlackIp blackIp);

    /**
     * 新增数据
     *
     * @param blackIp 实例对象
     * @return 影响行数
     */
    int insert(BlackIp blackIp);

    /**
     * 修改数据
     *
     * @param blackIp 实例对象
     * @return 影响行数
     */
    int update(BlackIp blackIp);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

}
