package cn.loveapp.uac.db.common.dao.dream;

import cn.loveapp.uac.db.common.entity.TradePddAuth;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;

/**
 * (TradePddAuth)表数据库访问层
 *
 * <AUTHOR>
 * @since 2020-03-04 16:54:26
 */
public interface TradePddAuthDao {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    TradePddAuth queryById(Integer id);

	/**
	 * 查询nick
	 * @return
	 */
	List<String> queryDistinctSellerNick();

    /**
     * 查询指定行数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    List<TradePddAuth> queryAllByLimit(@Param("offset") int offset, @Param("limit") int limit);


    /**
     * 通过实体作为筛选条件查询
     *
     * @param tradePddAuth 实例对象
     * @return 对象列表
     */
    List<TradePddAuth> queryAll(TradePddAuth tradePddAuth);

	List<TradePddAuth> queryAllBySellerNick(@Param("sellerNick") String sellerNick);

    /**
     * 新增数据
     *
     * @param tradePddAuth 实例对象
     * @return 影响行数
     */
    int insert(TradePddAuth tradePddAuth);

    /**
     * 修改数据
     *
     * @param tradePddAuth 实例对象
     * @return 影响行数
     */
    int update(TradePddAuth tradePddAuth);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

}
