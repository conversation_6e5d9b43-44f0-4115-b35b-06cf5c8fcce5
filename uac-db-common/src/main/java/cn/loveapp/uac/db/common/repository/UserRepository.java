package cn.loveapp.uac.db.common.repository;

import java.time.LocalDateTime;
import java.util.List;

import cn.loveapp.uac.common.entity.UserProductInfo;

/**
 * @program: uac-service-group
 * @description: UserRepository
 * @author: <PERSON>
 * @create: 2020-03-09 14:35
 **/
public interface UserRepository {

    /**
     * 通过sellerNick查询userinfo信息
     *
     * @param sellerNick
     *            用户昵称
     * @param needTag
     *            是否需要查询tag信息
     * @return
     */
    UserProductInfo queryBySellerNick(String sellerNick, boolean needTag, String platformId, String appName);

    /**
     * 通过sellerId查询userinfo信息
     *
     * @param sellerId
     *            用户id
     * @param needTag
     *            是否需要查询tag信息
     * @return
     */
    UserProductInfo queryBySellerId(String sellerId, boolean needTag, String platformId, String appName);

    /**
     * 通过sellerIdStr查询userinfo信息
     *
     * @param sellerId
     * @param needTag
     * @param platformId
     * @param appName
     * @return
     */
    UserProductInfo queryBySellerIdStr(String sellerId, boolean needTag, String platformId, String appName);

    /**
     * 批量查询vipflag=1以及需要刷新的用户信息
     *
     * @param level
     * @return
     */
    List<UserProductInfo> queryByLevel(Integer level, Boolean isNeedAuth, Integer maxId, Integer offset, Integer limit,
        String platformId, String appName);

    /**
     * 根据memberid获取用户信息(平台: 1688、淘工厂)
     *
     * @param memberId
     * @return
     */
    UserProductInfo queryByMemberId(String memberId, String platformId, String appName);

    /**
     * 根据appId获取用户信息
     *
     * @param appId
     * @return
     */
    UserProductInfo queryByAppId(String appId, String platformId, String appName);

    /**
     * 通过shopName查询userinfo信息
     *
     * @param sellerId
     * @param platformId
     * @param appName
     * @return
     */
    UserProductInfo queryByShopName(String sellerId, String platformId, String appName);

    /**
     * 通过店铺名查询userinfo信息
     *
     * @param mallName
     * @param platformId
     * @param appName
     * @return
     */
    UserProductInfo queryByMallName(String mallName, String platformId, String appName);

    /**
     * 通过vip列表获取用户名
     *
     * @param vipFlagList
     * @param maxId
     * @param limit
     * @param platformId
     * @param appName
     * @return
     */
    List<UserProductInfo> queryNickListByvipFlagList(List<Integer> vipFlagList, Integer maxId, Integer limit, String platformId, String appName);

    /**
     * 通过vip列表获取用户名
     *
     * @param professionalOrderCycleEnd
     * @param maxId
     * @param limit
     * @param platformId
     * @param appName
     * @return
     */
    List<UserProductInfo> queryNickListByProfessionalOrderCycleEnd(LocalDateTime professionalOrderCycleEnd,
        Integer maxId, Integer limit, String platformId, String appName);

    /**
     * 查询到期的用户
     *
     * @param level
     * @param deadLine
     * @param maxId
     * @param offset
     * @param limit
     * @return
     */
    List<UserProductInfo> queryByLevelAndW1DeadLine(Integer level, LocalDateTime deadLine, Integer maxId,
        Integer offset, Integer limit, String platform, String appName);

    /**
     * 查询授权快到期的vip用户
     *
     * @param w1DeadLineStart
     * @param w1DeadLineEnd
     * @param orderCycleEnd
     * @param maxId
     * @param offset
     * @param limit
     * @param platform
     * @param appName
     * @return
     */
    List<UserProductInfo> queryByW1DeadLineAndOrderCycleEnd(LocalDateTime w1DeadLineStart, LocalDateTime w1DeadLineEnd,
        LocalDateTime orderCycleEnd, Integer maxId, Integer offset, Integer limit, String platform, String appName);

    /**
     * queryUserProductInfoListBySellerNickCollection
     *
     * @param sellerNicks
     * @return
     */
    List<UserProductInfo> queryUserProductInfoListBySellerNickCollection(List<String> sellerNicks, String platformId,
        String appName);

    /**
     * 修改数据
     *
     * @param userProductInfo
     *            修改后的实例对象信息
     * @return 影响行数
     */
    int update(UserProductInfo userProductInfo, String platformId, String appName);

    /**
     * 批量更新数据
     *
     * @param sellerNicks
     * @param level
     * @return
     */
    int updateBatchLevelBySellerNickCollection(List<String> sellerNicks, Integer level, String platformId,
        String appType);

    /**
     * 保存数据
     *
     * @param userProductInfo
     *            实例对象
     * @return
     */
    int insert(UserProductInfo userProductInfo, String platformId, String appName);

    /**
     * 更新dbId
     * @param nick
     * @param db
     * @param platformId
     * @param appName
     * @return
     */
    int updateSaveDataDB(String nick, String db, String platformId, String appName);

    /**
     * 分页查询w1_deadline在指定时间之前的用户数据
     *
     * @param w1DeadlineBefore w1_deadline在此时间之前的数据
     * @param lastW1Deadline 上一次查询的最后一个w1_deadline值，用于滚动分页
     * @param limit 每页查询的条数
     * @param platformId 平台ID
     * @param appName 应用名称
     * @return
     */
    List<UserProductInfo> queryByW1DeadlineBeforeWithPage(LocalDateTime w1DeadlineBefore,
                                                           LocalDateTime lastW1Deadline,
                                                           Integer limit,
                                                           String platformId,
                                                           String appName);

}
