package cn.loveapp.uac.db.common.entity;

import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;

/**
 * (UserTag)实体类
 *
 * <AUTHOR>
 * @since 2020-03-04 16:53:23
 */
@Data
public class UserTag implements Serializable {
    private static final long serialVersionUID = 794213508854425483L;

    private Long id;
    //产品项目
    private String app;
    //用户昵称
    private String nick;
    //用户标记
    private String tag;
    //备注
    private String remark;
    //创建时间
    private LocalDateTime createdate;

}
