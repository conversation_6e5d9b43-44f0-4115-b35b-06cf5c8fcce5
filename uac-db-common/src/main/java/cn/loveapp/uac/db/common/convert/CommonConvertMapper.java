package cn.loveapp.uac.db.common.convert;

import cn.loveapp.uac.common.dto.OrderSearchQueryDTO;
import cn.loveapp.uac.db.common.entity.OrderSearch;
import cn.loveapp.uac.domain.UserOrderSearchDTO;
import cn.loveapp.uac.request.UserOrderSearchRequest;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface CommonConvertMapper {
    CommonConvertMapper INSTANCE = Mappers.getMapper(CommonConvertMapper.class);

    List<UserOrderSearchDTO> toOrderSearchList(List<OrderSearch> orderSearchList);

    OrderSearchQueryDTO toOrderSearchQuery(UserOrderSearchRequest request);
}
