package cn.loveapp.uac.db.common.service.impl;

import cn.loveapp.uac.db.common.dao.dream.UserShopInfoMappingDao;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.uac.common.bo.UserBo;
import cn.loveapp.uac.common.entity.UserProductInfo;
import cn.loveapp.uac.db.common.entity.UserShopInfoMapping;
import cn.loveapp.uac.db.common.repository.UserRepository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-12 18:42
 * @description: tiktok用户数据库信息接口实现类
 */
@Service
public class TikTokPlatformUserProductInfoServiceImpl extends DefaultPlatformUserProductInfoService {

    @Autowired
    private UserShopInfoMappingDao userShopInfoMappingDao;

    @Override
    public UserProductInfo getUserInfo(UserBo userBo, UserRepository userRepository, String platformId,
        String appName) {
        UserProductInfo userInfo = null;
        if (StringUtils.isNotEmpty(userBo.getShopId())) {
            // tiktok 需要通过shopId 查到userId
            UserShopInfoMapping userShopInfoMapping =
                userShopInfoMappingDao.queryByShopId(userBo.getShopId(), platformId, appName);

            userBo.setSellerId(userShopInfoMapping.getSellerId());
            if (StringUtils.isNotEmpty(userBo.getSellerId())) {
                userInfo = userRepository.queryBySellerIdStr(userBo.getSellerId(),
                    BooleanUtils.isTrue(userBo.getHasReadTag()), userBo.getPlatformId(), userBo.getAppType());
            } else {
                userInfo = userRepository.queryBySellerNick(userBo.getSellerNick(),
                    BooleanUtils.isTrue(userBo.getHasReadTag()), userBo.getPlatformId(), userBo.getAppType());
            }

            userInfo.setShopId(userShopInfoMapping.getShopId());
            userInfo.setShopCipher(userShopInfoMapping.getShopCipher());

            return userInfo;
        } else if (StringUtils.isNotEmpty(userBo.getSellerId())) {
            userInfo = userRepository.queryBySellerIdStr(userBo.getSellerId(),
                BooleanUtils.isTrue(userBo.getHasReadTag()), userBo.getPlatformId(), userBo.getAppType());
        } else if (StringUtils.isNotEmpty(userBo.getSellerNick())) {
            userInfo = userRepository.queryBySellerNick(userBo.getSellerNick(),
                BooleanUtils.isTrue(userBo.getHasReadTag()), userBo.getPlatformId(), userBo.getAppType());
        }
        return userInfo;
    }

    @Override
    public UserShopInfoMapping getUserShopInfo(String shopId, String platformId, String appName) {
        return userShopInfoMappingDao.queryByShopId(shopId, platformId, appName);
    }

    @Override
    public List<UserShopInfoMapping> getUserShopInfo(String sellerId, String sellerNick, String platformId,
        String appName) {
        List<UserShopInfoMapping> userShopInfoMappings = null;

        if (StringUtils.isEmpty(sellerId)) {
            userShopInfoMappings = userShopInfoMappingDao.queryAllBySellerNick(sellerNick, platformId, appName);
        } else {
            userShopInfoMappings = userShopInfoMappingDao.queryAllBySellerId(sellerId, platformId, appName);
        }
        return userShopInfoMappings;
    }

    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_TIKTOK;
    }
}
