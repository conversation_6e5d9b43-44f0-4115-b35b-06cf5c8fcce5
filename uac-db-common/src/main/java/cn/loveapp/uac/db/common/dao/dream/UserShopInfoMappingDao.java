package cn.loveapp.uac.db.common.dao.dream;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.loveapp.uac.db.common.entity.UserShopInfoMapping;

/**
 * <AUTHOR>
 * @date 2024-04-12 18:18
 * @description: 用户所对应店铺(UserShopInfoMapping)表数据库访问层
 */
public interface UserShopInfoMappingDao {

    /**
     * 通过shopId查询数据
     *
     * @param shopId
     * @param storeId
     * @param appName
     * @return
     */
    UserShopInfoMapping queryByShopId(@Param("shopId") String shopId, @Param("storeId") String storeId,
        @Param("appName") String appName);

    /**
     * 通过sellerId查询数据
     *
     * @param sellerId
     * @param storeId
     * @param appName
     * @return
     */
    List<UserShopInfoMapping> queryAllBySellerId(@Param("sellerId") String sellerId, @Param("storeId") String storeId,
        @Param("appName") String appName);

    /**
     * 通过sellerId查询数据
     *
     * @param sellerNick
     * @param storeId
     * @param appName
     * @return
     */
    List<UserShopInfoMapping> queryAllBySellerNick(@Param("sellerNick") String sellerNick,
        @Param("storeId") String storeId, @Param("appName") String appName);
}
