<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.uac.db.common.dao.dream.PddUserInfoDao">

    <resultMap type="cn.loveapp.uac.db.common.entity.PddUserInfo" id="PddUserInfoMap">
        <result property="id" column="id"/>
        <result property="ownerId" column="owner_id"/>
        <result property="ownerName" column="owner_name"/>
        <result property="accessToken" column="access_token"/>
        <result property="refreshToken" column="refresh_token"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="lastTime" column="last_time"/>
        <result property="shopType" column="shop_type"/>
        <result property="authDeadline" column="auth_deadline"/>
    </resultMap>

	<sql id="field">
		id, owner_id, owner_name, access_token, refresh_token, create_time, update_time, last_time, shop_type, auth_deadline
	</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="PddUserInfoMap">
        select
          <include refid="field"/>
        from lacrm.pdd_user_info
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="PddUserInfoMap">
        select
		<include refid="field"/>
        from lacrm.pdd_user_info where id > #{id}
        limit #{offset}, #{limit}
    </select>

	<select id="queryBySellerNick" resultMap="PddUserInfoMap">
        select
		<include refid="field"/>
        from lacrm.pdd_user_info where owner_name = #{sellerNick}
    </select>


	<select id="queryUserProductInfoListBySellerNickCollection" resultMap="PddUserInfoMap">
		select
		<include refid="field"/>
		from lacrm.pdd_user_info
		where owner_name in
		<foreach item="item" index="index" collection="sellerNicks" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="PddUserInfoMap">
        select
		<include refid="field"/>
        from lacrm.pdd_user_info
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ownerId != null and ownerId != ''">
                and owner_id = #{ownerId}
            </if>
            <if test="ownerName != null and ownerName != ''">
                and owner_name = #{ownerName}
            </if>
            <if test="accessToken != null and accessToken != ''">
                and access_token = #{accessToken}
            </if>
            <if test="refreshToken != null and refreshToken != ''">
                and refresh_token = #{refreshToken}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="lastTime != null">
                and last_time = #{lastTime}
            </if>
            <if test="shopType != null and shopType != ''">
                and shop_type = #{shopType}
            </if>
            <if test="authDeadline != null">
                and auth_deadline = #{authDeadline}
            </if>
			<if test="sellerId != null">
				and seller_id = #{sellerId}
			</if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into lacrm.pdd_user_info(owner_id, owner_name, access_token, refresh_token, create_time, update_time, last_time, shop_type, auth_deadline)
        values (#{ownerId}, #{ownerName}, #{accessToken}, #{refreshToken}, #{createTime}, #{updateTime}, #{lastTime}, #{shopType}, #{authDeadline})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update lacrm.pdd_user_info
        <set>
            <if test="ownerId != null and ownerId != ''">
                owner_id = #{ownerId},
            </if>
            <if test="ownerName != null and ownerName != ''">
                owner_name = #{ownerName},
            </if>
            <if test="accessToken != null and accessToken != ''">
                access_token = #{accessToken},
            </if>
            <if test="refreshToken != null and refreshToken != ''">
                refresh_token = #{refreshToken},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="lastTime != null">
                last_time = #{lastTime},
            </if>
            <if test="shopType != null and shopType != ''">
                shop_type = #{shopType},
            </if>
            <if test="authDeadline != null">
                auth_deadline = #{authDeadline},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from lacrm.pdd_user_info where id = #{id}
    </delete>

</mapper>
