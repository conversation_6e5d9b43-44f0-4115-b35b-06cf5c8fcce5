<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.uac.db.common.dao.dream.UserTagDao">

    <resultMap type="cn.loveapp.uac.db.common.entity.UserTag" id="UserTagMap">
        <result property="id" column="id"/>
        <result property="app" column="app"/>
        <result property="nick" column="nick"/>
        <result property="tag" column="tag"/>
        <result property="remark" column="remark"/>
        <result property="createdate" column="createdate"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="UserTagMap">
        select
          id, app, nick, tag, remark, createdate
        from lacrm.user_tag
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="UserTagMap">
        select
          id, app, nick, tag, remark, createdate
        from lacrm.user_tag
        limit #{offset}, #{limit}
    </select>

	<!--查询指定行数据-->
	<select id="queryUserTagBySellerNick" resultMap="UserTagMap">
        select
          tag
        from lacrm.user_tag where nick = #{sellerNick} and app = #{appType}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="UserTagMap">
        select
          id, app, nick, tag, remark, createdate
        from lacrm.user_tag
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="app != null and app != ''">
                and app = #{app}
            </if>
            <if test="nick != null and nick != ''">
                and nick = #{nick}
            </if>
            <if test="tag != null and tag != ''">
                and tag = #{tag}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="createdate != null">
                and createdate = #{createdate}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into lacrm.user_tag(app, nick, tag, remark, createdate)
        values (#{app}, #{nick}, #{tag}, #{remark}, #{createdate})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update lacrm.user_tag
        <set>
            <if test="app != null and app != ''">
                app = #{app},
            </if>
            <if test="nick != null and nick != ''">
                nick = #{nick},
            </if>
            <if test="tag != null and tag != ''">
                tag = #{tag},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="createdate != null">
                createdate = #{createdate},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from lacrm.user_tag where id = #{id}
    </delete>

</mapper>
