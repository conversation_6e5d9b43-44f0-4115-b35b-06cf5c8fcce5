<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.uac.db.common.dao.dream.TradePddAuthPayDao">

    <resultMap type="cn.loveapp.uac.db.common.entity.TradePddAuthPay" id="TradePddAuthPayMap">
        <result property="id" column="id"/>
        <result property="pddId" column="pdd_id"/>
        <result property="pddName" column="pdd_name"/>
        <result property="nickTrade" column="nick_trade"/>
        <result property="createTime" column="create_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="TradePddAuthPayMap">
        select
          id, pdd_id, pdd_name, nick_trade, create_time, remark
        from lacrm.trade_pdd_auth_pay
        where id = #{id}
    </select>

	<select id="queryDistinctSellerNick" resultType="java.lang.String">
		select
          DISTINCT nick_trade as nick_trade
        from lacrm.trade_pdd_auth_pay;
	</select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="TradePddAuthPayMap">
        select
          id, pdd_id, pdd_name, nick_trade, create_time, remark
        from lacrm.trade_pdd_auth_pay
        limit #{offset}, #{limit}
    </select>

	<!--查询指定行数据-->
	<select id="queryAllBySellerNick" resultMap="TradePddAuthPayMap">
        select
          id, pdd_id, pdd_name, nick_trade, create_time, remark
        from lacrm.trade_pdd_auth_pay
        where nick_trade = #{sellerNick}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="TradePddAuthPayMap">
        select
          id, pdd_id, pdd_name, nick_trade, create_time, remark
        from lacrm.trade_pdd_auth_pay
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="pddId != null">
                and pdd_id = #{pddId}
            </if>
            <if test="pddName != null and pddName != ''">
                and pdd_name = #{pddName}
            </if>
            <if test="nickTrade != null and nickTrade != ''">
                and nick_trade = #{nickTrade}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into lacrm.trade_pdd_auth_pay(pdd_id, pdd_name, nick_trade, create_time, remark)
        values (#{pddId}, #{pddName}, #{nickTrade}, #{createTime}, #{remark})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update lacrm.trade_pdd_auth_pay
        <set>
            <if test="pddId != null">
                pdd_id = #{pddId},
            </if>
            <if test="pddName != null and pddName != ''">
                pdd_name = #{pddName},
            </if>
            <if test="nickTrade != null and nickTrade != ''">
                nick_trade = #{nickTrade},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from lacrm.trade_pdd_auth_pay where id = #{id}
    </delete>

</mapper>
