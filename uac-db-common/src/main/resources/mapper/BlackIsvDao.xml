<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.uac.db.common.dao.dream.BlackIsvDao">

    <resultMap type="cn.loveapp.uac.db.common.entity.BlackIsv" id="BlackIsvMap">
        <result property="isvnick" column="isvnick"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryBlackIsvByIsvnick" resultMap="BlackIsvMap">
        select
          isvnick
        from lacrm.black_isv
        where isvnick = #{isvnick} limit 1
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BlackIsvMap">
        select
          isvnick
        from lacrm.black_isv
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BlackIsvMap">
        select
          isvnick
        from lacrm.black_isv
        <where>
            <if test="isvnick != null and isvnick != ''">
                and isvnick = #{isvnick}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="isvnick" useGeneratedKeys="true">
        insert into lacrm.black_isv()
        values ()
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update lacrm.black_isv
        <set>
        </set>
        where isvnick = #{isvnick}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from lacrm.black_isv where isvnick = #{isvnick}
    </delete>

</mapper>
