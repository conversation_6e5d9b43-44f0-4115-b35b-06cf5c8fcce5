<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.uac.db.common.dao.dream.UserSettingsDao">

    <resultMap type="cn.loveapp.uac.db.common.entity.UserSettings" id="UserSettingsMap">
        <result property="id" column="id"/>
        <result property="settingKey" column="setting_key"/>
        <result property="settingValue" column="setting_value"/>
        <result property="description" column="description"/>
        <result property="userId" column="user_id"/>
        <result property="channelId" column="channel_id"/>
        <result property="platformId" column="platform_id"/>
        <result property="appName" column="app_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


    <sql id="fields">
        id, setting_key, setting_value, description, user_id, channel_id, platform_id, app_name, create_time, update_time
    </sql>

    <select id="queryAllSettings" resultMap="UserSettingsMap">
        select
        <include refid="fields"/>
        from lacrm.user_settings
        where user_id=#{userId}
        and platform_id=#{platformId}
        and app_name=#{appName}
    </select>

    <select id="batchQueryUserSetting" resultMap="UserSettingsMap">
        select
        <include refid="fields"/>
        from lacrm.user_settings
        where user_id=#{userSettingsParam.userId}
        and platform_id=#{userSettingsParam.platformId}
        and app_name=#{userSettingsParam.appName}
        and setting_key in
            <foreach collection="userSettingsParam.settingKeys" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>

    <select id="queryDefaultSetting" resultMap="UserSettingsMap">
        select
        <include refid="fields"/>
        from lacrm.user_settings
        where user_id='default' and platform_id='default'
    </select>

    <insert id="batchUpsertUserSetting">
        INSERT INTO lacrm.user_settings(setting_key, setting_value, description, user_id, channel_id, platform_id, app_name)
        VALUES
        <foreach collection="userSettings" item="item" separator=",">
            (#{item.settingKey},
            #{item.settingValue},
            #{item.description},
            #{item.userId},
            #{item.channelId},
            #{item.platformId},
            #{item.appName})
        </foreach>
            ON DUPLICATE KEY UPDATE setting_value=values(setting_value)
    </insert>

</mapper>
