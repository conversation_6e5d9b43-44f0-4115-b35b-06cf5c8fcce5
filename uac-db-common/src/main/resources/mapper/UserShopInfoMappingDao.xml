<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.uac.db.common.dao.dream.UserShopInfoMappingDao">

    <resultMap type="cn.loveapp.uac.db.common.entity.UserShopInfoMapping" id="UserShopInfoMappingMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="sellerId" column="seller_id" jdbcType="VARCHAR"/>
        <result property="sellerNick" column="seller_nick" jdbcType="VARCHAR"/>
        <result property="shopId" column="shop_id" jdbcType="VARCHAR"/>
        <result property="shopCipher" column="shop_cipher" jdbcType="VARCHAR"/>
        <result property="shopName" column="shop_name" jdbcType="VARCHAR"/>
        <result property="region" column="region" jdbcType="VARCHAR"/>
        <result property="sellerType" column="seller_type" jdbcType="VARCHAR"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="shopsType" column="shops_type" jdbcType="INTEGER"/>
        <result property="appName" column="app_name" jdbcType="VARCHAR"/>
        <result property="storeId" column="store_id" jdbcType="VARCHAR"/>
        <result property="gmtCreated" column="gmt_created" jdbcType="TIMESTAMP"/>
        <result property="gmtModify" column="gmt_modify" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="tableName">
        user_shop_info_mapping
    </sql>

    <sql id="fields">
        id
        , seller_id, seller_nick, shop_id, shop_cipher, shop_name, region, seller_type, code, shops_type, app_name, store_id
    </sql>

    <select id="queryByShopId" resultMap="UserShopInfoMappingMap">
        select
        <include refid="fields"></include>
        from
        <include refid="tableName"></include>
        where shop_id = #{shopId} and store_id = #{storeId} and app_name = #{appName}
    </select>

    <select id="queryAllBySellerId" resultMap="UserShopInfoMappingMap">
        select
        <include refid="fields"></include>
        from
        <include refid="tableName"></include>
        where seller_id = #{sellerId} and store_id = #{storeId} and app_name = #{appName}
    </select>
    <select id="queryAllBySellerNick" resultMap="UserShopInfoMappingMap">
        select
        <include refid="fields"></include>
        from
        <include refid="tableName"></include>
        where seller_nick = #{sellerNick} and store_id = #{storeId} and app_name = #{appName}

    </select>

</mapper>

