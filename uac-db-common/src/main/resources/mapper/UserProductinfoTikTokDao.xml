<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.uac.db.common.dao.dream.UserProductinfoTikTokDao">

    <!--新增所有列-->
    <insert id="insert" keyProperty="userProductinfo.id" useGeneratedKeys="true">
        insert into ${tableName}(user_id, user_id_str, nick, createdate, lastactivedt,
                                 last_paid_time, vipflag, createipaddress, lastipaddress,
                                 order_cycle_end, subdatetime, topsessionkey, toprefreshkey,
                                 roleid, isdefault, lastupdatetime, logincount_pc,
                                 logincount_mp, logincount_ww, mau_pc, mau_mp, mau_ww,
                                 lastactiveplatform, lastactivever, is_many,
                                 is_main, corp_id, remark, is_silent, is_needauth, w1_deadline,
                                 w2_deadline, revival_date, seller_base_region)
        values (#{userProductinfo.userId}, #{userProductinfo.userIdStr}, #{userProductinfo.nick},
                #{userProductinfo.createDate}, #{userProductinfo.lastactivedt}, #{userProductinfo.lastPaidTime},
                #{userProductinfo.vipflag},
                #{userProductinfo.createipaddress}, #{userProductinfo.lastipaddress}, #{userProductinfo.orderCycleEnd},
                #{userProductinfo.subdatetime}, #{userProductinfo.topsessionkey},
                #{userProductinfo.toprefreshkey}, #{userProductinfo.roleid}, #{userProductinfo.isdefault},
                #{userProductinfo.lastupdatetime}, #{userProductinfo.logincountPc},
                #{userProductinfo.logincountMp}, #{userProductinfo.logincountWw}, #{userProductinfo.mauPc},
                #{userProductinfo.mauMp}, #{userProductinfo.mauWw}, #{userProductinfo.lastactiveplatform},
                #{userProductinfo.lastactivever}, #{userProductinfo.isMany}, #{userProductinfo.isMain},
                #{userProductinfo.corpId}, #{userProductinfo.mark}, #{userProductinfo.isSilent},
                #{userProductinfo.isNeedauth},
                #{userProductinfo.w1Deadline}, #{userProductinfo.w2Deadline}, #{userProductinfo.revivalDate},
                #{userProductinfo.mallName} #(userProductinfo.sellerBaseRegion))
    </insert>

</mapper>
