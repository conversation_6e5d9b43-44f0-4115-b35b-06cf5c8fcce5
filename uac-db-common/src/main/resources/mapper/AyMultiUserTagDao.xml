<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.uac.db.common.dao.dream.AyMultiUserTagDao">

    <resultMap type="cn.loveapp.uac.db.common.entity.AyMultiUserTag" id="AyMultiUserTagMap">
        <result property="id" column="id"/>
        <result property="sellerId" column="seller_id"/>
        <result property="storeId" column="store_id"/>
        <result property="appName" column="app_name"/>
        <result property="tags" column="tags"/>
        <result property="type" column="type"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <sql id="tableName">
        lacrm.ay_multi_user_tag
    </sql>

    <sql id="fileds">
        id, seller_id, store_id, app_name, tags, type, gmt_create, gmt_modified
    </sql>

    <!--新增所有列-->
    <insert id="insertOrUpdate" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tableName"/>
        (seller_id, store_id, app_name, tags, type)
        VALUES (#{sellerId}, #{storeId}, #{appName}, #{tags}, #{type})
        ON DUPLICATE KEY UPDATE
        seller_id = VALUES(seller_id), store_id = VALUES(store_id), app_name = VALUES(app_name), tags = VALUES(tags), type = VALUES(type);
    </insert>

    <insert id="batchInsertOrUpdate" parameterType="list">
        INSERT INTO
        <include refid="tableName"/>
        (seller_id, store_id, app_name, tags, type)
        VALUES
        <foreach collection="ayMultiUserTagList" item="item" separator=",">
            (#{item.sellerId}, #{item.storeId}, #{item.appName}, #{item.tags}, #{item.type})
        </foreach>
        ON DUPLICATE KEY UPDATE
        seller_id = VALUES(seller_id), store_id = VALUES(store_id), app_name = VALUES(app_name), tags = VALUES(tags), type = VALUES(type);
    </insert>


    <select id="queryTag" resultMap="AyMultiUserTagMap">
        select
        <include refid="fileds"/>
        from
        <include refid="tableName"/>
        where seller_id = #{sellerId}
        and store_id = #{storeId}
        and app_name = #{appName}
        limit 1
    </select>

    <select id="queryTagByType" resultMap="AyMultiUserTagMap">
        select
        <include refid="fileds"/>
        from
        <include refid="tableName"/>
        where seller_id = #{sellerId}
        and store_id = #{storeId}
        and app_name = #{appName}
        and type = #{type}
        limit 1
    </select>

</mapper>
