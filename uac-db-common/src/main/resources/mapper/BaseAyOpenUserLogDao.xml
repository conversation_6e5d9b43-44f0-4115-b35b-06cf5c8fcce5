<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.uac.db.common.dao.dream.BaseAyBusinessOpenUserLogDao">

    <resultMap type="cn.loveapp.uac.db.common.entity.AyBusinessOpenUserLog" id="AyBusinessOpenUserLogMap">
        <!-- <result property="id" column="id" jdbcType="INTEGER"/> -->
        <result property="id" column="id"/>
        <!-- <result property="openUserId" column="open_user_id" jdbcType="INTEGER"/> -->
        <result property="openUserId" column="open_user_id"/>
        <!-- <result property="sellerNick" column="seller_nick" jdbcType="VARCHAR"/> -->
        <result property="sellerNick" column="seller_nick"/>
        <!-- <result property="status" column="status" jdbcType="INTEGER"/> -->
        <result property="status" column="status"/>
        <!-- <result property="type" column="type" jdbcType="INTEGER"/> -->
        <result property="type" column="type"/>
        <!-- <result property="rule" column="rule" jdbcType="INTEGER"/> -->
        <result property="ruleId" column="rule_id"/>
        <!-- <result property="reason" column="reason" jdbcType="VARCHAR"/> -->
        <result property="reason" column="reason"/>
        <!-- <result property="remark" column="remark" jdbcType="VARCHAR"/> -->
        <result property="remark" column="remark"/>
        <!-- <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/> -->
        <result property="gmtCreate" column="gmt_create"/>
        <!-- <result property="gmtModify" column="gmt_modify" jdbcType="TIMESTAMP"/> -->
        <result property="gmtModify" column="gmt_modify"/>
        <!-- <result property="appName" column="app_name" jdbcType="INTEGER"/> -->
        <result property="appName" column="app_name"/>
    </resultMap>

    <sql id="fields">
        id, open_user_id, seller_nick, status, type, rule_id, reason, remark, gmt_create, gmt_modify,app_name
    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="AyBusinessOpenUserLogMap">
        select <include refid="fields"/>
        from ${tableName}
        where id = #{id}
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="ayBusinessOpenUserLog.id" useGeneratedKeys="true">
        insert into ${tableName} (open_user_id, seller_nick, status, type, rule_id, reason, remark, gmt_create, gmt_modify,app_name)
        values (#{ayBusinessOpenUserLog.openUserId}, #{ayBusinessOpenUserLog.sellerNick}, #{ayBusinessOpenUserLog.status},
                #{ayBusinessOpenUserLog.type}, #{ayBusinessOpenUserLog.ruleId}, #{ayBusinessOpenUserLog.reason}, #{ayBusinessOpenUserLog.remark},
                #{ayBusinessOpenUserLog.gmtCreate}, #{ayBusinessOpenUserLog.gmtModify},#{ayBusinessOpenUserLog.appName})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update ${tableName}
        <set>
            <if test="ayBusinessOpenUserLog.openUserId != null">
                open_user_id = #{ayBusinessOpenUserLog.openUserId},
            </if>
            <if test="ayBusinessOpenUserLog.sellerNick != null and ayBusinessOpenUserLog.sellerNick != ''">
                seller_nick = #{ayBusinessOpenUserLog.sellerNick},
            </if>
            <if test="ayBusinessOpenUserLog.status != null">
                status = #{ayBusinessOpenUserLog.status},
            </if>
            <if test="ayBusinessOpenUserLog.type != null">
                type = #{ayBusinessOpenUserLog.type},
            </if>
            <if test="ayBusinessOpenUserLog.ruleId != null">
                rule_id = #{ayBusinessOpenUserLog.ruleId},
            </if>
            <if test="ayBusinessOpenUserLog.reason != null and ayBusinessOpenUserLog.reason != ''">
                reason = #{ayBusinessOpenUserLog.reason},
            </if>
            <if test="ayBusinessOpenUserLog.remark != null and ayBusinessOpenUserLog.remark != ''">
                remark = #{ayBusinessOpenUserLog.remark},
            </if>
            <if test="ayBusinessOpenUserLog.gmtCreate != null">
                gmt_create = #{ayBusinessOpenUserLog.gmtCreate},
            </if>
            <if test="ayBusinessOpenUserLog.gmtModify != null">
                gmt_modify = #{ayBusinessOpenUserLog.gmtModify},
            </if>
        </set>
        where id = #{ayBusinessOpenUserLog.id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from ${tableName} where id = #{id}
    </delete>

</mapper>
