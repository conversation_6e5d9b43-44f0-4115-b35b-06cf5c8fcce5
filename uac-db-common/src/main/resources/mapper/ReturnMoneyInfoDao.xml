<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.uac.db.common.dao.dream.ReturnMoneyInfoDao">

    <resultMap type="cn.loveapp.uac.db.common.entity.ReturnMoneyInfo" id="ReturnMoneyInfoMap">
        <result property="id" column="id"/>
        <result property="userNick" column="user_nick"/>
        <result property="userAlipayRealName" column="user_alipay_real_name"/>
        <result property="userAlipayAccount" column="user_alipay_account"/>
        <result property="platform" column="platform"/>
        <result property="returnMoney" column="return_money"/>
        <result property="projectCode" column="project_code"/>
        <result property="pushedThird" column="pushed_third"/>
        <result property="thirdOrderId" column="third_order_id"/>
        <result property="thirdStatus" column="third_status"/>
        <result property="thirdMsg" column="third_msg"/>
        <result property="manualOperate" column="manual_operate"/>
        <result property="operateUser" column="operate_user"/>
        <result property="registerTime" column="register_time"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ReturnMoneyInfoMap">
        select
          id, user_nick, user_alipay_real_name, user_alipay_account, platform, return_money, project_code, pushed_third, third_order_id, third_status, third_msg, manual_operate, operate_user, register_time, gmt_create, gmt_modified
        from lacrm.return_money_info
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="ReturnMoneyInfoMap">
        select
          id, user_nick, user_alipay_real_name, user_alipay_account, platform, return_money, project_code, pushed_third, third_order_id, third_status, third_msg, manual_operate, operate_user, register_time, gmt_create, gmt_modified
        from lacrm.return_money_info
        limit #{offset}, #{limit}
    </select>

	<!--查询指定行数据-->
	<select id="countByProjectCodeAndSellerNick" resultType="java.lang.Integer">
        select count(*) as count from lacrm.return_money_info where project_code = #{projectCode} and user_nick = #{sellerNick}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="ReturnMoneyInfoMap">
        select
          id, user_nick, user_alipay_real_name, user_alipay_account, platform, return_money, project_code, pushed_third, third_order_id, third_status, third_msg, manual_operate, operate_user, register_time, gmt_create, gmt_modified
        from lacrm.return_money_info
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userNick != null and userNick != ''">
                and user_nick = #{userNick}
            </if>
            <if test="userAlipayRealName != null and userAlipayRealName != ''">
                and user_alipay_real_name = #{userAlipayRealName}
            </if>
            <if test="userAlipayAccount != null and userAlipayAccount != ''">
                and user_alipay_account = #{userAlipayAccount}
            </if>
            <if test="platform != null and platform != ''">
                and platform = #{platform}
            </if>
            <if test="returnMoney != null">
                and return_money = #{returnMoney}
            </if>
            <if test="projectCode != null and projectCode != ''">
                and project_code = #{projectCode}
            </if>
            <if test="pushedThird != null">
                and pushed_third = #{pushedThird}
            </if>
            <if test="thirdOrderId != null and thirdOrderId != ''">
                and third_order_id = #{thirdOrderId}
            </if>
            <if test="thirdStatus != null">
                and third_status = #{thirdStatus}
            </if>
            <if test="thirdMsg != null and thirdMsg != ''">
                and third_msg = #{thirdMsg}
            </if>
            <if test="manualOperate != null">
                and manual_operate = #{manualOperate}
            </if>
            <if test="operateUser != null and operateUser != ''">
                and operate_user = #{operateUser}
            </if>
            <if test="registerTime != null">
                and register_time = #{registerTime}
            </if>
            <if test="gmtCreate != null">
                and gmt_create = #{gmtCreate}
            </if>
            <if test="gmtModified != null">
                and gmt_modified = #{gmtModified}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into lacrm.return_money_info(user_nick, user_alipay_real_name, user_alipay_account, platform, return_money, project_code, register_time, gmt_create, gmt_modified)
        values (#{userNick}, #{userAlipayRealName}, #{userAlipayAccount}, #{platform}, #{returnMoney}, #{projectCode}, #{registerTime}, #{gmtCreate}, #{gmtModified})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update lacrm.return_money_info
        <set>
            <if test="userNick != null and userNick != ''">
                user_nick = #{userNick},
            </if>
            <if test="userAlipayRealName != null and userAlipayRealName != ''">
                user_alipay_real_name = #{userAlipayRealName},
            </if>
            <if test="userAlipayAccount != null and userAlipayAccount != ''">
                user_alipay_account = #{userAlipayAccount},
            </if>
            <if test="platform != null and platform != ''">
                platform = #{platform},
            </if>
            <if test="returnMoney != null">
                return_money = #{returnMoney},
            </if>
            <if test="projectCode != null and projectCode != ''">
                project_code = #{projectCode},
            </if>
            <if test="pushedThird != null">
                pushed_third = #{pushedThird},
            </if>
            <if test="thirdOrderId != null and thirdOrderId != ''">
                third_order_id = #{thirdOrderId},
            </if>
            <if test="thirdStatus != null">
                third_status = #{thirdStatus},
            </if>
            <if test="thirdMsg != null and thirdMsg != ''">
                third_msg = #{thirdMsg},
            </if>
            <if test="manualOperate != null">
                manual_operate = #{manualOperate},
            </if>
            <if test="operateUser != null and operateUser != ''">
                operate_user = #{operateUser},
            </if>
            <if test="registerTime != null">
                register_time = #{registerTime},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from lacrm.return_money_info where id = #{id}
    </delete>

</mapper>
