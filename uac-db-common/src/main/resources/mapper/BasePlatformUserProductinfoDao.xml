<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.uac.db.common.dao.dream.BasePlatformUserProductinfoDao">

    <resultMap type="cn.loveapp.uac.common.entity.UserProductInfo" id="UserProductinfoMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="nick" column="nick"/>
        <result property="createDate" column="createdate"/>
        <result property="lastactivedt" column="lastactivedt"/>
        <result property="lastPaidTime" column="last_paid_time"/>
        <result property="vipflag" column="vipflag"/>
        <result property="createipaddress" column="createipaddress"/>
        <result property="lastipaddress" column="lastipaddress"/>
        <result property="orderCycleEnd" column="order_cycle_end"/>
        <result property="subdatetime" column="subdatetime"/>
        <result property="topsessionkey" column="topsessionkey"/>
        <result property="toprefreshkey" column="toprefreshkey"/>
        <result property="roleid" column="roleid"/>
        <result property="isdefault" column="isdefault"/>
        <result property="lastupdatetime" column="lastupdatetime"/>
        <result property="logincountPc" column="logincount_pc"/>
        <result property="logincountMp" column="logincount_mp"/>
        <result property="mauPc" column="mau_pc"/>
        <result property="mauMp" column="mau_mp"/>
        <result property="lastactiveplatform" column="lastactiveplatform"/>
        <result property="lastactivever" column="lastactivever"/>
        <result property="isMany" column="is_many"/>
        <result property="isMain" column="is_main"/>
        <result property="corpId" column="corp_id"/>
        <result property="mark" column="mark"/>
        <result property="isSilent" column="is_silent"/>
        <result property="isNeedauth" column="is_needauth"/>
        <result property="w1Deadline" column="w1_deadline"/>
        <result property="w2Deadline" column="w2_deadline"/>
        <result property="revivalDate" column="revival_date"/>

        <result property="userIdStr" column="user_id_str"/>
        <result property="mallName" column="mall_name"/>
        <result property="mallName" column="shop_name"/>
        <result property="mallName" column="user_name"/>
        <result property="logincountWw" column="logincount_ww"/>
        <result property="mauWw" column="mau_ww"/>
        <result property="otherRoleId" column="otherroleid"/>
        <result property="memberId" column="memberid"/>
        <result property="roleid" column="1688roleid"/>
        <result property="mark" column="remark"/>

        <result property="shopName" column="shop_name"/>
        <result property="appId" column="app_id"/>
        <result property="appSecret" column="app_secret"/>

        <result property="topsessionkey" column="aopsessionkey"/>
        <result property="toprefreshkey" column="aoprefreshkey"/>
        <result property="professionalOrderCycleEnd" column="professional_order_cycle_end"/>
        <result property="isAuthExcept" column="is_auth_except"/>
    </resultMap>

	<sql id="field">
        id, user_id, nick, createdate, lastactivedt, last_paid_time, vipflag, createipaddress, lastipaddress, order_cycle_end, subdatetime, topsessionkey, toprefreshkey, isdefault, lastupdatetime, logincount_mp, logincount_ww, mau_pc, mau_mp, lastactiveplatform, lastactivever, is_many, is_main, corp_id, is_silent, is_needauth, w1_deadline, w2_deadline, revival_date
        <if test="extensionFields != null and extensionFields != ''">
            , ${extensionFields}
        </if>
	</sql>

    <!--查询单个-->
    <select id="queryByUserId" resultMap="UserProductinfoMap">
        select
        <include refid="field"/>
        from ${tableName}
        where user_id = #{sellerId} order by id desc limit 1
    </select>

    <!--查询单个-->
    <select id="queryByUserIdStr" resultMap="UserProductinfoMap">
        select
        <include refid="field"/>
        from ${tableName}
        where user_id_str = #{sellerId} order by id desc limit 1
    </select>

    <select id="queryByMemberId" resultMap="UserProductinfoMap">
        select * from ${tableName}
        where memberid = #{memberId} limit 1
    </select>

    <select id="queryByAppId" resultMap="UserProductinfoMap">
        select * from ${tableName}
        where app_id = #{appId} limit 1
    </select>

	<!--查询单个-->
	<select id="queryBySellerNick" resultMap="UserProductinfoMap">
		select
		<include refid="field"/>
		from ${tableName}
        where nick = #{sellerNick} limit 1
    </select>

    <select id="queryByShopName" resultMap="UserProductinfoMap">
        select
        <include refid="field"/>
        from  ${tableName}
        where shop_name = #{shopName} limit 1
    </select>

	<select id="queryUserProductInfoListBySellerNickCollection" resultMap="UserProductinfoMap">
		select
		<include refid="field"/>
		from ${tableName}
        where nick in
		<foreach item="item" index="index" collection="sellerNicks" open="(" separator="," close=")">
			#{item}
		</foreach>
    </select>

    <select id="queryByLevel" resultMap="UserProductinfoMap">
        select
        <include refid="field"/>
        from ${tableName} where vipflag = #{vipflag} and id > #{maxId} and is_needauth = #{isNeedAuth}
        order by id limit #{offset}, #{limit}
    </select>

    <select id="queryByLevelAndW1DeadLine" resultMap="UserProductinfoMap">
        select
        <include refid="field"/>
        from ${tableName} where vipflag = #{vipflag} and w1_deadline = #{authDeadLine} and id > #{maxId} order by id
        limit #{offset}, #{limit}
    </select>

    <select id="queryByW1DeadLineAndOrderCycleEnd" resultMap="UserProductinfoMap">
        select
        <include refid="field"/>
        from ${tableName} where vipflag > 0 and w1_deadline >= #{w1DeadLineStart} and w1_deadline &lt;= #{w1DeadLineEnd} and order_cycle_end > #{orderCycleEnd} and id > #{maxId} order by id
        limit #{offset}, #{limit}
    </select>

    <select id="queryNickListByVipFlagList" resultMap="UserProductinfoMap">
        select id,nick,user_id
        from ${tableName} force index(IDX_VIPFLAG)
        where id > #{maxId} and vipflag in
        <foreach item="item" index="index" collection="vipFlagList" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by id asc
        limit #{limit}
    </select>

    <select id="queryNickListByProfessionalOrderCycleEnd"
            resultMap="UserProductinfoMap">
        select id, nick, user_id, professional_order_cycle_end
        from ${tableName} force index(IDX_PROFESSIONAL_ORDER_CYCLE_END)
        where id > #{maxId} and professional_order_cycle_end > #{professionalOrderCycleEnd}
        order by id asc
        limit #{limit}
    </select>

    <update id="updateBatchLevelBySellerNickCollection">
		update ${tableName} set vipflag = #{level}  where nick in
		<foreach item="item" index="index" collection="sellerNicks" open="(" separator="," close=")">
			#{item}
		</foreach>
	</update>

  <!--新增所有列-->
    <insert id="insert" keyProperty="userProductinfo.id" useGeneratedKeys="true">
        insert into ${tableName}(user_id, nick, createdate, lastactivedt, last_paid_time, vipflag, createipaddress, lastipaddress, order_cycle_end, subdatetime, topsessionkey, toprefreshkey, isdefault, lastupdatetime, logincount_pc, logincount_mp, logincount_ww, mau_pc, mau_mp, mau_ww, lastactiveplatform, lastactivever, db, is_many, is_main, corp_id, mark, is_silent, is_needauth, w1_deadline, w2_deadline, revival_date)
        values (#{userProductinfo.userId}, #{userProductinfo.nick}, #{userProductinfo.createDate}, #{userProductinfo.lastactivedt}, #{userProductinfo.lastPaidTime}, #{userProductinfo.vipflag}, #{userProductinfo.createipaddress}, #{userProductinfo.lastipaddress}, #{userProductinfo.orderCycleEnd}, #{userProductinfo.subdatetime}, #{userProductinfo.topsessionkey}, #{userProductinfo.toprefreshkey}, #{userProductinfo.roleid}, #{userProductinfo.isdefault}, #{userProductinfo.lastupdatetime}, #{userProductinfo.logincountPc}, #{userProductinfo.logincountMp}, #{userProductinfo.logincountWw}, #{userProductinfo.mauPc}, #{userProductinfo.mauMp}, #{userProductinfo.mauWw}, #{userProductinfo.lastactiveplatform}, #{userProductinfo.lastactivever}, #{userProductinfo.db}, #{userProductinfo.isMany}, #{userProductinfo.isMain}, #{userProductinfo.corpId}, #{userProductinfo.mark}, #{userProductinfo.isSilent}, #{userProductinfo.isNeedauth}, #{userProductinfo.w1Deadline}, #{userProductinfo.w2Deadline}, #{userProductinfo.revivalDate})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update ${tableName}
		<trim prefix="SET" suffixOverrides=",">
            <if test="userProductinfo.createDate != null">
                createdate = #{userProductinfo.createDate},
            </if>
            <if test="userProductinfo.lastactivedt != null">
                lastactivedt = #{userProductinfo.lastactivedt},
            </if>
            <if test="userProductinfo.lastPaidTime != null">
                last_paid_time = #{userProductinfo.lastPaidTime},
            </if>
            <if test="userProductinfo.vipflag != null">
                vipflag = #{userProductinfo.vipflag},
            </if>
            <if test="userProductinfo.createipaddress != null and userProductinfo.createipaddress != ''">
                createipaddress = #{userProductinfo.createipaddress},
            </if>
            <if test="userProductinfo.lastipaddress != null and userProductinfo.lastipaddress != ''">
                lastipaddress = #{userProductinfo.lastipaddress},
            </if>
            <if test="userProductinfo.orderCycleEnd != null">
                order_cycle_end = #{userProductinfo.orderCycleEnd},
            </if>
            <if test="userProductinfo.subdatetime != null">
                subdatetime = #{userProductinfo.subdatetime},
            </if>
            <if test="userProductinfo.topsessionkey != null and userProductinfo.topsessionkey != ''">
                <choose>
                    <when test="isDistribute1688 == true">
                        aopsessionkey = #{userProductinfo.topsessionkey},
                    </when>
                    <otherwise>
                        topsessionkey = #{userProductinfo.topsessionkey},
                    </otherwise>
                </choose>
            </if>
            <if test="userProductinfo.toprefreshkey != null and userProductinfo.toprefreshkey != ''">
                <choose>
                    <when test="isDistribute1688 == true">
                        aoprefreshkey = #{userProductinfo.toprefreshkey},
                    </when>
                    <otherwise>
                        toprefreshkey = #{userProductinfo.toprefreshkey},
                    </otherwise>
                </choose>
            </if>
            <if test="userProductinfo.roleid != null and userProductinfo.roleid != ''">
                roleid = #{userProductinfo.roleid},
            </if>
            <if test="userProductinfo.isdefault != null">
                isdefault = #{userProductinfo.isdefault},
            </if>
            <if test="userProductinfo.lastupdatetime != null">
                lastupdatetime = #{userProductinfo.lastupdatetime},
            </if>
            <if test="userProductinfo.logincountPc != null">
                logincount_pc = #{userProductinfo.logincountPc},
            </if>
            <if test="userProductinfo.logincountMp != null">
                logincount_mp = #{userProductinfo.logincountMp},
            </if>
            <if test="userProductinfo.mauPc != null">
                mau_pc = #{userProductinfo.mauPc},
            </if>
            <if test="userProductinfo.mauMp != null">
                mau_mp = #{userProductinfo.mauMp},
            </if>
            <if test="userProductinfo.lastactiveplatform != null and userProductinfo.lastactiveplatform != ''">
                lastactiveplatform = #{userProductinfo.lastactiveplatform},
            </if>
            <if test="userProductinfo.lastactivever != null and userProductinfo.lastactivever != ''">
                lastactivever = #{userProductinfo.lastactivever},
            </if>
            <if test="userProductinfo.isMany != null">
                is_many = #{userProductinfo.isMany},
            </if>
            <if test="userProductinfo.isMain != null">
                is_main = #{userProductinfo.isMain},
            </if>
            <if test="userProductinfo.corpId != null and userProductinfo.corpId != ''">
                corp_id = #{userProductinfo.corpId},
            </if>
            <if test="userProductinfo.mark != null and userProductinfo.mark != ''">
                mark = #{userProductinfo.mark},
            </if>
            <if test="userProductinfo.isSilent != null">
                is_silent = #{userProductinfo.isSilent},
            </if>
            <if test="userProductinfo.isNeedauth != null">
                is_needauth = #{userProductinfo.isNeedauth},
            </if>
            <if test="userProductinfo.w1Deadline != null">
                w1_deadline = #{userProductinfo.w1Deadline},
            </if>
            <if test="userProductinfo.w2Deadline != null">
                w2_deadline = #{userProductinfo.w2Deadline},
            </if>
            <if test="userProductinfo.revivalDate != null">
                revival_date = #{userProductinfo.revivalDate},
            </if>
            <if test="userProductinfo.mallName != null and userProductinfo.mallName != ''">
                mall_name = #{userProductinfo.mallName},
            </if>
            <if test="userProductinfo.logincountWw != null">
                logincount_ww = #{userProductinfo.logincountWw},
            </if>
            <if test="userProductinfo.mauWw != null">
                mau_ww = #{userProductinfo.mauWw},
            </if>
            <if test="userProductinfo.professionalOrderCycleEnd != null">
                professional_order_cycle_end = #{userProductinfo.professionalOrderCycleEnd},
            </if>
        </trim>
        where
        <choose>
            <when test="useUserId == true">
                <choose>
                    <when test="isDistribute1688 == true">
                        user_id = #{userProductinfo.userId}
                    </when>
                    <otherwise>
                        user_id_str = #{userProductinfo.userIdStr}
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                nick = #{userProductinfo.nick}
            </otherwise>
        </choose>
    </update>

    <!--查询单个-->
    <select id="queryByMallName" resultMap="UserProductinfoMap">
        select
        <include refid="field"/>
        from ${tableName}
        where mall_name = #{mallName} limit 1
    </select>

    <!--分页查询w1_deadline在指定时间之前的用户数据-->
    <select id="queryByW1DeadlineBeforeWithPage" resultMap="UserProductinfoMap">
        select
        <include refid="field"/>
        from ${tableName}
        where w1_deadline &lt;= #{w1DeadlineBefore}
        <if test="lastW1Deadline != null">
            and w1_deadline &lt;= #{lastW1Deadline}
        </if>
        order by w1_deadline desc
        limit #{limit}
    </select>

</mapper>
