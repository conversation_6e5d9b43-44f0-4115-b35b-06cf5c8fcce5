<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.uac.db.common.dao.dream.PromotionActivityDao">

    <resultMap type="cn.loveapp.uac.common.entity.PromotionActivity" id="PromotionActivityMap">
        <result property="id" column="id"/>
        <result property="sellernick" column="sellernick"/>
        <result property="actCycle" column="act_cycle"/>
        <result property="optime" column="optime"/>
        <result property="actflag" column="actflag"/>
        <result property="sender" column="sender"/>
        <result property="promotionCode" column="promotion_code"/>
        <result property="isused" column="isused"/>
    </resultMap>

	<!--查询单个-->
	<select id="queryByIsUsedAndSellerNickSortOptime" resultMap="PromotionActivityMap">
        select
          id, sellernick, act_cycle, optime, actflag, sender, promotion_code, isused
        from ${tableName} WHERE sellernick=#{sellerNick} AND isused = #{unUsed} and act_cycle > 0 order by optime
    </select>

	<!--查询单个-->
	<select id="aggregationActCycleAndOptimeBySellerNickAndUnused" resultMap="PromotionActivityMap">
        SELECT sum(act_cycle) as act_cycle, min(optime) as optime, actflag as actflag FROM ${tableName} WHERE sellernick=#{sellerNick} AND isused = #{unUsed} and act_cycle > 0 GROUP BY actflag
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="promotionActivity.id" useGeneratedKeys="true">
        insert into ${tableName}(sellernick, act_cycle, optime, actflag, sender, promotion_code, isused)
        values (#{promotionActivity.sellernick}, #{promotionActivity.actCycle}, #{promotionActivity.optime}, #{promotionActivity.actflag}, #{promotionActivity.sender}, #{promotionActivity.promotionCode}, #{promotionActivity.isused})
    </insert>

    <!--批量新增所有列-->
    <insert id="insertBatch">
        insert into ${tableName}(sellernick, act_cycle, optime, actflag, sender, promotion_code, isused)
        values
        <foreach item="promotionActivity" index="index" collection="promotionActivityList" separator=",">
             (#{promotionActivity.sellernick}, #{promotionActivity.actCycle}, #{promotionActivity.optime}, #{promotionActivity.actflag}, #{promotionActivity.sender}, #{promotionActivity.promotionCode}, #{promotionActivity.isused})
        </foreach>
    </insert>

	<update id="updateIsUsedOrActCycleByPkIds">
		update ${tableName}
		<trim prefix="SET" suffixOverrides=",">
			<if test="isused != null">isused = #{isused},</if>
			<if test="actCycle != null">
				act_cycle = #{actCycle},
			</if>
		</trim>
		 where id in
		<foreach item="item" index="index" collection="pkIds" open="(" separator="," close=")">
			#{item}
		</foreach>
	</update>

	<update id="updateIsUsedBySellerNick">
		update ${tableName} set
			isused = #{isused}
		where sellerNick = #{sellerNick}
	</update>

</mapper>
