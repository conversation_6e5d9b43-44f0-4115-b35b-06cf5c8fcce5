<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.uac.db.common.dao.dream.AyTradeShopsDao">

    <resultMap type="cn.loveapp.uac.db.common.entity.AyTradeShops" id="AyTradeShopsMap">
        <result property="id" column="id"/>
        <result property="shopsId" column="shops_id"/>
        <result property="sellerNick" column="seller_nick"/>
        <result property="role" column="role"/>
        <result property="settings" column="settings"/>
        <result property="storeId" column="store_id"/>
        <result property="createdTime" column="created_time"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
    </resultMap>

	<sql id="tablename">
		lacrm.ay_trade_shops
	</sql>

	<sql id="fileds">
		id, shops_id, seller_nick, role, settings, store_id, created_time, gmt_create, gmt_modified
	</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="AyTradeShopsMap">
        select
          <include refid="fileds"/>
        from <include refid="tablename"/>
        where id = #{id}
    </select>

	<!--查询单个-->
	<select id="queryBySellerNick" resultMap="AyTradeShopsMap">
		select
		<include refid="fileds"/>
		from <include refid="tablename"/>
		where seller_nick = #{sellerNick}
	</select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="AyTradeShopsMap">
		select
		<include refid="fileds"/>
		from <include refid="tablename"/>
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="AyTradeShopsMap">
		select
		<include refid="fileds"/>
		from <include refid="tablename"/>
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="shopsId != null and shopsId != ''">
                and shops_id = #{shopsId}
            </if>
            <if test="sellerNick != null and sellerNick != ''">
                and seller_nick = #{sellerNick}
            </if>
            <if test="role != null">
                and role = #{role}
            </if>
            <if test="settings != null and settings != ''">
                and settings = #{settings}
            </if>
            <if test="storeId != null and storeId != ''">
                and store_id = #{storeId}
            </if>
            <if test="createdTime != null">
                and created_time = #{createdTime}
            </if>
            <if test="gmtCreate != null">
                and gmt_create = #{gmtCreate}
            </if>
            <if test="gmtModified != null">
                and gmt_modified = #{gmtModified}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into lacrm.ay_trade_shops(shops_id, seller_nick, role, settings, store_id, created_time, gmt_create, gmt_modified)
        values (#{shopsId}, #{sellerNick}, #{role}, #{settings}, #{storeId}, #{createdTime}, #{gmtCreate}, #{gmtModified})
    </insert>

	<insert id="batchInsert" keyProperty="id" useGeneratedKeys="true">
        insert into lacrm.ay_trade_shops(shops_id, seller_nick, role, settings, store_id, created_time, gmt_create, gmt_modified) values
		<foreach collection="tradeShops" item="element" index="index" open="(" separator="),("  close=")">
			#{element.shopsId}, #{element.sellerNick}, #{element.role}, #{element.settings}, #{element.storeId}, #{element.createdTime}, #{element.gmtCreate}, #{element.gmtModified}
		</foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update lacrm.ay_trade_shops
        <set>
            <if test="shopsId != null and shopsId != ''">
                shops_id = #{shopsId},
            </if>
            <if test="sellerNick != null and sellerNick != ''">
                seller_nick = #{sellerNick},
            </if>
            <if test="role != null">
                role = #{role},
            </if>
            <if test="settings != null and settings != ''">
                settings = #{settings},
            </if>
            <if test="storeId != null and storeId != ''">
                store_id = #{storeId},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from lacrm.ay_trade_shops where id = #{id}
    </delete>

</mapper>
