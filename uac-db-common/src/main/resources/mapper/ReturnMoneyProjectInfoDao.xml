<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.uac.db.common.dao.dream.ReturnMoneyProjectInfoDao">

    <resultMap type="cn.loveapp.uac.db.common.entity.ReturnMoneyProjectInfo" id="ReturnMoneyProjectInfoMap">
        <result property="id" column="id"/>
        <result property="projectCode" column="project_code"/>
        <result property="projectName" column="project_name"/>
        <result property="returnMoney" column="return_money"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ReturnMoneyProjectInfoMap">
        select
          id, project_code, project_name, return_money
        from lacrm.return_money_project_info
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="ReturnMoneyProjectInfoMap">
        select
          id, project_code, project_name, return_money
        from lacrm.return_money_project_info
        limit #{offset}, #{limit}
    </select>

	<!--查询指定行数据-->
	<select id="queryReturnMoneyProjectInfoByProjectCode" resultMap="ReturnMoneyProjectInfoMap">
        select
          id, project_code, project_name, return_money
        from lacrm.return_money_project_info where project_code = #{projectCode} limit 1
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="ReturnMoneyProjectInfoMap">
        select
          id, project_code, project_name, return_money
        from lacrm.return_money_project_info
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="projectCode != null and projectCode != ''">
                and project_code = #{projectCode}
            </if>
            <if test="projectName != null and projectName != ''">
                and project_name = #{projectName}
            </if>
            <if test="returnMoney != null">
                and return_money = #{returnMoney}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into lacrm.return_money_project_info(project_code, project_name, return_money)
        values (#{projectCode}, #{projectName}, #{returnMoney})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update lacrm.return_money_project_info
        <set>
            <if test="projectCode != null and projectCode != ''">
                project_code = #{projectCode},
            </if>
            <if test="projectName != null and projectName != ''">
                project_name = #{projectName},
            </if>
            <if test="returnMoney != null">
                return_money = #{returnMoney},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from lacrm.return_money_project_info where id = #{id}
    </delete>

</mapper>
