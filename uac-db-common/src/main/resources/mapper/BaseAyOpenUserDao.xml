<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.uac.db.common.dao.dream.BaseAyBusinessOpenUserDao">

    <resultMap type="cn.loveapp.uac.db.common.entity.AyBusinessOpenUser" id="AyBusinessOpenUserMap">
        <!-- <result property="id" column="id" jdbcType="INTEGER"/> -->
        <result property="id" column="id"/>
        <result property="sellerId" column="seller_id"/>
        <!-- <result property="sellerNick" column="seller_nick" jdbcType="VARCHAR"/> -->
        <result property="sellerNick" column="seller_nick"/>
        <!-- <result property="status" column="status" jdbcType="INTEGER"/> -->
        <result property="status" column="status"/>
        <!-- <result property="platId" column="plat_id" jdbcType="INTEGER"/> -->
        <result property="platId" column="plat_id"/>
        <!-- <result property="retryCount" column="retry_count" jdbcType="INTEGER"/> -->
        <result property="retryCount" column="retry_count"/>
        <!-- <result property="ruleId" column="rule_id" jdbcType="INTEGER"/> -->
        <result property="ruleId" column="rule_id"/>
        <!-- <result property="remark" column="remark" jdbcType="VARCHAR"/> -->
        <result property="remark" column="remark"/>
        <!-- <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/> -->
        <result property="gmtCreate" column="gmt_create"/>
        <!-- <result property="gmtModify" column="gmt_modify" jdbcType="TIMESTAMP"/> -->
        <result property="gmtModify" column="gmt_modify"/>
        <!-- <result property="userLogId" column="user_log_id" jdbcType="INTEGER"/> -->
        <result property="userLogId" column="user_log_id"/>
        <!-- <result property="appName" column="app_name" jdbcType="VARCHAR"/> -->
        <result property="appName" column="app_name"/>
    </resultMap>

    <sql id="fields">
        id, seller_id, seller_nick, status, plat_id, retry_count, rule_id, remark, gmt_create, gmt_modify, user_log_id,app_name
    </sql>

    <!--通过主键修改数据-->
    <update id="updateByStatus">
        update ${tableName}
        <set>
            <if test="ayBusinessOpenUser.status != null">
                status = #{ayBusinessOpenUser.status},
            </if>
            <if test="ayBusinessOpenUser.userLogId != null">
                user_log_id = #{ayBusinessOpenUser.userLogId},
            </if>
            <if test="ayBusinessOpenUser.gmtModify != null">
                gmt_modify = #{ayBusinessOpenUser.gmtModify},
            </if>
        </set>
        where id = #{ayBusinessOpenUser.id}
    </update>

    <select id="queryBySellerIdAndPlatId" resultMap="AyBusinessOpenUserMap">
        select <include refid="fields"/>
        from ${tableName}
        where seller_id = #{sellerId} and plat_id = #{platId}
        <if test="appName != null">
            and app_name = #{appName}
        </if>
        <if test="appName == null">
            and app_name = ''
        </if>
        limit 1
    </select>

    <insert id="insert" keyProperty="ayBusinessOpenUser.id" keyColumn="id" useGeneratedKeys="true">
        insert into ${tableName} (seller_id, seller_nick, status, plat_id,retry_count, rule_id,gmt_create,gmt_modify,user_log_id,app_name)
        values (#{ayBusinessOpenUser.sellerId}, #{ayBusinessOpenUser.sellerNick}, #{ayBusinessOpenUser.status}, #{ayBusinessOpenUser.platId},#{ayBusinessOpenUser.retryCount},
                #{ayBusinessOpenUser.ruleId}, #{ayBusinessOpenUser.gmtCreate}, #{ayBusinessOpenUser.gmtModify}, #{ayBusinessOpenUser.userLogId},
                #{ayBusinessOpenUser.appName})
    </insert>

    <update id="updateByStatusAndRetryCount">
        update ${tableName}
        <set>
            <if test="ayBusinessOpenUser.status != null">
                status = #{ayBusinessOpenUser.status},
            </if>
            <if test="ayBusinessOpenUser.gmtModify != null">
                gmt_modify = #{ayBusinessOpenUser.gmtModify},
            </if>
            <if test="ayBusinessOpenUser.retryCount != null">
                retry_count = #{ayBusinessOpenUser.retryCount}
            </if>
        </set>
        where id = #{ayBusinessOpenUser.id}
    </update>

    <select id="queryByStatus" resultMap="AyBusinessOpenUserMap" >
        select <include refid="fields"/>
        from ${tableName}
        where status in
        <foreach collection="status" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and id > #{maxId}  order by id limit #{offset}, #{limit}
    </select>

    <select id="queryByStatusAndModify" resultMap="AyBusinessOpenUserMap" >
        select <include refid="fields"/>
        from ${tableName}
        where status = #{status} and gmt_modify &lt; #{gmtModify}
    </select>

    <select id="queryById" resultMap="AyBusinessOpenUserMap">
        select <include refid="fields"/>
        from ${tableName}
        where id = #{id}
    </select>
</mapper>
