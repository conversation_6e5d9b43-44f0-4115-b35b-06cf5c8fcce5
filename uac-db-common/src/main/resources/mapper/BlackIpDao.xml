<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.uac.db.common.dao.dream.BlackIpDao">

    <resultMap type="cn.loveapp.uac.db.common.entity.BlackIp" id="BlackIpMap">
        <result property="id" column="id"/>
        <result property="ipaddress" column="ipaddress"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="BlackIpMap">
        select
          id, ipaddress
        from lacrm.black_ip
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="BlackIpMap">
        select
          id, ipaddress
        from lacrm.black_ip
        limit #{offset}, #{limit}
    </select>

	<!--查询指定行数据-->
	<select id="queryBlackIpByIpaddress" resultMap="BlackIpMap">
        select
          id, ipaddress
        from lacrm.black_ip where ipaddress = #{ipaddress} limit 1
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="BlackIpMap">
        select
          id, ipaddress
        from lacrm.black_ip
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="ipaddress != null and ipaddress != ''">
                and ipaddress = #{ipaddress}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into lacrm.black_ip(ipaddress)
        values (#{ipaddress})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update lacrm.black_ip
        <set>
            <if test="ipaddress != null and ipaddress != ''">
                ipaddress = #{ipaddress},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from lacrm.black_ip where id = #{id}
    </delete>

</mapper>
