<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.uac.db.common.dao.dream.UserTaobaoSellerinfoDao">

    <resultMap type="cn.loveapp.uac.db.common.entity.UserTaobaoSellerinfo" id="UserTaobaoSellerinfoMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="nick" column="nick"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="address" column="address"/>
        <result property="sex" column="sex"/>
        <result property="sellerCreditLevel" column="seller_credit_level"/>
        <result property="sellerCreditScore" column="seller_credit_score"/>
        <result property="sellerCreditTotalNum" column="seller_credit_total_num"/>
        <result property="sellerCreditGoodNum" column="seller_credit_good_num"/>
        <result property="type" column="type"/>
        <result property="status" column="status"/>
        <result property="isGoldenSeller" column="is_golden_seller"/>
        <result property="createdate" column="createdate"/>
        <result property="lastactivedt" column="lastactivedt"/>
        <result property="lastupdatetime" column="lastupdatetime"/>
        <result property="refundPhone" column="refund_phone"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="UserTaobaoSellerinfoMap">
        select
          id, user_id, nick, phone, email, province, city, address, sex, seller_credit_level, seller_credit_score, seller_credit_total_num, seller_credit_good_num, type, status, is_golden_seller, createdate, lastactivedt, lastupdatetime, refund_phone
        from lacrm.user_taobao_sellerinfo
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="UserTaobaoSellerinfoMap">
        select
          id, user_id, nick, phone, email, province, city, address, sex, seller_credit_level, seller_credit_score, seller_credit_total_num, seller_credit_good_num, type, status, is_golden_seller, createdate, lastactivedt, lastupdatetime, refund_phone
        from lacrm.user_taobao_sellerinfo
        limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="UserTaobaoSellerinfoMap">
        select
          id, user_id, nick, phone, email, province, city, address, sex, seller_credit_level, seller_credit_score, seller_credit_total_num, seller_credit_good_num, type, status, is_golden_seller, createdate, lastactivedt, lastupdatetime, refund_phone
        from lacrm.user_taobao_sellerinfo
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="nick != null and nick != ''">
                and nick = #{nick}
            </if>
            <if test="phone != null and phone != ''">
                and phone = #{phone}
            </if>
            <if test="email != null and email != ''">
                and email = #{email}
            </if>
            <if test="province != null and province != ''">
                and province = #{province}
            </if>
            <if test="city != null and city != ''">
                and city = #{city}
            </if>
            <if test="address != null and address != ''">
                and address = #{address}
            </if>
            <if test="sex != null and sex != ''">
                and sex = #{sex}
            </if>
            <if test="sellerCreditLevel != null">
                and seller_credit_level = #{sellerCreditLevel}
            </if>
            <if test="sellerCreditScore != null">
                and seller_credit_score = #{sellerCreditScore}
            </if>
            <if test="sellerCreditTotalNum != null">
                and seller_credit_total_num = #{sellerCreditTotalNum}
            </if>
            <if test="sellerCreditGoodNum != null">
                and seller_credit_good_num = #{sellerCreditGoodNum}
            </if>
            <if test="type != null and type != ''">
                and type = #{type}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="isGoldenSeller != null">
                and is_golden_seller = #{isGoldenSeller}
            </if>
            <if test="createdate != null">
                and createdate = #{createdate}
            </if>
            <if test="lastactivedt != null">
                and lastactivedt = #{lastactivedt}
            </if>
            <if test="lastupdatetime != null">
                and lastupdatetime = #{lastupdatetime}
            </if>
            <if test="refundPhone != null">
                and refund_phone = #{refundPhone}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into lacrm.user_taobao_sellerinfo(user_id, nick, phone, email, province, city, address, sex, seller_credit_level, seller_credit_score, seller_credit_total_num, seller_credit_good_num, type, status, is_golden_seller, createdate, lastactivedt, lastupdatetime, refund_phone)
        values (#{userId}, #{nick}, #{phone}, #{email}, #{province}, #{city}, #{address}, #{sex}, #{sellerCreditLevel}, #{sellerCreditScore}, #{sellerCreditTotalNum}, #{sellerCreditGoodNum}, #{type}, #{status}, #{isGoldenSeller}, #{createdate}, #{lastactivedt}, #{lastupdatetime}, #{refundPhone})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update lacrm.user_taobao_sellerinfo
        <set>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="nick != null and nick != ''">
                nick = #{nick},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
            <if test="province != null and province != ''">
                province = #{province},
            </if>
            <if test="city != null and city != ''">
                city = #{city},
            </if>
            <if test="address != null and address != ''">
                address = #{address},
            </if>
            <if test="sex != null and sex != ''">
                sex = #{sex},
            </if>
            <if test="sellerCreditLevel != null">
                seller_credit_level = #{sellerCreditLevel},
            </if>
            <if test="sellerCreditScore != null">
                seller_credit_score = #{sellerCreditScore},
            </if>
            <if test="sellerCreditTotalNum != null">
                seller_credit_total_num = #{sellerCreditTotalNum},
            </if>
            <if test="sellerCreditGoodNum != null">
                seller_credit_good_num = #{sellerCreditGoodNum},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="isGoldenSeller != null">
                is_golden_seller = #{isGoldenSeller},
            </if>
            <if test="createdate != null">
                createdate = #{createdate},
            </if>
            <if test="lastactivedt != null">
                lastactivedt = #{lastactivedt},
            </if>
            <if test="lastupdatetime != null">
                lastupdatetime = #{lastupdatetime},
            </if>
            <if test="refundPhone != null">
                refund_phone = #{refundPhone},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from lacrm.user_taobao_sellerinfo where id = #{id}
    </delete>

</mapper>
