<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.uac.db.common.dao.dream.UserAlipayAccountInfoDao">

    <resultMap type="cn.loveapp.uac.db.common.entity.UserAlipayAccountInfo" id="UserAlipayAccountInfoMap">
        <result property="id" column="id"/>
        <result property="sellerNick" column="seller_nick"/>
        <result property="sellerName" column="seller_name"/>
        <result property="sellerAlipayAccount" column="seller_alipay_account"/>
        <result property="addTime" column="add_time"/>
        <result property="status" column="status"/>
        <result property="vipFlag" column="vip_flag"/>
        <result property="vipTime" column="vip_time"/>
        <result property="optionnick" column="optionnick"/>
        <result property="isAutoReturn" column="is_auto_return"/>
        <result property="returnMoney" column="return_money"/>
        <result property="pushedReturn" column="pushed_return"/>
        <result property="payDate" column="pay_date"/>
        <result property="isPushed" column="is_pushed"/>
        <result property="platform" column="platform"/>
    </resultMap>

    <sql id="tablename">
        <choose>
            <when test="appType == 'trade'">
                lacrm.user_alipay_account_info
            </when>
            <when test="appType == 'item'">
                lacrm.item_user_alipay_account_info
            </when>
            <otherwise>
                lacrm.user_alipay_account_info
            </otherwise>
        </choose>
    </sql>

	<!--查询单个-->
    <select id="queryUserAlipayAccountInfoBySellerNickAndStatus" resultMap="UserAlipayAccountInfoMap">
        select
        id, seller_nick, seller_name, seller_alipay_account, add_time, status, vip_flag, vip_time,
        optionnick, is_auto_return, return_money, pushed_return, pay_date, is_pushed, platform
        from
        <include refid="tablename"/>
        where seller_nick = #{sellerNick} and status = #{status} limit 1
    </select>

    <!--通过主键修改数据-->
    <update id="update">
        update <include refid="tablename"/>
        <set>
            <if test="user.sellerNick != null and user.sellerNick != ''">
                seller_nick = #{user.sellerNick},
            </if>
            <if test="user.sellerName != null and user.sellerName != ''">
                seller_name = #{user.sellerName},
            </if>
            <if test="user.sellerAlipayAccount != null and user.sellerAlipayAccount != ''">
                seller_alipay_account = #{user.sellerAlipayAccount},
            </if>
            <if test="user.addTime != null">
                add_time = #{user.addTime},
            </if>
            <if test="user.status != null">
                status = #{user.status},
            </if>
            <if test="user.vipFlag != null">
                vip_flag = #{user.vipFlag},
            </if>
            <if test="user.vipTime != null">
                vip_time = #{user.vipTime},
            </if>
            <if test="user.optionnick != null and user.optionnick != ''">
                optionnick = #{user.optionnick},
            </if>
            <if test="user.isAutoReturn != null">
                is_auto_return = #{user.isAutoReturn},
            </if>
            <if test="user.returnMoney != null">
                return_money = #{user.returnMoney},
            </if>
            <if test="user.pushedReturn != null">
                pushed_return = #{user.pushedReturn},
            </if>
            <if test="user.payDate != null">
                pay_date = #{user.payDate},
            </if>
            <if test="user.isPushed != null">
                is_pushed = #{user.isPushed},
            </if>
            <if test="user.platform != null and user.platform != ''">
                platform = #{user.platform},
            </if>
        </set>
        where id = #{user.id}
    </update>

</mapper>
