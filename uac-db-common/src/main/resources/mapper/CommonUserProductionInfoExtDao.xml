<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.uac.db.common.dao.dream.CommonUserProductionInfoExtDao">

	<resultMap type="cn.loveapp.uac.entity.UserProductInfoBusinessExt" id="UserProductionInfoExtMap">
		<result property="id" column="id"/>
		<result property="sellerId" column="seller_id"/>
		<result property="sellerNick" column="seller_nick"/>
		<result property="storeId" column="store_id"/>
		<result property="corpId" column="corp_id"/>
		<result property="dbStatus" column="db_status"/>
		<result property="pullStatus" column="pull_status"/>
		<result property="topStatus" column="top_status"/>
		<result property="apiStatus" column="api_status"/>
		<result property="topTradeCount" column="top_trade_count"/>
		<result property="sessionStatus" column="session_status"/>
		<result property="downgradeTag" column="downgrade_tag"/>
		<result property="dbId" column="db_id"/>
		<result property="pullStartDateTime" column="pull_start_datetime"/>
		<result property="pullEndDateTime" column="pull_end_datetime"/>
		<result property="pullEndPoint" column="pull_end_point"/>
		<result property="appName" column="app_name"/>
		<result property="memberId" column="member_id"/>
		<result property="gmtCreate" column="gmt_create"/>
		<result property="gmtModified" column="gmt_modified"/>
		<result property="products" column="products"/>
	</resultMap>

	<sql id="fields">
		id, seller_id, seller_nick, store_id, corp_id, db_status, top_status,top_trade_count, api_status, downgrade_tag,
	  db_id, pull_status, gmt_create, gmt_modified, session_status, pull_start_datetime,
	  pull_end_datetime, pull_end_point, app_name, member_id, products
	</sql>

	<sql id="wxvideoshopIncrementGetRespfields">
		id, seller_id, seller_nick, store_id, pull_end_datetime
	</sql>

	<!--查询指定行数据-->
	<select id="queryByPullStatusAndStoreIdAndTopStatusLimit" resultMap="UserProductionInfoExtMap">
		select
		<include refid="fields"/>        from ${tableName}
		where pull_status = #{pullStatus} and store_id = #{storeId} and top_status = #{topStatus}
		order by id limit #{offset}, #{limit}
	</select>

	<select id="queryByTopStatusLimit" resultMap="UserProductionInfoExtMap">
		select
		<include refid="fields"/>        from ${tableName}
		where top_status = #{topStatus}
		order by id limit #{limit} offset #{offset}
	</select>

	<select id="queryCountBySellerId" resultType="java.lang.Integer">
		select count(*) from ${tableName}
		where seller_id = #{sellerId} and store_id = #{storeId} and app_name = #{appName}
	</select>

	<select id="queryMultiBySellerId" resultMap="UserProductionInfoExtMap">
		select <include refid="fields"/> from ${tableName}
		where seller_id = #{sellerId} and store_id = #{storeId}
	</select>

	<select id="querySingleBySellerId" resultMap="UserProductionInfoExtMap">
		select <include refid="fields"/> from ${tableName}
		where seller_id = #{sellerId} and store_id = #{storeId} and app_name = #{appName} limit 1
	</select>

	<select id="queryByMemberId" resultMap="UserProductionInfoExtMap">
		select seller_id,seller_nick,top_status,db_id,store_id, member_id from ${tableName}
		where member_id = #{memberId} and store_id = #{storeId} and app_name = #{appName} limit 1
	</select>

	<select id="getDbidValidOrderSum" resultMap="UserProductionInfoExtMap">
		select db_id, sum(top_trade_count) as top_trade_count from ${tableName}
		where top_status = '10'
		group by db_id
	</select>

	<select id="queryAllByMaxId" resultMap="UserProductionInfoExtMap">
		select
		<include refid="fields"/> from ${tableName}
		where pull_end_point = #{pullEndPoint} and pull_end_datetime <![CDATA[ <= ]]> #{diffTime} and id > #{maxId}
		and pull_status = #{pullStatus} and store_id = #{storeId} order by id
		limit #{offset}, #{limit}
	</select>

	<!--新增所有列-->
	<insert id="insert" keyProperty="userProductInfoBusinessExt.id" useGeneratedKeys="true">
		insert into ${tableName}
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="userProductInfoBusinessExt.sellerId != null and userProductInfoBusinessExt.sellerId != ''">
				seller_id,
			</if>
			<if test="userProductInfoBusinessExt.sellerNick != null and userProductInfoBusinessExt.sellerNick != ''">
				seller_nick,
			</if>
			<if test="userProductInfoBusinessExt.storeId != null and userProductInfoBusinessExt.storeId != ''">
				store_id,
			</if>
			<if test="userProductInfoBusinessExt.appName != null and userProductInfoBusinessExt.appName != ''">
				app_name,
			</if>
			<if test="userProductInfoBusinessExt.pullStatus != null and userProductInfoBusinessExt.pullStatus != ''">
				pull_status,
			</if>
			<if test="userProductInfoBusinessExt.corpId != null">
				corp_id,
			</if>
			<if test="userProductInfoBusinessExt.dbStatus != null">
				db_status,
			</if>
			<if test="userProductInfoBusinessExt.topStatus != null and userProductInfoBusinessExt.topStatus != ''">
				top_status,
			</if>
			<if test="userProductInfoBusinessExt.apiStatus != null and userProductInfoBusinessExt.apiStatus != ''">
				api_status,
			</if>
			<if test="userProductInfoBusinessExt.topTradeCount != null and userProductInfoBusinessExt.topTradeCount != ''">
				top_trade_count,
			</if>
			<if test="userProductInfoBusinessExt.sessionStatus != null and userProductInfoBusinessExt.sessionStatus != ''">
				session_status,
			</if>
			<if test="userProductInfoBusinessExt.downgradeTag != null and userProductInfoBusinessExt.downgradeTag != ''">
				downgrade_tag,
			</if>
			<if test="userProductInfoBusinessExt.dbId != null">
				db_id,
			</if>
			<if test="userProductInfoBusinessExt.gmtCreate != null">
				gmt_create,
			</if>
			<if test="userProductInfoBusinessExt.gmtModified != null">
				gmt_modified,
			</if>
			<if test="userProductInfoBusinessExt.memberId != null">
				member_id,
			</if>
			<if test="userProductInfoBusinessExt.products != null and userProductInfoBusinessExt.products != ''">
				products,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="userProductInfoBusinessExt.sellerId != null and userProductInfoBusinessExt.sellerId != ''">
				#{userProductInfoBusinessExt.sellerId},
			</if>
			<if test="userProductInfoBusinessExt.sellerNick != null and userProductInfoBusinessExt.sellerNick != ''">
				#{userProductInfoBusinessExt.sellerNick},
			</if>
			<if test="userProductInfoBusinessExt.storeId != null and userProductInfoBusinessExt.storeId != ''">
				#{userProductInfoBusinessExt.storeId},
			</if>
			<if test="userProductInfoBusinessExt.appName != null and userProductInfoBusinessExt.appName != ''">
				#{userProductInfoBusinessExt.appName},
			</if>
			<if test="userProductInfoBusinessExt.pullStatus != null and userProductInfoBusinessExt.pullStatus != ''">
				#{userProductInfoBusinessExt.pullStatus},
			</if>
			<if test="userProductInfoBusinessExt.corpId != null">
				#{userProductInfoBusinessExt.corpId},
			</if>
			<if test="userProductInfoBusinessExt.dbStatus != null">
				#{userProductInfoBusinessExt.dbStatus},
			</if>
			<if test="userProductInfoBusinessExt.topStatus != null and userProductInfoBusinessExt.topStatus != ''">
				#{userProductInfoBusinessExt.topStatus},
			</if>
			<if test="userProductInfoBusinessExt.apiStatus != null and userProductInfoBusinessExt.apiStatus != ''">
				#{userProductInfoBusinessExt.apiStatus},
			</if>
			<if test="userProductInfoBusinessExt.topTradeCount != null and userProductInfoBusinessExt.topTradeCount != ''">
				#{userProductInfoBusinessExt.topTradeCount},
			</if>
			<if test="userProductInfoBusinessExt.sessionStatus != null and userProductInfoBusinessExt.sessionStatus != ''">
				#{userProductInfoBusinessExt.sessionStatus},
			</if>
			<if test="userProductInfoBusinessExt.downgradeTag != null and userProductInfoBusinessExt.downgradeTag != ''">
				#{userProductInfoBusinessExt.downgradeTag},
			</if>
			<if test="userProductInfoBusinessExt.dbId != null">
				#{userProductInfoBusinessExt.dbId},
			</if>
			<if test="userProductInfoBusinessExt.gmtCreate != null">
				#{userProductInfoBusinessExt.gmtCreate},
			</if>
			<if test="userProductInfoBusinessExt.gmtModified != null">
				#{userProductInfoBusinessExt.gmtModified},
			</if>
			<if test="userProductInfoBusinessExt.memberId != null">
				#{userProductInfoBusinessExt.memberId},
			</if>
			<if test="userProductInfoBusinessExt.products != null and userProductInfoBusinessExt.products != ''">
				#{userProductInfoBusinessExt.products},
			</if>
		</trim>
	</insert>

	<!--通过主键修改数据-->
	<update id="update">
		update ${tableName}
		<set>
			<if test="userProductInfoBusinessExt.sellerNick != null and userProductInfoBusinessExt.sellerNick != ''">
				seller_nick = #{userProductInfoBusinessExt.sellerNick},
			</if>
			<if test="userProductInfoBusinessExt.storeId != null and userProductInfoBusinessExt.storeId != ''">
				store_id = #{userProductInfoBusinessExt.storeId},
			</if>
			<if test="userProductInfoBusinessExt.corpId != null and userProductInfoBusinessExt.corpId != ''">
				corp_id = #{userProductInfoBusinessExt.corpId},
			</if>
			<if test="userProductInfoBusinessExt.dbStatus != null">
				db_status = #{userProductInfoBusinessExt.dbStatus},
			</if>
			<if test="userProductInfoBusinessExt.topStatus != null and userProductInfoBusinessExt.topStatus != ''">
				top_status = #{userProductInfoBusinessExt.topStatus},
			</if>
			<if test="userProductInfoBusinessExt.apiStatus != null and userProductInfoBusinessExt.apiStatus != ''">
				api_status = #{userProductInfoBusinessExt.apiStatus},
			</if>
			<if test="userProductInfoBusinessExt.topTradeCount != null">
				top_trade_count = #{userProductInfoBusinessExt.topTradeCount},
			</if>
			<if test="userProductInfoBusinessExt.sessionStatus != null and userProductInfoBusinessExt.sessionStatus != ''">
				session_status = #{userProductInfoBusinessExt.sessionStatus},
			</if>
			<if test="userProductInfoBusinessExt.downgradeTag != null and userProductInfoBusinessExt.downgradeTag != ''">
				downgrade_tag = #{userProductInfoBusinessExt.downgradeTag},
			</if>
			<if test="userProductInfoBusinessExt.dbId != null">
				db_id = #{userProductInfoBusinessExt.dbId},
			</if>
			<if test="userProductInfoBusinessExt.pullStatus != null">
				pull_status = #{userProductInfoBusinessExt.pullStatus},
			</if>
			<if test="userProductInfoBusinessExt.pullStartDateTime != null">
				pull_start_datetime = #{userProductInfoBusinessExt.pullStartDateTime},
			</if>
			<if test="userProductInfoBusinessExt.pullEndDateTime != null">
				pull_end_datetime = #{userProductInfoBusinessExt.pullEndDateTime},
			</if>
			<if test="userProductInfoBusinessExt.pullEndPoint != null">
				pull_end_point = #{userProductInfoBusinessExt.pullEndPoint},
			</if>
			<if test="userProductInfoBusinessExt.gmtModified != null">
				gmt_modified = #{userProductInfoBusinessExt.gmtModified},
			</if>
			<if test="userProductInfoBusinessExt.memberId != null">
				member_id = #{userProductInfoBusinessExt.memberId},
			</if>
			<if test="userProductInfoBusinessExt.products != null and userProductInfoBusinessExt.products != ''">
				products = #{userProductInfoBusinessExt.products},
			</if>
		</set>
		where seller_id = #{userProductInfoBusinessExt.sellerId} and store_id = #{userProductInfoBusinessExt.storeId} and app_name = #{userProductInfoBusinessExt.appName}
	</update>

	<update id="batchUpdatePullStatusForSellerId">
		update ${tableName}
		<set>
			pull_status = #{pullStatus}
		</set>
		<where>
			seller_id in
			<foreach collection="sellerIds" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
			and store_id = #{storeId} and app_name = #{appName}
		</where>
	</update>

	<update id="updateApiStatus">
		update ${tableName}
		<set>
			api_status = #{apiStatus}
		</set>
		<where>
			seller_id = #{sellerId} and store_id = #{storeId} and app_name = #{appName}
		</where>
	</update>

	<select id="queryAllByLimitBetweenPullEndDateTime" resultMap="UserProductionInfoExtMap">
		select
		<include refid="fields"/> from ${tableName}
		where pull_end_point = #{pullEndPoint} and pull_end_datetime <![CDATA[ >= ]]> #{startDateTime} and pull_end_datetime <![CDATA[ <= ]]> #{endDateTime} and id > #{maxId}
		and pull_status = #{pullStatus} and store_id = #{storeId} order by id
		limit #{offset}, #{limit}
	</select>

	<select id="queryAllWithOutTimeByLimit" resultMap="UserProductionInfoExtMap">
		select
		<include refid="fields"/> from ${tableName}
		where pull_end_point = #{pullEndPoint} and id > #{maxId}
		and pull_status = #{pullStatus} and store_id = #{storeId} order by id
		limit #{offset}, #{limit}
	</select>

	<select id="querySingleByMemberId" resultMap="UserProductionInfoExtMap">
		select
		<include refid="fields"/>
		from ${tableName}
		where member_id = #{memberId} and store_id = #{storeId} and app_name = #{appName} limit 1
	</select>

</mapper>
