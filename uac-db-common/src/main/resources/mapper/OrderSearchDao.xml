<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.loveapp.uac.db.common.dao.dream.OrderSearchDao">

    <resultMap type="cn.loveapp.uac.db.common.entity.OrderSearch" id="OrderSearchMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="nick" column="nick"/>
        <result property="subnick" column="subnick"/>
        <result property="articleName" column="article_name"/>
        <result property="articleCode" column="article_code"/>
        <result property="articleItemName" column="article_item_name"/>
        <result property="itemCode" column="item_code"/>
        <result property="orderCycle" column="order_cycle"/>
        <result property="createdate" column="createDate"/>
        <result property="orderCycleStart" column="order_cycle_start"/>
        <result property="orderCycleEnd" column="order_cycle_end"/>
        <result property="bizType" column="biz_type"/>
        <result property="fee" column="fee"/>
        <result property="promFee" column="prom_fee"/>
        <result property="refundFee" column="refund_fee"/>
        <result property="totalPayFee" column="total_pay_fee"/>
        <result property="orderId" column="order_id"/>
        <result property="bizOrderId" column="biz_order_id"/>
        <result property="activityCode" column="activity_code"/>
        <result property="itemName" column="item_name"/>
        <result property="maturitydt" column="maturitydt"/>
        <result property="appkey" column="appkey"/>
        <result property="eventName" column="event_name"/>
        <result property="from" column="from"/>
        <result property="extension" column="extension"/>
        <result property="originality" column="originality"/>
        <result property="hasAct" column="has_act"/>
        <result property="day" column="day"/>
        <result property="openCid" column="open_cid"/>
        <result property="tjplatform" column="tjplatform"/>
        <result property="primaryClass" column="primary_class"/>
        <result property="secondaryClass" column="secondary_class"/>
        <result property="isSubuserOrder" column="is_subuser_order"/>
    </resultMap>

	<sql id="fields">
		id, user_id, nick, subnick, article_name, article_code, article_item_name, item_code, order_cycle, createDate, order_cycle_start,
		order_cycle_end, biz_type, fee, prom_fee, refund_fee, total_pay_fee, order_id, biz_order_id, activity_code, item_name, maturitydt, appkey, event_name,
		`from`, extension, originality, has_act, `day`, open_cid, tjplatform, primary_class, secondary_class, is_subuser_order
	</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="OrderSearchMap">
        select <include refid="fields"/>
        from ${tableName}
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="OrderSearchMap">
		select <include refid="fields"/>
		from ${tableName}
        limit #{offset}, #{limit}
    </select>

	<!--查询指定行数据-->
	<select id="queryBySellerNickAndOrderCycleStartAndOrderCycleEndAndItemCode" resultMap="OrderSearchMap">
		select <include refid="fields"/>
		from ${tableName}
		WHERE nick = #{sellerNick}
		<if test="orderCycleStart != null and orderCycleEnd != null">
			AND order_cycle_start <![CDATA[ <= ]]> #{orderCycleStart} AND order_cycle_end <![CDATA[ >= ]]> #{orderCycleEnd}
		</if>
		<if test="itemCodes != null">
			AND item_code in
			<foreach item="itemCode" index="index" collection="itemCodes" open="(" separator="," close=")">
				#{itemCode}
			</foreach>
		</if>
		<if test="sortBy != null">
			order by id desc
		</if>
		  LIMIT 1
    </select>

	<!--查询指定行数据-->
	<select id="queryBySellerNickAndItemCodeAndIdSortIdDesc" resultMap="OrderSearchMap">
		select <include refid="fields"/>
		from ${tableName}
		WHERE nick = #{sellerNick}
		<if test="itemCode != null">
          AND item_code = #{itemCode}
        </if>
		<if test="id != null">
		  AND id &lt; #{id}
		</if>
        order by id desc
        LIMIT 1
    </select>


    <!--新增所有列-->
    <insert id="insert" keyProperty="orderSearch.id" useGeneratedKeys="true">
        insert into ${tableName} (user_id, nick, subnick, article_name, article_code, article_item_name, item_code, order_cycle, createDate, order_cycle_start, order_cycle_end, biz_type, fee, prom_fee, refund_fee, total_pay_fee, order_id, biz_order_id, activity_code, item_name, maturitydt, appkey, event_name, `from`, extension, originality, has_act, `day`, open_cid, tjplatform, primary_class, secondary_class, is_subuser_order)
        values (#{orderSearch.userId}, #{orderSearch.nick}, #{orderSearch.subnick}, #{orderSearch.articleName}, #{orderSearch.articleCode}, #{orderSearch.articleItemName}, #{orderSearch.itemCode}, #{orderSearch.orderCycle}, #{orderSearch.createdate}, #{orderSearch.orderCycleStart}, #{orderSearch.orderCycleEnd}, #{orderSearch.bizType}, #{orderSearch.fee}, #{orderSearch.promFee}, #{orderSearch.refundFee}, #{orderSearch.totalPayFee}, #{orderSearch.orderId}, #{orderSearch.bizOrderId}, #{orderSearch.activityCode}, #{orderSearch.itemName}, #{orderSearch.maturitydt}, #{orderSearch.appkey}, #{orderSearch.eventName}, #{orderSearch.from}, #{orderSearch.extension}, #{orderSearch.originality}, #{orderSearch.hasAct}, #{orderSearch.day}, #{orderSearch.openCid}, #{orderSearch.tjplatform}, #{orderSearch.primaryClass}, #{orderSearch.secondaryClass}, #{orderSearch.isSubuserOrder})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update ${tableName}
        <set>
            <if test="orderSearch.userId != null and orderSearch.userId != ''">
                user_id = #{orderSearch.userId},
            </if>
            <if test="orderSearch.nick != null and orderSearch.nick != ''">
                nick = #{orderSearch.nick},
            </if>
            <if test="orderSearch.subnick != null and orderSearch.subnick != ''">
                subnick = #{orderSearch.subnick},
            </if>
            <if test="orderSearch.articleName != null and orderSearch.articleName != ''">
                article_name = #{orderSearch.articleName},
            </if>
            <if test="orderSearch.articleCode != null and orderSearch.articleCode != ''">
                article_code = #{orderSearch.articleCode},
            </if>
            <if test="orderSearch.articleItemName != null and orderSearch.articleItemName != ''">
                article_item_name = #{orderSearch.articleItemName},
            </if>
            <if test="orderSearch.itemCode != null and orderSearch.itemCode != ''">
                item_code = #{orderSearch.itemCode},
            </if>
            <if test="orderSearch.orderCycle != null and orderSearch.orderCycle != ''">
                order_cycle = #{orderSearch.orderCycle},
            </if>
            <if test="orderSearch.createdate != null">
                createDate = #{orderSearch.createdate},
            </if>
            <if test="orderSearch.orderCycleStart != null">
                order_cycle_start = #{orderSearch.orderCycleStart},
            </if>
            <if test="orderSearch.orderCycleEnd != null">
                order_cycle_end = #{orderSearch.orderCycleEnd},
            </if>
            <if test="orderSearch.bizType != null">
                biz_type = #{orderSearch.bizType},
            </if>
            <if test="orderSearch.fee != null">
                fee = #{orderSearch.fee},
            </if>
            <if test="orderSearch.promFee != null">
                prom_fee = #{orderSearch.promFee},
            </if>
            <if test="orderSearch.refundFee != null">
                refund_fee = #{orderSearch.refundFee},
            </if>
            <if test="orderSearch.totalPayFee != null">
                total_pay_fee = #{orderSearch.totalPayFee},
            </if>
            <if test="orderSearch.orderId != null and orderSearch.orderId != ''">
                order_id = #{orderSearch.orderId},
            </if>
            <if test="orderSearch.bizOrderId != null and orderSearch.bizOrderId != ''">
                biz_order_id = #{orderSearch.bizOrderId},
            </if>
            <if test="orderSearch.activityCode != null and orderSearch.activityCode != ''">
                activity_code = #{orderSearch.activityCode},
            </if>
            <if test="orderSearch.itemName != null and orderSearch.itemName != ''">
                item_name = #{orderSearch.itemName},
            </if>
            <if test="orderSearch.maturitydt != null">
                maturitydt = #{orderSearch.maturitydt},
            </if>
            <if test="orderSearch.appkey != null and orderSearch.appkey != ''">
                appkey = #{orderSearch.appkey},
            </if>
            <if test="orderSearch.eventName != null and orderSearch.eventName != ''">
                event_name = #{orderSearch.eventName},
            </if>
            <if test="orderSearch.from != null and orderSearch.from != ''">
                `from` = #{orderSearch.from},
            </if>
            <if test="orderSearch.extension != null and orderSearch.extension != ''">
                extension = #{orderSearch.extension},
            </if>
            <if test="orderSearch.originality != null and orderSearch.originality != ''">
                originality = #{orderSearch.originality},
            </if>
            <if test="orderSearch.hasAct != null">
                has_act = #{orderSearch.hasAct},
            </if>
            <if test="orderSearch.day != null and orderSearch.day != ''">
                `day` = #{orderSearch.day},
            </if>
            <if test="orderSearch.openCid != null">
                open_cid = #{orderSearch.openCid},
            </if>
            <if test="orderSearch.tjplatform != null and orderSearch.tjplatform != ''">
                tjplatform = #{orderSearch.tjplatform},
            </if>
            <if test="orderSearch.primaryClass != null and orderSearch.primaryClass != ''">
                primary_class = #{orderSearch.primaryClass},
            </if>
            <if test="orderSearch.secondaryClass != null and orderSearch.secondaryClass != ''">
                secondary_class = #{orderSearch.secondaryClass},
            </if>
            <if test="orderSearch.isSubuserOrder != null">
                is_subuser_order = #{orderSearch.isSubuserOrder},
            </if>
        </set>
        where id = #{orderSearch.id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from ${tableName} where id = #{id}
    </delete>

    <select id="queryBySellerNickAndArticleCodeAndItemCodeAndOrderCycleEnd" resultMap="OrderSearchMap">
        select <include refid="fields"/>
        from ${tableName}
        <where>
            <if test="orderSearch.nick != null and orderSearch.nick != ''">
                and nick = #{orderSearch.nick}
            </if>
            <if test="orderSearch.articleCode != null and orderSearch.articleCode != ''">
                and article_code = #{orderSearch.articleCode}
            </if>
            <if test="orderSearch.itemCode != null and orderSearch.itemCode != ''">
                and item_code = #{orderSearch.itemCode}
            </if>
            <if test="orderSearch.orderCycleEnd != null">
                and order_cycle_end = #{orderSearch.orderCycleEnd}
            </if>
        </where>
        limit 1
    </select>


    <select id="queryOrderSearchList" resultMap="OrderSearchMap">
        select <include refid="fields"/>
        from ${tableName}
        <where>
            nick = #{queryDTO.sellerNick}
            <if test="queryDTO.startTime != null and queryDTO.startTime != null">
                AND order_cycle_start <![CDATA[ <= ]]> #{queryDTO.startTime}
            </if>
            <if test="queryDTO.endTime != null and queryDTO.endTime != null">
                AND order_cycle_end <![CDATA[ >= ]]> #{queryDTO.endTime}
            </if>
            <if test="queryDTO.itemCodes != null">
                AND item_code in
                <foreach item="itemCode" index="index" collection="queryDTO.itemCodes" open="(" separator="," close=")">
                    #{itemCode}
                </foreach>
            </if>
            <choose>
                <when test="queryDTO.sortDirection == 'asc'">
                    order by id asc
                </when>
                <otherwise>
                    order by id desc
                </otherwise>
            </choose>
        </where>
        limit 1000
    </select>
</mapper>
