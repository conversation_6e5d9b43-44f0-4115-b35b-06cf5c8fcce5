package cn.loveapp.items.api.service;

import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.uac.newuser.dto.request.GetPullDataProgressRequest;
import cn.loveapp.uac.newuser.dto.request.GetSaveDataTotalResultRequest;
import cn.loveapp.uac.newuser.dto.response.GetPullDataProgressResponse;
import cn.loveapp.uac.newuser.dto.response.GetSaveDataTotalResultResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Author: zhong<PERSON><PERSON>e
 * @Date: 2023/1/17 12:40
 * @Description: 商品-新用户-rpc
 */
@FeignClient(name = "itemNewUserService", url = "${loveapp.service.rpc.itemNewUserService.host:http://127.0.0.1:8080}"
    ,path = "itemNewUserService")
public interface ItemsNewUserRpcInnerApiService {

    /**
     * 获取用户存单进度
     * @param request
     * @return
     */
    @PostMapping("getPullDataProgress")
    CommonApiResponse<GetPullDataProgressResponse> getPullDataProgress(@RequestBody GetPullDataProgressRequest request);

    @PostMapping("getSaveDataTotalResult")
    CommonApiResponse<GetSaveDataTotalResultResponse> getSaveDataTotalResult(@RequestBody GetSaveDataTotalResultRequest request);
}
