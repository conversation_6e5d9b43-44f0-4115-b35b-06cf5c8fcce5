package cn.loveapp.uac.domain;

import lombok.Data;

/**
 * @Author: z<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023/3/9 14:31
 * @Description: 公共消息header实体类
 */
@Data
public class ComLoveRpcInnerprocessRequestHead {
	private String tid;
	private String requestId;
	private String sellerNick;
	private String sellerId;
	private String status;

	/**
	 * 平台
	 */
	private String platformId;
	/**
	 * 应用名称
	 */
	private String appName;

	/**
	 * 供货商id
	 */
	private String supplierId;

	public ComLoveRpcInnerprocessRequestHead(){

	}

	public ComLoveRpcInnerprocessRequestHead(String sellerNick, String sellerId, String platformId, String appName){
		this.sellerNick = sellerNick;
		this.sellerId = sellerId;
		this.platformId = platformId;
		this.appName = appName;
	}
}
