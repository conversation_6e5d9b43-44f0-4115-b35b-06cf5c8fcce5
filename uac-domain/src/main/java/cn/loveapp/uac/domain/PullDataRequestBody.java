package cn.loveapp.uac.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: zhong<PERSON>jie
 * @Date: 2023/3/9 14:31
 * @Description: 用户拉取数据消息body
 */
@Data
public class PullDataRequestBody {

	/**
	 * 拉取类型
	 */
	private String pullType;

	/**
	 * 是否忽略与当前库中modified相同的数据
	 */
	private boolean ignoreSameModified = true;

	/**
	 * 开始时间
	 */
	private LocalDateTime startCreated;

	/**
	 * 结束时间
	 */
	private LocalDateTime endCreated;

	/**
	 * 是否重置代发商品关联关系
	 */
	private boolean resetDistributeAssociation;
}
