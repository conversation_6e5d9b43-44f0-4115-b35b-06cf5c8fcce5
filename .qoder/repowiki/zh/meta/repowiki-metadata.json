{"knowledge_relations": [{"id": 1469, "source_id": "06d759c0-fab3-40f1-b848-cff676f413fb", "target_id": "f312f940-527b-4bd8-a705-fe52b29f61bc", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 06d759c0-fab3-40f1-b848-cff676f413fb -> f312f940-527b-4bd8-a705-fe52b29f61bc", "gmt_create": "2025-09-17T17:56:03.814026+08:00", "gmt_modified": "2025-09-17T17:56:03.814026+08:00"}, {"id": 1470, "source_id": "06d759c0-fab3-40f1-b848-cff676f413fb", "target_id": "b5e3ed1e-e731-42c6-af40-ca0a39f962a0", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 06d759c0-fab3-40f1-b848-cff676f413fb -> b5e3ed1e-e731-42c6-af40-ca0a39f962a0", "gmt_create": "2025-09-17T17:56:03.826371+08:00", "gmt_modified": "2025-09-17T17:56:03.826371+08:00"}, {"id": 1471, "source_id": "06d759c0-fab3-40f1-b848-cff676f413fb", "target_id": "ed49d354-ccf6-4fa5-a92d-4bc87c19df96", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 06d759c0-fab3-40f1-b848-cff676f413fb -> ed49d354-ccf6-4fa5-a92d-4bc87c19df96", "gmt_create": "2025-09-17T17:56:03.836726+08:00", "gmt_modified": "2025-09-17T17:56:03.836726+08:00"}, {"id": 1472, "source_id": "06d759c0-fab3-40f1-b848-cff676f413fb", "target_id": "e215b524-7be0-4940-9086-dfe2b9084dd8", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 06d759c0-fab3-40f1-b848-cff676f413fb -> e215b524-7be0-4940-9086-dfe2b9084dd8", "gmt_create": "2025-09-17T17:56:03.842404+08:00", "gmt_modified": "2025-09-17T17:56:03.842404+08:00"}, {"id": 1473, "source_id": "06d759c0-fab3-40f1-b848-cff676f413fb", "target_id": "21105de4-86fd-4545-82bb-e94c5d4152fe", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 06d759c0-fab3-40f1-b848-cff676f413fb -> 21105de4-86fd-4545-82bb-e94c5d4152fe", "gmt_create": "2025-09-17T17:56:03.847478+08:00", "gmt_modified": "2025-09-17T17:56:03.847478+08:00"}, {"id": 1474, "source_id": "06d759c0-fab3-40f1-b848-cff676f413fb", "target_id": "aa13f8b4-5727-4769-8537-9ea1b7956b93", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 06d759c0-fab3-40f1-b848-cff676f413fb -> aa13f8b4-5727-4769-8537-9ea1b7956b93", "gmt_create": "2025-09-17T17:56:03.849964+08:00", "gmt_modified": "2025-09-17T17:56:03.849964+08:00"}, {"id": 1475, "source_id": "70c2a73f-5176-4fe7-b5cf-ba46ce23c4aa", "target_id": "0b03ac96-a1cd-4f6d-9530-03c09a3957f8", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 70c2a73f-5176-4fe7-b5cf-ba46ce23c4aa -> 0b03ac96-a1cd-4f6d-9530-03c09a3957f8", "gmt_create": "2025-09-17T17:56:03.855757+08:00", "gmt_modified": "2025-09-17T17:56:03.855757+08:00"}, {"id": 1476, "source_id": "70c2a73f-5176-4fe7-b5cf-ba46ce23c4aa", "target_id": "95c293e0-0fd1-41b3-b790-59f2769f6fe5", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 70c2a73f-5176-4fe7-b5cf-ba46ce23c4aa -> 95c293e0-0fd1-41b3-b790-59f2769f6fe5", "gmt_create": "2025-09-17T17:56:03.866827+08:00", "gmt_modified": "2025-09-17T17:56:03.866827+08:00"}, {"id": 1477, "source_id": "70c2a73f-5176-4fe7-b5cf-ba46ce23c4aa", "target_id": "93d9e201-8453-46c5-86bf-73d3bd849f75", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 70c2a73f-5176-4fe7-b5cf-ba46ce23c4aa -> 93d9e201-8453-46c5-86bf-73d3bd849f75", "gmt_create": "2025-09-17T17:56:03.87073+08:00", "gmt_modified": "2025-09-17T17:56:03.87073+08:00"}, {"id": 1478, "source_id": "9a9810fa-53a4-4d83-b0bb-a247aee8c025", "target_id": "fc80cc6d-be28-47f5-a9ce-6019f1687d68", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 9a9810fa-53a4-4d83-b0bb-a247aee8c025 -> fc80cc6d-be28-47f5-a9ce-6019f1687d68", "gmt_create": "2025-09-17T17:56:03.874912+08:00", "gmt_modified": "2025-09-17T17:56:03.874912+08:00"}, {"id": 1479, "source_id": "9a9810fa-53a4-4d83-b0bb-a247aee8c025", "target_id": "2eaf7d03-7395-4eeb-b0c4-c31f27b48e6a", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 9a9810fa-53a4-4d83-b0bb-a247aee8c025 -> 2eaf7d03-7395-4eeb-b0c4-c31f27b48e6a", "gmt_create": "2025-09-17T17:56:03.875932+08:00", "gmt_modified": "2025-09-17T17:56:03.875932+08:00"}, {"id": 1480, "source_id": "9a9810fa-53a4-4d83-b0bb-a247aee8c025", "target_id": "d6f1dd95-54d9-47f8-8f5b-e4d0d30ea54a", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 9a9810fa-53a4-4d83-b0bb-a247aee8c025 -> d6f1dd95-54d9-47f8-8f5b-e4d0d30ea54a", "gmt_create": "2025-09-17T17:56:03.876388+08:00", "gmt_modified": "2025-09-17T17:56:03.876388+08:00"}, {"id": 1481, "source_id": "9a9810fa-53a4-4d83-b0bb-a247aee8c025", "target_id": "3a7a5ca3-06b0-4b57-ace1-633ac40c9c9c", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 9a9810fa-53a4-4d83-b0bb-a247aee8c025 -> 3a7a5ca3-06b0-4b57-ace1-633ac40c9c9c", "gmt_create": "2025-09-17T17:56:03.876827+08:00", "gmt_modified": "2025-09-17T17:56:03.876827+08:00"}, {"id": 1482, "source_id": "9a9810fa-53a4-4d83-b0bb-a247aee8c025", "target_id": "ddbd353e-7c3d-4a90-8262-9307931d29b0", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 9a9810fa-53a4-4d83-b0bb-a247aee8c025 -> ddbd353e-7c3d-4a90-8262-9307931d29b0", "gmt_create": "2025-09-17T17:56:03.877193+08:00", "gmt_modified": "2025-09-17T17:56:03.877193+08:00"}, {"id": 1483, "source_id": "bcef76a3-bc7d-4611-ac7f-add7e716945f", "target_id": "d68ff3f0-fa40-43ee-9370-1561e6858574", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: bcef76a3-bc7d-4611-ac7f-add7e716945f -> d68ff3f0-fa40-43ee-9370-1561e6858574", "gmt_create": "2025-09-17T17:56:03.877609+08:00", "gmt_modified": "2025-09-17T17:56:03.877609+08:00"}, {"id": 1484, "source_id": "bcef76a3-bc7d-4611-ac7f-add7e716945f", "target_id": "9fa0b0b0-68de-4a92-a069-c0a95a17a508", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: bcef76a3-bc7d-4611-ac7f-add7e716945f -> 9fa0b0b0-68de-4a92-a069-c0a95a17a508", "gmt_create": "2025-09-17T17:56:03.878191+08:00", "gmt_modified": "2025-09-17T17:56:03.878191+08:00"}, {"id": 1485, "source_id": "bcef76a3-bc7d-4611-ac7f-add7e716945f", "target_id": "5da84cfc-95f9-4e70-9ffb-6f4f2a15916f", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: bcef76a3-bc7d-4611-ac7f-add7e716945f -> 5da84cfc-95f9-4e70-9ffb-6f4f2a15916f", "gmt_create": "2025-09-17T17:56:03.878565+08:00", "gmt_modified": "2025-09-17T17:56:03.878565+08:00"}, {"id": 1486, "source_id": "bcef76a3-bc7d-4611-ac7f-add7e716945f", "target_id": "a4ac4b17-76c1-4a78-8c74-7f84a77ea9ae", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: bcef76a3-bc7d-4611-ac7f-add7e716945f -> a4ac4b17-76c1-4a78-8c74-7f84a77ea9ae", "gmt_create": "2025-09-17T17:56:03.879042+08:00", "gmt_modified": "2025-09-17T17:56:03.879042+08:00"}, {"id": 1487, "source_id": "fe26aef8-465a-4b42-8938-ad10d3676344", "target_id": "7d1c9fc9-6f16-4121-88d8-169389ec2d82", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: fe26aef8-465a-4b42-8938-ad10d3676344 -> 7d1c9fc9-6f16-4121-88d8-169389ec2d82", "gmt_create": "2025-09-17T17:56:03.879475+08:00", "gmt_modified": "2025-09-17T17:56:03.879475+08:00"}, {"id": 1488, "source_id": "fe26aef8-465a-4b42-8938-ad10d3676344", "target_id": "75e8cdab-8d3e-40d1-8eee-afb52d94e098", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: fe26aef8-465a-4b42-8938-ad10d3676344 -> 75e8cdab-8d3e-40d1-8eee-afb52d94e098", "gmt_create": "2025-09-17T17:56:03.879831+08:00", "gmt_modified": "2025-09-17T17:56:03.879831+08:00"}, {"id": 1489, "source_id": "0b03ac96-a1cd-4f6d-9530-03c09a3957f8", "target_id": "ab93395f-84bc-46a5-9d79-64c69f4c30b4", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 0b03ac96-a1cd-4f6d-9530-03c09a3957f8 -> ab93395f-84bc-46a5-9d79-64c69f4c30b4", "gmt_create": "2025-09-17T17:56:03.880224+08:00", "gmt_modified": "2025-09-17T17:56:03.880224+08:00"}, {"id": 1490, "source_id": "0b03ac96-a1cd-4f6d-9530-03c09a3957f8", "target_id": "65dd2915-10b7-4fd9-b132-7c486d1de4ac", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 0b03ac96-a1cd-4f6d-9530-03c09a3957f8 -> 65dd2915-10b7-4fd9-b132-7c486d1de4ac", "gmt_create": "2025-09-17T17:56:03.880593+08:00", "gmt_modified": "2025-09-17T17:56:03.880593+08:00"}, {"id": 1491, "source_id": "b5e3ed1e-e731-42c6-af40-ca0a39f962a0", "target_id": "7000795d-9985-4e8b-afee-2ae2babba0d7", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: b5e3ed1e-e731-42c6-af40-ca0a39f962a0 -> 7000795d-9985-4e8b-afee-2ae2babba0d7", "gmt_create": "2025-09-17T17:56:03.881136+08:00", "gmt_modified": "2025-09-17T17:56:03.881136+08:00"}, {"id": 1492, "source_id": "b5e3ed1e-e731-42c6-af40-ca0a39f962a0", "target_id": "1f6a7944-333a-4feb-ac62-70657eb06a51", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: b5e3ed1e-e731-42c6-af40-ca0a39f962a0 -> 1f6a7944-333a-4feb-ac62-70657eb06a51", "gmt_create": "2025-09-17T17:56:03.88149+08:00", "gmt_modified": "2025-09-17T17:56:03.88149+08:00"}, {"id": 1493, "source_id": "b5e3ed1e-e731-42c6-af40-ca0a39f962a0", "target_id": "99e2f7f6-c3f7-452e-901a-dea94bbeaa5c", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: b5e3ed1e-e731-42c6-af40-ca0a39f962a0 -> 99e2f7f6-c3f7-452e-901a-dea94bbeaa5c", "gmt_create": "2025-09-17T17:56:03.882162+08:00", "gmt_modified": "2025-09-17T17:56:03.882162+08:00"}, {"id": 1494, "source_id": "b5e3ed1e-e731-42c6-af40-ca0a39f962a0", "target_id": "a3bb7e92-3bb3-44af-a8de-4746ab7b87a4", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: b5e3ed1e-e731-42c6-af40-ca0a39f962a0 -> a3bb7e92-3bb3-44af-a8de-4746ab7b87a4", "gmt_create": "2025-09-17T17:56:03.8825+08:00", "gmt_modified": "2025-09-17T17:56:03.8825+08:00"}, {"id": 1495, "source_id": "b5e3ed1e-e731-42c6-af40-ca0a39f962a0", "target_id": "2ad69cac-8d40-45eb-8e4c-beb5645e7b9b", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: b5e3ed1e-e731-42c6-af40-ca0a39f962a0 -> 2ad69cac-8d40-45eb-8e4c-beb5645e7b9b", "gmt_create": "2025-09-17T17:56:03.882863+08:00", "gmt_modified": "2025-09-17T17:56:03.882863+08:00"}, {"id": 1496, "source_id": "75e8cdab-8d3e-40d1-8eee-afb52d94e098", "target_id": "4ab5c311-e98f-416e-b1c8-6d4735c6c996", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 75e8cdab-8d3e-40d1-8eee-afb52d94e098 -> 4ab5c311-e98f-416e-b1c8-6d4735c6c996", "gmt_create": "2025-09-17T17:56:03.883265+08:00", "gmt_modified": "2025-09-17T17:56:03.883265+08:00"}, {"id": 1497, "source_id": "75e8cdab-8d3e-40d1-8eee-afb52d94e098", "target_id": "5d54ebaf-e073-4b6f-a5fa-654c15ea6437", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 75e8cdab-8d3e-40d1-8eee-afb52d94e098 -> 5d54ebaf-e073-4b6f-a5fa-654c15ea6437", "gmt_create": "2025-09-17T17:56:03.88376+08:00", "gmt_modified": "2025-09-17T17:56:03.88376+08:00"}, {"id": 1498, "source_id": "75e8cdab-8d3e-40d1-8eee-afb52d94e098", "target_id": "137d498b-7e77-4492-b642-8b30ba0173d3", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 75e8cdab-8d3e-40d1-8eee-afb52d94e098 -> 137d498b-7e77-4492-b642-8b30ba0173d3", "gmt_create": "2025-09-17T17:56:03.884108+08:00", "gmt_modified": "2025-09-17T17:56:03.884108+08:00"}, {"id": 1499, "source_id": "75e8cdab-8d3e-40d1-8eee-afb52d94e098", "target_id": "af738f88-c6ae-4bd3-ba1e-57cb09d8b1c0", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 75e8cdab-8d3e-40d1-8eee-afb52d94e098 -> af738f88-c6ae-4bd3-ba1e-57cb09d8b1c0", "gmt_create": "2025-09-17T17:56:03.884459+08:00", "gmt_modified": "2025-09-17T17:56:03.884459+08:00"}, {"id": 1500, "source_id": "93d9e201-8453-46c5-86bf-73d3bd849f75", "target_id": "f695c05e-9f3e-4dd4-8c8d-1040ab861344", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 93d9e201-8453-46c5-86bf-73d3bd849f75 -> f695c05e-9f3e-4dd4-8c8d-1040ab861344", "gmt_create": "2025-09-17T17:56:03.885034+08:00", "gmt_modified": "2025-09-17T17:56:03.885034+08:00"}, {"id": 1501, "source_id": "93d9e201-8453-46c5-86bf-73d3bd849f75", "target_id": "948857b0-d5f3-4098-91a2-9f99f950ebcb", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 93d9e201-8453-46c5-86bf-73d3bd849f75 -> 948857b0-d5f3-4098-91a2-9f99f950ebcb", "gmt_create": "2025-09-17T17:56:03.885681+08:00", "gmt_modified": "2025-09-17T17:56:03.885681+08:00"}, {"id": 1502, "source_id": "ed49d354-ccf6-4fa5-a92d-4bc87c19df96", "target_id": "ccd6398f-c947-4b00-9ff8-3725c1ae5da8", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: ed49d354-ccf6-4fa5-a92d-4bc87c19df96 -> ccd6398f-c947-4b00-9ff8-3725c1ae5da8", "gmt_create": "2025-09-17T17:56:03.886147+08:00", "gmt_modified": "2025-09-17T17:56:03.886147+08:00"}, {"id": 1503, "source_id": "ed49d354-ccf6-4fa5-a92d-4bc87c19df96", "target_id": "2ad98ad7-95ab-483b-baa5-523e6217f099", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: ed49d354-ccf6-4fa5-a92d-4bc87c19df96 -> 2ad98ad7-95ab-483b-baa5-523e6217f099", "gmt_create": "2025-09-17T17:56:03.886704+08:00", "gmt_modified": "2025-09-17T17:56:03.886704+08:00"}, {"id": 1504, "source_id": "ed49d354-ccf6-4fa5-a92d-4bc87c19df96", "target_id": "5b58902c-93b9-4ff2-bd92-bfa869e7b0ec", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: ed49d354-ccf6-4fa5-a92d-4bc87c19df96 -> 5b58902c-93b9-4ff2-bd92-bfa869e7b0ec", "gmt_create": "2025-09-17T17:56:03.88715+08:00", "gmt_modified": "2025-09-17T17:56:03.88715+08:00"}, {"id": 1505, "source_id": "ed49d354-ccf6-4fa5-a92d-4bc87c19df96", "target_id": "f1ddc595-0bcd-408b-8948-a5357d2a361e", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: ed49d354-ccf6-4fa5-a92d-4bc87c19df96 -> f1ddc595-0bcd-408b-8948-a5357d2a361e", "gmt_create": "2025-09-17T17:56:03.887558+08:00", "gmt_modified": "2025-09-17T17:56:03.887559+08:00"}, {"id": 1506, "source_id": "e215b524-7be0-4940-9086-dfe2b9084dd8", "target_id": "da193803-a81a-42a9-9dc7-3376df9c0db5", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: e215b524-7be0-4940-9086-dfe2b9084dd8 -> da193803-a81a-42a9-9dc7-3376df9c0db5", "gmt_create": "2025-09-17T17:56:03.88804+08:00", "gmt_modified": "2025-09-17T17:56:03.88804+08:00"}, {"id": 1507, "source_id": "e215b524-7be0-4940-9086-dfe2b9084dd8", "target_id": "d5cd5afe-72c1-4dc6-8e64-e9709c061d6b", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: e215b524-7be0-4940-9086-dfe2b9084dd8 -> d5cd5afe-72c1-4dc6-8e64-e9709c061d6b", "gmt_create": "2025-09-17T17:56:03.888468+08:00", "gmt_modified": "2025-09-17T17:56:03.888468+08:00"}, {"id": 1508, "source_id": "e215b524-7be0-4940-9086-dfe2b9084dd8", "target_id": "9148d765-a91a-4adc-a27a-d5e83fccc7fb", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: e215b524-7be0-4940-9086-dfe2b9084dd8 -> 9148d765-a91a-4adc-a27a-d5e83fccc7fb", "gmt_create": "2025-09-17T17:56:03.888855+08:00", "gmt_modified": "2025-09-17T17:56:03.888855+08:00"}, {"id": 1509, "source_id": "e215b524-7be0-4940-9086-dfe2b9084dd8", "target_id": "9ed314ab-9722-4389-9f9e-60dd0ec874ef", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: e215b524-7be0-4940-9086-dfe2b9084dd8 -> 9ed314ab-9722-4389-9f9e-60dd0ec874ef", "gmt_create": "2025-09-17T17:56:03.889496+08:00", "gmt_modified": "2025-09-17T17:56:03.889496+08:00"}, {"id": 1510, "source_id": "21105de4-86fd-4545-82bb-e94c5d4152fe", "target_id": "ac387d32-2baf-4c44-801c-9787d0b549fd", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 21105de4-86fd-4545-82bb-e94c5d4152fe -> ac387d32-2baf-4c44-801c-9787d0b549fd", "gmt_create": "2025-09-17T17:56:03.889967+08:00", "gmt_modified": "2025-09-17T17:56:03.889967+08:00"}, {"id": 1511, "source_id": "21105de4-86fd-4545-82bb-e94c5d4152fe", "target_id": "486bfbc8-d4c9-40b5-b9be-3773fac3a342", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 21105de4-86fd-4545-82bb-e94c5d4152fe -> 486bfbc8-d4c9-40b5-b9be-3773fac3a342", "gmt_create": "2025-09-17T17:56:03.890365+08:00", "gmt_modified": "2025-09-17T17:56:03.890365+08:00"}, {"id": 1512, "source_id": "21105de4-86fd-4545-82bb-e94c5d4152fe", "target_id": "8158c7a9-bd07-4240-9fc7-1ca1cb23e3fd", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 21105de4-86fd-4545-82bb-e94c5d4152fe -> 8158c7a9-bd07-4240-9fc7-1ca1cb23e3fd", "gmt_create": "2025-09-17T17:56:03.89076+08:00", "gmt_modified": "2025-09-17T17:56:03.890761+08:00"}, {"id": 1513, "source_id": "21105de4-86fd-4545-82bb-e94c5d4152fe", "target_id": "0f9637e6-7361-4a60-b217-92d20890abf9", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 21105de4-86fd-4545-82bb-e94c5d4152fe -> 0f9637e6-7361-4a60-b217-92d20890abf9", "gmt_create": "2025-09-17T17:56:03.89169+08:00", "gmt_modified": "2025-09-17T17:56:03.89169+08:00"}, {"id": 1514, "source_id": "948857b0-d5f3-4098-91a2-9f99f950ebcb", "target_id": "3bb22d81-0a32-4ad4-87f3-87b7af4047ac", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 948857b0-d5f3-4098-91a2-9f99f950ebcb -> 3bb22d81-0a32-4ad4-87f3-87b7af4047ac", "gmt_create": "2025-09-17T17:56:03.892691+08:00", "gmt_modified": "2025-09-17T17:56:03.892691+08:00"}, {"id": 1515, "source_id": "948857b0-d5f3-4098-91a2-9f99f950ebcb", "target_id": "646ad3f7-0c4c-4e95-a67c-d65e2c3a0515", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 948857b0-d5f3-4098-91a2-9f99f950ebcb -> 646ad3f7-0c4c-4e95-a67c-d65e2c3a0515", "gmt_create": "2025-09-17T17:56:03.893123+08:00", "gmt_modified": "2025-09-17T17:56:03.893124+08:00"}, {"id": 1516, "source_id": "948857b0-d5f3-4098-91a2-9f99f950ebcb", "target_id": "d49706cc-94fc-41e2-8484-39e2702268c4", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 948857b0-d5f3-4098-91a2-9f99f950ebcb -> d49706cc-94fc-41e2-8484-39e2702268c4", "gmt_create": "2025-09-17T17:56:03.893895+08:00", "gmt_modified": "2025-09-17T17:56:03.893895+08:00"}, {"id": 1517, "source_id": "948857b0-d5f3-4098-91a2-9f99f950ebcb", "target_id": "d9100272-9dbe-4ebc-99d3-c967dbdb9740", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 948857b0-d5f3-4098-91a2-9f99f950ebcb -> d9100272-9dbe-4ebc-99d3-c967dbdb9740", "gmt_create": "2025-09-17T17:56:03.894856+08:00", "gmt_modified": "2025-09-17T17:56:03.894856+08:00"}, {"id": 1518, "source_id": "948857b0-d5f3-4098-91a2-9f99f950ebcb", "target_id": "c0e285be-5113-4647-be96-a1d2042a8229", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 948857b0-d5f3-4098-91a2-9f99f950ebcb -> c0e285be-5113-4647-be96-a1d2042a8229", "gmt_create": "2025-09-17T17:56:03.895263+08:00", "gmt_modified": "2025-09-17T17:56:03.895263+08:00"}, {"id": 1519, "source_id": "948857b0-d5f3-4098-91a2-9f99f950ebcb", "target_id": "288ba19a-eb52-4d89-a7b1-201770fcc0ac", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 948857b0-d5f3-4098-91a2-9f99f950ebcb -> 288ba19a-eb52-4d89-a7b1-201770fcc0ac", "gmt_create": "2025-09-17T17:56:03.895673+08:00", "gmt_modified": "2025-09-17T17:56:03.895673+08:00"}, {"id": 1520, "source_id": "948857b0-d5f3-4098-91a2-9f99f950ebcb", "target_id": "075d6f05-9008-477d-abc5-2e18a0ccd5bb", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 948857b0-d5f3-4098-91a2-9f99f950ebcb -> 075d6f05-9008-477d-abc5-2e18a0ccd5bb", "gmt_create": "2025-09-17T17:56:03.89614+08:00", "gmt_modified": "2025-09-17T17:56:03.89614+08:00"}, {"id": 1521, "source_id": "948857b0-d5f3-4098-91a2-9f99f950ebcb", "target_id": "5da24188-e100-4a57-948d-1b508c336931", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: 948857b0-d5f3-4098-91a2-9f99f950ebcb -> 5da24188-e100-4a57-948d-1b508c336931", "gmt_create": "2025-09-17T17:56:03.896899+08:00", "gmt_modified": "2025-09-17T17:56:03.896899+08:00"}, {"id": 1522, "source_id": "d5cd5afe-72c1-4dc6-8e64-e9709c061d6b", "target_id": "cc299b89-1600-420e-84f5-114ce4c47a50", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: d5cd5afe-72c1-4dc6-8e64-e9709c061d6b -> cc299b89-1600-420e-84f5-114ce4c47a50", "gmt_create": "2025-09-17T17:56:03.89733+08:00", "gmt_modified": "2025-09-17T17:56:03.89733+08:00"}, {"id": 1523, "source_id": "d5cd5afe-72c1-4dc6-8e64-e9709c061d6b", "target_id": "079c59f7-f623-4f18-a490-5eeaf6777e5b", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: d5cd5afe-72c1-4dc6-8e64-e9709c061d6b -> 079c59f7-f623-4f18-a490-5eeaf6777e5b", "gmt_create": "2025-09-17T17:56:03.897811+08:00", "gmt_modified": "2025-09-17T17:56:03.897811+08:00"}, {"id": 1524, "source_id": "d5cd5afe-72c1-4dc6-8e64-e9709c061d6b", "target_id": "56e8e822-4c90-4f12-bd1b-f19b908725c1", "source_type": "WIKI_ITEM", "target_type": "WIKI_ITEM", "relationship_type": "PARENT_CHILD", "extra": "Wiki parent-child relationship: d5cd5afe-72c1-4dc6-8e64-e9709c061d6b -> 56e8e822-4c90-4f12-bd1b-f19b908725c1", "gmt_create": "2025-09-17T17:56:03.898299+08:00", "gmt_modified": "2025-09-17T17:56:03.898299+08:00"}], "wiki_catalogs": [{"id": "c6ee37c4-9b2c-4cb9-ab24-5a4456d3894b", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "项目概述", "description": "project-overview", "prompt": "创建关于usercenter-service-group项目的全面概述文档。解释该项目作为爱用科技用户中心服务的核心定位，描述其在多平台用户统一管理、认证授权、账户生命周期管理中的关键作用。阐述项目采用的Spring Boot微服务架构、Maven多模块组织方式，以及与淘宝、京东、拼多多、抖音等电商平台的集成能力。说明系统整体架构层次：uac-api（接口定义）、uac-service（业务逻辑）、uac-db-common（数据访问）、uac-common（公共组件）等模块的职责划分和依赖关系。结合代码结构说明服务间通信机制（如RPC、RocketMQ事件驱动）、缓存策略（Redis）和配置管理（Apollo）。为初学者提供清晰的项目全景图，同时为高级开发者提供架构决策背景。", "parent_id": "", "order": 0, "progress_status": "completed", "dependent_files": "README.md,pom.xml", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:06:38.909103+08:00", "gmt_modified": "2025-09-17T17:10:32.597187+08:00", "raw_data": "WikiEncrypted:0MI1/XkBoMl0lTbK6t0Cn/+8FdvqrJ62ianMLvZj02elJtBUgH0Ns0veIx1WPtM9wwkxI+/XAqHLxJLlHB8wV7ojrH0+b4JMz5OewwhBN1GL9RFom5fXvY6NZobmRhl0ELAnAxHoQT753wZLMVeib3zj+TgwWyL5d8MQK1zbKOqH8q9OBzlgOrGETCa1fOIaarSvvzi3NIJLRQsd5PVvS2TL8bA0jjbGRZwwfIRS46aw/zNM1GUnw4MJ90pYILlCz0DpPUlbmW1E82fef9xbQS3KZ6+iYFN3/JXQfqYubXEZAFKEi4LLSWXxzPhOiMnln8KiSiR/mNx1bYbVqEaLJaj2+TxHO77+O8p+ouv7ZxKsJc+RblfE9r/B5Ioqj3TBKH+T5HTFMaPBN7vJtXLx1IjlhOzH3PuUbjp+vk6fkOqAl1opMbTMzt/Q/4PI2nNEbxrf8LU7bTFJk+8XtLTmJm8bm01ZTTSy48za/lUWuOjpLsHKdMdgnVxbaGnBApQxuufJQ+JwU7lpfC8shKL1pBk1L2QWibsO7qpbwwwEted1wNcdqhSkxtp3NQt/++4MD7ZGMn18MVgJ0sB4uaZXvvJASYc38mE+HzCTjHnUMYdfGfRcUx+bN27jrkTUX7eHe7HXXqdPLyhycLO8Idm0lBlc36/PgUJ5kGODadoUU5lilwNbFVna8WEEDbSMpaK6Y58gEMH7zF06lfbfwBd0sCalZsyViDaIh+I7MsVAnGMHs896T776Ko7g392ypUxZY4Y0/DlZWKw23OQBrL5nk4DtuYdXn/b6kbbtSp+AqhvcPnczwAJwB9s4ZMakILr8H960fIgE+ldQEetmQB5HJZP2x1c0Z6jbxA17JEb8/elF3rCHSJjTgoiS/Ydj4UOyDCRpvvCgd+MwHA8gJhB+oI3/8B/nFHtsnEUqVHXqUpfdKNEprMLJR9JlwTSPhkohj1ykZSxiUBkdBsrGE0oy0k/k2S2N2SBq2oba/b1Ju8f5nc9KebZB1WI8uxB8aHQTNonlbtHG+3iVqjHrwEfZwWTL+6u064QQTBEpfoo4bC87qoj+Pj4JNw9ezNJKHbWLCU5ri2/P+ndzcEoFn3nMqRMhhAWztrkNG8WIkc1ojdWSFtLBDQfrL/GGY4Y/9+fe0GSLEeTtzme5NPdHCdurP4shIAibTjUZXENwrtgoB01Lw4z28JTdVD+BXNWliiyjBKbvTu+ybjUmAqDx4/pnSH11womP3hY1RXFcfi0OAQIurqju7tcflu13Kd/oacn1HkWzV+HZWMErm5VIehpIjrIEmmUbmj7rJW52EIhDeXobzvrtA6Yl5sajdHY6S3QvDBqUTG3aD3v+DbsZQv9BWhXgQ/I7rOE+Z0MgBxA6P8gyjvVxzvy/WHsdAebeTedb", "layer_level": 0}, {"id": "cec3aeef-d7fa-4435-b007-54a9231d0f79", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "用户管理API", "description": "user-management-api", "prompt": "创建用户管理API的详细文档，聚焦于用户信息查询、用户设置管理等核心功能。详细说明BatchGetUserFullInfoRequest、BatchGetUserCacheInfoRequest等批量查询接口的请求参数、响应结构和使用场景。解释UserSettingCopyRequest、BatchSettingUpdateRequest等设置管理接口的调用方式和数据格式。提供实际的HTTP请求示例（包括请求头、请求体）和响应示例。说明接口的分页策略、过滤条件和性能优化建议。结合UserController中的实现，解释接口的处理流程和异常处理机制。", "parent_id": "21f95d6f-85b1-496a-9f5c-1fbcb3015ee0", "order": 0, "progress_status": "completed", "dependent_files": "uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserFullInfoRequest.java,uac-api/src/main/java/cn/loveapp/uac/response/UserFullInfoResponse.java,uac-api/src/main/java/cn/loveapp/uac/request/UserSettingCopyRequest.java,uac-api/src/main/java/cn/loveapp/uac/response/BatchUsersSettingGetResponse.java,uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:06:52.897325+08:00", "gmt_modified": "2025-09-17T17:17:57.66803+08:00", "raw_data": "WikiEncrypted:piKNT601qxwAxFbFc+bbN/slW9oXRgJ2J5v1hHOGyO8lEKylDEeChcYMMeScFuNyAVFbc45Irlrs6HhbR2BGe6tVKkE9AXRt/9FqzNcJPZjiNq5TT86gpknPZRbN9L3llBFvjQ5tUvA9urnobaUTEgTLqYtXgIk0WK9xRZJomr5OJlCDdaw8979KkwMYs8xnPiUYwsJgN+JAaMbseL3lZi9KMK1+od7sP5payXpS58YsUdCSIsvN9+XGlHMYwv3p0aPaRPQNoEpwPWTTKRIIRURR09RnXmHn+tMOdR0171EQfujGvy/sThsOc2WmOcowb3RB8sut9Is1GUrthV1eXYcrW/y+a3yH3hloVjXh5MSecIf4nDNMMOijb7clLaHliGO5YRX7aKEJQ7BkZKsVQI53rm5+0LEzXyj1KbwyaBE+DTQuArgZPlGAdb/ZUG2kyzpsdd1R8a3VPfttMc9WPDevdggoGv2TFCpfnatJBOdyN/GPoI3R9b3w4+82nZHN/1R2ohiglTOr4JFwLaL5jBNCREobdZ8S5+X42Eo9th4VEF8Ua3CjroOldtptIq0B0C6XYkcE8qRzJP5cabvVXzz9z2jxcbGqgRhUtdRXm59hHhJJ7QvB+wp2olm+5f8zCR2837EBY48w6H0Clq8Y7+IvQHpMgTVP8di7rzlGMCyNUWAyoJ2Cikj7PPMzh7FwflWL5bX1KxdujtfpxPFBQ4pI/OfU4+SNenSX6T8cfqKdz4nZx5Zqa4n3YSiA/ok5j+GwVYtkgAfCikvZZw6h7hX56Xh76wW4wYQpPVbrWTlvOabHLcbO3YKLNYAuy3z61Nq2B1m04gWG8pcFOwn7XUNXUQ2EQ1IbhpAc4HSP3K1tdFFYbRtrP4oUoVmbNTUEOzPwnmpzmJ7apEd5s/wh4UUQOM2sS/N6HY/eqFZkML9ApzstItF/mK+19O9snO0OzXqi5t98kZ5Y3RfNPffzLu0YPvCwT47866O9SUy3+ND/4+3wyOdWowkPwdy4Idxu7Q37+KC8RVFoN2bqzXTOT5zeNmEo7VmzpL6V8L0TYxsR6WAl1LxZsB0GufRle4F0Sd5ibA74lZV7FsNis+JtOM/5o7eNUl1CRiutcGgVKTPfbh7IP3OP1GHSXJgxuoeT9IQGVhMumO3PPfOvqjkRPRsnQSnK9sWqK4FFmLim7eKgpLVB+C0xXqNfAfbCxnQeDFkKwsAqzVLKsaculcI0+8QRdUf//+MJ0ZWIlMjETF1sf0BvdNp+aELhmF6BRkohp8xQ2wvISQEYJtq7QjCs11KCUYyFJaB12HuB/N4yoWuevTUFkdDd03DIWGoZ3IYxsjMjbcePofP+J8AAX9n/XnDwueNsUXIpKQawOdGozlkJSuHMCqmnZD5dvG9gqOiOsPPbcOJKrm7lsigS16QtmJ2fbGu0+h5QnFLIGdQZ7vek2VLMtHnWaEZ7WQqeguBUhZ+gtrbWffHcjhX0fPsfLX8XvC6ecXmVKK0d0EATQsAk6STsiMkpvvupies+4iiDe/TwBrgLNa3GzMa0MCBwylLM3wyPIdUK8I8nnqF9l6rwqb8/bnEh73f8FEOwhwFz0MB6JDkmHnPU1lVT9Bz7hyUaT8BIw2ZaSj/YulDpDY1SZe8+mCa6f2ltmvBjHrKolQCS+ngRmrqwHt+PwD9tjJ4oTiRbvlkDB+XHkncqaUw=", "layer_level": 1}, {"id": "77188613-0ed6-4eaa-9f3f-d3be974a3939", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "uac-api模块", "description": "uac-api-module", "prompt": "深入分析uac-api模块作为用户中心服务的API契约层的设计与实现。详细说明该模块如何定义统一的请求（request）和响应（response）数据传输对象（DTO），包括UserInfoRequest、BatchGetUserFullInfoRequest等请求类和UserInfoResponse、UserFullInfoResponse等响应类的结构设计。阐述其在微服务架构中的作用——作为服务提供方与消费方之间的接口契约，确保前后端及服务间通信的一致性。解释领域对象（domain）如UserExtInfoDTO、UserSettingDTO的设计原则，以及服务接口（service包下）如UserCenterInnerApiService如何定义远程调用方法。同时说明异常体系（exception包）中BaseException和UserException的层次结构及其在跨服务调用中的序列化与反序列化处理。提供实际接口使用示例，并讨论该模块如何支持多平台适配和版本兼容性。", "parent_id": "84196b87-4eae-49d1-98d3-e8f06be3b022", "order": 0, "progress_status": "completed", "dependent_files": "uac-api/src/main/java/cn/loveapp/uac/request,uac-api/src/main/java/cn/loveapp/uac/response,uac-api/src/main/java/cn/loveapp/uac/service,uac-api/src/main/java/cn/loveapp/uac/domain,uac-api/src/main/java/cn/loveapp/uac/exception", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:07:11.365625+08:00", "gmt_modified": "2025-09-17T17:18:34.37283+08:00", "raw_data": "WikiEncrypted:QlajxxYCAOa4DyYhd1iD82nBQ3R63B5yNy84H3xIStPq3iYiIK3phpPSuP2hXzWZtUE608NdAdbzEowp7rvVQVZbQv8lakplrJKBqod5dlaVnz7fxIRQKficH1XuSJv/PD2d2ZJjjaGHYUg/FNzQrsID9gIostuP3VuTYlEzBzkAH/GoGAs5HBZM28jGlW4SWGAFuGMJ/GV9hIhYRmRXB1SmEsMTWd8NZAkqHKUEYHXmlPoezsrAdKsRrYxjVSdg+mLi7Zf23z4uAvW96GgeqJwrKrltuNZrG7QVlu4Z2gzbJttnZdZrQDsKdNtZbjLZarh4R4WqZY5FDQeuqDmz6r8Rcc1+xqbsotQevAbS2TBTJ130eIeRmCX1vPnboIucb9qvlAN+y0HazDfN5mJfvE54FRWTpkBRBXVTq+x54jfH5wc55PrJCN7H/qebIb9LJveC6itHQd2OjXTtZ+Q1LtwZj65tLbodeJhmJyH6W0JZew9repbM7c23vyOxUEXwjPsDEfZgh0oz1TX90B9nd/8Hs6AKI2FZ5Fi01+O2iBfm4haeFxoExzXZX+nNgSe95hnnZE7u/wrDfJflub9ruDPsxuatb8bx+83C7EkQL4EbXQBIVdph/OUYf3RaDozYIBTWlUiAHcRHMWx5h8y5/9wVXBBcd2hxng0csrdio5vJteUBGYr5ZdTeOZQLI+xGxIYBtdSkf4XZ85FRm5lteRxFJoJrUNa0IHfpXZKYvZHV13mdYPOPx/l9pux3FzoI6GXP2IqPaKuoLHPeiCofIcBDPKwBhDQK/omA3otByDRWGyPpnLc/HWLNHqVs146G5DELzOaEp3OVbw8irOLNCuY7I7L5Tl+xbvERlgfHcjffdyB+7txZuhxE+DjOZiKzKoIuQWrWAJITmPwJ/Zb0n29H2trlhkGDxRfQdaAITJcvHK86est66UwuYP2JSEg8TuNwwBPI4cASn2xKHOGgGm2JLo7ZjzLJtzcnK9AVhKw2SMqwnDnLMlyK/9nNqSUwH42TE/LLk2kESOi7UjBiKxJdUwEBZ5h71kzzft9aV8JA9ekAG9ccnZJJsrN9NNYYk67012z7a4n0iZu2JTSsbfvhpudUYyveHPcev6tdAoGu6mGHDE3YvuVNGM7b63mfqccMz68BrSZjwyoK62Qt/9XA/erMfUSbIur/U2C6QPeSjz/fVOFpRNcC0O6ZCIF8MfH0SiPFaZZFE1G2VpE4SOGpmV+Qy6AGk28nY/h3JHSdqSCZMvR83MAoQX0KCzV/sf5kcp7DruJkVmPy4FM3apFtY/OGlDtTwPArkFwFAEbwsO4Sl5pUm4nBL0r9OzudPgUI/QTFRZc7ybvzspVLk+tmf2naqH3vTy6m8x0D8U5mgHZ8fP1OVILVkppKRUduGB67IpK4lB1cd/150Nm1ts6T4CLdTxZJJ3TzPko7WkSC1QgBwTv8pYcYH9keNlSbIM80F5AaAAPXlw92lTEoK2xH3xtsOTG1gK2QcYPHjQmq1aMWZ7yTMpRguRlI/ZFOoSCLZnY7DYwWbvVNjryKNoTSOEL2Zv/TjNRGQmL0ylnaLYTaC5dMtcoIbpixyKGOsvn8YEAIEdZMbthw6micyceQhbQZt7vid71gB2GR2yK6NO0eyO7aFhQzcOT2Z54klwMCfCe5GdtpMyToBatEAcTl9ZIy3wMeaC2OHJGhMcE+6xrbNC5sqIz4pyvN6dw23vkZq6T68Tvh/TtCKnKroSrzAUppPc/p7WaMK1oW7E3lrNclabqBCtFrDOGwkkYsCiPiLu8mt8URLV8MKCOsAB1uFe/L7UaNj2e+BxIEXYqaovM1ihyHzSMybZkh/6q45AIRKYWmrsfxiWP5VTmk6yiHs81A6EfEu/9hEVVG68Jxji8ZYp9Y6s//V7ATQsAolC2r2DikYnwq7w/op3XSFvOxhNvt0pKV21dT2pko0sR4b12DB9N23zyZ0nyEZkex", "layer_level": 1}, {"id": "f48da353-a1b9-4f38-8e40-abc721e82e2c", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "用户核心信息模型", "description": "user-core-info", "prompt": "创建用户核心信息模型的详细文档，重点描述AyBusinessOpenUser、UserTaobaoSellerinfo等实体。详细说明用户ID、店铺信息、认证状态、创建时间等关键字段的定义和业务含义。解释主键生成策略和索引设计，特别是针对open_user_id和shop_id的查询优化。结合BaseAyOpenUserDao.xml中的SQL映射，说明用户信息的增删改查操作逻辑。提供实体关系图，展示核心用户信息与其他模块（如授权、订单）的关联关系。包含实际使用示例，如如何通过DAO接口查询用户基本信息。", "parent_id": "103edcac-ed4f-4fff-b35f-73e2fafc3279", "order": 0, "progress_status": "completed", "dependent_files": "uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/AyBusinessOpenUser.java,uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserTaobaoSellerinfo.java,uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserAlipayAccountInfo.java,uac-db-common/src/main/resources/mapper/BaseAyOpenUserDao.xml,uac-db-common/src/main/resources/mapper/UserTaobaoSellerinfoDao.xml", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:07:17.41074+08:00", "gmt_modified": "2025-09-17T17:19:08.108255+08:00", "raw_data": "WikiEncrypted:avus8+fk7Mk/OhQ++5qWitb2c0NQKA8gIETqo5+VDRR7lMUUdgCg/6jvUHMwyv+txmPiHKLF1pin/luaKEJHl3AZkRpwvtyARxf0/K2CDvyzmtrFU81lJVpjdwilhoiloYYahb50u5Moo+jZvrnXhIF39G6Z+RJvGbBF0tBOoa0mS8P2Yjub2AqBqjZkJQelK3k3saSVAYLWs2h40NQKjjPl9+hoZTbg51WO6NIlos7MKEEh3FQoHdrl2CL58bFg0mJ3sLThRItxTFWNN2cMzNUyrVAOOy/MP90Rx7ZSplG3n16gWII9KhZ8MAUmF6WeFQIGJzWYLll0sk8KHMtxOku3PnBxtYE5qWa6QGUg/mIxNVny5WTK8LvQ8wcJ6qgHnztiqnGllykjkxD/0e3L3fEAeeNrX4Y8cwm1nwEB2iMp9e9sjEf8wPO9sMi34mydVUTvzqEs7qV1gWuPzRCDwRIy53iTyyFH2a3Xr8pNPXNWyUsTRVVsITCvSmDbrE5Tt6aH4C3I1hgvPPZyXTXbpmZAlsCdUJs8ZcvWjwQqZSR6bFlWu5c8WrNqgxsLJucAla+UCCNd1nZcOpdlA9bZl9m+bZSs7a6igkbQ2LgpIvKnhOw8MffFGV6ck66cQrFSjAvQm77DfXa22vZJV7n36N+Pl/TGNN7RZCmCIm4AkyRBPYA6HT8wwrgwDTVmqMcsrjpNYL7TEqXc+TE67g8rIRa3172UXClB1OTpKi4DHqR/HwtGJ33f3zKaAqZQh05qnK+oNTJg6iRMCRZwOiXAH0S2JDdjOOcNi3NCYQVkzG2ioj5G9qT5mTW25vHIUYdL1EJFguM7Hle99sGKrj1i9R7EiVOyKOMp/D8UrFSSIaD0I3N5YIuBmMesbU+qfEKl+PHlIRKp2fC4yjK9SNH+Lz2vWmLdb3vO0+YxNtfnons6si6Hg6WIxhfdxteQFhMl+QmYDClkP5gIKQbuzEnj4XA5mXx6TOoha9flaZlAN1+lBvEC5giFWRmviAIUCKVKbhG5x5YAQjsqbF6gxqXQr74rfz6MARL3Co5uQ+RiPSbZAvmU8FW9dba8l+eHsk7dDBdu+ryIQJ0tFDcTXUok8PVOv9lEWz5w66TVFkNbWRCagiDU3pnWYB1Fy2HWme3cXuiG1ldltBdNifYmGpQPVjKK5v6JeEyC4iiPTeMXIFS1em2E94usiuIL4xIHF1IhyTIBPip4T3Sm1IxaeYK3YlSMTuL72c4o0MQ83Z5z93jb92AtjmF+stCQjTO6li2hV32+uW/2IP4Xv5B6QhEZn0jFmZ9UBD/SjFfnXdECP/eYrNITfQ+PfH7fGaI5p7/zxRndZsCqPaPmG74lGIJ4Zf8RMTL+aIbeGN7I5iQWO+ZnaYzi/0F1ufeMxER+oak1mDZZblPQ2m9S9VvDs+fZDoSzO5SRd6MjBgaTDoWDPUsu5IwdxuzjIO1XbzsiHqxV/N6WM6WUmH17CDHphgqufm0btcsAMz9BWgn4LKndCUaoR3ciXAKKvaA+PzpNcl3a0ETMiagI2cVrWeFdwJw5radtXwSpDbk7tIMlUsYP3bq5x9ihYvaLUlNZ49k+KrdSEusppXWcABKmdT7HgTV6OCHrGpFHguL9VnDABCcu6de77OXxdmEWF46pnL5hKwySNCkwgvXODgDYKQ8ZuxW/lPpPWFbla8F+nLhnOQyL8No=", "layer_level": 1}, {"id": "082c46c4-157b-48e3-b828-14eaa03bd875", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "Token刷新任务", "description": "token-refresh-task", "prompt": "创建关于RefreshAccessTokenTask的详细文档。解释该任务的核心功能是定期检查并刷新即将过期的第三方平台（如淘宝、京东、拼多多等）的授权Token，防止因Token失效导致服务中断。详细描述任务的调度配置，包括Cron表达式、执行周期和线程池配置。说明任务的执行流程：从数据库或缓存中获取需要刷新的用户授权信息，调用各平台的AuthService实现进行Token刷新，更新本地存储并处理刷新结果。文档化异常处理机制，包括网络异常、平台接口限流、刷新失败后的重试策略。提供性能优化建议，如批量处理、并发控制和监控指标（如刷新成功率、耗时统计）。结合代码示例说明如何扩展支持新的平台。", "parent_id": "11640edb-f5f8-4737-a6af-0ab226c39804", "order": 0, "progress_status": "completed", "dependent_files": "uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java,uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/config/RefreshAccessTokenTaskConfig.java,uac-job/uac-authorization-job/src/main/resources/application.properties", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:07:28.463325+08:00", "gmt_modified": "2025-09-17T17:19:59.09643+08:00", "raw_data": "WikiEncrypted:BNQwBTrLoJXpxFN0JfaqoPZB+uDFb0IL/TfrfE2oMZxvXxl3TuEZIUdGU6XwWAyo7raVDKR/j4Gi/ulr39E5kBt/km7sdervEYUVbgRkVYN6OSDr8io+v+kD2lpjO4YnISYY1Va8fQVSpaXNPj2KcLfM0ooC8q7/Q7yoGiD0MrXeCxSrBgWvMGQJa8r1mjVeZDRoLbD3fAJlZtJciRZvfuoRloVVjbQxEvluipbT5s1BeVflpvFR8Ypt/qY13+tUpZybNqoGOPUQGZ5JkCdoAarsgYW15NQTH/a09K/FZ21wrS95N84W8jIiTVVoxkek/S+K0PuzjHfKe/K+23ycs7TOdT6J80SYdukIWROM8hSC75DI5E1Ykn+ETNWZDGA+VIBeZZ7zvSFqpL2NRIpgEAdHmghLDASFsD+co1684EKN4vpXNRoM7q5LvbDR8jlOB3yFZnUEbZCTpo1PoRnBSJZ9LmG6NC0GRKLzVREwlPKwoQjckNBNiGn9wT0GWDt9KIXU84E6wLnAaqQryif2hF1s20RV/XY8hxmxzfgdxf51Z5MgIgfksoonzjGdPFRcHl2RXCdD/uDsQBTi0JlFe2iFf8xgyzBxThT0RZow3Zl7pTMYZvFPgz2/wP7BNtwnUhuFFo0pWt2lDTBRcej0QH1NMuRFFHLsVFWyjGLf7AwjKsqo1/UtjUcss5OTbk1gc3OVIpf9kStvEPu1d2UQ8/Gfe4t/yGrmQzV+L7vLfV17T0Y5Y7dQ3PIIZuVZrW0NNxubtFrhrllr/GAtqVwiJQTx8ErJkDrwuGfqQ47gJUJxY5h+4BqZyVMIqkHUzEapmLg+05SRGmF8cscFEzTAC88vMIhzkafIxcSbpo2g8m2TAjHg8P8fXUOoXZ35k0s4DFGX7HWK1dfT/DY2PkLNzWFUNcNCYX6yw+Tl9DPo+cfkim6AxiIoknn/HJrXqxbcdbECnOFw82PwctjBWhyEU5Pu5/b1i8USbeuGrg0vthBtWLrG3ZO3vt4jhvOCuZ8VD9RyFbkdD25yA2/fn/Eq3J5josiItq6tS55RgYnLPjESHqiwxsWqSh1/jvSj+z7sZoGY6v5/W4Cc8/se7SxS9jGX9VgwB7+Ybsksi07TuwJuOS5Z+EIfknAkH/dgzT8z20A3BTbVhlW1p3/QRtLw4sRmWxwNfwTCmFONmEhT2S89KiQaYEQlf9Ks8kByQqEwCqRyE8jSfvX2juo1kwIAKqBUAjOBsbMNuTa6beRKqld+l4Gq++r6zvHTSV82ub/AEXptbob0LfoVkDdwwgDtPmdk/QkQ7VygzazKq+vhROPtGc/aJg9JPUqt6s74IoF5E4FkNYeapXRT6rTeHba50yX53iJcjXHtCVEqqOBy5pp9taeMDZVwMCVUam30C+eYoGKGO5xQ1Ec8i8nNM6KeCEpOATl9PEI2IrJB3u7ARBuoSmeXiiCnzFwGSuZHjQ5zPm8959cD3sx6sBnISSZbIIZIrf2YtV4kFOidS2p8dCKhqAX0yN8UVqLGF1e15GcFZASuQZ45y7lT4JLm5qaznJ8LDwIamCmbXtDFxOWQTm5WbzOA3awOrAzOpgc+VWqrrDnB+TGT4I3rWgnMZICaZFQau+bEn7WLOtIXzkBAXqNPIYpGHBPRhA1SEfyW71+YiUz+tvRqm77ze7tqCAmpCmVxuhTDKA/HXxXqx8DyqfmJ54HII9lrGKpa1EX6Xwr+pL6M/momrrmhsUwoRpxv3zBCQFlrCJ7rUycHA5ejSU8=", "layer_level": 1}, {"id": "61b178e3-66ba-49d0-8907-1d2633827fd2", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "用户信息查询API", "description": "user-info-query-api", "prompt": "创建用户信息查询API的详细文档，聚焦于BatchGetUserFullInfoRequest和BatchGetUserCacheInfoRequest两个核心接口。详细说明每个接口的HTTP方法、URL路径、请求头要求（如认证信息）、请求参数（包括必填/可选字段、数据类型、格式约束）和响应结构（包含成功和错误响应格式）。提供实际的JSON请求和响应示例，展示不同查询条件下的调用方式。结合UserController中的实现逻辑，解释接口如何处理批量请求、缓存策略的应用以及错误码的返回机制。说明接口的性能特点、调用频率限制和最佳实践。", "parent_id": "cec3aeef-d7fa-4435-b007-54a9231d0f79", "order": 0, "progress_status": "completed", "dependent_files": "uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserFullInfoRequest.java,uac-api/src/main/java/cn/loveapp/uac/response/UserFullInfoResponse.java,uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserCacheInfoRequest.java,uac-api/src/main/java/cn/loveapp/uac/response/UserCacheInfoResponse.java,uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:07:36.953253+08:00", "gmt_modified": "2025-09-17T17:31:58.635901+08:00", "raw_data": "WikiEncrypted:eFStdOOCJ/uTWtXKsz8g6oogmDsrfunaZuFyB7VW8GGx2QYc5cUcn+QTFz+Fz+dV1xjDhKggvaGbSDX4B9U9iZWZYCvy2KE4OmJhbfhDlwrdgXJ000mFiiT7oWkmK1dProkZQmvQVN+NgJd+0R9EVB7SNW7Bra3MjkyRmsM3XS78+uoPOotw1e15KULVrdrXMOzBU/n4mnVYgNWij2pKxc1XZJv6FRR+tQRSMDcXRhFO3VrC4fWlZ4QWQzg+hFD2nAIZcdTPcow4JNUd4t+K0uHiB8o5eLepwe+xAV/RGzw0OrxYekk4SQZNysRs2Hj8rYvorbik16hWKqzI8DgdWDeUb98+cVgxzNex5OsfGht3o0o12dZ2wlCTgUvVIok2befpIjCwraUD6lfjAg2XoEalCZkegO3iqi0hduu3HtArcDAEekq64CnNZvlVtC97qxjAziD0gAzwTqq2a5vIoLFpbxlKSIoV7RW/i5TpiK/EisxcDiQA8DKxaNtzxvZ9lNg1Svs1sT54aLiOqEhP88y9G2f12cjKDF3tex/T/imKiX79G9HnkWoh6pJXFIMxtGr1XF8J2z7xOduwnWnC2SkLLnk1VmVYRzO8wbfOKskl4lfY332sAHzWH0wE6eAqSJiK+nKZCklLBQxwSp04J9Y/rqE07E2iWlUb62k2MJu0NO0MifeWGiRQruO2XorK04/S9AOSA6v1Mev39g7tY5DetdeCNBB8i8983r+9i7tvRBmptyjRX+D42mbhVZraR74+3Z6001I0qh0nlDrxSQpMeskMVd6Rev/HRFN1Y5PVqznv/NYuDKq/YNDlBzyuQLZmZqvEnpKH4l0lXl3Ut9FD9h7fV+soVOODonYxj4rT2ztfBzCu7qqvEUEP65AWhN+qSNHZfWajhDzptOIMRLAKnycmalcnFl3MXpH01c411i5qCGt0ky0NP5Vkt0Yh+TV6umFbM210jNOs4WdHiWhOqpPwgbxB7F8kyh16SyyshnE7MJ/P9KQ+Z8gbnF+nAP7w1NX98LMoGju3Dx72eyB9MLJ1OSU6hri4iO+kMs9pkvwAztdXjZz2ZmUduiZcWFf8VUj168dosV9EnuXTFfkP5VRx0k3yeXFob+XIvW6wJ/sHWsh107+BPuoyvGCzk+QM4q1Z0qXQ3lydsP0JRJ4UfClpigPbYfUMxg4QqIcNCcxRZu0M2QRry2cXszf4jvxvcCGlA4BCfXfxpxReKSk4JYUT7Ha0b2F339lcqOILULxUHNmpXng1bcD7hYdVFhVJc13s+alX/EABJk99QxW7drnXJqBXyfMlgQtgR/KxYFpGCFhDuKDH7AEHNc9bPgyZaPZwBY8KoFw/JvwkTooG+v1CxlCwlYpH04ZP2ZXkEvcAM5e9OO+lxrNx10URisl3cycKdbLty5ew3c3lO9WKtZOaP02QnlMR7pY0tLUjEsFdMo80S2QJ6z9Xsw7nsBu1DbOqcWYyNphDSLrgNR7Hkjx1x/+0yXOgMONDh6gF0s10huyrYB1JPQDVLkO1cXS7gY6BJ6GgIYr7qCYBD7o/To/eQZnyVS5DZjlwgQSnjoB3f2P4NTF+7ZJVzsQdwr1B0gOqQgf2Gza/jsbCTEheXsR9OboN+29VJ14iQGluLWHGatuFtnyb5fRlbC9p2J1LSeMHVery7u9mqT13k1QLO3Ln/JU6i5ZK5Nq3mswVewfvsMeY266elTIG0vBmIfSqMW5IlezCPVj1PpP0OMwp41c9a6fuBivckDno3Kw=", "layer_level": 2}, {"id": "6692730f-9a40-41a5-b8b3-3dd1a8e4e6e1", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "用户认证与授权", "description": "user-authentication-authorization", "prompt": "深入解析用户认证与授权流程的实现机制。详细说明OAuth2.0协议在本系统中的具体应用，包括用户登录请求的处理（UserController）、认证服务的平台适配实现（如TaoAuthServiceImpl、PddAuthServiceImpl等）、Token的生成与存储策略。重点阐述定时任务RefreshAccessTokenTask如何自动刷新即将过期的访问令牌，保障服务连续性。结合代码示例说明认证拦截器、Token校验逻辑及异常处理流程。提供完整的认证时序图，解释各组件间的调用关系，并列出常见认证失败场景及其排查方法。", "parent_id": "484d4aea-c0df-44d0-9b9e-c951ee96a3dd", "order": 0, "progress_status": "completed", "dependent_files": "uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java,uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java,uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java,uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java,uac-service-common/src/main/java/cn/loveapp/uac/service/base/BaseOAuthService.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:07:38.43197+08:00", "gmt_modified": "2025-09-17T17:20:40.370276+08:00", "raw_data": "WikiEncrypted:SmmNTSq96N8aZksVSKE+80BguGnK89evWQ9a0gLD1iUUzGxQF1Ul50HirPgPehz/Px3cMjUN6yVIM2T8+6hxUG61OAURGnj9UW6FMU7n8p8DLR1erwKxwTsOrE+SmsCFqVNC8OuE8GjK4vuGWir+8Jh19GOId8nh8llZ2f/q6B0SYMiMaotF8ul15LPgprHdJtASPGnDsXUKeTFD8Ik6Bfwk+g4/h+X11/Xx7urOfBbJvQyXYyGTSUQRunbevqAPBWYJZVjEiijllDHlL8vTEKKAXOHnwT7UJG0KKYIlJV1FsBLtdXEDsdotj5Y1N2odj0ri3a4eQOp3eTp87dUcSzS9nbFUD9moYiB64LjKMkOQX9Y7Qm7MKmm3yKuHBq1V6/Ftnt3e7MQO+2ax1tgKXhQs2mjNfFAFs1OxwblI545wSfHtkYmVfwRbDm1NAIxAsRtCUx34tTsDbjGmTLNJxiuPkWpkK65nSfiVuluNgJFrSU7qqvNQKkB5CAuYKzhW6vcF1OCQA/ka022p9E861f5xo6R7BxHdi8/PBQpAahOOn0L+vv7pnPyw2fDxSehpjudKvsUbULeQ9Tyt10AoNdt3dW+XbkE9u7F5GlNUUVII4yxTxYT/ys9qRZw2HkUR7q0Vtn/KPCn7M5AC1++RvJJuSL4ZJvWQP6BhzAq8yDUOd+YamDFVdyZkJBIFnLqkt8lmwj8JbqMazDOnU9kPBhAUBH8YOFH/0gpUUPQYPSK7O9DbC3xlwWbuTt96dDem0zqogufb+0U9vboFRV2GItLbxusZbUSdVWrrhBOILKIoeNMXBPGSLIZwFWieKzf22D7kgxhPgZc0Ut7K0hltr8xnqLkZbqkgaTFdpLL/UeUDLIfojxNcNCsw28qI/ht+DfDGnBovpoVFM9f/OcUGczPrAeoV1r3HMx/17Uveeody2vHAEuueb10i05KB0lQnq31cWxyfpzSC/G4IKRKogCeT8H9C3/JuYs8jXrI9J2Iyv/NyS7u214jtAdbFz/CdjX2Wmcq7crSY4DvX1PfjYw32Oyhnbnkj49XMWtlSkcslJ1a/AH4f10hMOrRCou//2CUgYNLJj3cp0rq2iMkOxVv5tshgChvZt63mO++l6n7oP1NXJXty4YSBgC9/HfkdB6IP/6B8yQISkJ0X00ToWQVNkjIn7l/joV5G0gWg0u3XzynBmvvOyb6vmOzsykz52QDkC9jdN6FDlaJVdkGD+/90Mog0f50SCTIcKH8b/mkFFYhXhhFmyIjVMPABtsdfT+K0iL7ZM5SV+rko4kcDpvudFovOPpk40TKyQ2B1zZtcGvADRdjM+hdcZ013p6F/nNUW/RBSJM2pteluAMzZgyb2BxVUZwoFEseP5v/4ukNXqYaap1RVd1AFOIdmstu/e84KyPXVx54+oTrrDzE414PHuIXs7gizWpISb3IEo65r+EECH3bZD98RJAXM503rfHYDO/+we0Si1fqshlv9PrKh+6N2uvrMkLY9N1GzzyderjwvDexkwpj5ntSY+z9s+ZcuILrxJyP1gev9GrBMxnbpFOKq1Q9I9ntL6DwLFl6CAO+3WYVCDoyk16Q7fwX+5IZQvgVoWjN0ONwvG6BbaDQgozDrBjPf6es9VWrdNREarlf9HiECdHOeV8WMWXP1iUM7kP48xFzbHCh9WJ7lwbjbWrxFdbj+6F45Q4yom40Au6tCquY73CJ2UhQXULcl/5tPklpk0fBB9BTiTVI5dM6UQl6fJHx4KA6M3UT9uofddioSaQe3PZuOlsz/iBNoDUhFXuGFoIX5tCGKz2R1Yw==", "layer_level": 1}, {"id": "360d690b-4d6e-4228-a247-41edf79b71f5", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "回调接口规范", "description": "callback-api-specification", "prompt": "创建回调接口规范的详细文档，重点描述CallbackRequest和CallbackResponse的数据结构。详细说明请求参数包括platform（平台类型）、authCode（授权码）、timestamp（时间戳）、sign（签名）等字段的定义和验证规则。解释CallbackController的RESTful接口设计，包括HTTP方法、URL路径、请求/响应格式。提供接口安全性实现细节，如签名生成算法、时间戳有效性验证、重放攻击防护机制。给出标准请求/响应示例和错误处理策略，包括网络超时、参数校验失败等场景的处理方式。", "parent_id": "8ad78008-869e-48a0-ae54-58673875c3bc", "order": 0, "progress_status": "completed", "dependent_files": "uac-api/src/main/java/cn/loveapp/uac/request/CallbackRequest.java,uac-api/src/main/java/cn/loveapp/uac/response/CallbackResponse.java,uac-service/src/main/java/cn/loveapp/uac/service/controller/CallbackController.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:07:45.197657+08:00", "gmt_modified": "2025-09-17T17:32:03.482227+08:00", "raw_data": "WikiEncrypted:ICiVViANlur9hTgF3KdhPNEvai3nny4QAilaz80paKnZZh4AtTpM8zMZTBWkZBP50y/n1vA/cUXGjj9DoDEtk94ruPBj47AyFyf0PGxgmYtpjUqmcutxD6+Q+dv/YnUmjNDZOq6eTkHguDwem65Taemh08Y2TcXRPLh18jBygIw8nBhHdjhmr6TzVhx8N0fQVEYMxr+HJvcqR9JEDmX3izVv1hm1oysCJ/N0IhqZKf5V4qKh0zJRzxiw7gYnK0mwOtDTq7qEW3eUXYuJpqly48I8snz1wKyfuK1itSC/cVPsJ8q6WlUCzTgJdCGFLqQT5PEtfoPbqR7qappD6mlPyRy5uE5qeT8aBhDdFK9PtYp3o9KfEm14EG/LyTAlo3oD+J6g5U1AxNPea4DAiLse2mmyGLxjbJj2kkBl/Mx/Obm1DCg2fT1SeuNxJuue9YjIph7tIWeV0KZAOzycrEw2qZ76pUSF/jq2Lcu2xDlWos/clP1cEfd4S1aXhaUdkfdewikhElfuIgGrntQFu/lYqxLmqGiyCkPB0kwL06tlx0rM+cCl4vARpwQm3sc3yTmMcYlXNpMZPtlJsJ2MLPnGDpbJDj8gDEodd3Nvva3oJbuccAIvSkFLfVvaEXO6l26/7J1KRYcPmzDk6oddFn/VwPQNzvK7gU631ydvXhH5Aja9mxaMsCY4/o15rLQlyNg9LJHhAzZ+WF38cbpUpMGVEn7ti7R1I51r+tPVf8bZAmMneCZKx4e6PLR52E5qx4r3z66QvEaPw4plmo/q63hr+pe2ftXuuKb1eim3dXUkyZWLHVx6vlhALmMKaCL+LwQi0G8+/mjQ6Q2ki8mqdutMjLWnrjV+MtH4C3iA0IndsLpCEOf7UaOGlCT2XehZQoCL2rc8qGxQLkSRgMf1ROkuO9/xahdl8xQb8NEylY0TS1egTJa05HnDtYzINGwd1HeWOVFu+raNrmuvRadwiUJNCrC36Re3E2ibDylW9Ia2di/J4E1K6PKGPp5pPiy4w+JnyiQmh9iiddgrCGogB199k/wtDuk98NI/2Jvza3R0AmO2+219XG/9tHPJoKHBHdiLelQQ5h6Lbl/jRIRx203npr5Zff+7LDW7IhCGXqPJRgIYm68LkszFlrZX8e1XLvtykkQEgNDWhDm3sUv2PanaGYCP0qcpaG3NBsHp3hsHTrckle15xBXeP761ADNQpy7VqRozfH0EouhqUxxFaTyABnZSX7rx9LP0KVf9WnU0LMcZ/m/hlLMQHDNRG/6CKFyswRX6jxAW4uXm+NqHgp1GYGHuo9azhQOB1zN8ADTthQ5RUXTmOgMLWNQeJWHKXXInpfcELw4MkW0UX0s2a75ouScqgZlf9Abfo9jcclS79QJjqZSJg+udV1We1mltrkvA", "layer_level": 2}, {"id": "0a61f2b9-4e69-4e0f-b457-f68f18ecb285", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "数据访问对象（DAO）", "description": "uac-db-common-dao", "prompt": "深入解析uac-db-common模块中DAO层的设计与实现。详细说明BasePlatformUserProductinfoDao、UserProductinfoPddDao、OrderSearchDao等接口如何通过MyBatis的@Select、@Insert等注解或XML映射文件（如UserProductinfoPddDao.xml）定义数据库操作。结合具体SQL语句，分析复杂查询（如多表关联、动态条件）的实现方式。阐述DAO接口与mapper目录下XML文件的命名约定和路径映射机制。解释通用DAO（如BaseAyBusinessOpenUserDao）如何通过泛型和继承实现代码复用。提供实际代码示例，展示从DAO方法调用到SQL执行的完整流程，并讨论其在事务管理、异常处理方面的最佳实践。", "parent_id": "89fda65d-105d-42d5-a727-d1e38d49ba61", "order": 0, "progress_status": "completed", "dependent_files": "uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao,uac-db-common/src/main/resources/mapper", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:01.357733+08:00", "gmt_modified": "2025-09-17T17:33:22.617668+08:00", "raw_data": "WikiEncrypted:dM0MoPE0IfmxFmuMw7gEz/OxNP+EmgmsshpQ25bYJZal0kvV6sDS0E/5kvHzxTyNrGBhbW5mUnhB+cGiqeiApV+J+e0Dcdkt9rZCrYo1ke4gMhgXx26HUe2Bpnne9jk10CLq6upSq2TrHDAfdvzrC5tNAr6rePzSxCB/0uBY9MEWjirbOX7BqIJbee3jAJXhJWWO17JI/PSeFCG4nAA3Nh4uBeFft2bi9hEP/mQDAVsf8aPmLoConskxgZhDidpy78gesLGXXCyW2agAzOKnbD3b1f4dMFe954DrqzjvZ0o6zob/tcA41sEUsd73wMsFwmvwByLh3dnA/a5SwIVd3L5Lt6yrryI7UuHf9UXY2/2IcVRZqq/dnBVx3g2kAI/5xzZoJnmDXRjaowuJ9YsEhugkALjDLlAIAktdpIBxURwa01CHkIRaRo0We/USYFmapofcXUkF59dqYcNoJxw0tK8ASXd+n0+qaPmYSqytntXVY1RcQzMh0HfKJIz4+AOUWxNEBqM+jkTmnBLFGSXBPwrNzwlXljvwgMeAQiIB2e4qFtRFSfb1AwO2HENCCJt76cdp4xSIjO8n35t2JeMiGE0/Z8c5JNLwdwhmXrGkBKmR6vlfNsS6q7HHkZy9Qd6YScvTolgPwwFhbFfdn/mK1ykwWIjymNvGXv3ioFHSY8Xmor5r85m6WPNEyoCVG8Lcx6/0ujv7eqa1tU75SPkRBi5KEoAdFON3cSitdR5tIzYthluiC7Tt2OfBWJ6G5KqTio233wegYDGVOyeCSw+bgEVBflTRKsfcGNMfE3MY6qjIGE+5g9Bmum3LlCVZax7+CXdHUI3FxNpJ+3Wtb5wdUMypAJz1JC0pmEtCGXyTWk2N3+I1RJtnBn6VF6nnFDkwcoQkYTWY8Nge/3Qi3UWUzzmJp9w9A4LtwYZOQmHb9uVoA2mHWjncKNfkp890RBvS+FL3KIV5KwIBm5ow5sPjh9w0EFVsP6pl1nW3jemDHG4sQoPkwG4GtKjslCKfaYtgK44u/Dq7E/GJD86iGIcYfk36BmquWq1JHdqy/UK2aak2ukpo15h6zzEqty1VumAttDjRqwq11ZKAiHvFL4aasS8/brwkxMASQcBK0LhWlMpEfumrJ90zNz7dmWqFDFiYSdVttN1lSAUFyaEk6G3X0Jqb5Wog19Oye0qEg4WfjrZTBwZIPX9rdhM+CilgWq4jwRenY+kKaMYbboBPW9V74Q9ZBpGeTIUtYqX4ZYhxhfK/7oZBhUSHDLsLzzmAkyyq5CKIZh7SitnO1Nio2/oRgU2OlLNHnFR21sHL4lyv/D1Lz4MaXfeADaV4xMWX7QSif+ZCB/q4sduO+jhJdKHn0cQwDTgUXR3oI8Ap1Isj8EzbVIZBn+QjFeF9gpgrN6UakI6+feWTvVJg1md1VtHS0WGyNlttby/etL2ALsi0qKG049vLdeUQMVEF/vC4x4w7", "layer_level": 2}, {"id": "7d20823d-8c4b-4dbb-8c6e-4b27e04e1b79", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "配置管理", "description": "uac-common-config", "prompt": "创建uac-common模块中配置管理的详细文档。深入阐述config包的设计架构与实现细节，重点说明针对淘宝、京东、拼多多、抖音、快手、微信等多电商平台的AppConfig实现（如PDDTradeAppConfig、TaoBaoTradeAppConfig），这些配置类如何通过Spring的@ConfigurationProperties机制实现外部化配置注入。详细描述RedisConfiguration和RocketMQAppConfig等中间件的统一配置方案，包括连接池配置、超时设置、序列化策略等。解释AppConfig基类如何提供统一的配置抽象，以及DistributeConfig、TaskConfiguration等全局任务配置的作用。提供实际代码示例展示配置的定义与使用方式，说明配置热更新机制（如Apollo集成）的实现原理。分析配置分层策略（dev/prod环境）和敏感信息加密处理方案，并给出最佳实践建议。", "parent_id": "5dc01eb8-7e2d-47b2-96e2-f5337e92784e", "order": 0, "progress_status": "completed", "dependent_files": "uac-common/src/main/java/cn/loveapp/uac/common/config/app,uac-common/src/main/java/cn/loveapp/uac/common/config/pdd,uac-common/src/main/java/cn/loveapp/uac/common/config/taobao,uac-common/src/main/java/cn/loveapp/uac/common/config/redis,uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:07.056644+08:00", "gmt_modified": "2025-09-17T17:33:24.756143+08:00", "raw_data": "WikiEncrypted:+As6soyi8z53pneR0AbwL+B4piIcif6O99FlFHsRQpCXiYqOlCBJWFSDX8AkvA2rrePJBvjVhSa1ILP8vQUPX/yMak9AGq6LJhBegdPxWaYIqwU5W5uW/SV/T3git29iMMi50/ZBw0zUIxZwXcDQUZMSAniRqBOizJ9KHSv7h+CFpeLdqkdEd+7dvPgpaTAw3dhXr32Fnnwb1mA5qroE5XpNIvn7nSYJ0COMc8546M3G8Dv58efvznM0dra/8QHpSCxoWGTnnbFun0xChEdp7RDo1oJUNC9O5vtbDJIAjYYcvveIvV7KDKfMoOpjMFOOj/5vTr3wJWU1caWoXN0+1y8DP7HOIYs8ONyYdtjDp9We4SZt3pb77oNyKoPWdMPTOAikXO6sz5EqrFhNnJ4gPcqIkEi3UyHahFXsAymDhIqZfntmgM1Ghn5qAg+v/xtTI05WQQ7Xyyrihb1YUrX224DTDwDeOx7flH+ChT5DiybtHrVvM3CCre8qNfpbOQFkcqWo4c7pWO+1c8jRhVGwbM+BdBISWd1/ZtI63u2lM4WLiKqEHhFpz4JJlS3D64J8v8lB7fvVswI8NjTrpbLLvxZOFcwmnFEHB9z/LINT5rNWN5QnL2EcOh3ikMFNZS12mpkvmhbTGKk7X/dcXog6LWz9w4iJVZ1Kuu/keBitd6ZWC3qa/N7lAIYzNMzUx9h/fkLCQmslmVPzG03F9q/LqJb3Kcrb41fY/+V2bZhQImuBkfAXexvl58lOpfQmyEYeFeBE/F7yWF+JiVW0J/nCIX+WnLDq4fSY/ntuaTireRyZPFt3Xq/dg1NltUF1Jf/MNnhD9Cr9tjsjIIW0ZlUomhjy75E38aoBnfoIb3Ol+pCAd9xDjEbVr3iDc26m+FM4ebxR7bhqBvSZY53u2UfrrSZULS/rCc+weiHVl7c4q/Doc9gXaeNOrzJL12x0lammVFCchnqIWiSzQPC99u2anuK26n7lhu7FqUwbFgNK6MWVwPH6tH3IpfNs3WQuqF77Dd8HF8iNBwLeQtrLjFj2yeZ7OB4tX1A+AQdDD/xbeMd8zjAU7PxaG3WS0wS5XbFfva9rmM4XLmyryPOYEzEJHMz1XfAck29d6LcAW5r/JFGhzGIldSPtWw83Qb8hbTgkBAZYPbRcZ3++oc05m/OxNA9vjDF1JsJXGfoIyqAzgQojAMdShPRguOVVXLu4CdSmcuF5tJIZl8WLtS3mwSCBfM8SwDPC/DeuqpALGWCObg39M5s+HqfUzii8zwNIh3AsItfWGd0Ic7Qe3cZ3smj+WPrtXpgNeVQvrOyCfdSFuF3aqYd4Um/ltyEBhEZsBWNvMxcmiQ6F0ynodzokMGzpAFWXARcoAOLtwKTTJ9+zD7tQtxKv9ZuUeP/nWrcrzRyu4t/YNP7Kl3jw7hay9Ho7dswII0cjCw4f8rPTQeABmjLMGNeH/+3AOCZt7yua79GbvEzFfSWpPYIF0kf/8y5oBxVI6IR/rDAgb1NntFRAMFw6BFGi8yeviV8nLMYdpwfXfH0vC3oBWaJKVwYfpSxxZLO2oSl42Da9yAdOZ3HzeIOxjaqsLOUfRb6qJqn6tDyE7ZIhuh56f0gXuQFYSVX6eTTG0ySe0+aK31fi/d+IgYv7Du7Mt0R7lUXjil8AiM3QlD+B/EyPNmrMl6N10tQH9Q2byJIMvQUZEVLVipPi9j+4Y/C7zU0On4RY5BjjSKguZ5gEjQNd2+y7zgfdMk7SHVncef/jK3tfj0Inb01VQwSyxJa6RhrxA/T37pJHEaR4ed7wl4LUF5XD5ObjMlHrX9S/mv0l3Fwy6FgE322tf/ejK9gGeLXtET2mabz0apXTotsl2SrskkjXX8jHDTgZGhjkT014L5LTQM7xCL1Y9igTYioZ9FhfqZAZMp/tu6q+xwvvI5t5/EWhIUUdrV4aWAdj3TajJ3tANhItaGzyPb0whWmSHhoQ1PC7OJpfmng5", "layer_level": 2}, {"id": "f4ec820f-4858-4667-8931-8e350a37c018", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "控制器层", "description": "uac-service-controller", "prompt": "深入解析uac-service模块中控制器层的实现机制与职责分工。详细阐述UserController如何处理用户信息查询、更新等核心API请求，包括其与UserCenterServiceImpl服务层的调用关系、请求参数校验（结合uac-api中的请求对象）、响应封装等流程；分析CallbackController如何接收来自各电商平台（如淘宝、拼多多）的授权回调通知，处理OAuth2.0回调逻辑，验证签名，并触发后续的用户授权状态更新；说明CloudNativeController在云原生环境下的健康检查、探针接口等支持功能。结合Spring MVC注解（如@RestController, @RequestMapping）说明接口设计模式，并通过具体HTTP请求示例（如GET /user/info）展示完整的请求处理链路。文档需包含接口的URL路径、HTTP方法、请求/响应数据结构、错误码处理，并指出与uac-api模块中定义的接口契约的对应关系。", "parent_id": "de8c6980-4198-46c9-9021-65354ec8ffcf", "order": 0, "progress_status": "completed", "dependent_files": "uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java,uac-service/src/main/java/cn/loveapp/uac/service/controller/CallbackController.java,uac-service/src/main/java/cn/loveapp/uac/service/controller/CloudNativeController.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:20.345309+08:00", "gmt_modified": "2025-09-17T17:35:01.596081+08:00", "raw_data": "WikiEncrypted:XZ3L/EMswFjUY8Q80yiTV6bGzqP3pRReH7mhIKVWuhuxNPxaVoxCvk4eCvoaf8ASbA0Ys8NSqL4tEwL5VfbycGHqZhWRjuDWghLXadSRiUYbpUOnkiwh9M18s4fqKDgdk6DVZlzTfRqEEJsFhqpRvmDrpgkvU+LzgHHIGpbcFSgotPtWbqkkN80ciycbzheq4KJ2Wh5sAH8ZVmooA74zt4pwUYMTcR4uDALBEnQDaodbDpCyuOSdyMZKhq5EaZUWdjGhxD/zahAbOqosN/vpVPevNp/9s7fMM+TI1+PR6aUvoMWR0mlx1Jo7mh/45kR6x16S/z1SleOWP7SYbLVUrvG4bbomTExey8Js4JNlgJmr2cXpNvqdUCnR5CAr8iWzBVZleHuqK7+qTZJJePtbIGxhnwmxI17BDP+/UQq8qogNXrHvOjsW2LKw1/yG3VGb6t2qDvpUZuHD9XBS42sEbfkhPFeKGxq4AfVv54mNMiZ8pT7MEa+3PHtMnZFebf74ZY++LXSss6t7+Codm+DpAVY+dPMiG4WmjxQ0+GA2fvxt5/jGTwZ1NPfx5ILubpYtCgYJIyN8Qb7nt88/wdb/pt85GpmEM9BMsmKGdjxNw9kXpEK2J6yY/yP8Xk2pmj7lGqbQSVDSxtd46HN9zeB9igpDAMpguSYaysMzQFHLx+jwpu9wxbuIqwWPskF+kBAQw/ocvXp1Fy6MKmk+iFT1WIP2v10ANFaxZUsmkz3ECp2OCFZbWxp9VV6wpWgrJoANzTRZcT7KSGioqhZT7zi8qLm3NhKDmT1VA3mo/9tioKYKcKn4XaajUkTj5UPeCcKPdPYCDvirN8Rxyp3xIKrUpYP/lmqIsINw95X0PdzIfXdeTkHsSVuGP5qu9hRToNnPBnH06PlVRifWg0lRyVphXN1IaUO3azBfkNOCb4OF50eORVmo+ePsqVpdYE3vN93ThNFeYedBcKunNwOGj6+y3QEdinjKCnACwbHMUSRAE+pavm1TKfG6Cm/ypLNtFBplXAAZU0tpSlughJ4xVq0mHuYgODIK+Hqqypaz61DlGEMwfs4JlOyB4ILI8i1rbuLCtojNvNoJX3tmrBwK9bDAPqEXYr5yExUJI4xef+vkhsdAFikCSFNIBPPKruX1RRwg78WhVJhtxdwV3+s6F9SkKitfJrXlBowVoHXZ5oy90r1Q7hbxsm2Z/KOLrjUnh85RxZINBLx0uFKQwpz2Vlp3tdteoMBn7bq8D7u3xtgf8OF9qqBAzrOt7saax/Ea7pF9X6cvSb6o7Lc7DGWXONujtZgUATtG8jmedG+PAQC5YRBsYk1yu0Lt/4dv1ztnzJzYiHbGtZb1QkC1DpKU8upWsTMchRfG0nk7P2U/QQ01wb0SwmcEwYWpN09Lu2wIvYhjA60lLCVKbtgPvEmMGx9Aea3d+7AkMoqAyCjOrMkhQhiRSO+8Gd7Hv4hA1CdBnvt1kZZaWTj/kjqI7sOHuRkK32ZDTLyWqkDk6AcazLHAv1w80tEiZ0dY97H1gVdY9IrrVUMunKl+T2k0vZlovlPqYnBXXKLglFgnhncDVxnc+irAwOAvjMAebSLfjuenRqDNk6fVvGyX8KQ9f30pfMuW7HFpm3pvDpwqeiBKoZy1jO3EvX1h6Y99rBqfJSfOOM4vllpavDOrBJnm3V1Zndzi7JkW7V6eYhEbun2h43//SnifcUW7GzuInDbmIgyUwiXXHoyXA6QjtR4RNxiR4Zh9m98b729lZ0AuoXFYQktiKZLkqdG3hYrX5twpYZjXTiQOvNijdfD+8ssde1hTX28mDlvJ8XSB8Z4Kzg5FWp0zWspujYGU+iV55KsuZbW8xd2icqqRCQ35vK7Y1f8eyZWxOAgGexO3tzGdajx5RBk5coCxStQ0is20cPQNaHFlpNbM", "layer_level": 2}, {"id": "745fc134-e9f2-4109-9501-785b2a9f7f0c", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "新用户调度器", "description": "newuser-scheduler", "prompt": "深入解析uac-newuser-scheduler子模块的定时任务调度机制。详细说明ScanWaitOpenUser任务如何扫描待开通的新用户并触发开通流程，ScanRetryUser任务如何处理开通失败的用户并实现自动重试，ScanPullUserDataTask任务如何驱动用户数据拉取流程。阐述OpenUserConsumer消息消费者如何与RocketMQ集成，处理新用户开通相关的异步消息。分析任务调度的频率配置、执行策略、异常处理和监控机制。结合代码实例，说明Quartz或Spring Scheduler的配置方式，以及任务执行时的状态更新和日志记录。解释该调度器如何保证新用户开通流程的最终一致性与高可用性。", "parent_id": "f4d5c9dc-098e-4306-bbcf-0fb86498f79e", "order": 0, "progress_status": "completed", "dependent_files": "uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/ScanWaitOpenUser.java,uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/ScanRetryUser.java,uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/ScanPullUserDataTask.java,uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/consumer/OpenUserConsumer.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:29.54461+08:00", "gmt_modified": "2025-09-17T17:34:58.395702+08:00", "raw_data": "WikiEncrypted:QFBIVr8PNrA/8GmHt1XK30CHgvM/SpNhUzeeGSjFwwAqEWG/WuHPmM5+9SE7bTcB43OC33irlXq2KLqjv/iBD/uAaTCR9DyS5mh4656s3nQpDXVBuK9BRPsjXrJyAtZoQuu8CjiIIYfE4kwZGbXOJ8V6k+ap2pMJO9cZeZfdzHaa8oF7zmz/W3Ra6K2rwd7fD7v7c5fTKhLJVqsxKgSVqJ3FPSudjLhd31qCd01Y6MxEIF8dSJXo/AWD0U4zFxmDlwrSxb2vRW0Ly+AHjpDRkQQa5wVmFoelK1Fef1J/0o777eWfbebELTGb8X0ebTBd82KM2LBu3J97v3elikDlrgwqv2jyaNJtcPJxa75T58bWVzu6J9ZKv1j/KgjFBwR9la5xh9wDtWGRVB++ED8blFc6z4d54vBy6nJ/N7Mv/cfDZCgxef97jBp+RXFs+XsRSTkN+vGo8Nsat43pSqB/xS1FHpFUIAiE9NViWnRKxXVfCLkUU3+Bux+eoSyRROJaGBhCLXqyGhCftoaGc383oR2QG9eHwmNcj0A3yxAeih5CjJ0GPSSfdEv+UYV+TErdLdCebTcatWtYz90w1DOnURaNQ5Pz4joCjpCf6gjVw+vwspB532Wq8VqSXLaxitLDQ5WO44rFOPCz6xuT71FxqNgugcMFdVaiz2j7QFc9uFChC9+MgSgxx0xyi4LPatLaWc211SXZgSIWLRYst5+dFXNCtFtn+pIBfiszM189fwzZ1Qnx+REIQeainYnx20ZrRcLNUmAzGfCfw7/8Sy0yh8F89MYnbBhiJQpwwItjmoTgGxHWt2KOUy0KaSxQwHQ1qzq4mR9CuQCWGjmnqOkKHobW3h1DhAfk7tZyp/itUJFqsiRPdqQGiYGUNXi62NhnLI8x2pJKBz5InFy2ZPUw7GErwB6fECDrC+dLf59GdLuY8ZP/tNSul3N3lnQsR9VgWXVQQ0l3krgPQ6kM6OQzVaS8TCDg+604MWVOs+xqC5YQAr+Oi2xd2iBjhcoqh51q7HgqW7NI3eKAziM8FbdjKD+k/27gfn2OrcEde+egMTZ54spVHmD/iqZxag24vb2d0nsTc6TGetx/Oc6knDP+a1FWmHL5zTdBn2gZ6aEYIjfMwAajn9sYkpRUWHUyXfPc6bc3JWCgK7kcksG3PsqlX/wYzWyY1t+1fO7I6w7HlNdwhq9fk9fICl2GROpsVAF5pJuntAQiYfTs15PK2kO0JEvZFpBYnkwo59hrS/4JnlwodkaM3HPdh1i8XMwNQP3mklCWmIXq1Lb8fXWNksrli0a0HODdnCTWYPy2iRBb1ylcobNQmKHIhKu6PTGZY9mpUE0hhuRPEqz39+7HmfvWuXZTJMAcoGXPBaLR+pyxEEebdiObMNK0VAFcFoDz4tt/4Dq+KJqtPhcFlUKbNmeGsR0UdIQC1csqCx3BYprbZ9/awKT9nwMocO9fa0FjyGqe3sCMkqlg/eE5t3P0z6LEVmLUHI/+nivgtQJ/tTGVEuT0lnmAKsGN6K0d+uT6XjTuPirBouL1+CSiXjh6PbTFYa4fVSed4cd+p+77Jzsly8Mwd1+hCdjLiSArl1qT3sxLQvrL54CUiH8bNnCuXP+LTJB5WbAAbCGqMD0mRDUYYdaZ+mbgOLubTJbPXhGZUwXEutjoKZ0WgwYvEYSSrIUBrZuHj3jzZSPFLq/zbsv4JhzWtojN4Lqe94mzOqiTfHJk2u3rONt3Z8KWH3/sPRTaRQVPhJ0hRiZ7DrrSSirIbxiH23Y7NQPKLKgSPILro5z1BIXmEo+f9vcPrvCUilloF9N3WvfcsEA1ojONL5d2wscppEzhj/gHHqu6xnMvdIqhHjJOaDG0b9UFCDUsNrtXEbrFDsm/AbEPe1NAj2ZipIU=", "layer_level": 2}, {"id": "cc6a6177-7fe2-4f3d-8c9f-a0f9494635ec", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "待开通用户扫描", "description": "scan-waiting-users", "prompt": "开发关于待开通用户扫描任务的详细内容。深入阐述ScanWaitOpenUser任务的实现机制，包括其定时调度配置（Cron表达式）、数据库查询逻辑（从UserProductInfo表中筛选status=1的记录）、分页处理策略以及与NewUserPlatformHandleService的调用关系。详细说明任务如何通过平台适配器模式（如TaoNewUserPlatformHandleServiceImpl）处理不同电商平台的新用户开通流程。解释任务执行过程中的异常处理机制，包括数据库连接失败、网络超时等情况的重试策略。提供性能优化建议，如合理设置分页大小、索引优化等，以应对大规模用户数据的处理需求。", "parent_id": "5e77a8d5-d8b1-44ac-85da-7479b9b4a49f", "order": 0, "progress_status": "completed", "dependent_files": "uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/ScanWaitOpenUser.java,uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/service/impl/UserServiceImpl.java,uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/config/NewUserTaskConfig.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:38.211305+08:00", "gmt_modified": "2025-09-17T17:36:27.927649+08:00", "raw_data": "WikiEncrypted:Zjlat1ajbECqwXvjzbeouHykyxuIXMCZ/+fgc4zBDXk6L+P6sVyPK/3qHc9eae75LzEIYE0jzMjeiJdFZb3o3J79cl7CVB7DJK8BfSKsdMXTPEM1ZqxYnj5sKpwwDhm/D2FH3z1wkD5MIou0HoQskEf2RvTGyJlEuDoxmjQNDUov1rKLMfARvCxqPCpxSECfxbC74CYwsuS1zl8Klb1pth28eniFriFGWdpBKvXxCjbTOWyyX/uvp5bF9dkMTKGHyaiYpfmBEkIkvFNVwxN7dsNC7OEYa5wB65EbQEb8Cjvx2ED9CuICC04e+NUwTT1bHIn+ZaKsBKhjZWVmmiGeyu1zsqoxr5+wQ63C5JjsYbVGJzT3s4ECH9d9RREe7cGPqYqg2xKC1gnD9942c4x/E2MQdIxd+WXyQu8IPUvzUWG/wXrQ6CSzc4XUe4oakI09mOwVl4clHgcanZTlwTZU7X10tH53zd4bRggo+Z32Q/2Q5BRlhEhr5t15uinQ2+ZMuQzwqkfazxmK9tbQXQvjNr6BXCbnMgyYq9gUY5NUXdtcm8+Y20jg//7vfYFCCvTkrD+fiLU7ug1Uj08yi99bJVZYxK7A+a9XcHdFHDVR89aF2w5rSb3Us88OHSgo9jA68aqOB1aPRZ8lCp7gz3HFLSnhbPKG3trM8CRa0fIhuNfXVXZk4svuC2gZwBy1ZslSUJn5ObMVHg1OMib6NnrXZ6tL3bDMAzFTp+3l9OfmCdWaMoDeggSk/fbi1RciqCUmzVpaegjNr385VvX4fL2m4r6av0I1lb6YecsFyWe9XJwzA29zT2t5CWgOJMjmTasG/wQjMfS/oxLjesfSHQDR22tBs/9RMg++fn+GJfMDmjR59YvmFO2TBg11Ifs/5Ws46seXiO7RFQupjR+7ZpyGApCoot9fsOIsrYSsuvGa4AUtfuSRfvjPxKta09HatQL3qR/zFuvnSqFFAxn1D0AoPjBsJLsi1P8RUbpfMOacZP6sUm0ry/Z2OAKShF7qSLvx8/BLf2JFn5ueH9nSaYvJCbtfhEwHUqZDopCWBR11VfPvIy7zf5SJaUHGBL74Q8vYq/yob5kZyhudI2hhr8QwbmM4ir/7rwPcXVeMgR6+fAU92KrIHV5Nh9+moZVM+0y81cTExM+/UNzt+iVwiQg8jRYIC6I0O7Voju2Oe9UTZk7FlKnHgSZnTKOrcab2YZplZBHNl2FCWPAvsYLnc6CNEKimR/yfFFJIhwkPH2w40Jhg33wqgLJ3NGV/Qic8V/Ah8C4bxjHX13WsWg+NUT5FukLTBFxpRGnh+mLgvEVddRSpS0ZkMxTYEtevUTm4hZ40mJniFxhTLKK82PKopGh+0BT5mb9sz7Mr8ceUouXOzMznEXpmF+syC66dQ/CxPFS8k95qcp2rP+4+8t5GDEkYwoA4+ox48iIrgwM6YGbIpJp1/yjLShvukSXg50LBj08BfwipKSe5YBJnWxD/rD1ZKaFLWrHy/XZ97SwAtSFLPFUblMOAhtK0dWsD3RJ31LKDY4tlHFo6EXa5T3QlrD0NyehicAkWsOzpFKvRla6rve5p8sMUp1Ju0EPM7XQ9loKgeTtY9v0cxG9RjRDxWMKA4twvzGpZCmJCx3UH5r5YMgSKBapz3XVdkBQihV4Mxb1Khk8AxGjjjSZ4eFHXy9ghLLkJFzonCB3EBRY1rL2DmjA=", "layer_level": 2}, {"id": "8dd29984-02fc-4f77-97d5-25106f3f8d63", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "用户中心服务实现", "description": "user-center-service-impl", "prompt": "深入分析UserCenterServiceImpl类的实现细节，作为用户中心核心业务逻辑的执行者。详细说明该类如何实现uac-api模块中的UserCenterInnerApiService接口，处理用户基本信息查询、缓存读写（通过UserCacheUtils和RedisRepository）、跨平台用户数据聚合等核心功能。重点阐述其与UserPlatformHandleService的协作机制，如何通过平台适配器模式支持淘宝、京东、拼多多等多个电商平台的用户数据获取。文档需包含关键方法如getUserFullInfo、batchGetUserCacheInfo的执行流程图解，展示从请求入口到缓存判断、远程调用、数据组装的完整链路。同时说明异常处理策略（如UserException的抛出与捕获）、性能优化手段（如批量查询的并行处理）以及与RocketMQ事件系统的集成（如用户信息变更事件的触发）。提供实际代码片段，解释关键注解（如@MethodInterface）的业务含义。", "parent_id": "fe90e6f6-d714-41eb-993e-136cf1422460", "order": 0, "progress_status": "completed", "dependent_files": "uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:56.891058+08:00", "gmt_modified": "2025-09-17T17:49:51.497836+08:00", "raw_data": "WikiEncrypted:avus8+fk7Mk/OhQ++5qWiggPbb0fZPLbOmCzI/r5uSPEQi9Xvqdfrzx0kxqSC6cHm7yGcdiQ4MqvsAgcO4YnvDx3QP/J4edqHuYxyuhxmF8KE2marhk4OhVuYdmBT1JyMgOqMiDjtPjrc04VuBvGRKmAdkG2jUZyqkEJZLMflp98/gr8QU1qwstqHD5bTLSm7R/W7PJ3gy3OK27W+0BUrKdF7NnAPMX284AFVojeYE0zx558vQ5c2dgoZJkGjEl6++aMpNv79Ei9XkeMzsnFWCDKxY9xWXzrGENLNdtcpEaB+ot01BxQnmE2TOEpNTc23OoT2s0iwxhAqIZvUVdCE6ebHcNwXOz07cPetJmRytRm5mWp5cMlEe7VgyN2JrxZHUmjjcset3NE9Y53ejG2/WRvPIzXaWYS05oRwMkG5BpukHB2fsQQ9JnzOD64nPDMxp8MPfBGgJlPuVticy9wz5KKuAcKteFIdslVPCIARiuv1KVl+Yzjm8eXEIK1oHVqEPZIpg4pos64RMlTLNj+4jHuJgTFL7lZqoCD3TT+6MSbszPRoKvK1kZVGxMkQUztj5JhcN5yaQl5WoukO/aPUEl8kM8wTE6VSimV+w8OQUl8lJCq6mEblfnF9Iy6RleGzjaskHdBs9wEXFEXcyEy/lqSqPswxrFHo7A3JUzd+ohAVh0caR3EPBYRIFdcHs4mBXMi9q9b5jhy9EasDDkga5VoAFCNOhCNynNC6hvXprXq9Gn9QiBWie/OpXdkKvmijfnWiSWqEd2/s2lScdXwwFmwyURjvqVInhTJKoW2pgD9nRJsr8OBIoYWYfYSVQOFTDgIqJq0+HpsBruVTlBGlYNcSJLZ4jU1zbnLVPayvprqZqrabKPmSgY3c3qaS6y6l+HJ0dJWpXdUg9ejPtkNfn7HjP4eS+gY6D3wG0aiEA+XD23wqGomGa/fWWas+xSD1SO0fWY/VVfJ9FVx1rCXGh0E93eGX50f02FLfCKeq91B3ozJqHGFWARZg8CyZmuMDChpCJm5PIHKv2fs02qzGa3Ngosbuoje96PkOEXX8o7feWzG2VMb6jeEIkOWZWZo5vFOCKo2hhcAv987Rzw/6W1x+pvmiNu3uDFajpZEbczYAqUrSQAb1OPUMwEeFfY6XotOwFzIOKeXQ4/am1tLU9wYYb7b1Rt0utgMnHJmF49AbVdvxavhoj4Rc31UjaysUZLrDzQQ+FDi9MiR6af7AIhUVyF18lZcgbJlYwF6EiiM/ZuD9JM06cc5YKEG8oIc06PvEAuJ6RXPoxPqIj3Liqj7B3dQ389alsj55p1QXqCX0f1qj7w8s+RZChrcS00Bhl7Yo0OgX6G3zzCJDSjrL5xCl/2a1rjUE0Wq1nUE0dquOGTvkcIAgrLTQht+iHnO+WHgQ6z2BkXTuB3zkUkrXsBq2fpe2KI37V9L1hBunEgfwFznyyahlYmNSJpsrp6z+GQoUHyDzDJ07qr9L48QeEp+o1CA/QHEEKRePMsR6Y5ZB78qh5+PWv57pBLu32DQSceTOyb/hvyO7DOS6XbcD4eZ5A91AfnCznvKQbh+I1hioQBmoL4kuhErfIQmmtB21WhQdNYVdXqVRc4UPkGgscd1jMVIcQ0TNEmtEJ54Ypud/uX4EdHO9//ijEEK+gQy+6ye+sOhTyTunq2T4BcqjzbtFtKuPDPPZulHQ91bSMOFWW7bZ9SnfxPgh3KnfqwXUqEII6NYvC4jbF2Zpy3qkzA3DCI4Z32+lJv5XO+q61H5GmvGgwaCBf9AdEaKEvMt", "layer_level": 3}, {"id": "ed0be98c-9e64-41b3-b287-626ccc09cd14", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "拼多多认证实现", "description": "pdd-auth-implementation", "prompt": "开发拼多多平台认证实现的详细文档，重点分析PddAuthServiceImpl类的实现逻辑。详细说明拼多多授权流程中的授权码获取、access_token交换、用户信息拉取及token刷新机制。解释PDDTradeERPAppConfig等配置类中的关键参数如appKey、appSecret、授权回调地址的作用。提供处理拼多多API响应格式（如嵌套JSON结构）和特定错误码（如TOKEN_INVALID）的最佳实践。包含代码示例展示如何调用PddAuthServiceImpl进行授权验证，并说明重试策略和异常处理机制。", "parent_id": "10e48e47-ef03-4f56-9d9f-4504a0820575", "order": 0, "progress_status": "completed", "dependent_files": "uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java,uac-common/src/main/java/cn/loveapp/uac/common/config/pdd/PDDTradeERPAppConfig.java,uac-common/src/main/java/cn/loveapp/uac/common/config/pdd/PDDGuanDianAppConfig.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:09:05.032797+08:00", "gmt_modified": "2025-09-17T17:50:01.192585+08:00", "raw_data": "WikiEncrypted:mNgtEJMdxkoqpiJsOvvs8PdrKqlYTJQ9zEtZ1x2iPcUQRRmAiZ+sheYrOnQ9MusI/2bUSBPu4qUMZWLjmtiZS8j9xK+cnj8rRoSoFZnl9arR+Q88ISb1mlaxSJv0rgb9w5kApsjHFGYphYdHBqcFcS6zvYs5KAOjLMakM+V//Su5feX5IzJqchDAdswipTRlamN0kiQt1F/J+2FL30G44ohWG14qB0v/4LpJvWiJUBRZRYsQNKJLYME6Dw2YgoEiRLaDgM7tpwlY1zlZzBqPcl7ziB36C8bo94+biYYVBZ9sYOjqFoBJZ9JzSUbY6M56AIQkRKKUj/PlP+h958pv9y3ovxhb1nFataaSvqXVg/A9j0HNdD0N4JGHrEEIpsbg1CV7h+VndgLxBO/S2eStuyxiR2iH5d+8kDEceQJfvrqym2Bu1j5cTpffC/lYQ1vRpKFLbPPoTJ/PozICwvw5br7NsSVxd1XBAOWUHEWEGkJ5y5PDERV2m6ZdOxxZ2y6cUMjqyBjp4SyInGFR+ZVCw7644DuqNjTcPi33wRZqL78gb8feFjAaYSameoef3b36ykfrNbO0PQVaDX3cMHfUJgB6fqWqliSk12/Ti6aeG1c895c3dnIjBebQ8uLGnX8kbCGgh9fnfetKB8bc0ZlqEH4r3prh+rK2WZPlan3F6VbNReqvI8pjEsRLcmmby2Er4rDkczOcdGodFeYHuH+BogpHoD6QXis9+88W+07dhRwV0UyxXSiqY24vVi4XtT4YXti/WzxSPeKTDAYUbuvXDzRPqt18zNNOC0COYrHSa+s4m7+/DHljWwfkPVRHlXiqdmR1fr0McXCC+Jl/ZI9dIHpOFBoB0tNTrCsvcd3i1Uq3JOM4UZUgWUk12Ze6nysHsM7/DAJiZeGVvkb1VZLuqaI37MgQzBfojNGByaCC/22m8LDTw73xz2ZaPzzTPZqpztzfIHijmMgy+MAQwQWCTUL1XqWpYfuD8a7YZISVsAf3x0dbxj7DVp4BnHQxISnfPnrjPVk4gQPGuOYMsD840whYDirOhFDMGLsFxhjX79UwfZpXYlemhaF11rJF3HB/xTekj0OzBxLlSydmELrkFDtJ+R3RAhc95avfXvnU+b+SjEkibSusTKXQhgFjnnbXSiou6mSb8Jv5/WzTo9GuEaw3ZHGDZpr0+06zMPXjURBTHUKKd5UOmSO+PC2Z3SsOXFUqOOZ5XCWAHHcZyVJktCmPS+DiyaW9qsTG0R3dq655auclwOeuYeQeXEc3FAKLOPiwAmICZHl4z1SAXj0/zvb2gjtEOGlvO+ZU+WQcjMwCkX+qql9+5W0M+XnofhRGHt1uOJPrYI+DeDXovn4zVec1qp62aQOh0vI+maqOuQh+L5q0MFcHN5dDJZXLh/zA", "layer_level": 3}, {"id": "452c29a5-8639-4bf8-8b75-8cc0d4a9ffe5", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "技术栈与依赖", "description": "technology-stack", "prompt": "详细记录usercenter-service-group项目所使用的技术栈及其版本。重点说明Spring Boot作为基础框架的作用，MyBatis用于数据库持久化操作，Redis实现分布式缓存，RocketMQ处理异步消息和事件通知，Apollo作为配置中心实现动态配置管理。列出核心依赖库如Lombok（简化Java代码）、Fastjson（JSON序列化）、HikariCP（数据库连接池）等。解释各技术选型的原因及其在项目中的具体应用场景，例如RocketMQ如何支持用户变更事件的发布订阅。提供Maven依赖树的关键部分，并说明内部模块间的依赖关系（如uac-service依赖uac-api和uac-db-common）。为新开发者搭建开发环境提供技术依据。", "parent_id": "", "order": 1, "progress_status": "completed", "dependent_files": "pom.xml,uac-service/pom.xml,uac-common/pom.xml,uac-service/src/main/resources/application.properties", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:06:38.910795+08:00", "gmt_modified": "2025-09-17T17:10:26.515953+08:00", "raw_data": "WikiEncrypted:w0CkdCdnXCcvlN5xOpiEhEX3U68GI0Ngj6azFMJo9xQD0Nwsj9XajeArLn33w436LuYJWl4xP0dT9zLNODhOpI2IFAi7bU1tz9apYGtFtymoAnARCvI5i3RMrHKe6Lc5Oxy2WzxedNVLIBybAuIZzFWw+s4GC7x5pdQ7H8GdtnO7MEdwZvOE0t/RB8w9ftc4SBZYV705AsczFgdgqK6Mk0cw3kcexrj8Py8Yt83Fceyg01G78KR4F2QQ2bQkn6gYi0uH5Op6o+UuoI6Sk1e7hQGVGevmLPKBItReFQfgCXJGbrTYKNXvur7PTPO5uQfmtpQoElU9uwR4jU0K4m0XAYTkwPgz/Wc93IUZuuwC6IUBZDm3veGGAGPKd9zLSC5GOyIaXie3kYP6Y6MHKw3ENrSsXUSNrV+oLau4kocAkrPOQVBm0/um7CfFale/DHetJt1rPE+9MeN5HiBpQSMPLdrp5IaiOMAx0K23wZYOE7RUtaS13BHwsmfizYvnDC5xHmXPDbAwhOBmuXa5zPqdEriFmEk0NnWmV/4tat4mHMUxr9Cz/CcfxNW4XuBDmHxI5sV+Ep1z8trjweyBUOSG00HNZWw6W12of+1T92zD7D68lMiPydA5Tl2uvh0n5xNZpa8rrAjKOwMsz7nnmEtjP8ZE0413GTS58hGf2MDBdy4w/kXxJ8kqw9diOgTOyqUGxMa3hjLGTHIgCwLGyIUR84CW2KVGnFhOenqQdXoNdZO6ZykEunDLSFi125/jXN9eS3kBofLhbtH69X8WacofWKbuyamui9c6eJpaUDk3IXthzp0pjw5yCZ1GuFQX+gXi7AzeUigo5IB/08FxQOm81cNw1OEhMb3mtGGNkG75omeTLW3gU8pS9scyoxcHdeBAmK0qHP6Klcv9Usr3qCN/x1c+UJgrH+R7WUzzz6mYkZ5QXFHbeGrlGF3rK9dLXJyRMWWQO++/EodZ3t8em8AAiCVntHJbvVk82J7aKRoNqDNwtGVZFy0V7scy6v0H/gMKAt1UNvdCF4ObnKnZF/qrA29Vka2YFVzCYL/+ZXx8CFp5m0nckohM0d6qDKZaLWKacUxjHwV5SPBuTHfu4OkJS/zh/DLtGwphqnpKj5+03UjkBDzZITHnejhPvrqmyBOUXiE0ea1XJbvek25UDAGPb7frLmBTJBTCLc+ZVUwwWj77TS44VZbc2UWoA3dmOeOtktGVBdoG4zjW83dVrpcF+BJQDJ7KGzmt62ReI3GpeEnI8XB7RFqMDYQ9XH6+eFJyzCeTAeh/kAC1twemXBVEz8jyKO8vj+f8L5CprpT8mPaOgvVOWlC+dK8GXW3Ci2M4eKKt7rpWMmQVfWjIjER8XF9klIS5ns2/EIGuTpozdD6VTEbVElKllCIlYgSEqDcF", "layer_level": 0}, {"id": "5923fd82-9366-4eef-9b14-e914245f96d0", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "新用户管理API", "description": "new-user-management-api", "prompt": "开发新用户管理API的详细文档，重点介绍新用户数据拉取进度查询（GetPullDataProgressRequest）和数据保存结果查询（GetSaveDataTotalResultRequest）等接口。详细描述请求参数的含义和响应数据结构，特别是进度百分比、任务状态等关键字段。解释这些API在新用户开通流程中的作用，以及如何与后台的定时任务（如ScanWaitOpenUser）协同工作。提供接口调用的时序图，说明从请求到响应的完整流程。结合NewuserController的实现，说明接口的安全验证和错误处理机制。", "parent_id": "21f95d6f-85b1-496a-9f5c-1fbcb3015ee0", "order": 1, "progress_status": "completed", "dependent_files": "uac-newuser-api/src/main/java/cn/loveapp/uac/newuser/dto/request/GetPullDataProgressRequest.java,uac-newuser-api/src/main/java/cn/loveapp/uac/newuser/dto/response/GetPullDataProgressResponse.java,uac-newuser-api/src/main/java/cn/loveapp/uac/newuser/dto/request/GetSaveDataTotalResultRequest.java,uac-newuser-api/src/main/java/cn/loveapp/uac/newuser/dto/response/GetSaveDataTotalResultResponse.java,uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:06:52.899934+08:00", "gmt_modified": "2025-09-17T17:21:10.145908+08:00", "raw_data": "WikiEncrypted:w47R73gfxoZDB7HFA0pWZB0LqjRqbv1Oxf9wUTW6a0CdPlB/2ibVzh4/Mhlvffa170ydywCqVmUA+rhXdLrViGwLgaR5Bk9084JTnfVwLnhTnB+tnSYbaHAlcO/mYQEDFZk8CX7kHnPA78s8UqETG7DZvk9crm2xRsT/P3G5RbkkOSa/VpAOGZwxKbqhcV0ojaKAJelNhlZdvPOC9UOwcabWBkoMQoTVQQzn1bOnQ6Bi29xqD7wKU8iq76g9jMo9W4iccGzMkonwXEt1kcToLGR84goDxtrSXoNetSYuMDuiuJCZ2gSuDeCHDunG8Wx9m+HgGuR1eM6vwOQKf7+eGZjK38hhBnhSA5IKexZk9voXEF1CcOM8HWz91o6XrE3qvw3xsir+LjXz5nqDUv1odQSgzew8JG+oa7wEQtk0yohF7oFrPQYQHV+Ocj3NH3UJensEKulN9cMYEyxbfmVmIrsORARwNHT+YAIX5YV4QeEoYPu/z2sDlmk/dd9UQnxEnKUZAIGgzOgdAPzx1+s56BsJK9W5w3f1p+2jlIkAjX8UpJLb9MtFR8bryy0CBiDEvghYDohRyL0Dv3AtdkWC1H3255jyezfdbfeob7iW61wXWY4Ob+QRg+OprGUjWY2x54CtgdEjhvhsVeT0oFY4ZT72BA7kYElo0E6Gv/u7qdtYMuPnzkstHnFpyKMp/ylm+0F3LoIORSEzRL8bf1CYDNnFjhKzLSARNcod3aP176Abh0/sm/dDJXnETlsC/k81Llt6zCdtWafAI/eLIDX0Q4kAqjBWUDq/BeqALOq+k0QDR9oKE1te8WjlZcUs3MYLA1U+PZlPorPXwK59SKP2Mkfh90L/Ndp2FyE37vFfBn6tmgyp/3BV5ZgroaAD3+SUGeBkTOakuhwWfJlTYO/z80z1+3QExYeaOg7WIAiplOvAMRoWu6PLTwHrVL98wEBpQqwDk43nzVTWiKfR+qeiwzsvYtrNbq8IVvtSLJfxA+QqDt64eE7AXW/XHOFYkG3BjCcH9eSppCY9+qLJlmjcMmop/WnrcHuzwzbJfBrketW3+jDZXn8g3fZfnVVk79eyIhnX8z8aFKo0vEEA2tZrdSH/FfOjHUaTV4OdfFQ+P5tZxy7RGOYngm+rM5Uvy+VrANHZpTFFLjvdcdGY3/znYkWBrgfH8FdY7WHYs5DeLQtgKWx2eJaIyvb867kkoSyuFIA70V+3B+bSokcIqTv2z9G2e50Dnpy9apjtPGhgIk9Oc3a+BoDjgn20J1mlx0Oerkgto+3oJLorMfDC10sLkkVsHXp2lZynyKV6L7KG3AADn4yGrzJdjWEr6GqqYW5LJ5K2fzDt5mxghundxaQAGoby3sDJzFRKkazujswOMhQA2jJuyZ9Bcxt1xRPuTrWOreGuu0AlCpQAckm6rYBAy0TdCwh6ElrADSU3Uv6G0CdRH3e58CmW8r65iQuaQAKsUXnDuF02OztUsvEOW3F4gU0lFmDMw3mvKUSp5W3Sou1ClBsrEJKQQ360DcHi7TMaj7QqXTZaKV01nr4CtWU9VhB1YE2oPDoom/ClVEdIg/2QWEaSuvztVhAbxAhFf6IqhmWmM9vX+FwC61fcPL9IALDg8ugNpf2d64fPxv4zDxFEJqlmzgtuy37J1mp3PwMIA37wQL5AFnJZFTbYw3yw31uoCH2ky7An8EO6r9bnoXe6KwD4fhT+oTrAwSKzPJsLvcuk2VvgS/UP6IwYGt/XGsn8ODsusom9vF1h/6cHkuXrHGlhuNx+HJM8aSr4t+lRGuslIiqjDogU3WeMA/RzktWleMwsmewl3cGBd6sIyeBxYLu9rmxYxgWQEpw0P6kSCk3wKa+9izGUcqncDBVEQw==", "layer_level": 1}, {"id": "5dc01eb8-7e2d-47b2-96e2-f5337e92784e", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "uac-common模块", "description": "uac-common-module", "prompt": "全面解析uac-common模块作为基础公共组件库的核心作用。重点阐述其提供的多维度公共能力：配置管理（config包）中针对淘宝、京东、拼多多等多电商平台的AppConfig实现，以及Redis、RocketMQ等中间件的统一配置；工具类（utils包）如DateUtil、HttpUtil、MathUtil的功能与使用场景；异常体系（exception包）中网络异常、缓存写入异常等业务异常的分类与处理机制；平台适配层（platform/api包）通过AuthService和AppStoreService接口及其实现类（如PddAuthServiceImpl、TaoAuthServiceImpl）实现的多平台认证授权统一抽象；Redis数据访问封装（dao/redis包）中基于Spring Data Redis的BaseHashRedisRepository等基础操作封装。说明该模块如何通过提供可复用的基础设施降低各业务模块的开发复杂度，并保证系统一致性。", "parent_id": "84196b87-4eae-49d1-98d3-e8f06be3b022", "order": 1, "progress_status": "completed", "dependent_files": "uac-common/src/main/java/cn/loveapp/uac/common/config,uac-common/src/main/java/cn/loveapp/uac/common/utils,uac-common/src/main/java/cn/loveapp/uac/common/exception,uac-common/src/main/java/cn/loveapp/uac/common/platform/api,uac-common/src/main/java/cn/loveapp/uac/common/dao/redis", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:07:11.367665+08:00", "gmt_modified": "2025-09-17T17:22:11.798715+08:00", "raw_data": "WikiEncrypted:+As6soyi8z53pneR0AbwLxVJjPC2634O30WW0vn0q+******************************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", "layer_level": 1}, {"id": "0bea1de8-40fd-4f12-97b0-62216b323eab", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "用户设置模型", "description": "user-settings", "prompt": "创建用户设置模型的全面文档，深入分析UserSettings实体的结构设计。详细描述setting_key、setting_value、user_id等字段的数据类型、约束条件和存储策略。解释该表如何支持灵活的用户个性化配置，包括数据序列化格式（如JSON）和缓存策略。结合UserSettingsDao.xml中的SQL语句，说明配置项的读写操作和批量处理逻辑。文档应包含实际应用场景，如用户界面偏好设置、功能开关管理等，并提供通过UserCenterServiceImpl访问设置的代码示例。", "parent_id": "103edcac-ed4f-4fff-b35f-73e2fafc3279", "order": 1, "progress_status": "completed", "dependent_files": "uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserSettings.java,uac-db-common/src/main/resources/mapper/UserSettingsDao.xml,uac-api/src/main/java/cn/loveapp/uac/domain/UserSettingDTO.java,uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:07:17.430315+08:00", "gmt_modified": "2025-09-17T17:22:16.266669+08:00", "raw_data": "WikiEncrypted:7GMpDAULkInIjOJUaNlZ4lSsdIXkMkfjr+j3yDvxa5G0SMQfbZ77T902yu0DoNvpqvdUO2lPY5QNSob3tnzVUJHZYaFc7KD9+BYh8McNiLCVvMzXoZXInarPwxyM7vaB9bi+MLDRkt5cQME4AEbM93qpxyu4odqChiAdkpna+2Us3d38ME49QIRjFj8OdWwY2wg2mnWaGqeMbLwWsOvLRk1ZkLNytprzQtkzSG042UneRtatmWT/qFxmTU+DJA6DitVcQenxolGxxUt4jMLFBOJGmE3IkTeTGb99UEpdF0xOHU6dTtuiy/e9E26e0FXNTOlpf1BCYTjGcQcwWBikavDnCgQ8Vezk4+SDZlY22KbUOaU6GZomUQyS37Bqjcv7/PgK7ClK9PiQBTQ1WqJUEQOoFetBpxssLKEt5jniIqQ9mn2oL04mCeMi9KLgHQJfLYmc7p2zFZ4GTIAoQZV0ExFUi23wDbJGWpn9F9iav2UVStggha/QyCfJL0IwK3ezkHWOzrkhek840TR/LiNsc2zyYJfHwIuDvqNdh5L5ACA/r3LmnkRF9PFUYp2dl23yg+c2JMgBkQAohmflVv1SXSAQX9X4pXki/W5O2nYnOfl+T+oWoPqGysldzRzgR+9DbX+PGLux39uaUJCXPW2XXqIb4VXhQz1SQrm8rzWwS74vY1j3wGQfUpUhFCwDwcLMnETrU0o3Vdhv+mpU+C5SXPNaU9WoYklCjAjMtVxD1/dcPDKMj118yVEglNsb3bKgwOUI6b5NnrehK43x/9OoNZMlwzBCVa1Px86KwncsLyXjo6I5nLkAnWLiuCnkvuZnfdB+vTGRyGkycrtCPDkYrN5zlFl/2qYYFSpv7+8NSEI6BcrnItRCx+MkfexBx5oJ2xxSc5MUY1jmu4PeT/Ql5NtVcsDXSeSZSsW3lTwUgTdiK5cH4GqYLSZYD6mUEV+fRzlhp3nEyt2/X81HPm3fnKcQ4FEHIB50RDpznIn9Ab3ogkUPYGMfj60Kv8NeTvOwBNoTe6kgHzNN5+BDZBSgoQwGL6zgFyfTsj+ZEbzvlMgHT6i9TcLdjpaKP9zagnHzPJO4+vC5CS9dT80w+XmH/XPYwpx9gw/b6W03+xgSsAhoxvfQ+OOXVZxXQR5qS3vd6HKkFBlXTzpPQq/YSBIrSuviODSf0yB3X+YuQgaT31XUIAM0fLVM8wc9pxnuvQSptRyB4j1k5fD6YqecX2afa0Fi5MkTiiJTi2QhXcZNy9s60RTU2/oGuSLI+pCjN/Dwf5ucX7vXhxtSc9L+0Ml/Qq8vgCkc31ie3soaxdWLXSHwg8oBHEH+ALjAShmNb+hEIAYcOrgwswUpRQfARC1lA+Ie84GG/6NutGympEyHBGUZGXOIJsrBno/qvvRJGny9RoXswWSfzndP62agpEJ1B4EXtEUds4u9ubdSxaSvLweVhj1XcL6uN14cvZf9dk7Q", "layer_level": 1}, {"id": "5e77a8d5-d8b1-44ac-85da-7479b9b4a49f", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "新用户开通调度任务", "description": "new-user-scheduling-tasks", "prompt": "开发关于新用户开通调度任务的详细内容。全面阐述这一系列任务如何协同工作以实现新用户注册流程的自动化。重点描述ScanWaitOpenUser任务，它扫描处于'待开通'状态的用户，并触发开通流程；ScanRetryUser任务负责处理之前开通失败的用户，根据重试策略进行再次尝试；ScanAuthCancelledUserTask用于清理已取消授权的用户数据；RedisClean任务则负责维护Redis缓存的健康状态。解释这些任务的调度频率、执行顺序和依赖关系。详细说明任务的实现逻辑，包括数据库查询条件、状态机转换、与NewUserPlatformHandleService的交互以及错误日志记录。提供监控和告警配置建议，确保任务的稳定运行。", "parent_id": "11640edb-f5f8-4737-a6af-0ab226c39804", "order": 1, "progress_status": "completed", "dependent_files": "uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/ScanWaitOpenUser.java,uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/ScanRetryUser.java,uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/ScanAuthCancelledUserTask.java,uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/RedisClean.java,uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/config/NewUserTaskConfig.java,uac-newusers/uac-newuser-scheduler/src/main/resources/application.properties", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:07:28.464695+08:00", "gmt_modified": "2025-09-17T17:23:30.051149+08:00", "raw_data": "WikiEncrypted: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", "layer_level": 1}, {"id": "0004d29d-a159-443a-9afb-5b629204114e", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "用户设置管理API", "description": "user-setting-management-api", "prompt": "创建用户设置管理API的详细文档，聚焦于UserSettingCopyRequest、BatchSettingUpdateRequest和BatchSettingGetRequest等核心接口。详细说明每个接口的功能、调用流程、请求参数结构（特别是批量操作的数组格式）和响应数据格式。提供完整的HTTP请求示例，包括设置复制、批量更新和批量获取的场景。解释设置数据的存储结构、更新策略和一致性保证机制。结合UserController中的实现，分析接口如何处理事务、验证输入数据以及返回操作结果。文档还应包含常见使用场景、错误处理建议和性能优化提示。", "parent_id": "cec3aeef-d7fa-4435-b007-54a9231d0f79", "order": 1, "progress_status": "completed", "dependent_files": "uac-api/src/main/java/cn/loveapp/uac/request/UserSettingCopyRequest.java,uac-api/src/main/java/cn/loveapp/uac/response/BatchUsersSettingGetResponse.java,uac-api/src/main/java/cn/loveapp/uac/request/BatchSettingUpdateRequest.java,uac-api/src/main/java/cn/loveapp/uac/request/BatchSettingGetRequest.java,uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:07:36.954745+08:00", "gmt_modified": "2025-09-17T17:36:24.68652+08:00", "raw_data": "WikiEncrypted:7GMpDAULkInIjOJUaNlZ4ovHbk3wokEHJJSgtRtqKnydKLTwAJq/PrAUZ7QUpMMGQfHMXFmY9jneK2Ja+v5LBcVCViOl0ZlbAS7fWufauDMLYzjq91U0iEbkzYgtvRzqFDLdpdDYwyetcNAQ7TBlCIL8WLA5Iw0M1ByqTmZMdNg00rDRGSv0ewDrRwP9Q9oV9PBrAARK3nzsrTuJTKAKN4v4hXS2QEfQwGC7X5LQw6f4YSHTaoR0tmzMB6H10OpcofQtAI15YIkZIdxmLmXAl5oD2RW5g9Dycv6IKC0dr8pqoQvpTIFbs0YtLCU4aFb335GCEO9CHBFPpY/awqPYZe+Q3+7PwpCEEhOdAOK+XFV/+LR9A7XY5hDa3shA7EZXlzASU/gGiGSCxw8ego2Rwml2zoYnWrzAiPY9xpoCI/GnWiG5gpdO/iV7eVy8viR2B/0+/EYLZajB2xMAWwYrmNvITocbdz4hatFxFtF5a1T0osR8on4pQDgkfZW2ALwbpPHTi6r3cSRFH7dDE/pDHJqcK4s2Ee0thMPe0exfQras4RG3GeFSeIRW4+DLgX6HURT/TkXbGO1YxvcAVQnHJ1y91HYcpoJv3iFf218PuGRbr3yJIrqE1jKoyoePGrO3S+1IfAoQEIdnylocplgUdwEO35jvmR3pULNZBWBY5YJlsMvSZbMxj4Lrrc/P6VqC/lsfb6M7mUIAjIMOo8udmoEHEHm8lLUplwWzObJbmtgZKrSS1W+wIXaL0YgsBniXWGBDMEVsaZrkidW7ZvuMdz7QCMa2VMcd7EzRKbO6U+zCG79qQkZtPTxsMp67djV2VVDp5WL4w7yw3q/pj6JuZEOmQkmqnmZ13+6qLXkzWrfRsFa1wPvrLAFpmVYX2cM74P8Z6f6EtX3swfC2A8fmb9OAl4OCU9BYO4dGBv/eu9Y39xuFklKatN4JE2DxTOG0aD6KGnur7O+BrvEOrxxkSbD1fcS/UrLeSRhgYW2w+r2flwYjJQCv20LxBffvOBXLaMible7NFBepgVCORTinqJFQKqc/DneBeQpXyBnbr2gXFn8uBYBknqoCIzsaP/ZTkUg7yNxqU3EJZ8SjEmuGuYu1myd8rmO2CDsP2/ck09NPnGK7boe4EH6bWsVu1FjaOg+loV7wWe+Klg3f3m6fM3SHtj5fWzW5v+bwoCtRi+rc0xwJGcxcjzQkHzP37cnGpw7GTK8nUAMwBgBIHKnxHq0VAGKocOnQntFyNNaUNQKgwcSbNSJFm+O9iX+N50Uc2lzTga9JtsxzhjkE23PEXGI2N35JHrnpSZQduAYjozSAGvMTKbbJJIRjNk2SBqs5zcoKstw5nXZlPdv2S+IpWQixeSLXohRITRhhXdmppjlrRvr+vuG/HWS2zALMJvWH+UX4yZTmR15q5vrsVcoHitTwlTqjUHuNn+Be6jbXCESLFRA5hEcHWe5MB3AcLlF9iz9es2sCk2LqRywW0rTxoRKcWK0/pA8TyQu6PwF2QRLXy1iKtDGHX8mo/9uRkKmBXKtTQTO2I85BO//v/K1aMej22QJCc4jXvhSWXj/2mSz8ZSRx9k112UycARm7BP3K+Ba5/m6VSV3G3YYiz37stZO22wV5QnFDAs9v0JgdSOuDTAwZawNhJ4SFEFblJjFBtB8ghmycqZybN9BXaXUGoVqySmG1ySIX1K/ILchKMb/FxhKXsgqTAQ2lYsmDVmuEgET+UUl/3RnuZLIRYhnnL2OR1Yz3M+FKzAFIOaO8ZJSGwmJr5OMNuHto2V3fRZE5owv8sInk+qPJ4/WgMRrHRw==", "layer_level": 2}, {"id": "5193f333-bf85-4c97-8cc5-71fec6f2785f", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "新用户开通流程", "description": "new-user-onboarding", "prompt": "全面解析新用户注册与开通的完整生命周期。从NewuserController接收开通请求开始，描述数据如何进入消息队列，由OpenUserConsumer进行消费处理。详细说明调度任务ScanWaitOpenUser如何扫描待处理用户，并触发各平台专用的开通逻辑（如TaoNewUserPlatformHandleServiceImpl、PddNewUserPlatformHandleServiceImpl）。解释开通过程中的状态机管理、重试机制、异常处理及结果回调。提供新用户开通流程的状态转换图和关键代码路径，帮助开发者理解异步处理模式和分布式任务协调机制。", "parent_id": "484d4aea-c0df-44d0-9b9e-c951ee96a3dd", "order": 1, "progress_status": "completed", "dependent_files": "uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/consumer/OpenUserConsumer.java,uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/ScanWaitOpenUser.java,uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/platform/impl/TaoNewUserPlatformHandleServiceImpl.java,uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/platform/impl/PddNewUserPlatformHandleServiceImpl.java,uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:07:38.433561+08:00", "gmt_modified": "2025-09-17T17:24:02.596474+08:00", "raw_data": "WikiEncrypted:w47R73gfxoZDB7HFA0pWZKre291SJe/kuLax/oNosBukFg7/38fVrtVyiYEUyewLCs669g+zEGCGJe7sNOnFqL2lE4uQ4XXI8kGZq7AXAdSJ+ByeI0ssjIMpdfQqn/03zbYplx4bEbaj4LKb2cVkv7qzANDL+SCcGeI1Mk5+Hc4htcdYpi7Rjw9sxBwi7EGY8ZCheusTW33kle9bhIRRsVMzg46sBqDXtRqOCiKfg0MWCqK01tM0QPs4GSF0jmgbJUiZPsgkQOmqo6pmMw/+SB59o7hbz38GP4XblmO7G5dFEpbmFi4JQvWzBgSnQB7pV3Vqe7PSoEx3T7apTuxYYu+CFRX0Oh5yA6gIUlXo6nMhGLEkDu+NyWxvZO9FbbvLSkrxs11NURdXxZ0SxZoNEg9c45LzGVJQXSocJTuGL6lbO3ETJex9NjD/7EZ+U03fBPUZCkDTo+LJBY9FIfGxAvfnUAQzEFmAO2qLItgFqlEUg8ByfcFld84VDZXdUY+AzZWhfmvgdgnTMadHP23YcX5pPu5sZD75VcWe1AYnzDoR9s24x+w1PPyW31ydY7qZG7Mz6uGCbMPKqytIx4cyItwOGcfXhWTNhOKzFHknXNxGke5Wp9sxxiRTJlt/1T4WWnqErxufiggf/m3LHuy+qfou+lF4YyBVolRJBlW2AJH9N7/Af9LFdtN0HyM23OAqaBOCt+FxIhjua63JhPaB9KQtznHsqxoctHnpTgtTN1b+t5nLQf/K2++0/CbAmXe+UXsTdPKdqbeq3rNR6/y8Tjv9FHy0oVurLePz+6wbb6bSC2su6BsbPcRDjGcZnMbwHAYVZdr6lG6ayah3trRdcmOdt5GyBH2NT/Tna62qaohrx8t6W41rZ2bVf81n6/1Q7+hKf5Pjgc8rerqn7Qf0Gr5bhxqxdgaNvpQ27CdHYVXet9OD9OIQCzf0TTyQzDH1A/KsL76smXrJsf/OGb6yjDk3iB7uGizKLAVCgg0XxlEgQ/+DTMSbW+TmI7Mn9VlpBRR8/DRfzApy2kfbEVb9s3UFYQ1ILufu3GvbF9QZaQjZ1/MscdEY5y9kHZKXro4D1mx4zuQBVljM5DrsWtf/O+oqfvqsVc2X8k/1qNx4IOVRYF1SeUknRHgghuNp53upVnEXLRULPVRLwctBjvvyaTARoIB2h4Fj2JDRQuAttOMSAjnwm4Gxy+BLzB24gEtzrBQhe63tILmEO8rDnihjM9QJJ8DJE36TyRJIIfENW3ikmPT67jOH0HPGw8dW/BvLAXaJ344t7QNQ0xOPgy6yR6uWI23RG4O/kd1eB2P4GeQDo9SOPNpW2kd/PXb9gvQX6WDaClEcaTGX34LFq5/zxipSZvNa9BU3YeGchIq5r5Dne7dwLFZ18uqqMae7N8qtaKwSHEnfIkl4tJIkJl05p5aYyzwxkeNpcKMMeorvjvGXyFgTYN9Cp0dBy610ZlxAY3+dhwlBN1GzLgMhmqUILq0h8HNhysg85ohHJXzYyamDFK12ZsYvpU5eRyg6Fqn1V55r4IYdGSbNuXcHOcKG7zYFvGXZNTQaYKShoTs8QnsYr1KPbBgvDW9TlzLBvPsJOHtjhKUxXbTZ19qTYG35YAlGd264j6B9zCavs/z9tWU6/u0DZnHImXtDXu2hCdmGUEVHBY17XKMczBhnia9TZemdSaTtkn+fcvR15OnJviCRchb5/qkgspXjM/BLrLlHPX47KhYsrI6dz4ArUdNlHh6KU2CyiiClpAcA82CyCVr77VrNFNIwtenBpVX84l3jd+Tiafhuhyn/e11HMwaVUYex4t8A9nAGZoCLfnbGNs5F9Lu1xZ925rTvL+m66R2fHVwfgXd35eEPBK74AB7VAqmkywWpAqQxjrAe5KIl2v8drPjpoUD9SZJJNUnc73oL1lyb6EaFUlo3MPB8BxQqxeku1pY+F7jT/8sf0158mlQ=", "layer_level": 1}, {"id": "10e48e47-ef03-4f56-9d9f-4504a0820575", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "多平台认证实现", "description": "multi-platform-auth-implementation", "prompt": "开发多平台认证实现的详细文档，重点分析PddAuthServiceImpl和TaoAuthServiceImpl的实现差异。详细说明各平台认证流程的共性与特性，包括授权码交换access_token、用户信息获取、token刷新等步骤。解释平台特定配置类（如PDDTradeERPAppConfig、TaoBaoTradeERPAppConfig）的作用和配置项。提供各平台API调用的重试机制、异常处理策略和性能优化建议。包含实际代码示例展示如何处理平台特定的响应格式和错误码。", "parent_id": "8ad78008-869e-48a0-ae54-58673875c3bc", "order": 1, "progress_status": "completed", "dependent_files": "uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java,uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java,uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AuthService.java,uac-common/src/main/java/cn/loveapp/uac/common/config/pdd/PDDTradeERPAppConfig.java,uac-common/src/main/java/cn/loveapp/uac/common/config/taobao/TaoBaoTradeERPAppConfig.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:07:45.199744+08:00", "gmt_modified": "2025-09-17T17:37:54.078793+08:00", "raw_data": "WikiEncrypted:Zt230nV3k3ww0eU+N8tTkv4cIRwsczVEVw8ZOWoTvvMMxe+2tUxa7CwwC9Y04BvQrghgNz6zWPsNr7tW0gzXlWITTGGUTUZv3sB/3qbcufUtRw5F/vT1a0KwhcuOP2VJ4WmrzezFH+Z/KUJwLc3wuBzG3pqig3RyTfezBwsTqkAd09W4XAuHczxcPboZfGqAWPvsTrVpNNc+SimzZ5H7Y1ZQo3cC7a31uSXz00TgwPfaVUsbxxvlUfsGRKfMnoKYVIyh6R6LnDED7PNnUcLPbj59Qu733P0x/Z7a/TAYSIgEUrNa0zTTzOmH6TLjtBiRcSLXc7FEJXGnPjHHxxAUa8QFCfMGdWiM/iPJLnFUa1t0mn5jLVIYQp54TlTqwwpi6qlZB80Vth/f3A5oj+ZtcwcCe3PzHC1SaOEkLuvTYZi0I3qmDCmWdMpWd47+NBimlWreqTBPcKja5ZDXyKxqiLp2y5sHc+9hrIiHgwWG3wEqe8KkTxNRvCUm0ffA91e1jWwetaTZhTV0sFbG+m9EOcUeDOIYe6lzs5X2yPxXAVQSDsx4c9kTqN+icmr6FGCigfSsHzOq1WT3TB3W/OhXTD3sn95kYKeVDEI84qByLxczgFDXT+ONU+X6/GKhXjjYtvmv9EyAbjvWZIVqb0DntK1LDUnihuQ8UfrQQ+lK+TFOuHCauZTTweVTKUZFtUP2A1YRLr1aMtDGQpzy2LeF7o+GpTou6op5qothJ7MVWuLuiXZbMctH9dCiCBmT4Ug8bcfcgmNnefNtITDtaTKP0vBWuUo9Jzxr3ALMu45kclWo9ISkQXkJJ+f0tAWUD2DmyWOUhJwrDqwNJ00GzdvyENV4rdn6QcHSdFhsYDAQoU84u34S11O2aZ4LG8RQIqmLmYm9Ec2K8UE3q6EcDamaiZWdDYpIyKsRc5Fq1d8jqvp5j9nWrjdE8qtaA6HWL6wSum1kK3X/cmQ2yI+46umpy2DdIWWglfWA/KTfNvSpfeF4D6E3ZVD3lQZ9/WB6VtjiDQmZXeR8FdgiOWc+aBMGFZCKfxc7yR9cx4/1YOiDnSa+qBhG6f0sxe8SRZpq7aa6PaMSgHhm1e2VUUtTIRqzZpUpSz9eRNQfs3I1CcprhDAks3ybPwwbaBjasyakKmKs3TkuRQqssyuJC/ftN+mOLZeZiY05onYDC826Cl8rcUlzvE/9m1CuCQ+u8uNUDhlqJadIsNWQ6n5HHpYhsvW0/OqA4AaLW/QkINblWq+qznhf8SVf88E78FEiU3rb23AP27X7H0KcfrwJO9L98MUsWaHe/LX0VgYqk8GO2mj+o97+m6ay5qg75so+NnHDNX/jnsBkyBIXzk0tsMoOLC/xhqGIBWMx4oDnuiqK0x4ckyLQdMjsljpEq8826bvfFuuO0YNVfNlte2sM4Cc3Vzn6O9mPuLwR6W1JS6IkHf+T9sTiUydmrsGELpV36lWPwWqS0vwuATQnwQW2fYzsT5oMYDj0AriGOX5FjOQKPsxHo6WkbPzYAGJJALLLisNgshBZQ2A2McWsbPEbHmWzxHIG7EWa8Kb2Vi70KrQ1KP10bfKgWE7K8YgLsbuliFS71CijPTuqCDQlavanV7+1hadA8VRfkSGaDWFn/yBjba6U+qo=", "layer_level": 2}, {"id": "154f2366-1b1e-4afc-9c66-95e40f4a353a", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "实体模型", "description": "uac-db-common-entity", "prompt": "全面阐述uac-db-common模块中实体类的设计原则与实现细节。详细描述UserProductinfo、AyMultiUserTag、OrderSearch、TradePddAuth等核心实体类的字段含义、数据类型及其与数据库表的映射关系（通过JPA注解或MyBatis配置）。分析实体间的关联关系（如一对多、多对一）及其在代码中的体现。解释实体类中使用的Lombok注解（如@Data、@Builder）如何简化代码。讨论实体类的设计如何支持多平台扩展（例如，通过继承或组合模式处理不同电商平台的特有字段）。提供实体类与数据库表结构的对照示例，并说明其在序列化、缓存中的应用。", "parent_id": "89fda65d-105d-42d5-a727-d1e38d49ba61", "order": 1, "progress_status": "completed", "dependent_files": "uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:01.360413+08:00", "gmt_modified": "2025-09-17T17:37:47.103044+08:00", "raw_data": "WikiEncrypted:dM0MoPE0IfmxFmuMw7gEzxHwx8pFtam6ReZZ0aslA44zJCspOLjg+vWcSRFgdMajfLL3mN37JawoL4Ixrj5Z6LuaI71vhdxVgOJK6NTjJg7k0GGJfWnXIx3GGWQd00uPcDnTDXNo9OEv/mcQ4M8ihmBIHXKfZA9KJszh/KsK7wsopmDhAGFgtVoCQ5690OXXKz6oqrAC+7CH6C4zIC/uOXYu57dICVZpqF1NrvRZ53q3iTuKK3ydNpJO4JWAvR1mBar8zKOhzSlONX9b59iePUJrWqozRYHdGIsyLMHfv/cIh+5c1NfxU7ZYasJJclZ8/5D2kaZX8NdWB8SdE47pe6N9X7uwsJ1gpEI3hDZwNIeCB6iMZ85vxvMY4jdIvdR+k5hCVN2skPzD0tr3vOIYtb5SLJmKNh2OyrXatKGDdHfKPU2jQ106Ei0sO5bwJphB5VxexnF0ruZ8xfdRRH8JC7Ra+muMWvml/xGRTpPkzAfW2b2DpB8phWrB0wX/q8Da8w6kOGCtm0eyTXUI7eNj7Bb8oLVr9/RG8qnZyc0jgtG9v7EkmSqq4lb38g2vgwMuSHDU+XS9F0s6HAC+4x3GEWA2IJRX0OREos6V2iKiiUTsWyDBPGq4r6sYy0x+/T1Ut6aKU7tLrVafVSJ4sud7CzCaKIbnGsvsDUHtcJdR+jV9nAUwndDyPHqDSasWZ/ETNMwZsB8HCWcfg7OEfsK8VBKDmLTTxtbuvMrByORWj9Q3NJrzWJcpStXL/psOon3uOPgJ1WxPIaXvgXSRrV094VJm9ZM54aRLdj4gweiSd3vnD0RtWrjHyCKHK+KWN+aTBp/cesThYgLCn45jPuhTxd9q1uijPHjUBypBzBvbSB+sg9dHs9UxxAE0tZLICwi0D8Ywda76aGwEUfHbej5Ow6KCCBAC8TSxIKzdkmfgNxxWg90JQc23xvwa5+0c7FmeRdm5Kykm2uxVXYLDY6M42kVuWdcW/VKnibkOVVsYFjWUpIvbIyyUqjtY5JatupTuCm8gcA1P1Y6Gx8AFKWXTG5CGCfvSKPdK+nMtObVFz75wR7pkHC29Uauyr9AJJafdtLUHz37zh8oo/xYUmA2O989huQYA+l/6bWQ5XHBav9iST3wbVFgI2Rgq+VtG8VKuDtmmcKwVgK5jMu5GE69qnJzoN3YENGCSX3SyfmXshFKqoIAi2pUO65GvPhQr+o8nyo0szcxXeKlRyx/FkDAhP1EzZf65xW85rVzTMT1fMsiNumFiHxbg1P9xeCywm3O0VC+uU1F3zWfvJcXjnRK+g3OUixJNrz0U8hAPGIwR1DPKpzU+2rsnr/SOlTG/SUrAcEDgqCdDJ9XAhBQN9tVObcuxmGfGqUypAsbcilpD8yU=", "layer_level": 2}, {"id": "412f6f46-b685-4245-b70c-153986faedb4", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "工具类", "description": "uac-common-utils", "prompt": "开发uac-common模块中工具类的详细技术文档。系统性地介绍utils包提供的各类实用工具：DateUtil中日期格式化、时间戳转换、日期计算等方法的实现细节与使用场景；HttpUtil封装的HTTP客户端功能，包括GET/POST请求、连接超时、重试机制、SSL配置等；MathUtil提供的数值计算、精度控制、随机数生成等数学运算工具；RocketMqQueueHelper对RocketMQ生产者和消费者的封装，消息发送、事务消息、延迟消息的使用模式；SerializedPhpParser解析PHP序列化数据的特殊用途。为每个工具类提供完整的API参考，包括方法签名、参数说明、返回值、异常抛出情况，并结合实际业务场景给出使用示例。说明工具类的线程安全性、性能特征和最佳实践。", "parent_id": "5dc01eb8-7e2d-47b2-96e2-f5337e92784e", "order": 1, "progress_status": "completed", "dependent_files": "uac-common/src/main/java/cn/loveapp/uac/common/utils/DateUtil.java,uac-common/src/main/java/cn/loveapp/uac/common/utils/HttpUtil.java,uac-common/src/main/java/cn/loveapp/uac/common/utils/MathUtil.java,uac-common/src/main/java/cn/loveapp/uac/common/utils/RocketMqQueueHelper.java,uac-common/src/main/java/cn/loveapp/uac/common/utils/SerializedPhpParser.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:07.058328+08:00", "gmt_modified": "2025-09-17T17:39:02.806876+08:00", "raw_data": "WikiEncrypted:+As6soyi8z53pneR0AbwL6Pkwv+s7B+XTb3bwssIhyCh7YJGKxxVgaFHnmhcz7gl1YSGdU23CHBYxbDa6PaunTXiX06L+pvx6N7ytMsQLS5pI4Z9qZlT/yMbmsyE5rH3adV4jJ83rSoFN401hsbuPI0VCJi1FGY8T3VpX6tOxU5JazyiV+kF+g8VAvbV9UGjnr1TtpqXknad/pnsJ2rYxwv0+Uw/apbPCyEPvfEIJys+rIMChS6hv696G4wI8VoPyEvIlAjEHUTgDLmJmNipjrRSkiXgu2dmot7VOxO10oWTXyoGfxDgk+sM+xcguJCWDTWWab2tiwlBsz55gh/5NAM69Yt4wioMrDuBYzS7LAY0e64UHec96PjfOEVf1LKkuuojgjkC57zNV1By6752XNuSER35/FT+YMVSQpSfHmT9Fvzxgj8ylF5DI3uzg9H1dJm/K5ZydIUgWPIor0WuxQgQ/o7TEo8LKvZGrvrCwJzCE2ihrAvEGSYTiFONaVvsuHquEU2nDTdN1iZ/nYY80lQlZxYcXN3sGWHGnAALTa+T/BB8iorvE2R5CHJiHFJ4pED7II3GOamf5Lm9k6kaAFx2+GcTxQnLlHuVH4i2h7oArw/196i9fpQYPcBlYrN+KI8ohG9XoNKu4SRHnSIkizPo060aM3l1XgA8apkm3ppr2x7BpkpqSTGGXMeXqKZj6uq0nguBcDyw5r++rqSS1a8nfxItRsGJm6myHIXpefnqW5rRXbyZshWqu1jjvAaGQopXdTpr+rm+xyuMB20tjHrFjAItJ7RKSYmfjRsu/pdOfDLhCdpBc++f5bnspuiXBimh+UQx4DFnLpGTh5PH54z2c3PAIZoTWS1iC1jyIjrvyNWpKWn3biuVLl74but6KjPksuTkK6OFq4Us2BTJXGVP1gRBjieU5/bQ7fY/lV0CCXpVxErxJ45pB2mFElQcyO6XBe89ijnvHJMufCtAmfPUESmYuU2pRpZ/Uv4mhQUdxV5Xrka7cmHY60lotTDRTIXOrs1fxG4+vBynwSNmG/6yr1qIPFBWbOG6JQ/afqk7eTZZ/YHXcvR4z0ZfWG8l9h70R+6w2v7aga1GEUpiSPeqVwPZxuWZdaL6QkOFVCL3tPHIz2aCZ46/94GCToF6GyiCiJg7d+WT5OlPjUTvsBOWMTmCtVqa+CBn9q3OLb9RCX8olLdZCf8rND2ezq9Y+zDbMUXc9zPs5Uykp/3k7Q3Jm2AAsQh35MZ8U7P1BagmV647uIVC0SqNec7LyYIljpnRsCYWuuHSfI/kTIhxsqmTqzoQIeYkhpYliqCrnMxo2z+FF5uaL/doH5LjNC7+/C33EjbHGF+OwHnJZ5FymHVaFWVGbqg7zSctz/C05S7I5p5V0yJDSRU5x9pdvOdRCxt/7dJTKMMoyx7CMUKac07PAY/HlPGRimPr8psbsHlG59ZQZaffnIrgfUtV0PoHvas5bUXl5f4JJaaN5CUW5A2JeN12B0TsnWX+LNte8ZEcjqbSodH265/Nu4ksMSfhhqANXare8t0SMLwpmk+Vt7EoWZyPGIuE7EXJKjqZ3Tf3M4Q/yfQBBRTk7PZQ0ayq+IHD4uVvr6wyKGNirVC58ohjvAycZ/GWxPAOHi5MSbcK/+7heyWQ7EbDvlJwXUdIweIwCedUZO6Mj0mwCRVgnN6vOdPA98/Ck7kSStmn+EkNTkw90ECsktLPCxDPFpms4zC9AEKBD+4787R2ypDOdNj9Aw7tMA2BFmVqwanPUpPJSeA7LowW4eAU+BMa0ZEAuUn/Y/fjF5EedzsF/LXQka3/iPeXePM8hIDsUAveHXkcZzMNvOgh1QzsugFtpbHYhTTfZQu8LB60VZ97BxNnjEAtehvZjrqUImhVd7+MZ+JEPoNp/FUMi38n8iu8vuzGeCFGC3jDt13UP0OSH+VzyQ==", "layer_level": 2}, {"id": "fe90e6f6-d714-41eb-993e-136cf1422460", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "服务实现层", "description": "uac-service-export", "prompt": "全面剖析uac-service模块的服务实现层，作为业务逻辑的核心执行单元。重点分析UserCenterServiceImpl如何实现uac-api中定义的UserCenterInnerApiService接口，整合UserPlatformHandleService等平台适配服务，完成用户信息的统一查询、缓存管理（结合Redis）和跨平台数据聚合；阐述UserOrderSearchApiServiceImpl如何处理订单搜索请求，调用底层DAO（来自uac-db-common）进行数据库查询，并对结果进行加工和过滤；说明UserProductInfoExtApiServiceImpl在处理用户产品信息扩展数据时的业务逻辑，包括数据校验、持久化（通过UserSettingsRepository）和事件发布（如用户信息变更事件）。文档需详细描述各服务类的依赖注入关系、关键方法的执行流程、与数据访问层的交互方式，并提供代码片段说明核心业务逻辑的实现细节。", "parent_id": "de8c6980-4198-46c9-9021-65354ec8ffcf", "order": 1, "progress_status": "completed", "dependent_files": "uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java,uac-service/src/main/java/cn/loveapp/uac/service/export/UserOrderSearchApiServiceImpl.java,uac-service/src/main/java/cn/loveapp/uac/service/export/UserProductInfoExtApiServiceImpl.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:20.346396+08:00", "gmt_modified": "2025-09-17T17:39:24.953324+08:00", "raw_data": "WikiEncrypted: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", "layer_level": 2}, {"id": "52680662-3e75-42a3-bc4c-85865400c3e8", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "新用户服务接口", "description": "newuser-service-api", "prompt": "创建新用户服务API的完整参考文档。详细描述NewuserController中提供的所有RESTful接口，包括获取开通进度、查询开通结果等端点。明确每个接口的HTTP方法、URL路径、请求参数（Query/Body）、请求头要求、认证方式（如AppKey/Secret）以及响应数据结构（JSON Schema）。为ExportNewUserController提供数据导出接口的使用说明。结合实际代码示例，展示客户端如何调用这些API。说明接口的限流策略、错误码定义（参考uac-common中的ErrorCode）和版本管理方案。提供API调用的调试技巧和常见问题排查指南。", "parent_id": "f4d5c9dc-098e-4306-bbcf-0fb86498f79e", "order": 1, "progress_status": "completed", "dependent_files": "uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java,uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/export/ExportNewUserController.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:29.546326+08:00", "gmt_modified": "2025-09-17T17:40:14.104742+08:00", "raw_data": "WikiEncrypted:QFBIVr8PNrA/8GmHt1XK3xWMrB6FNdO9dbqj6wjE+uJXlTMyHZT0s/4liVh/61It7gBuJi1Wbe3PdHSiUBr5jD1bAss+oS/USTyZQr/0kHkNdIHOoFG3hWPktec4wL4SZfyiU6UD1nFBr6GCJi8X0Pm+Nm6voBh+fXutGrOPb7EjCj92n2tsdbeuXsomJTOzjjlTEvNsBfzKLgq1msGXa4FxZEwdXX5QTXyLF5XhmfLfP+w5tg5l9lhfcPgZ5oA7N/EFMm1Z0mYn/eUJ45ykMkcf+u70H8h3jsXtnxYt/MoAyt81rRALsZ1OTCKSItNXK+1//prUj4fQhcikZIn9c3QFWdU41fK/k6Dng7Uuh8KOgCdRrq9FkTRkkjWdRBF6Jv0KBbvaiF88E083tyfQQqlFpHGk7V4lcYzGfpzihMuMx8mLv97dbe/hgmiCm2WySL3D962o4uptSw7sHtVN8Yy4emcYjXFGtoS0ytzSFcRqm6pneja/COM/kh1hCjBUOPmDGgsq2y17Um5F/f5GhmJ8z3JVm8x88FT72HnZ+QqEZvweZY4t5D9tZgKDx+KLufqXU/cUxiwGybI8e0L/ibHPsRloJpaVlxKib+nMmxCTTBkgvGwm2aucmnt+IFit4pC1wiLHZy/NOiowrbXxAJLw7vFo8I3Q15Uui5VefZ3zeEoSryMP07l7HPwj+mCB3OH0UeG2sDnyeZAZj2vJFxsVkIqeN/VjykcbIjsVvEr4kP5iN+4ze9R634CZZNYQ96DyJkU2Dtt9xvYkfraz3iQswnTS+A/jwXJnyFJ8b/i0e4oKnRKXOTJvruQCm8yyjeiQNTkvInK0Sg7L5mQgxtEcBhb17TS19FBPNQYT63QOwKZQ4KXMcAW9JZ+Mar7g8YZGvM/TFK7DcADgg+tya8WF+LtPpKCtoiOwg5GHHgWE8FJbYjvig+3+E7Mgm4m+qgEPQWbl9gb/IsNiC9w8HVNLw5SouN7TXg2GHO6LpkVKAkxpBZPvdRrF6n/TX1q52BpSu/gORQjO3KdvC9vvHd3V7B2CtragwHmIEa9MTONdnIKUdxoN0mOX8V3i1XyMX3jkByFQXmnjHOXvpy63I4SGiTYdaWh9XUXH2M1E4H70sXhmHrwtBDmNLDJa8KcIdvcHnAaRqn0YjkWWnOIh2tSTmfrLPO/arW8OS6E3BLvdSl3e5UKN42fADBY4WuhNi4H+js5zkP7ME4W2ysdHnfzHDYlXheXPmUADuHWnbfmZpRTJHy4QxKeM35NBKsgNniRBK8uIwckE+l0Mfy4YwvNViTIrEceFKGeHcBAHVznTU/PPr44WDy/XC1wF9pvgnzcDf66Bj6ICrj27LEXvhtqTIRDAFVgup30+6ehyKtRYR3HLo8kOaV0M9VyLLiUcv+NljrxqYn5gv8nOnjWMtR5Su7BtuZBoR488XfV7FCt9uyeSqatiu5MHBFGg0S0mhqka86MgquGaQB4XH58SOaidI7H6KbdI5WcNYnS2lP4=", "layer_level": 2}, {"id": "419e8996-5ae1-4523-a06f-daab4846c399", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "重试用户处理", "description": "retry-user-processing", "prompt": "开发关于重试用户处理任务的详细内容。全面解释ScanRetryUser任务如何识别和处理开通失败的用户，包括基于retry_count和last_retry_time字段的查询逻辑、重试次数限制策略（根据LevelConstant定义的等级规则）以及指数退避重试算法的实现。详细描述任务如何更新用户状态、记录重试日志，并在达到最大重试次数后将用户标记为永久失败。说明该任务与用户生命周期管理的集成，以及如何通过告警机制通知开发团队处理持续失败的案例。提供监控指标建议，如失败率、平均重试次数等。", "parent_id": "5e77a8d5-d8b1-44ac-85da-7479b9b4a49f", "order": 1, "progress_status": "completed", "dependent_files": "uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/ScanRetryUser.java,uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/constant/LevelConstant.java,uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/service/impl/UserServiceImpl.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:38.211878+08:00", "gmt_modified": "2025-09-17T17:40:49.945944+08:00", "raw_data": "WikiEncrypted:xCek63ndZZjwgkDesPX2NbwfB2LQGYfiMXMdtzP2TkRDEf8vR1LIq2fwuZKqnyfgS2o6RSV/RNJA3yEDceAf4QkubV3dHYoSYXsDtUzCb0nefzAjL+XoIpz+5oGh9FxZo4zTJQk2CMAC4I90nVmzNV9yjghlYMglCFi0f7cnh2f8ntS0oYM/Bl6PAvtQ87BT0AGyApmiCBYG1i0WTGcUHScUysBl01Viebf+vgk7m1Iu8/QyBovLeP8PCUI/ga677J+xJI8tXaWE1+jMfZp9VWKAMiF9S97JQfOU+OLQiqif7EaF0n0K9XVVhcagG4eppohHvJ+FrmVRoxHh4rqSfHXbBR1obn4iEs9V2mqBz3UWdGYVHc/xz1CXoA79hbbQKEcS2eCJAnJb4EEteTF/Ad3i+egyRmUtbh4N/PZsc4K5wRFIvZ/Fd5YjdK6vd46/4A340IXuVng10qnLsY3YmquPomRyIrqfBkuL1zOYjLS29T6NEpPgsDjBzvBwNeVaR+VTG5Nn5H2kEfER2TpF3AOZ+oJkaLQIbIX22lU2QZFyA6QpuoiHLV9KkgTwvGLQSiXbkA2JckI2c17IwiEJ3g0qMsQwiD2BjKw+JzI1RcyqUPMqmNvp1mlQLePmf1Q/RIdXIv+BjO88wHP5DxlOnryn0QmRUP4NatxZlH4ASIQySj5wAuJxD2XoAKgy119Wsp8ke33jyp0tHdQ9HTmrguNKk8KJ+hRP5uftHYsN+dPylz9+1IRiSOYbt+fSl5lmfS5ZI8bIghClJZ1CkxVMlf3OIOZYzRnLwMhPuwwrHeRXblDUd5JRNaZfeayp2qVQBpkiOxMlnUS8LydvGZ2B0k4zLoarD08MmXBGgdts/So0Y/FMaCIMGN9lZEYtbLniTR3jN1VJqKcsoGyNJ5FCgDddw8u8VqLOvoNXvWS7WV8gDaY2oR4LfC3xKB8BcosaoctghC+yisbqrL9jMBq+/cQDHkKJ/Q5gTHIrLixany3QJGKS6Fw4rb/0UrFYvDyjazBP3D6j9B/OyGQ+mueO8nIlB/rYEHbJ60l1RG+QWscWWhSqACWWb6RWuu0/qg0S6r26AlcS1CnI7BHkz7S/zMSz5DlXLRrSfKfbPql3mZAab1XvqaKvMdJIQQLt8bKqF0/ywUYbf0NyfpQ8kOiyAs3v2NStZfJSUBTLX8zvIJSsCHWPPThRbbQpn1Qlxe/wJO22FZAv9ISiyiQuL+uTvPgTBLpERaIC7fxIVzm/HMEl4MML5UEqPW721Ex7EdGmOQDYxv04LcrYeK/kDOlVEi9QmFlc9LdozikhlvEMmVwYmsvLXn7lIrfYXuY791RDLCL05R8L9OVAzwK/kqkYRlVnqbN5O76XWuz7/qK+C8J/Po1Lh4L6nws2WYNLAhrvkzIZhtRzGuYAtGUupRle59EUMjDKQIu5678mmdgJjwvKbSAb8aXdelKxjz/6WKFbAeHROezslb57FfjbCUd+JK949Obgqsqt1ARK+lUIeHoUKscIvM5m8YALLmJSns+WUEn4FeuXz4ruU+dm560sC13gLSkEggD27G83LFuFqejhNrEqb549qo7BOBydBepj2YaRGWQAla9eYfn2/ZAUOg==", "layer_level": 2}, {"id": "085433df-07aa-43bd-80bc-2d5e9c5d6ad5", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "订单搜索服务实现", "description": "order-search-service-impl", "prompt": "全面解析UserOrderSearchApiServiceImpl的实现机制，该服务负责处理复杂的用户订单搜索请求。详细描述其如何接收UserOrderSearchRequest请求对象，通过OrderSearchDao执行数据库查询，并对查询结果进行分页、过滤和字段映射。重点分析其与uac-db-common模块的交互，特别是OrderSearchRepositoryImpl如何封装DAO操作，以及MyBatis映射文件（OrderSearchDao.xml）中动态SQL的构建逻辑。说明服务层如何处理多条件组合查询（如按时间范围、订单状态筛选）、性能优化策略（如索引使用、查询缓存）以及错误码的封装（使用ApiCode）。通过具体代码示例展示从请求参数解析到数据库查询再到响应对象构建的完整数据流，并解释其与用户中心服务的协同关系。", "parent_id": "fe90e6f6-d714-41eb-993e-136cf1422460", "order": 1, "progress_status": "completed", "dependent_files": "uac-service/src/main/java/cn/loveapp/uac/service/export/UserOrderSearchApiServiceImpl.java,uac-api/src/main/java/cn/loveapp/uac/service/UserOrderSearchApiService.java,uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/OrderSearchDao.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:56.892752+08:00", "gmt_modified": "2025-09-17T17:51:24.602378+08:00", "raw_data": "WikiEncrypted:1TcXmWRnDAgI41cGfWTUk8yNZxvQVmMpG8+AoGz5l8WwBOKp5NKKdAIEy0ql/uMzRO3nWdZEr3FXnUc1tW0eKcuQRzosJZRMr38euZ78s4dSDC2V4mZf+osypPRZT4hvl5YoTAHjcMwEHnFXBz5IAdd33pjc+YdOZdnUxWA+GWek+/VH0Zb+gNQsTdI7h7rbO/7Jvnf5NnER5FZlRQ18Hoj7iF9pDaK9B2jVnd6rPQUYV0B5dLc8Q+xIMf1KYfyzbFln6XQ2oMzkUZGpel77ihevduuaPUdC3i59x8ck1QIzee73Q/1GsSfNrHUgpnmXg3ry71Wvic4rQtcnUzX7nKc4oOkQ7F3/MWXdLhB3YS47PhMqpoavrpqlDetEhi1UYQEfBkNtqxdxU0Ko35i6Nc9YplrqCHgsgEZfL7izs75ETGuC/cOqLfDRXMTKE+8xNelfbVoVMYTlGz8IKfzyNPl1ikAVKJWSaByiCCEl1m5F7Lb6g/EWXR0jrDO1XU6CV1/57Cprf6DFPg+hjt6hhGjslZRsmxZF7palhv92mhvQKluJ29imRel+H9daGES39h5IRmknmjA9klyU1kp+NGgwGuzuqUX6fxX+Bn1/irc4ME4MGgmwN6pZfX/jtxRNqukIeckWiz2bLZrflpCtBiA8hwDqP1oK8y5f5PfSzjEWVu942tD8HLpgFTKoBrAGUKq006p7OCzPVZIV0AzNhTzUMm/AlEaJbwK/ZsVddIJi2apB3LcoVOvTM5/lnM/bOzfo1HaiiGJnZrnhmBVrO1tdxMEDHKqZfUAKAVG71dCix9KZbsF8SCaj0EY7QKvB4H1CWuRnFvPKvWnx0O7u6l7eCyPT4KW758HXeBgYLsPJUhqsatcI+/yTY9DqCJZgO3Ip14G2mc/XWqSIdIbksae1bl1owb0vXEMcqjMLlvXWwSR0OduP16ugvHrIlSne7KmUZsORhslRwSL4LGSPcZL9mvSY0FXAM0I7/lWKBPOd3kKcsa7cVfdG1sJvC23vmrsqP23pGFYsy2D21OoaJwH0kwrXphamHnOsw3oFmFhlU78rvllGeK8abIq6ka3BCQp0hboBzwf5k3Qi5A8LItdaR9w2oqUGC7SKC62Ql4qs6s5sUZ50hPmjDoxKHT+3FSoj8KD6OWg4EEIglhLwl+afopYKVPJ0Gnxsp2l+958sTXDBl8wVULqkUrhLHDc/qv7//tN6IQUVoMo1hoWevrvDhnioiWimwl7FBAeAPWE/c6Tl2hjU2gonTTVcfNYDBWKfcJ/aOqUK+mAaHG27YtPYoFvO1csfck0VPpmmyT0FwWJm2eibItYrtwMpIbuo3cyyOhkvJyxsvzx3tru5icfOy8hqZbV3Q5tverFRUsvY0jFB+1wflNFN3FtvPlxOj9hB5pepxLFxAVRt25FecAkv5TxwTRB2ryqF6dX1U3UWtf+RftjNqhGHsYBltattOazv1zMk3xiH8bVKi9MGBpFDfN4O/aIDQxJWPirKPoo3xZKZaYe32MV8z2XN4ICnittwfflvtWrc/kiyhiJvXc14cZwwckgaePX6cn0yiBYuR+Fc0Hfw7d5dCzdvG3BXk2V1NyC7F1hjvb06HVZBw/R/0y9wiLkitEKxUDg8KBtywqJ9uTucLkd4AsHT7HnIGDPTmUDNstxvPQd9cE1QG7ggbQLjG96jBH/7nE6rFfgPLb6D1Ip4uf8kOCsfECEYx0HMyMYbWT+4geZh2jT1l7MhmIU2kGw82IWtdPiAY6HdTwmyilj3pHPC7/Adb7c0dIBbhoZfa1P8ibssQuGpxpWYdVwNjF49Lzii4IvupCZlmo89m9AaTq/L8LItJ5MC", "layer_level": 3}, {"id": "69c43034-95a7-4965-9ceb-e8d76a867654", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "淘宝认证实现", "description": "taobao-auth-implementation", "prompt": "开发淘宝平台认证实现的详细文档，重点解析TaoAuthServiceImpl类的核心方法。详细描述淘宝开放平台的OAuth2.0认证流程，包括top_auth授权跳转、access_token获取、用户身份识别及token自动刷新机制。说明TaoBaoTradeERPAppConfig中各配置项的意义，如授权作用域、回调域名限制等。提供处理淘宝API特有的加密签名验证、会话key管理以及TOP错误码（如isv权限不足）的解决方案。通过实际代码示例展示如何集成TaoAuthServiceImpl完成用户登录认证。", "parent_id": "10e48e47-ef03-4f56-9d9f-4504a0820575", "order": 1, "progress_status": "completed", "dependent_files": "uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java,uac-common/src/main/java/cn/loveapp/uac/common/config/taobao/TaoBaoTradeERPAppConfig.java,uac-common/src/main/java/cn/loveapp/uac/common/config/taobao/TaoBaoTradeSupplierAppConfig.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:09:05.034404+08:00", "gmt_modified": "2025-09-17T17:51:29.543426+08:00", "raw_data": "WikiEncrypted:3rjnjunK9RnuTLRwamC21lAqM4XVAntIggPLDdVSSVvivM6X8uNwfzSFRP0gdtZprp2lVsjNPZ8hjzjdcfJ4n0dt6GUBRB+NDc/YCdNrJwvp3bMS2nCqMScCzIcJyKQaqrOogdqZ0nYFluWWWEO0gko2Aa3bShDRB0wft5g/nLH/VtTaI/QVEqply9S2WcXIlQUNQJrTvCcxfrSJkHSGXo3YjB3/Uk0C+66BOOnawCsKYzd36Pf/vIcoXcpEZchoRzbUHiRIoxSAj7UqXbt0yoYWXK0cKI3Xj57dKxR3O2rO5uAzPrxuyaeTHw4MVw0Csoem/wPqhssi4quboRxjlsKF5X//OuZF9idp7c4+JI3xZIe99K1qGf1mJMYZFof0x4BFKu3jDkC08lGvxa9OKTsA9uUWoqiXyUQpmNXz9iv1zMDXweUgWRJ33bPnDbjawTfFHR0fM4Sp2g38xu5ortEyP3wEehz6N87Gnne7yS4nu1YRR4IW9ltnNO7EdRayBXybohs1icoM78E7wGDS2bRFASF/xwkL65rFPpIPmMYOZ7m6d+yX1OpOawiVWEibZOJ8KYbKkq4jf7Tgb7fMqrezD9w478yoMFu1ppitRek3QwaQbCDzCqjQDI0Lap308H0nBQ6Nt8y+QEbZH3jxzNXK73zbGtPmLchDPAI/EOHXNVZaa3TSbd/HkxR/D1ucNwFUi2tP8vsJgTHUBG4FYCTNw85G99YBaArVHd13FORWFB/GcOtUxaqjBAQ0a0UegRqqmjnnRZYX+UDvdo8WSGqv8UnP5qwa57qVVxVxMYGt/x1fnGTgrqDkBJI75zolSg1ZEa1XqvJWHGq2iny9YeuyppQr6U2le8J7bhxr7vA7j3pHvYz1qH1lbvAjY5i7vMTmhtvGZkgdvNt818VDX/pTdbQhKk8SUkGsQVrY9TvUJjknt0RiwDgsmT2c+JnA4d7+dSko4gRfSaCMYmPv0f4Fh8I8QMiNgfNNBbgfDbmfOHzyUxMIPZS5tpu5yTiyWGMyWghYuKjdaIeo6QzJDWLKM1wvE+vo4EwvnIOnFn6iihb3LTzk8bzjBHoy92KG8FUWXS7SzfbAo5n6q+OlRCq5K59B9IzOX1FSC2oxh5dye3UoW0i3S3dMKnjrCCqb6Nofsc+T2udaikkNM6XJMtVJROVn2I+u544ucBS8VAj854Ifj/akN57EVD0N6JWjTDJv+rqcdO9Pmky6NRUtRXEzXWieXlE7oMOm7ipJSSGrVbrbMjoxezESihqANvkbQNKE5XfKj1DT5PwEdb40PraOih8PyXOrN7QlZHr2IaWHjclRP1Krup2KlkyXh0uCfxAnzjPLd6esRNGfJbbFbFPuqZqjXTFAtczfR1LFNxhLhxZ2orV1JKTN8wwuHAww5VwYQJNnShlihBesOjpCdz3Nowrhj8inxX1pqockjjQ=", "layer_level": 3}, {"id": "84196b87-4eae-49d1-98d3-e8f06be3b022", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "模块架构", "description": "module-architecture", "prompt": "深入分析usercenter-service-group的多模块架构设计。分别阐述uac-api模块作为API契约层，定义了所有请求（request）和响应（response）数据传输对象；uac-common模块提供跨服务的公共配置、工具类、异常体系和平台适配接口；uac-db-common模块封装了MyBatis DAO、数据库实体和Repository数据访问逻辑；uac-service模块作为主应用，包含控制器、服务实现和拦截器；uac-newusers模块负责新用户开通的调度与处理；uac-job模块运行定时任务如Token刷新。说明每个模块的职责边界、松耦合设计原则以及它们如何协同工作构成完整的用户中心服务。通过模块化设计提升系统的可维护性和可扩展性。", "parent_id": "", "order": 2, "progress_status": "completed", "dependent_files": "uac-api/pom.xml,uac-common/pom.xml,uac-db-common/pom.xml,uac-service/pom.xml,uac-newusers/pom.xml,uac-job/pom.xml", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:06:38.912004+08:00", "gmt_modified": "2025-09-17T17:12:02.404735+08:00", "raw_data": "WikiEncrypted:MaEDad3+dpW6rLlXdYaTGY+uz0vuQbTX4fobmJ8dk1cVwAIAqwVUQvQ4w+Z9pbagzsU1XuJJM+TeSo/L1AK/qr2YD3xTBDLXXtQZ+/JaLkUZg/M46+CL/V1TlmAuGh5KJqaGxkusu357ESRIRxF9uA5IlNnQbJOD/eoklF9wQvdKA+fsK7nIfv9vUZkz1p5pc9o3BgDHdR3qq1fmzFet9a9cs4/U4cY13SMEGmonULjs5pHfTuhB9YmZvM6W66GnxaKHnOOTGkrNyptJjCrnHO/uodePm1tK3a71WEBtDO3YQ3nv50E7WhFX1g10MU3T6JsV8XWZDQCdKd2oFB+ciGSqbMC1k0eB3jESFPzQwPxGyCmiJnPxl+p6qw1Dn2RamYJ4yNbkouv8qy0pYmLFNAapAyGmhBY5N/MFg5ujnI9NXHcPklfaD7AURWgk8yh8sOnSbfFA+lgKHLwb8QH72FMeH2dUtyg+cE9IlE4UCbce+t9L70NyIF1ahQNeuirM0303M7ah6DoQJZ9MKKeOfQGfqYrXuSI7KAZqdOQ98sbtAbolWdheedw4a3Yne8uXj18CcYHkO5soSb/xFhpqZI/5JXSxpo6AG/oA8I8vVpX04ywOvVDl3rdly5AMbI/YtoDpUDIOxAhDSY7HbKFax+bj1ah2unhi/A5WdHDD2ORm2vdtXg3bf8luIjQsWPUNb+syKtQJQjEgOR84slozy7tyHs+nQJrzYuHFpeaBdlsCAf1WAuT0ntoAW1Xm1DDM5nc5Cz72y7v6mFNeh/QMo9RekKm+Z8+auT77SQs9Giraf3XfwQVp1nLohpaP2arsezfAL7KqBsDfs87/12H8fJHf7velFL0y3SpMZ27ADae1YQ+NJPsFiYVhCGBvHUnKmUvBP5GhnT/GFrVBX1QsYSjlIZuw7AoDvQeYeR8CAslZF/4nfvykJ/FZWhvbGdabUxg9SrqsKtJx91rDcqPIGgdiYuT9da3CS8W2prWfyMp7cnSvDJRdk+rSANmwUY83b0DfvLAIcl9WGcdwLnA2DyvIp995atWJl2EIOtfh6Z3gle/18ZUNaF+kfHGv3rqpicvxrLhHOPsV/KdgLuKDJZLNjNmwP0AMdbhIkXqtZvM+iNPMIRNS2WQSSU82RR1eqXXJwB9+I1bCWSmugOI+iTrMDhdnHBgVKz8kJoW6YDI3iFthOTZSCALrdRFGqbwbM6LMhGauJPjBYd1zaBFlm2PzaufeCkyC4ixNUIttL/4zcELaXJ2MCfVFJOPHbpoW7sbenI1+6SvvRVTP1nA5pRvn7H/p9XNha5kBN2rkvMo0hkhUfGq8ZsbtYyEdWW7kAH44iQyaqk0BlXC6w7gcC0qSaZUVNHGAIshhEtbpOTwO+YukaEkhrfaFRQaskdunB/pr2P3XSaAW+SA2DJMqYgHcbWVDwySHzoGrxN+Up8ipu54qOeUNC2XAGbhh2KmM/i0ReWkYCIDvsVcAPeRCUSpIKfY0IkXw2Ne3/cH+lhlX0+lHAopnMr8LPcx4Y27ODUo61d9jQJqHrukFWMI9JXaHq52f1bnX+pHxOJb/bPnDSTMRqNQu6fHOaI1BM/MX80j5XaE/PJBo9BelqZQT5i810rsnJdWolxN8UYnfaa4=", "layer_level": 0}, {"id": "8ad78008-869e-48a0-ae54-58673875c3bc", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "认证与回调API", "description": "authentication-callback-api", "prompt": "创建认证与回调API的详细文档，重点描述第三方平台（如拼多多、淘宝）的授权回调处理机制。详细说明CallbackRequest的结构，包括平台标识、授权码、时间戳等关键字段的验证逻辑。解释CallbackController如何根据不同的平台类型路由到相应的认证服务实现（如PddAuthServiceImpl、TaoAuthServiceImpl）。提供回调接口的安全性设计说明，包括签名验证、重放攻击防护等措施。给出模拟回调请求的测试用例和常见错误码的解决方案。", "parent_id": "21f95d6f-85b1-496a-9f5c-1fbcb3015ee0", "order": 2, "progress_status": "completed", "dependent_files": "uac-api/src/main/java/cn/loveapp/uac/request/CallbackRequest.java,uac-api/src/main/java/cn/loveapp/uac/response/CallbackResponse.java,uac-service/src/main/java/cn/loveapp/uac/service/controller/CallbackController.java,uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java,uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:06:52.901201+08:00", "gmt_modified": "2025-09-17T17:24:51.69416+08:00", "raw_data": "WikiEncrypted:uxAckj1IXK2TQH//kEdeBI0ZdNUL0PDn+nucAby3CgnkSBTxfUMwZx7cd4GUW1V/Uc1Wc9dVyNCUGyZ8KQsNFacRRx/wjNi84Y7WD/KQpXumIqscG0kAfVa7t8k4x/T8cUJwthdEukuEcgGMSADg3ySEzZLYz4a029ROdAiPYjz8ik2mDSaygn6fyuDEZzd7ByT5m21vjl6BaZ47yH6pM2QuJTKUHKiknXIbiND9VsHLbOhzau6sAwVaTKGDuLatgAe7o0JHreALp4jXW1ucmurigfKHWsp4mdjstMJ82vDANanvDNArvxt816tMv6uqs2svsstlyeMFXkk0MrCbbdJANKeO5k7t7anJdpzT5Fc3qF6Tlxt9xa8VVFzrrDSBc4r0WDcJ2uF2n+wQEl0FnirJYoit6N1FlgLmoodCKS4DGbHU4EOl9GX4oDU0HHl2enGMwmvzLkhbStiypMhvhdp5DjUccHPOvf5PXEKndQ3LmmsJLJ+I5IS8D+BVBkJAGV7llb2Hi6JkJKzzgQuzHkucAk4m/Y5qAqvOMDu9CzkvOtQjhLoguy30u9G7oJCHGVQJLKuoWu+tIAz3Mv/H1FKp832d52suERQ4Ugf3zI1BMHsVNOEwJfztzrHyFGZxx3OxIyTPiZEAf/3Wsjq1xbtMiRD+MMyi5NVuBkFK5DSIdQ1kBsPBssFv9ldwPwsC61eeiDNVfGraqvlJmYwXjFtQScUUEb0ZHfDr4Csx8Le1RrWWnvvRq4WF7mqDIgfXVmL9Ylytdwq0OEpi3Q+wriV6AKuV+PDGX5kq1iu5M+QLlOvQJi85UHE2IWuY2BXrtoZzlXNBKQnNyqapk9VUVv/e0emxClsp0to6hFgHZybRZilip3wMbagqozT97AQ3GR412iB1Mgg9CxT/F3M4a/AwCG669exAnPCke8F07HNewGp7yBu75woE7Nmda2tEMonJYNck8gsp2+rmPGbZrz8hWwgEHUzpmmkQgHpzYTdafBH9H+RX9sU89Owr/CxPKcaugFCZXO/vLFgj6opXEgX25lFIy5yHjTrR3mx+I36vAfxhiWhm10QwpdheKNYUwQ+LvQwmVRVrQY9oXCb5u1FQWo5AsqlUwRaFsZa/203ZkdyfzUSjqG/0Yffdv646HFQEhjXPktY2j0Tk2V0WviL9k3D3qUmtCltfVL60HPRSSEitarDujN0+moG5aH9oIQuNZYW6mTSSSJ8cfBPZ6hvOk9hnLS3Vz8uondDFkru2x5013pJ1psl2OeQjKd7TI+1PDcZPWd/Dk89paf0YREj2uOT8wnxiJrUfNF4nCt3HkkfwnLD/NfPy5fiwZAzJHO8580ZEGdAqJ/XWPQ4Y+n+abMpUtzn51ufoN4taiAoS6ymU6ZIx93PLclORTQniFCljpD6Ckb0K92w2U+RlDD+aVUHHq4x7SDkvVIA40xv3mApcW7XDSLeClKKbXng78JPDFyv2XGC/muP4/XGEFM7ouwbMmPx8bXht6Y6j+xpUPZhilAeWRYWshG6uORHZlfpKEqNffBJ11vlqixL/lyXFwo+LG+pfGZrXx2E9vZ4snG3WSSpKH2xqXp3Ay+yCEMzy3TUhMbyEqdyZw2asMrc1W2FH4heFoDOdkmRMD1SDAPKjWInXbxCxLSpezKh0", "layer_level": 1}, {"id": "89fda65d-105d-42d5-a727-d1e38d49ba61", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "uac-db-common模块", "description": "uac-db-common-module", "prompt": "深入剖析uac-db-common模块作为数据持久化访问层的架构设计。详细说明DAO接口（dao包）如UserProductinfoPddDao、OrderSearchDao如何与MyBatis XML映射文件（mapper目录下）配合完成数据库操作；实体类（entity包）如UserProductinfo、AyMultiUserTag与数据库表的映射关系及设计规范；Repository层（repository包）通过UserRepositoryImpl等实现类对DAO进行进一步封装，提供更贴近业务的数据访问接口；PlatformUserProductInfoService服务接口及其多平台实现类（如PddPlatformUserProductInfoServiceImpl）如何支撑不同电商平台的用户产品信息管理。强调该模块如何通过分层设计实现数据访问逻辑的解耦，并通过DefaultSettingLocalCache等组件优化性能。结合具体SQL映射和Java代码示例，展示典型的数据查询与更新流程。", "parent_id": "84196b87-4eae-49d1-98d3-e8f06be3b022", "order": 2, "progress_status": "completed", "dependent_files": "uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao,uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity,uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository,uac-db-common/src/main/java/cn/loveapp/uac/db/common/service,uac-db-common/src/main/resources/mapper", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:07:11.368985+08:00", "gmt_modified": "2025-09-17T17:25:20.703264+08:00", "raw_data": "WikiEncrypted:dM0MoPE0IfmxFmuMw7gEz2lmNliqN1Jf4bZTaLclYodlYVlvX7+rx0mEGzywOud3Po70AH5ccjZnae1EPIOWsDTltaEpSf+SxrlcnuZPdczGPQ9RTzwl18m0gx832PGlkOnFsJAEQdOKxJijozOJ+8ESgWvXFLf/XYnwK+WtagwgXzsQJ0vHuzefEsMJr0DimHBf4I2yJRgegx+LR6PkxSDb77t7e8kdiN5oZBjfOW9CJyNzd0cgJuiK5MMwccal5PQMn8F1itmTcqsiCQ5UfLIhdazKIqnQIca/sN86XlyMQOuLIEpqg3etfEP5IGLlfqSBWPLK13kX5Giuj9eBlbGQNZZ/e1YkH6D7yhhjWXd+OeyWJXAU6wrNbo/PUWxU1zTspGnJ92avWEkTfZjfPfWZjgk4dsx6H/v5yAqvo+a/mQ1kZy/gdHm8s10Jfp36xjFVh1yURIdM75f92YsbkgtdjDNYKq4NBoZ2m/uUUEibwKCR1iNWsx2roNHUxRLX9vsbqoxsXEtqW3jN1sgicvKAEwnDkT0i9iUzBQZRVw30BWoHxWvVbnIBuqf07MHJRWWROuHGnX8Dl3D3RtOtRVz+eLm8Hmbm58b3bMnAI6eqZaphJ8EdXSpSLAqtfHHyZdFZ8eOt9JGeycchFuAMsRabgDJas1B+70hCd2sskIHRKTSmf5kf/gMCxFvjgjk9tE94FZapu8/hFiHN9p5cvPu6Gksz7puGWn8I8zAcvrBjyP4DEHjuRAipoW2Pz3FdktKUixtnfD6gAiBlFGaPKO2dpmKvOauLNOcOzASESeYLzZ7YJ6tu+odGvPRQ34LB2pJLlwLNWdLHV+W+a66ezV1ogQ59amhXKgXjMeE8dKbX3dDlYsbUHPmmGAjFpd1QqusmigOAPN/Ab12DhhJ+VzbB11lpl/xlqcSP97EvQg6UT2ZTXWhgwNdYQwIdom3g5sCRDeNt4ECCro6IRbn9G55uhBZm86kLuWP5QeoRKCQqmUh4FsLE6DUo/NU6jytswX/nwsKaR99bnXBgkzzVAoMdF6PbdKto1izPhM+aelnsjhfosoQGXkFrPH1l8vAPDKwDU3z+DkQXMY7uSAMtUOYdT0xTYDn8INGJoYtglX9NMtlUq8bdtPs2tzbXKGyeFVV3TKFTkZ71ARudmaIkjG26Jlg6zBcg83cbRGdsxG3527DIAS2cn/U1OS9/ZBws4a6o4qEZcCdmCBrrXYV7KD+HvLyFFp9WKr5JXFsBnML/bPYuotH59A+ZWPEiN5x5Y0dUDkM7oMzGkEvK5a25kGxDKaAFillKqAL2iQDk+5OFPRIC0mwamH7/UXkgPCGJp2MTzzEzRXZlSO+8SrRNpi/j2JAgrkp4R86FwLRl0ANDunIv9ErLqP0XYwVLfRnn4NO006hURsl0J1v+iQtGmHumuVIoEeMyjNP8lXWZ9gQhAeVWWIRPzIhEH5f/hsWX/fU05Fki1EKHmCEqMJmZQZl7Z+BuJpUMngPNzbOrPjLXwvOCV2fQHJN16y+5BAfvjQhk/mo8OIu2lMn79wIYzVEpqDvsGDs5fsabnGNyQi89oKCi9nq62sSSCVNar36xvpZAxttDdjqz5tM0XETSk4Apkyu034VXPqV5pUuTn8d+Xk+Qx0C0tdNInRHCG3iamhQ4mP/2h9nengHrtlfXOSF3OvXt2n1cQdz1HU7OaB52gkHVtGmFUjA1k+3aCEjI9Yk0JMjiV62spWh5ktUazZ4ZmwExK/X9HADTrkSBJkLaXNwjI2Q5k86+7hISwEsXtnGL4HFPip2NyFZj3XNyU/vq3V9OTT34Z2dXQAzC+pG1ZYFHj5gXdlN1uPV48P5xBr+RlcFD3i4BwHcngKfS/yfpYfR26PxN8zO4L5eTfXxsZ+UxueQd4tL9iHIMTJw8E/mIzkTr/ZLnpnN/9+7j5HzSI5/w36uzctgezLxDJryAbVugCQr7V4W9t1qUWvqo37H5i0uKVBgdJOtTM38h/h0v5XVggkaxNc9x0r/SvX3lrEaObr9oxBaSVDvHNRPSnfTXLw2nSp45HAmz+PyKwLzTEIrKho1yTIBxpY6zDQAlmHctidz08PlJwQck5/lS", "layer_level": 1}, {"id": "3ab1acc2-e81a-457f-ac28-1d6f4e6cf248", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "授权信息模型", "description": "auth-info", "prompt": "创建授权信息模型的详细技术文档，重点分析多平台授权数据结构。深入解析TradePddAuth、TradePddAuthPay和UserAuthTokenEntity等实体，描述access_token、refresh_token、token_expires等关键安全字段的存储机制和加密策略。解释授权信息的生命周期管理，包括令牌刷新、过期处理和安全审计。结合Mapper XML文件说明授权数据的查询和更新操作，特别关注与定时任务模块的交互（如token刷新）。提供安全最佳实践指南，包括数据脱敏、访问控制和防重放攻击措施。", "parent_id": "103edcac-ed4f-4fff-b35f-73e2fafc3279", "order": 2, "progress_status": "completed", "dependent_files": "uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/TradePddAuth.java,uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/TradePddAuthPay.java,uac-db-common/src/main/java/cn/loveapp/uac/common/entity/taobao/UserAuthTokenEntity.java,uac-db-common/src/main/resources/mapper/TradePddAuthDao.xml,uac-db-common/src/main/resources/mapper/TradePddAuthPayDao.xml", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:07:17.431358+08:00", "gmt_modified": "2025-09-17T17:26:36.426467+08:00", "raw_data": "WikiEncrypted:8KAaDORgRF+kec+AZh7pwagQo9fMtjd1Jq3ntJ9m8sTPRiOJYKnXNRR6VAdX7NqNSA25oEq/e5NqCO/dReTXm0/fE0JYDyTY5PAC7LAKajJARgcrHjm0Z5d02AhwPXud1w+PMCMwaxkbqobXcIhWScZKga3NZzM8g/uaviRyxAv7/ZY11bZn3qL77MFq0ZPHk1MMscvVx0lcH8aLwjSG4tc2EjcbEjYhjRqayuOjwPEZWxPhB3rYe5QI1hjxid49Yt4CzZkiQJnQdxBbpepZE8xoVIo2pOT2flFYSxkj/REOYdyH4OYiP4BNIDplvI2EpHVAdA0JXQUEB+3B+CsImxmh/8OnHcuXk7Bavx5NzrepCoviCqLx/UbQQxIdhXVnjipo/IM7+w9mnH9r8H0SxhV1QoKLO6DK/6P21zUkIzdyWPops1YeybetICUSCNSOBvcTCgONmtq70QqNslq+oMqKjGBPcG92c1L43GXc9hloO44Ht0xMplvokdrEdWoGMgp1UcAaXI/y/b12k2cYgdLsiXvG9+VZ5kUy8LFH9g2ba+actYG6DPduPucYtaeQKzjqucSHv2HXy/xPqYYk33uwzz0Nw/jw7+9aIJU3hCgi6QrnKjdghKhuLEZT6o/ReqjC7vRH5JXdERvp9vHwcs0zNRLIH0HPY58YdvMaThtSskFJV6qWNnryZZbkbhzEvuyBAdIGVpViGL1xEojL47MGZYKZ0jCudmLhdhkgttnrd/GuCFFaICgmnMGwXsVFjLjPKfWFjxNbTcJK7pMuzZeFKNbNSxOiV9QAKZd0V9Tbrtpvq/sEs2+MBHmr9LRASieXlppdq7XTemMgcw4iacVAcNhoUSewZMuotbeqWfZQfc4OOfitUsDcUgANWtfmp1KpJP0gAD58ZxDIGe7MDRz3GSEFEZ6c+xsWtUIKUn2xjmONbSQRyanQEaizFBwx2ZDEPrW2fLNOGhcErpv1ZClMSNAv/q1zCqfDvE1BXFgZcLU72IA9Jh/X6coJKKT9k7sh52gT+MerOcWU3Nw9kT7BV1NA88x0vBMWVwoxxu6aYKcs+yKaDdKcYLg0/mheaO3xcc29CEEswl62nAL5wPdV5TAQfZwl3xf38hK5tiux+f2MuCUZZH5EO0Xeovi9t/uMfEB++Idmq75I4RHU+7bxw4N/+sdFoeu6d7KxxFAGqomU1dfZErNvr6qbUa0VPUmevtqoUUQL/D3flSqZMrWRLR6VKSEnJo1DWvfbWli+JczZuXCu5+z2K5Y/9glv8W5980G+kAG3IR7zZ17wlIOcN3WN8mqx7QE8DuxMhYG5MTeZuHHWqDIXOZFTvaOZr7tNL1L2/A4dnMBbYcimNuLB/gtBm5dKWmtTaocbAiwlojHlP9LctpuZVSY+GV1LIczoOF7X8/rllCFSlrQ0LSBfn6stBG+Ef4C+Jriwx2BGMC3EkyFIig+i85SiIkelq1ju4IYAp1h3xEtsORJ3XfddDz1SB3xFZ2JcIFLFatRA3tZxZMe3+FjOWWmTiUm3Mk8wBXCJEoe5vlbJlqsFqSYypSJWbV3ZccgXejAt0tmcNNS/M96vVZxUpUrt9+8OkXrkIeUl7NNeLECO9iqvEMZs1D6kL35dh68cltCUxsw=", "layer_level": 1}, {"id": "f138caf7-70d4-4a22-96c7-6c32e8165d57", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "用户信息变更事件处理", "description": "user-info-change-event", "prompt": "详细说明用户信息变更事件（UserChangedEvent）的发布与消费机制。描述当用户信息发生变更时，系统如何通过CallbackRequest触发CallbackController接收回调，进而生成UserChangedEvent事件。解释事件如何通过RocketMQ进行异步广播，以及SubscribeUserMessageEventHandlerImpl如何监听并处理这些事件。涵盖事件结构定义、消息序列化、消费确认机制、错误重试策略等内容。提供事件驱动架构的流程图，说明事件在各服务间的流转路径，并指导开发者如何扩展新的事件处理器。", "parent_id": "484d4aea-c0df-44d0-9b9e-c951ee96a3dd", "order": 2, "progress_status": "completed", "dependent_files": "uac-api/src/main/java/cn/loveapp/uac/proto/event/UserChangedEvent.java,uac-api/src/main/java/cn/loveapp/uac/request/CallbackRequest.java,uac-service/src/main/java/cn/loveapp/uac/service/controller/CallbackController.java,uac-service-common/src/main/java/cn/loveapp/uac/service/event/SubscribeUserMessageEventHandlerImpl.java,uac-common/src/main/java/cn/loveapp/uac/common/utils/RocketMqQueueHelper.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:07:38.43429+08:00", "gmt_modified": "2025-09-17T17:26:29.528893+08:00", "raw_data": "WikiEncrypted:eFStdOOCJ/uTWtXKsz8g6rAyXBmGs6qouOmKsmJF6H1rJ3KpVb62+tOIUj1VznGNxrSsKEmmg8tu18xdELnR4CNA4uFlftI/TExWCP7r8gnU14fdvhAkaWbwiLWzKHE2wgk3hekeDnLRtIC9okWn2CeVIC8bw0DB1HQP51ITOO1Je5B1/HprKhVcZQbU6bdTFkufs1+Fdp4glifeydWzq+ubruUfYoMXAGBJduOfwF1oyEbFQdt+rHfdjd6w6NRFUOXSPVapeEcmey2O0YRBnKBbNJMmyjESXHNewNaRSibLquZZU4OM4zNoHk2y9rnzvIfv8mfQ49wbeOkcN3k0RSP950bgFHVb+YW/77XtfNMKUYFtUYk3sjH2nVFHaPGJ0Wqb8pGfvPq/LZLYaN4DbnviCI0XchYA+xdX6jSmpuJkBJ6GjJyoP3EMY1/N8EbR9SZhOSH34tiainDxL+4AcuTI8z7BeCPvSKmaQ3/ee+649bUGj3Uo2Zx79imIAwvbR8NLVBax72qeh8Qk4NkexjmUmP0x7Fvxnr6wOvxqAY56KFEwiYE4Xh4x0PxtBVAPK3Nlh+VJwQTH4upU/UKMBtfxWriSEH7jhVaNSp99581Ei6zfF8CIrTIqVxBVkTBK+ofYDSsVONRzUv77/j0M/m39lxvqNvNIE8zWPWI4AXQcGGUv5xdWM87fzCUQAmXxj//NUo9WXu5FRJRqN9lvI/X5iHpINVD5O8qMHwmBPzYBEH6mG8r4BvanoqBRSn8gP/6LtJhfbFqfzXg94r84sHHmcqDKzTFp3JQNmRSfzu360OQZtKK3ZcvtEfgxpuJTdardGpxx5hLC+nGP/62ASgMOpSAzYNd33H+5R6VygUiTKquZWrOPDy59d4CZl54ta+9PMSBuQ3HyZp/jYivnDAf0tprztpsiQTkVjY7rB06T44kbfKxt2FlKbCnGavm2SjpSSzAWE1wI4gIv3NmtlAOXk/d6g/qQUTtx9DMroTn+JPxzeO1c8hiRaEas7pM/nx2NanxckXDeU3B+8Atd4KPTeL7nNrBkMWbqBKGvXHZs2cPWKPSf1ipHwEn4k4CZKn3wx0+W74HFx81FlkoBJhA16/zEARgKUHzz9vbVku9DpLS1XaVALoE6Of0h/Ysx5KuQ/VuqV4KWWJeSdWg7oiiZpjWOujORTRR0t5kZ7zvZK0j1XBKtxqpbuLE/oBVB2Mixa/UK7qpRuzmCfAdNfdx8Dg1Rp+V1OMYkAxLKZKIn4uLoDXhxO25lTGPhrIH/h3MzZ0SscerMUnECEQkz3fd32yKfQaPFjyHFkb/knMKuzYZbqbrIOUapTncj97dxWUAgR41o4n+wIRwENTzh6dTvZ9ImHE7SznAdNYYiQbqCHMmM4DeJ/GeaCn9KUeAyjp3e+2FxuYT6vEyPVLmdNZ+0Q1aP7edd3xd5AmIB1rUW3/pRe1d6yr7GgRX7fVPsUo76YYZvgP2TCAT6IUc/dGOiP0emaP5tT4BLdKWRsxfcFWuC2GiPRxyVYwyyeNQaqhmtRAcxPH9u+PoSbLT+UAHLAv9oxNHeGgrPrxRqcMqaOCHblqdXkqN6qe9ncJgyJhu2D+aObEAJ3DH/1/ddm2r2HuONniRPG6MJ2Vp9qWE5Dmpr5+vd0g8r6fac8METhlihiKhqKhFR43OzBvvb1y7UCCJIxr2UBq2oyPOgiKboiHy59pEYFHPWTk60An3l1B20Llw6Cgi1WR6Ll6qgLQ==", "layer_level": 1}, {"id": "71443bf4-7dc3-4627-be01-75ff8e1327fe", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "仓储层（Repository）", "description": "uac-db-common-repository", "prompt": "系统性地介绍uac-db-common模块中Repository层的架构与作用。重点分析UserRepository、OrderSearchRepository等接口及其在UserRepositoryImpl、OrderSearchRepositoryImpl中的具体实现。阐明Repository层如何封装底层DAO的复杂性，向上层业务逻辑提供更简洁、更抽象的数据访问接口。通过代码示例展示Repository如何组合多个DAO调用、处理数据转换和业务校验。讨论DefaultSettingLocalCache在Repository中的应用，以实现热点数据的本地缓存，提升系统性能。解释Repository层在解耦业务逻辑与数据访问技术细节方面所扮演的关键角色。", "parent_id": "89fda65d-105d-42d5-a727-d1e38d49ba61", "order": 2, "progress_status": "completed", "dependent_files": "uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository,uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/impl", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:01.361826+08:00", "gmt_modified": "2025-09-17T17:42:10.484453+08:00", "raw_data": "WikiEncrypted:dM0MoPE0IfmxFmuMw7gEz/cxipl94L7Iisw89KjTOSK68m9nD7D/n0+VqRHzi8dlcck5Ly7VxOl+SvsffYNxe8tWcftCqcTHBagbEZ0BpkyZ+ubmxyZ/dgrioFaIIYk2nBhQ2xpBJ6kYdWA9Doinfd7lMw1Vgeh9Ot6rz373W66A2pt9EypyZjo67H8FaTmsCOL7XE8iOojp9td690DTkS3cDKfG4iitA9pPd3UqV24X4yEdBw5N0/MvsEwZj6KJpuPraQ8XR2Q1igWOAqCv+mY8eSVc5fmJUXxb2MTHNiIo+NvAG1IqSROGHhoGBQhgnbi9zNaShMQLaBG5cQVeSrm1P0YgcycBJN2d/TD7ana8k023kW4cDnhff//C8c4Njpe76v1mPgQ+3gh9GKF0qwAo2PRwBRAzixKqxU0vAUm42njWk/pEGzQoq6K5+c0UD5bN22PqH1cIKVG9QpuqQys8mRHNunMfKpGoWtjiAwRJ/yF9o/OwwSyfW2aeTOasZezZAPRXhoKuTi/woJb3+wNz/CfVq8d+zKi9WAeLo8FTTHO/F9Bh/tG7HsWR+/2Vp3XoMxul8nNuTPP3uZ2movCiYsUz9Xert3CzgGS8nb33YC3lyYgVFAwto/gXlRPmAmDksnz4Mx54Ytn6oJJvlTCSfQZg3c0qU4gmJZgkRw1Me6wBJGuLQtlXT9qiDKGBDcG2VIxYVLVJfqT5l/K8OBxx+GBnyvlwIRL9PUcgESDLjWC/NjrOPdfhZfR5hmvnBXmVbYFTEeAizgyD5IaSKssLunZwURkAMP4l/lUsbuS9ZgVfUhIMU7exzMO0GbTdPiViFtZLcE2PGUi3v6X7ZjTZzipKeVPYrm4dIzf+hbNThEpsTuVYecZe/Bd39YR0dPlOjT5zKkF2ytzv5IPvnvelub8/I26Ac2iXNBl8ppRRat9AP5tlC+CRRObi+aCVSWR5IIduBjt3GBTr7oWYjQm0Jm9MJyr7zcL0QEelx5EWBcONrjjAYxj5QYs4FIShKNU5QIdDC/l0R7uuwnEO4BQwzFQii4eWL7n4A8mtn7JpeUHLN9+xr8UDVkxQrmA/sOs/BZMSq8Uq9s2glNjU/jn3nkI9YbiUpNnUQiTP3s7YzoqZx/bUkhzGBtn0wY8UBeqRhSdYhaA/4LU9zzHIFBZ0uGO5ftdVBaUp/kD0HjTAKcqwpV+PgwUY2Vs3pnEMgUVhvxJpmx1aEFozsZcxFmP9MiN0qejS8po+BdpxRjZbkCv//NBfegq+2woLKocah84EKLV2MeMvy5mcWpvRWygGac4CVnHWShXZhC+6/rLgz78NezFxDjNBurJfKzg77i/IQ39y8w/Es2NFtwJ+3zGg17hfyvT1CJhc1EJ3g0R9+P7aRIUctZHL2gEcjopEXV/xPq5wnlhjQi7TkFpwAVBxVUUSFg8qw/iG6H/G8k0=", "layer_level": 2}, {"id": "5a9d6d59-14ab-4e62-a64d-72d415e04c6e", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "异常处理", "description": "uac-common-exception", "prompt": "构建uac-common模块的异常处理体系文档。全面解析exception包中定义的分层异常体系结构，从基础的CacheWriteException、DbWriteException到特定的TaobaoException、UserNeedAuthException等业务异常的分类逻辑与继承关系。详细说明每个异常类的设计意图、触发条件和处理策略。结合ApiCode和ErrorCode枚举类，阐述错误码体系的设计原则，包括错误码的编码规范、国际化支持、与HTTP状态码的映射关系。提供异常处理的最佳实践，包括如何正确抛出和捕获异常、异常日志记录规范、异常与API响应的转换机制。通过实际代码示例展示全局异常处理器（如@ControllerAdvice）的实现方式，以及如何在服务间调用时保持异常信息的传递一致性。", "parent_id": "5dc01eb8-7e2d-47b2-96e2-f5337e92784e", "order": 2, "progress_status": "completed", "dependent_files": "uac-common/src/main/java/cn/loveapp/uac/common/exception/CacheWriteException.java,uac-common/src/main/java/cn/loveapp/uac/common/exception/DbWriteException.java,uac-common/src/main/java/cn/loveapp/uac/common/exception/NetworkException.java,uac-common/src/main/java/cn/loveapp/uac/common/exception/ResendMessageException.java,uac-common/src/main/java/cn/loveapp/uac/common/exception/StorageException.java,uac-common/src/main/java/cn/loveapp/uac/common/exception/TaobaoException.java,uac-common/src/main/java/cn/loveapp/uac/common/exception/UserNeedAuthException.java,uac-common/src/main/java/cn/loveapp/uac/common/code/ApiCode.java,uac-common/src/main/java/cn/loveapp/uac/common/code/ErrorCode.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:07.059115+08:00", "gmt_modified": "2025-09-17T17:41:58.655572+08:00", "raw_data": "WikiEncrypted:+As6<PERSON>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", "layer_level": 2}, {"id": "79c37b58-5b2d-4a6b-a941-c2079e7d6f92", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "拦截器与过滤器", "description": "uac-service-interceptor", "prompt": "详细说明uac-service模块中拦截器的设计与实现，用于处理横切关注点。重点解析UserRequestInterceptor如何在请求处理前、后及完成时执行特定逻辑，包括：基于AppKey/Secret的API请求鉴权、会话（Session）有效性验证、关键请求参数的审计日志记录、请求耗时监控等。阐述其通过实现HandlerInterceptor接口并与RequestInterceptorConfig配置类配合，注册到Spring MVC拦截器链中的机制。文档需说明拦截器的执行顺序、对请求/响应对象的访问与修改方式、异常处理策略，以及如何通过ThreadLocal等机制在拦截器与后续控制器之间传递上下文信息（如用户身份信息）。同时，分析其在保障系统安全性和可观测性方面的作用。", "parent_id": "de8c6980-4198-46c9-9021-65354ec8ffcf", "order": 2, "progress_status": "completed", "dependent_files": "uac-service/src/main/java/cn/loveapp/uac/service/interceptor/UserRequestInterceptor.java,uac-service/src/main/java/cn/loveapp/uac/service/interceptor/RequestInterceptorConfig.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:20.347143+08:00", "gmt_modified": "2025-09-17T17:43:07.36898+08:00", "raw_data": "WikiEncrypted:XZ3L/EMswFjUY8Q80yiTV/O+ST/vhTxXnRo7pAsrXXiZhHB6xAewJJ+7HmKKYi758yUnQjcm3OHh+S01vYWFuLRCO8hQXJuWDqhgpMNx8IC6+inI6TGB1es9aa4hgiWF4x+y86C4A3403uE5yzriTWOF/7RkywoseSBL2BSvgafrGTnwaR8SJ3j9xgEESy20zc0S7AaX4MgxV5fq4+N+G7DXLHsHYLfg1z5UQbaKAN0x9Jh4Pfa0BMMoSTycd0d8K/mmoIB57dhE8RX0DXb0nhYSaFZxQRGuid+QbUJ3warqYSCWciONXTcvipjGJWMprECW4FVuzRB6Hz0EGZzodFGIeJsDlGE0g81vnd6Gp9+sXLwJm4YBvqFkY8ZNvnpF//czVNs3mjT8CSZ3DSVnhXygeg2/jgiFI8ZcAeLTi+5+dB9c8pjoVG+LVtnLXCBNnrntuwnOOI2BfoDHusazaTOkNImY22N2QFmGn30ICKMV1l76lbL8CzCBy9EpCn8SY2VwvJ3Y482CFWX7AxIOuAwhYYg8QNimLu8svUncweKCO4vlQegYtLuHn5m88jV2bCZ8XD10CkL7vmAZ4F1dXybZf0ddQyFJlftTKFjkt3GdAdYKUBr4YAe05jzqm++ZngiOpORqEq3TAcoNuqlPGRb8tJRjt+h9p93W5OfKBwsJPmDhMb5ur9/K7sxWOiGKa4EaIoQu/gwX/Lqw3c4Mmz+/UTyXGAqA7ubWN3GxlhvqvuPaWxd9KudOgb6ISaqMh8XvRHRVrngGQEbRkaNZ8MqkwB4aNKneyg7i4rBsPchHd6p34yQkjB5ZkS8W5hGxwx/J3/EV+U6ljdLXpCxnnyn5zCoicJLTzpj3JZrSVeLjT53DJW6ttPxw0vXQ0lELPBiqYJReByW7XDoZrtPFlc4VzqmhB3jhkSXcxRjMmadQD6//5zOONFAUsBoey53JS1ymXf/wp5rRX1hipPGQYcK6yWJ0zZ01482uIaK5L3b4JQutz5fT6dSEXSTOZzi+Dg9XDK/bTRXTO94knw08lUDzjEmCVRsuV5O6fJmXmxa4L26oeiYao3nrW4tdkkMLxbXQ9HxgCE5PnYMmMODfweubwfY5dqedW1dzTC6HFpv+0upTlYeYl4umwjmuB9JCUYzYPGguKNfROAWFMPQHLIOsq23SJ4ZXl3drLWDhknOE/hGf386d37tSFDMF7frBN7agyljrAdeQcIp5mKaOjY/dnEahOpf3hqxW5J0vzOjX6ZqnpANkClmt2c1ohtKh8R1M7K2XvOmFi2g4TTdGCSYvreEF0dB3VlmszeUOzV94H0nGfjz+4m4bW2mIXSRDjCdVtMl+eXXSDwNHlqGy+IttRF61IPqNQHcm0UgX6dmngNiN/DCotlVOE5bSzAACpCinXADzlmsq4FL0lWcIIeFkKKawEgix+U656hwmfoi9XBWlKF+Ee7atmAswYlEpRFYC5VWGIsya9SG6+S/5l6eR9FFuWOe8X3GKmXDA9OSeqD+YjMOz2Lsc9undUr3Z4r6inHMIx55jLlKkLOx1NNxbDc5K3egaYsp+fG0sdyMlZGXrQEm/6gyvrpe3CoY+TytS4k+hbi9IgYI4uzNnyZ6v4pwENJr9iegILY8Tz8vjAcy+JWaFkacuPSxhDNjq", "layer_level": 2}, {"id": "5a85234d-4ba7-404b-8cb2-dfc9651f6e3e", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "新用户平台适配逻辑", "description": "newuser-platform-adaptation", "prompt": "深入剖析新用户平台适配器模式的实现。详细解释NewUserPlatformHandleService接口定义的契约，以及针对拼多多(Pdd)、淘宝(Tao)、抖店(Doudian)等不同电商平台的具体实现类（如PddNewUserPlatformHandleServiceImpl）如何处理平台特定的开通逻辑。分析适配器模式如何通过统一接口封装各平台的差异性，实现业务逻辑的解耦。阐述平台选择策略（如通过platformId路由到具体实现）和扩展新平台的步骤。结合代码，说明在适配过程中如何处理各平台的API认证、数据格式转换和错误码映射。讨论该设计模式带来的可维护性和可扩展性优势。", "parent_id": "f4d5c9dc-098e-4306-bbcf-0fb86498f79e", "order": 2, "progress_status": "completed", "dependent_files": "uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/NewUserPlatformHandleService.java,uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/PddNewUserPlatformHandleServiceImpl.java,uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/TaoNewUserPlatformHandleServiceImpl.java,uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/DoudianNewUserPlatformHandleServiceImpl.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:29.54742+08:00", "gmt_modified": "2025-09-17T17:43:36.539495+08:00", "raw_data": "WikiEncrypted: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", "layer_level": 2}, {"id": "e2d66b84-37c4-45e0-a6bc-01d619147bbc", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "用户状态清理", "description": "user-status-cleanup", "prompt": "开发关于用户状态清理任务的详细内容。深入分析ScanAuthCancelledUserTask如何检测并处理已取消授权的用户，包括通过调用各平台API（如淘宝、拼多多）验证授权状态的实现逻辑、状态同步机制以及数据清理策略。详细说明任务如何更新本地数据库中的用户状态、清除相关缓存（如Redis中的用户信息）以及触发后续的资源回收流程。解释任务的执行频率配置及其对系统性能的影响，以及如何处理大规模用户授权变更的场景。提供数据一致性保障措施，确保本地状态与平台状态的最终一致性。", "parent_id": "5e77a8d5-d8b1-44ac-85da-7479b9b4a49f", "order": 2, "progress_status": "completed", "dependent_files": "uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/ScanAuthCancelledUserTask.java,uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/AbstractNewUserPlatformHandleServiceImpl.java,uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/impl/AiyongPlatformUserProductInfoServiceImpl.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:38.212282+08:00", "gmt_modified": "2025-09-17T17:44:29.081758+08:00", "raw_data": "WikiEncrypted:7GMpDAULkInIjOJUaNlZ4kZ/qmjSkeTJ/tYjk/yL1ffT5HLBVoFOWn3Jdfpk5Laz6BwYLgYpHNYAXs8aU8h12MQenGpfXJwCAjxuy00cF3MyDcqFmrT97HqYXznbK4sKY41bOhFn85e2Ejq18JS3NzmuJP3IRaN8QTwGmiQDnNeMGtIUEJ6yma6mlVw0gokEXihyl7symwHYr6TrXhpKimWClGWXnIl2Qm3p5aDQTUwO8TibUbzbUDYDw3WuKR7d9NiAXjbiMU0GnXfQWPf1AN9rq8pN7WLT8pORijab+lUHO6XDcbXjLoA7NRjyWIV1JGBmmA/13hJfmdn3CXmCTT0OWrNEOQJjVhNHWLdzbJiFCXjzrhgc2GwDBv9hisVVVsGKKvSm2Ak61VxtIJjNYTF7V14iFFqKmAEd1bSOy72kHB1+ZABauvUlqob7t0k9kkNFfLVHPuMz/vSFBYI31Dx9Zoh7AmTbeAQgugplKWGY+hrPxS5oqkvUas+SpagqcQQlIsNrbghnbquekLaN/MfVO2XALKBLcJkxJpfOOoSINYmKkjv8lEeINj69S3WMgMbLhDTG2SPLOgXZmKaTtmMR83yJA16yQl7DV9sToBappLvvsXndgzIwAhg/GZbXAvHYLLRQ9vvzijoIdbf3dbei2n4V6Pq4ahNVApGrX+BPagz5ZfwdhsZFtxauA5gDDJ1AUT+fiRD++wZr20wtvmTPmtho+FTJ+eP7iyzxc3qgcLgPP/C/3QIJDuHzbezPmxSH5hoOhGHeY2eQawsrMcz0BPqPC1g9j7dtP4/3OhdvNJA/WGyvZ2rT2LR0r9dxYPjbrhqNNmJTf4bnU46Es1bGZLshm3cHfASFlWEAoGwsWzxb35LBvWKMLx4VMi0oUHPUDGAIZOTNRlr+wlgxq1UkwOiupB4R38pUn7eYcSAW1NWKi7fKcUtgeiq/+9wTPnP33oQOPOjGyBSP+RNfl5E+2J+0MLofW1x2gXfSG92RKsRJUv/Dr+JET+mpYpuFzTc4hru0YP38NlASV4ZAlksjUPpr5dJqmQNOEE2ok9PgOZHneuNTKPUVjWvMo7XyZeYQVxBHzp8RsTrL2UOk5QOWuw63c6PmIbD2BfeZUFXn7QqCkMadgI9/t2Bt9zr/8ta9M5T0w3cTt7NbJsgRIvXEEhmlFf6V4GYBH7ZWRUGk8hxiVxs8XAs1llSuBZAKRziOPGVejy+BRegZTa7iBNWf+aU/N3Ms2ik7vABa3aYMZN3dpQ6ZpbuZwKkXCXDyTFC32MimwFUKs7vQlwy0lIqvQMBMSda3MnIy/mGDOGlhXKZR00+5A3pXZmO1HVRtnIa/AX7SODhpwgifugRxrz3asUYXVapCvPIOSHNAYlC3wdELBCEGEReBzs9IFS8L5MVgIlgt1FRu73EzXUJMzOrbJsPxEjuv7UxvAaWnADebYNhZT22Kx1ZSkZdOGlYUUi5iFk59gyepP8+0cWs9WBstRakmGjztNaxWwrj4Ernhp1BkLHg/KUG1zba3b7rrwJLUzdgAPKVLu7iFiKJ36tRiL276t/n0SLNq4u8iOiBO3OI39VzcNq3qdUpZ+lVAACqYXwDkPCgzs3iUafNZyN8AaTkwSfcikKArizQr7AnySQ4BIry0b311pPfn+BQwiWSVEcD+PeCx/k9Dwjo/EYaOUmS59BFznCDcF/vryY2kbgSzHiKAOC3ONZGE5pGIgstJi+hwnmr03NJuOkne/Q==", "layer_level": 2}, {"id": "8cf50346-63a5-4e23-aa18-311edf07400d", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "用户产品信息扩展服务实现", "description": "user-product-info-ext-service-impl", "prompt": "详细阐述UserProductInfoExtApiServiceImpl的业务逻辑，该服务专注于用户产品信息扩展字段的管理。说明其如何实现用户设置的读取（getUserSetting）、批量获取（batchGetUserSetting）和更新（updateUserSetting）功能，重点分析其与UserSettingsRepository的数据交互过程，包括数据持久化、缓存同步策略。解释该服务如何利用UserSettings实体和UserSettingsDao完成数据库CRUD操作，并处理并发更新场景下的数据一致性问题。文档需涵盖配置项管理（如通过AppConfig获取默认设置）、数据校验逻辑（使用ValidatorUtils）以及变更事件的发布机制（如触发用户信息变更的MQ消息）。通过代码示例展示设置项的序列化/反序列化过程和事务管理边界。", "parent_id": "fe90e6f6-d714-41eb-993e-136cf1422460", "order": 2, "progress_status": "completed", "dependent_files": "uac-service/src/main/java/cn/loveapp/uac/service/export/UserProductInfoExtApiServiceImpl.java,uac-api/src/main/java/cn/loveapp/uac/service/UserProductInfoExtApiService.java,uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/UserSettingsRepository.java,uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserSettings.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:56.893903+08:00", "gmt_modified": "2025-09-17T17:52:48.182037+08:00", "raw_data": "WikiEncrypted:A2q6dsB1G5KaNgPAwP2ZpComepcPChWk0csxm+zoxIKGZ6srS1T2+mItEn1e5YtYsG7sykD1t/WOlc+T9aQnMCWRyitGqyYSkHeyh2BKCb2HJ6PbUU/pgT0lE2kcJLquRKoZFTi/jk1miz3o8QhuvgGRNVPKYMq49s1jFvR6tM3zXv4smd11xbd3k/4AleGQI3Hmr7EAw5zXfqpdBo519yYQ8A8qP9dlohHUDg+0qx3HhZOj1BJxhkU44+Tw5c93MhRX81gf7H0XjuHVoMivoBUYr8j8mUBADPPhb9KxLk0FFs+G0P1rCqvLg27ZIXFNdq0IEvpNyHgv5OAi7c4NPlbfNOoknNKIN2QAmwpcChqj4ZHug44kNshPZ6QomnaheH6EnhZb8dojAYdy66lqkH08y8ZibnLAxvqYA9yC5cYBgtdRKHG+OnzUP+MhfEh/8fuSAKVdZYWBrcrHKLbfu5V105wIKmJNJRzsAH/JOS0MqNhMlCT8mB7kk2ogbeMGzRQ8Q3/vbXhnZUq4VrMSuHUrSm5QNOx0EL3sc6nvKOvxGykTU9KR6AY0oY+dpATHur+/Nr1ar3UAXzNz3IEUB4cmGgJhs5PPl2LK1DeRzc2g/wTcVCQeFQd2mNZeFcpEjZVuODf/cXzK36jW3rtihVqh0GcR2szdRPc3Ehx0e+1xh8QwcyeA/Yjd0ANhdAr2Fau6d4hjzTfIQK3mOgfNzE3ou0Oeq8brHt+SoU5mOoQvo9UO+YYsbHyb46UDu+v5s4vAu5e59GmzmMnJOIBtpBBYBeMLoHpS8PzW1KFyaFB55uxqyDirNKz9mFuBbLVgHNh94IQTLtiSqoQRIjurPD3J6fu7Uz2Esre0im7j0VaVSkf+dzMFFrdjVXF9jJu9ROPtkttRE0YMCZtXuMH9HHQVvfs7JvTLPc4SeATJQ3nAgQqB+ELhyFN4UusEeSdImwjeDUxpjLXL2PEGQ8YfA2zeavz3mjiZhKxCqq4uo6jU2obxxvini6Ti+dyqw4i9gRS3Pm8qBQ5PduDzp2aKTzJCiYmH9DyKh4KKGGJUprIKICknFBnmMoxWh2W9Xj3pEgwmZe3HE81XNItbkxg8g/2NS7Ezb6SUSt8BiaDram3Telox8pN3s6slEHGLlsf0gfdr0fTB3bP+3WFbQRR/gX7cQDagWoPypcW4buy3med4OufMycwbMTyC6jJWWydOzWmprd84tB80OO6rx5w8rNhDVRVrT8OVrNXJ8vwB/zCXleIEnqE/737Mx06zGVXJjQcgRAeQfNoceq215tq5SV/TKr+T6wDgdaGpylXIw/uJT9RKR4cSyQ8v4MBI3bXQd1qZ8tmag1qm594dLXoaJAlfBJA77H3coQX2duN/fSfFjqd4SEham85YIGchZXRhza3SavwqIG/ZWmbf7zgqA6RrfL29Ju39qpdV/8lgRO1kBxKD8x7KDlcpZ5kGlOoV/UWIrAC1A8t1q2uwbJGzHA48AveGRuzJYtb5de8EtG7PJqr1YU3e2kq2l/dv487aTlOze+Bed61BG5ZDJ6xdlxB81H21j8WhS3wiJvRUEuIJ4dgVmjy42ZARGrVCFhbARkywItoA8Wwku7fO24De0VFA0fVDVkKJGWYhCHtjH7epMNUwooKa0aNrDibavpRxk30tptFiCySmhH7KvPWly7xmICk4hKdj3wP1ht41174QYxwoum+EXn0qRqxljb4HItsAiEgCZ45N0p+iWu2JhhX9lCyO0RkySmNEm9XJWLZZEkmlpOIk1sHNbeTwVAQxd1XjTo2hL4SaOa4V9wemDrgDkQ/BKeIcdhnEuQbGdJIw/eQkE4buaIpVXw2T/rmrPC+wkNPmubKQ7QmRL9wZb8Fea5xtpiOqW4PtRv+Hn0JLLcHKWOKFmoYCo9fQdxcgRfeSJl4WIWOzlYPRaBON+XWf+FqLC4sjy1D0Umj+bxFJYp/lUz95YRcq/me0F2jk", "layer_level": 3}, {"id": "be4f2411-f8de-413b-8e4f-280f3568e62d", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "抖店认证实现", "description": "doudian-auth-implementation", "prompt": "编写抖店平台认证实现的详细技术文档，深入分析DoudianAuthServiceImpl的实现细节。阐述抖店OAuth流程中授权URL生成、code换取access_token、用户信息获取的具体步骤。解释DoudianAppConfig中app_id、app_secret、授权作用域等关键配置项的功能与设置建议。提供处理抖音开放平台常见错误（如invalid code、access_token expired）的捕获与恢复策略。通过代码示例展示如何调用DoudianAuthServiceImpl完成授权流程，并说明其与其他平台认证的差异点。", "parent_id": "10e48e47-ef03-4f56-9d9f-4504a0820575", "order": 2, "progress_status": "completed", "dependent_files": "uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/DoudianAuthServiceImpl.java,uac-common/src/main/java/cn/loveapp/uac/common/config/doudian/DoudianAppConfig.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:09:05.035191+08:00", "gmt_modified": "2025-09-17T17:52:32.356559+08:00", "raw_data": "WikiEncrypted:qjvuIcdaDiWs1eYx6G4d1jlORb05Bi/TnZF9GOIr2pWkU3KdfrAQHU9q4t3aotmWmGgShWPG798D1BLBhlV9rPuCSvU2Z3QwCm5pCO+9wPuEPejGcYHQkVSE4rdV2r+8XD2yHmz8IUwZ/2/IFW73UHIp81+3G4faqLi+Kar73Dc/hCWmh6htYXiE3ENCgMyGBJh7jBFWgoEgWwYG+tM3ET1MrBhSGdrjnJGgA5yOq4a5vjYTPXs0wQOX0L6l+ML2G0DFwSqn/+Lt/byV9oWX7oSs6phQV0J5K/GpuwhDddXixZn4Cq013d0KE1feIXWQA0Sid45p2xqRwmRMqT0XGLtsZ13MwtwmimHkzVF0dXD245EQOobdShbFxJRUwoFe0S+Hes2D2dv3LWfA4ffMmgw8h4HsHbBdOxTsnNGJKakDQ8G4TRIj2A0PDggoOZykeb8VNYhndtJuBvrV5e00oRgAgA4DKcAGpWENmYi6MNs3hiqpOJaMw9cXhmcSw89fypyGXO6gFzuRn3nHnk7Nn0BP/AcJpBEB5wAfxY95VGn6xhsYHR55FOLoI6jT2vKr0IbQUAyhVBBjQh+UWoCbpnq9UU/JYnXNxLT+NZU/m3Yk5lHaO6HByhgkV1tIH7L4Q8wtqTyyprtTOc0qaYzl+0ehhFg+3Mv1t4CKhv79coRE8XzUtHA76zNbWJnmlWySLZ9FXBWPPpGo7Wkrq/aTZR30x0+tPINi4ZoNpB5JP7A6yLlAnR5hEb5jekUnw8aEFTV54i3hLsIoNLX/pckZNM8H7rB9ysCl8RoFnNxWne98a9z7wyq+dsp6yGZMr1rrNTXkQd/ViYpT1ytaqTrgf4QR+6M+KXTogdUSauBo9RxwUpM7Y/fzOBrF62O2/7nwjwunsQqhnfEF9wktOn0peueQe0TD94RFaF8nulUCJMrLvmLsOiWMb+orIIRhYcucQR+BeGiBAActMzmDeDfteDfCowSSbcgSLw34JB3CXOYFks/byyQysk3DoMJE6ZZbMGUutq/2MQnYyd65BCh6bqDXJWa5Xr89vh1rUZ0Ll/U0Z3y0BkZWU4acGYZpfJ2K/mP+b3GArymwEkrRIaT+uDroJfRD0phf5nwQJjvO6M7kjpVfaueEyL7U2xH5lzMIFbfS0YlOOSDGkUrCeP8AVF8WLDGzVyYGSXcCBLTShPKumC8jfv5ZTPekC0yP1g/1aWvyEysDgRbPrYvtZeIgxS+wyEgAT9Evatb857cw/SsmNirMuPChERAknORExIUe", "layer_level": 3}, {"id": "21f95d6f-85b1-496a-9f5c-1fbcb3015ee0", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "API参考", "description": "api-reference", "prompt": "为usercenter-service-group生成完整的API参考文档。基于uac-api模块中的请求和响应类，详细描述所有公开的RESTful API端点。对于每个API，明确其HTTP方法、URL路径、请求参数（包括字段名、类型、是否必填、示例值）、请求体结构、响应体结构、可能的HTTP状态码及错误码（参考ApiCode.java）。重点覆盖用户信息查询（如BatchGetUserFullInfoRequest）、用户信息更新、用户设置管理、回调处理等核心接口。说明API的认证机制（如AppKey/Secret）、调用频率限制和版本控制策略。提供curl命令示例和Java调用示例，帮助开发者快速集成。", "parent_id": "", "order": 3, "progress_status": "completed", "dependent_files": "uac-api/src/main/java/cn/loveapp/uac/request,uac-api/src/main/java/cn/loveapp/uac/response,uac-service/src/main/java/cn/loveapp/uac/service/controller", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:06:38.9128+08:00", "gmt_modified": "2025-09-17T17:12:03.00297+08:00", "raw_data": "WikiEncrypted:C34GewOyK1SlumqKiPsSg+WPNa2UHH7yP2PPjE4/OPkUytUlTK+XtmhJqDmkbtEHDxQrdv6tARuxaj1pKn24Ry/E2uOuvmOKuvOYnlSPuvx0H7dmRQ3SM1/0XcFtFVno2SEWH5MdwsMIDy1FOeflGH7LswsKmaqrrufdSmMoPzxx0p0OiWLkcPMnTf53D/sZ3qTLYiC4oA9JSoTP7mawRQkaaOCaIgV0Ajcy9zMWOxbzbf6UOwDluKZZuWl/3TPAwTXvIiyRG3Z/TC/iDPLUKT+cN9ezpckN9yQfEoRfDzB2a9zRdZVgb5/dI9C7frJWaWmqPeZGjCVrSjdYz8Xr0aSt1kWBQ2MpxqPrXy8O1SVzwaIJcx82eOV9nS0Ik2paQhjD4FpuwX0vwiaZFSyedgS7uFEjF+BKlkiRBvJSF3XYsPiQhvSkvbrDxVY8nFu8gBmUv8dtnA+qgHum+lbaiPe3vEId2cx37gN8tC7ek83MS5gGVC88j0nxi+Iio1XGto9rAUWIWz9/ybOCHTa5GYGgMuN1uzS0j3vI46Eo9vxlLXZNymIt4rPadzAxFSmgr0L3TpMpcz84EL3G74ey34HndPLswESFLra6kfWyCi1/e1ZgX00LSHJcN6ByS6IAZdhl8WBUM7mDOVHzshHrJTki5byJ/mdsw7nq/qNYxz7rYnTEWzXau9LmwAfjXBIQCP4OBU/Q78QlxRESlrkmQ9NTbfYbzIUQwyEeY0CjPb+W681/IXa5dJo3qDg4MfzE/0YQtV0ckVwTzFO/IJxludwEyttedg+z3AL3Ys4kHOqt2q0eG7Sx9w7DKN4xlRtIRZPF8LDy6HH1jBu/sDBR9JFVcJgySzGReJRFJgEAifQothYtM8uxYeZdtOABnhlwB6RjjVS6e0Y6vl0euHVhPUtNi0iDt4jivTMJXe+pm0Lk0nBXDp22XBahb3dvaR+p4ZQV78gWWDrr7ZZlRU3/ThcJkV+IIX5AB2jHY6KrUOw0EsNlCIVktEMd6s1KjvbeCZf1/aA3vUkJ1GryblYOY8HlmxnUP/VHgA//pIZIhrvas7MotNvTaMef7nvmgvmATPfIZOm5TGWpv34GyJ7oWdUcHMOTRhS90/SP4xB59OVtlyfp51D1QJJf2zhIhonf7cVaRkqH4ehdI09OZ5/9Z1hz6Z2kTmkS8N7ABTN+xjx80ZBYjd9zxSe8uPqSgzQGFcs/0GAEtNgXhXgqM9sn2KwGvj53aW6H2SdIjhlZ9rHVBs2RXabjOmt0naZ9xh6DZbLEKV+qLE58hs8Nicvuwre8YfgQfK0+Iwjv0JcLd7ANYHoxkEQaN8wVtK2VYtHBnKOScyn9Qpwz9oikESb+EEZX/lXg77RKvM4N9vOJVNAVA44Ism1FdmqE0yg2kCxvvj9TLLX8pqB4JPqYbdWe/0dN6Sl0aehCA0rO75d3CQo/xm+ah19pJ3WTx71E9286cdZIWl9Hc2Xe8vhfjc9Xfhjm3zB0DdvEoyHSFpC8n7J7lRkf6707vIRUvt7aeTQ+vRFw4GYz66bLLDI1lBNz3b4dxdmOKoilDgPwh4HhvhA=", "layer_level": 0}, {"id": "de8c6980-4198-46c9-9021-65354ec8ffcf", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "uac-service模块", "description": "uac-service-module", "prompt": "系统性地解析uac-service模块作为主应用服务的核心职责与实现机制。重点分析控制器层（controller包）中UserController、CallbackController如何接收外部HTTP请求并调用后端服务；服务导出层（export包）中UserCenterServiceImpl等实现类如何实现uac-api定义的服务接口，并协调内部业务逻辑；拦截器（interceptor包）中UserRequestInterceptor如何实现请求鉴权、日志记录等横切关注点；Web配置（web包）中VersionGrayHandler如何支持灰度发布。说明该模块如何整合uac-api、uac-common、uac-db-common等依赖模块，构建完整的业务闭环。结合Spring Boot启动类UacServiceApplication，阐述其自动配置、组件扫描和依赖注入机制的应用。通过具体API调用链路分析，展示从请求入口到数据持久化的完整执行流程。", "parent_id": "84196b87-4eae-49d1-98d3-e8f06be3b022", "order": 3, "progress_status": "completed", "dependent_files": "uac-service/src/main/java/cn/loveapp/uac/service/controller,uac-service/src/main/java/cn/loveapp/uac/service/export,uac-service/src/main/java/cn/loveapp/uac/service/interceptor,uac-service/src/main/java/cn/loveapp/uac/service/web,uac-service/src/main/resources/application.properties", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:07:11.370205+08:00", "gmt_modified": "2025-09-17T17:27:46.383203+08:00", "raw_data": "WikiEncrypted: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", "layer_level": 1}, {"id": "176bfe95-5219-4c07-84da-a5e320f1ddfd", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "订单与交易模型", "description": "order-transaction", "prompt": "创建订单与交易模型的完整文档，系统性地描述OrderSearch及其平台特化实体的设计。详细说明订单号、交易金额、交易状态、时间戳等核心字段的定义和业务规则。解释分库分表策略（如果存在）和大数据量下的查询优化技术，如分区、索引设计和慢查询处理。结合OrderSearchDao.xml中的复杂查询语句，分析多条件组合查询的实现逻辑。文档应包含性能优化建议，如合理使用分页、避免全表扫描，并提供实际查询性能调优案例。", "parent_id": "103edcac-ed4f-4fff-b35f-73e2fafc3279", "order": 3, "progress_status": "completed", "dependent_files": "uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/OrderSearch.java,uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/PddUserInfoTrade.java,uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/TaobaoOrderSearchTrade.java,uac-db-common/src/main/resources/mapper/OrderSearchDao.xml,uac-db-common/src/main/resources/mapper/PddUserInfoTradeDao.xml", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:07:17.432035+08:00", "gmt_modified": "2025-09-17T17:27:49.016744+08:00", "raw_data": "WikiEncrypted:1TcXmWRnDAgI41cGfWTUk1KnDUl1MgY4+S0VtioTZk+gbWm6bLABRmOIwd56PKHChe/cCSe/Ot3VZCii8285m3VRUtlPhHLR5muAfXB8nuESAFUjCqMFeHmJ9YE9QSoCexJNkgl5+CzAdSXNleuCjDc6A8Bg6HeZc2frSxHjFyVF2F7FnFoiftlWnY5AF/n5T4SfxxakuI20/lDf4dlPEEtiAwNubjKVSfvAWG5t5jncWHbNcmvGtfZkPsAmDvn5IJqe3H0EB5RjkBkFts9M0PhADK7yVMp0pxADF6WL9BmUzBpDF/u19pPdgJNmEW0/qpoz2UrhxgG+A/OZMocROC9e9tEOmrF6roMUbTBDLPfDZfYo+0OLRfYUE93pZ/NljEnmGtLIxHPGYtCzvNmrOzLPOtFzZIBF3EF0Bd+rIEJ5NDgg7fe9RaUI9FPWcPcWPaHKiyJrG9JdyXKv/znO+ZDtTuSTXAW/L5KPQkE8cYAHAOMd1ifwojlbikp4k1LK6UIxhYrroKw4qKUcKPa5dfz6ouIeJYyklrB7rguosmKvM44sIrZJ6UYENX3a9SkRosVlV31+c+47ANCPIEjoH/aK3jvhZIAbyYS3GiHSFQgAQAFFkzM+23umVVm5vzC2VL/NGH+ZJgYtRvp8mVy/VU8cIUs6ec1eF1KXS9cjBuQlTsW+34ALPDNLTjj2vayIWYgjw5LbS9iQ/hGdNy9WAwU2ag0gwJxg6G1CT6iswKFBzNaq6z/yjukJLg731HACzKsDLO6CWDKwf5OQ7i2/Q2a6uPBzlRz+VamdVL7UT9icmUBYQQe4JpXFNyaZv+avR8qwN6r7ZINthT9diDyUdNyR6Ck8i65DxdMWQc2rUjDbM/81h0L2LwAmnOjRo3m3VH96D+qMpGEzPIanRe+Rp7thFudFHuDDP556fanP7y4Y2LTxz1zzqBrYtmXmjcWV0JGws6vYOKI7zfz2OLdYM/9fyA08dYfTMmCLs7cnvKWMOtWh2JdCHvumLafuj9K2E2MnU377wXWX8RPlvgjjoteTLz2wbwb2dv1Dp86AaNYgbX/QCVh8erPJcjvz3jGejrXwI84i4IcmyMEBPBlS/GoNz+8XTG/jEb1sUegAxG8pUSuX8nBRCIvv4bVDGEJJRyOEgKsdWMgOHwqFQpPx+ptIiesew7Fg+rTDWBagMzO2J4Q+Sr3S78lmX5nDpS0AzY4CQp250NdFqURa0hQBRtriGPArA+hZWfy2Y89wjGu4Reve0aP3ZoEXI4ZuLiKSURE90Mo2JqlLBW7iJ16oMTSCBMEwECkv0MxSY86WwP+3rmIHWKkUXrNm6VaHT2j729mLzgd5KwNKJTbGWUBmCcyfpCL4V5CpuhNcjeQaPKDid2robtW0P4TqAb0PnCiESe65Wkf978uCGfj2exYMokYRpWlApbaeDwOjdl8ss2ya3iO0qEjNcrNtcRUo7bdi+LU9tISd6esKRAi60/xy0ZJEjEH8WWOcnQyvarpe9gY/9Gyr948Lo+xnPO7eSgXsSe2iH8M7TkAclqtXKO2f0atGPP8v23uCgpDMymJA40q1s/tULoEjZ0k1pUN6PKvTdhhl/NGK5QHyMO3EpUpeZfkvSrO6Hm/Dgzmv2yKRTLk=", "layer_level": 1}, {"id": "73c7f169-179d-4efc-a8d8-c9a87f47449c", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "多平台集成逻辑", "description": "multi-platform-integration", "prompt": "深入剖析多平台集成的适配器模式实现。解释AuthService和UserPlatformHandleService如何作为抽象层，统一处理不同电商平台（如淘宝、拼多多、京东等）的差异化逻辑。详细说明BaseAuthServiceImpl作为基类提供的通用能力，以及各平台具体实现类（如TaoAuthServiceImpl、PddAuthServiceImpl）如何重写特定方法。阐述平台配置类（如TaoBaoTradeAppConfig）如何注入平台特有参数。通过代码示例展示请求如何被路由到正确的平台处理器，并讨论新增平台的扩展步骤和最佳实践。", "parent_id": "484d4aea-c0df-44d0-9b9e-c951ee96a3dd", "order": 3, "progress_status": "completed", "dependent_files": "uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AuthService.java,uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java,uac-service-common/src/main/java/cn/loveapp/uac/service/platform/biz/UserPlatformHandleService.java,uac-service-common/src/main/java/cn/loveapp/uac/service/platform/biz/impl/TaoCallbackPlatformHandleServiceImpl.java,uac-common/src/main/java/cn/loveapp/uac/common/config/taobao/TaoBaoTradeAppConfig.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:07:38.434974+08:00", "gmt_modified": "2025-09-17T17:29:21.624985+08:00", "raw_data": "WikiEncrypted:Zt230nV3k3ww0eU+N8tTkkWPRo91Q0Cc/dEGQ4cDaHLQQ2ayp7nUxx7vk7l5/C9H5lA4aOinsVVo4RP3APPM0DT6zqmyJIMu8b6IOur0I8QlpCqddqiWByUSKMRlHjBwQt+UouSFEJZKNW8AN7bDha85X/cBt/CQqU66FgGvQcGvLz/8JTPDZWYpmO+yxclOkFJPnoySJE0QJTBRcQOl9rPtou0wbLSn5qIMwoXv3q74iqVVs8HjF285Ts41iH97jH5+LiR5RQl2hHntGkKY3gu05Y3bp/35ravH5lO7mr8sNEtXtEQqy4djzbBQrVIYafla8hSVeH5aYEyxQUIrXPJMucQQXSu2QnzxWkcQyrdtUwNg1HsRdxMRZnoB+H00CowaioHKrX0u14rTNzkk0Nwub+31anwyWIVkp3qeBnHxzj/XhTlwHhS0w5t9D1uPri92SbLnWoRkKql8iGr7G/g/qh6yiGVKni+sXKDayRDpw0w//SeV1APrFr24wYlpD0VoqbpcEBL6qLFH8VaYveL9iYuJIL5oIE0mZ/C86IYTqrg6JYydD2/POBwxvXThpmZf/rWhME5vmc60eIK8TIRHPnKE9RWAGUvtUA39IAxdP0C9zcGSZcJOMuJ5ROXiY6R9JzHAQHLoULgLbMVZ2g4PQcBhJk15OOxxbrkQD3cf4hVdzk63pNQzMRv0WnyjHc8Dswpp4XE+wlEu6nLtuMtBim3Htahf2jMmB2qz/UWB5d8H3Wma7gfB1LDowmHpQU0yNu134SdnkleFeWg3CPWkgL8cWKD20NP4mCFY/nSFUpSm/mjSfcIzp+cp1+s/kgit4GHPeAFMlXpaGjbUITo9nK+0xsbhuoVpj+fEDinhzyMcfCgu1oXmBmcpQVtWESPGtxS7FvWwkDlfI3hQsrSnMrWAFzAtCnWBSe4vWHCmpmQ+ZWYowOIhTEKNxMsUn2WPZ1MMD52ib+tTmhhYhqyeoRDUt/SZ7xppYt0sUtS+Nyp6o4gVVm8PvO4FeTDiHy5FW2FMLi21OTguGMTXECnpZ7/PwkQP+ly2oTpjf8/VrzX/6LB4Cn2A9lhLL9umo2nlJSgeu18YFwIHYSQApVUZ1+a0HQkn34QsGTyt5Gtn4Op2DLpFWECBe7XG+CDLZ6/hB7Baz2hS6QYuW/dsbDYg/5Kp9WFm4yOQB5BHlivBIdogE4Ndv4MeR02oQYOGAUHvxOPbmmA49cx/1S2E0KCpk877+HgZxGY4Lo93irSu/YyesjFFur9/AjsJcdcon459aKH8Md4uS3OHk+hecVWib2/6d3zkANTXpzZfBJa9aYkFAPptqkm2jAhkT9CLwVIyQp/39HbReBfdNEM7KVbuZhSfVI6G9wBlYeFmAm7Z0+BLmult/g6zaoRKt2c6geiAOaQm0oWiKxxkx/zJyn7XAfTOnP0dYB2i7q+KZKjG6riTptrdN+phwQGCMe7D6eed2qOM0d2iprqIbr3gXYVqKaII3QNmaNABr0ky/ZSCDnLvvtsX19Ird2ZM3z8omdABUTe1HOMtLGRQL8ujt4yTnz64ikyGnubpL5MVYg2gpBgcwAcu1pE7jXPDNZO+rKRj7KPs4qKBqvfGJ6S5Y9Xr37HwForeeaC2T6fftbeci3akhrzmwge2HQXFPxZGwKuOmUfr9OLeKxHQXk0OIt9PF3GkA5qLlVTOBEavQKFHinqIGDlJKcZCFh1x2k9SYS7UH++Z2dTW+qBN04ednld2jnfw+Dw6NCUlcH+ECwgm/lCHMpwDBJXAapMhpUdelm6a8JVsdBJDzCTNPuQ6r/GptuimhcqXcSoZrxVp09U=", "layer_level": 1}, {"id": "dd2f2057-ef1c-4a70-a759-714ea943efd4", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "平台服务实现", "description": "uac-db-common-platform-service", "prompt": "深入探讨uac-db-common模块中PlatformUserProductInfoService接口及其多平台实现类的设计模式。详细分析PddPlatformUserProductInfoServiceImpl、TaoPlatformUserProductInfoServiceImpl、Ali1688PlatformUserProductInfoServiceImpl等具体实现，如何针对拼多多、淘宝、1688等不同电商平台处理用户产品信息的持久化逻辑。阐述DefaultPlatformUserProductInfoService作为默认实现的作用。说明该服务层如何协调DAO和Repository组件，完成跨平台的数据访问。通过具体业务场景（如保存新用户信息），展示服务调用链路，体现策略模式或工厂模式在多平台适配中的应用，确保代码的可扩展性和可维护性。", "parent_id": "89fda65d-105d-42d5-a727-d1e38d49ba61", "order": 3, "progress_status": "completed", "dependent_files": "uac-db-common/src/main/java/cn/loveapp/uac/db/common/service,uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/impl", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:01.36397+08:00", "gmt_modified": "2025-09-17T17:44:56.360087+08:00", "raw_data": "WikiEncrypted:dM0MoPE0IfmxFmuMw7gEz26zyfD9TizvJs0tIUSmJvN8WMKPp5+R759Tbad20LONv+TzbIOAat+3Nxn1eyaqTcjXCU28hHbsVCXrr1cSRt9f9KsQbzk4z5WvluvqYQMxvKYIJBXcSmsXdMGjQ1WUMv3xYYYBFE4D4jQIOpG1mdefJtWhrvbO/mVJYVfpX1tywHFH1+XHGTXojwisbgp8zh8wBAGvCZEJc5zxNxa2h6IONo1vNdw+lBiK7TCvIxrM11MA2O1zUATecW2+e95xXB9XNPfq7mYBtmx0biAbrY7huaPOBfmr+saA0yBRmZsDi/2vptz+tYhXuvduyW5QcHXuGICg8IKWB7VcLr2ZRNwwLYhuj2RkeR26KirND90qTxIBjlNgeW39OS/YM4Axu/IUDAaqzON1xsaoVK6pPV3N5B+qmFD2Z4DuOZy4ZByMlUmUum13IkZzzZlZy2yc4vxSJL1iVo3j/+F/H2SAW5SomwvWrY0883G03xaOkoRkOhef2iJeQgGxmAEXQZ5vPZ9CmqwWn5Trg8y0gBWeoFQ9uLjxBTftARb7EbCGcqM/hElAKQVoxeKj2q5Z1iYpia8mklf4bdS1K1ExUVYurUU2SLt7kZS5XaAFEMZGvlhehzWStkweFQDTt+ogR7vLJU40Jm9Uoo980tQ4h+qMLMOlzsk8jwJR7fAofxjTwgnihbsGFldvUrM/qPRthhJfEzYmQooKXC1aFQzE8RjuzbR88B2P6WXAInwpjO0mwFE8709izMtWhdhQtTMxbUjUg5fHQkUi+xSG5yUoUyeJs2sAoSuCQykh1d7GwG8pAWo/2s9i+EhHwWEDQvcIDZxQ4GxyrulcTGb3NovwZajXZ1nnfZSWmNEuAaX0QCyL6duG32xqgUKXCBNtXq2NDUurIjqWKgpHd9QFgFZVkW/qxmtkSlHz716VKMmKcth3aJzvxEhMqLIQMk6ADaNiJ3v0CVhDxHMkUbjmxjcQtU5hSwvgrYqgn3+yr+KHKcRBjSoBBNTAPUdxlTQhT/oEgIDvdWDBgrkdAFUbG4daql0gUc287DoFzciSi1O2rJq5x8YngBBdF+hZFHkqrl1gXyyuybrWR4VTfwpyny+6Aq2s0S1wiv4B3OK8tHI98mLyLdjrPjQOm+SGBQNq54QmUI9Sig7nV663lcarQFH0RDk+mbv/qNH7dHcfu3a/Ageax6GZLJ+nNBNHTSDic0cVuVElJNPGVInMb1ugdZcYOjqqpufycTvhcsphC7WfDJUW8E7TvvRE/6VFZPn0xCnspYXZviPl8jegNDaNFfN9CKhWILRcFx2kgRZV5Am2/GREb2HMvytPR76aF+edz2AxUIAcUcN0UQGK3fewMpOvrpdXNWCmKg/CqJ0+iCw0VOl00H0AVdTJgwbFwolroKeGy0ykOnwJaIramfX8sNy7IIeZRz0OGdt/TvSy2kftW4ZcZi2KHq1YaPmGRTbQBrr7gJYBthtECiHgjh6/hy/YSIXgmPOgF7roR9keN64ha8NiIK4fD20uGkofGdUa4omJpiETKu+8NDE3FsIN4vzuRNSvcbIydM1Si2no/4WlUNsf/VhrvecdmsbzYcL/veIoW/cC8A==", "layer_level": 2}, {"id": "70d95884-8792-4d05-952e-d4a89215b20a", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "平台适配服务", "description": "uac-common-platform-service", "prompt": "创建平台适配服务的深度技术文档。重点阐述platform/api包采用的适配器设计模式，如何通过AuthService和AppStoreService接口为多电商平台（淘宝、拼多多、京东、抖音等）提供统一的认证授权和应用商店操作抽象。详细分析BaseAuthServiceImpl基类提供的通用认证逻辑，以及各平台具体实现类（如PddAuthServiceImpl、TaoAuthServiceImpl）如何重写特定平台的授权流程、Token刷新机制和回调处理。说明接口设计如何平衡通用性与特殊性，支持新平台的快速接入。提供完整的调用序列图展示认证流程，包括授权码获取、Token交换、用户信息拉取等步骤。文档化各接口方法的参数、返回值、异常情况，并给出集成示例。讨论服务发现、负载均衡、熔断降级等微服务治理策略在此场景的应用。", "parent_id": "5dc01eb8-7e2d-47b2-96e2-f5337e92784e", "order": 3, "progress_status": "completed", "dependent_files": "uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AuthService.java,uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AppStoreService.java,uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java,uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java,uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/KwaishopAuthServiceImpl.java,uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:07.059851+08:00", "gmt_modified": "2025-09-17T17:45:54.666497+08:00", "raw_data": "WikiEncrypted: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", "layer_level": 2}, {"id": "d2da99e8-0dca-4ccc-aaf5-f96abeef6c63", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "Web配置与灰度发布", "description": "uac-service-web-config", "prompt": "系统性地介绍uac-service模块的Web层高级配置，特别是灰度发布功能的实现。深入解析VersionGrayHandler如何根据请求头（如特定的灰度标识）、用户ID、AppKey等条件，将流量动态路由到不同版本的服务实例，实现新功能的平滑上线和风险控制。说明其与WebConfiguration配置类的集成方式，以及如何利用Spring的HandlerInterceptor或自定义DispatcherServlet机制来实现路由逻辑。文档需涵盖灰度策略的配置方式（如Apollo配置中心）、匹配规则的定义、流量分流的算法、以及灰度环境的监控与回滚方案。同时，简要说明WebConfiguration中其他Web相关的Bean配置，如消息转换器、视图解析器等。", "parent_id": "de8c6980-4198-46c9-9021-65354ec8ffcf", "order": 3, "progress_status": "completed", "dependent_files": "uac-service/src/main/java/cn/loveapp/uac/service/web/VersionGrayHandler.java,uac-service/src/main/java/cn/loveapp/uac/service/web/WebConfiguration.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:20.347833+08:00", "gmt_modified": "2025-09-17T17:46:18.205127+08:00", "raw_data": "WikiEncrypted:XZ3L/EMswFjUY8Q80yiTV9bWA83UhjWhM2PoRnj06QzwggA3rmVgpO320I2JgCRyYs9LHDLwXz5J85tbV5YG5KxpUUrxVjA8eiO0gcb+bWRdaEkjn/0O27mfgABvYAaZk7FpTp2Z8AA46TdFWcXF7plpX6zN4u4JFAAKWzADQWrKVC8GOzmZzpYdSWru+cZ17uvPz8QXSnpZgA+k5nEfJqI+WFIswVsdfd0Aipq+uL6HduP9RJc98HSSLOWfzIpE0Ktz3k3E4BAco8b1GBzsAHjkXIZIcGEQjvKzECMVMIDacT3067BqzsS6YfYSbuvgaTbwX/1rscJQ3wom7OCMxlV4T1fjd9YTUOcV+GoqUhded4vzwFj2HiZ1iqMc9+JpnZ2ppufp0NTb4mr5/N+gjievpvwDVM9s+FkxxSIT1kyj5d1r4qpkHSyVC5LG1acDC1AAz/v59TJQM05etYpEL9Vm2fKEgW3t93uPFxAJuaxVx6Ul7/BvxyRfC/KqWBNJcSwOykuQMnbzQLv2UdEPtXZ9qwQrTjYNolarUTzejDODnqeaAwqkyPrjrub+ue6Zz/39pNDKvCK3nNlbvYuGbMF6m8/aF6uGBx7pRRthgPI8P0RUX7l8w9NDkvDBgmGMWLoCSZnFGEP/Sbvmu5WSoz3K/HNbzy9kb9IMqr13tjRpoObOxaqqC/x2ifegvmcCmU3PvjF/uqr0hUFNSw16nU3CaT+IBxktXgZqwujD9HivBUhuE/9ZtFnoUW2H0KbNtdcCJ3I8lTXdXCFjey695MLJZd56WJRPVAzKt5fXCzYEDSOUsThgSiR2ljhKRwBsvBizDdZIF13EwAPnhinwk5vS/nVtw8yJordXFfi3HaSGDyysd3OyYQdEl1FhdTR+FMGBUT5g4GPBJEvuKUZ5KRAvft8PriGWL5CBzac8LFx4XD8rffLvI01PCGC0EJuqNsYv72G0mDyZsJ68aUw/AuUNvldeUgYBzPrdSJOvDZaOtZpne7+WvvA+/woZXhkbBeKloxEkMuidSPeE4mYRT85LHco31Klc0z/CEiCKIIbhn+WBRTHRGD+NX0VxWUq5zp3vkAKsvC/VSAptPN3B6iRRJ7L6INt1B1uh57ARKuFcllcUpqejm1IEsDWaEH7KTVcn7dSFBm5PBvxKy7zkKlM57fw0CsY9TUyiKN3vasNV4+cQVYITXpbQJMYwNg6aO5qf2noyOIvIKWMGeSUzuKA0YzqcCPqocxoKyBPRc7jPI1Kfy+q0eXjlmPYarip2b+ckvMqh3QL8v2W4NTPLDY73hyaF+zx4gs+mp7vVbcLfPv8H3MCkDCG+hEHEoRcq+9u0wGpAkGseLB2KKEiCbWsM+W3AV4ghYNmX9YKvQeUTCKZSpVWXBtcDo8RR+u1t5fupHNCmyO1zJS+sEKXGxWPJlNNRvJ2cy7rGFiA2pUZAVbFBizFj+l2Bhxnqy6XIb5kR7FAGhttL25MgB32Tvx0yVT+TrMEfZXTJ1CSe+t7lYfm1LigbkrJ1Uit21ocrQ8hDbI/hvKmQwYR9e1/2ddnDaKxqIbo1MPV7dz3P//4=", "layer_level": 2}, {"id": "61264e82-7f3a-428f-b1c8-9230d8fcc015", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "新用户业务处理流程", "description": "newuser-business-process", "prompt": "全面阐述新用户从注册到数据落地的完整业务处理流程。详细描述UserService接口及其实现类如何协调新用户开通的核心业务逻辑，包括用户信息校验、状态更新、数据持久化等。分析UserSaveDataBusinessHandleService抽象类及其实现如何处理用户数据保存的具体业务，支持不同业务场景（如商品、订单）的数据处理。说明SaveDataDTO等数据传输对象在流程中的作用。绘制从API调用或消息触发开始，经过服务层处理，最终完成数据存储的完整序列图。重点解释流程中的关键状态（如待开通、开通中、成功、失败）转换机制、异常回滚策略和幂等性保证，确保业务流程的健壮性。", "parent_id": "f4d5c9dc-098e-4306-bbcf-0fb86498f79e", "order": 3, "progress_status": "completed", "dependent_files": "uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/service/UserService.java,uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/service/impl/UserServiceImpl.java,uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/business/UserSaveDataBusinessHandleService.java,uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/dto/SaveDataDTO.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:29.548279+08:00", "gmt_modified": "2025-09-17T17:47:09.005157+08:00", "raw_data": "WikiEncrypted:QFBIVr8PNrA/8GmHt1XK33hFd59uNXmvFCWHhARJ7Q2x7qxSD2w7c/qQeXVcPTo3Bfv2u6gnedx1xFt1/sDGJmcj43258Pc1cJ+vGoWVzRPEtpBkDS8chEnAL7uZDx4KIS1mI9E322gP8/I4T7NIukaaSwsnmuTlkh2u3VjMGFWXXpWBd7O8UOVDk2PzRzKjQ1OUqYfFcmSjBkf7XaKZZqk6SxKBDMRGZw+hGCMrbHOlcQ6Q8iGcnAauDyUgff02/jFrBO+4qR+qp6CAn00lWl3AxpoT5nM10GC5yvXaJ1MWhmnSn/2EPPCR90A1HCAeLT676AW/jX8WWC4AdnMtZ7P0HYrWbrnV6a9/4/73oWsd7uzcWnn40C5hSoxKKxd6ei07FxCP4XFihxSr/mDO3VKCXsrlBzR0FMZBFx6I3kUC0SR6oOr/dCDbF++ARP6s+32zrtSgIHGpfk/LCPhWWvnmyaicDB/3tlQjTTklZjrtD8w3LJWqTnVtdVds8Fbcrpy5G/Q5lGL3rp8gyF0mDWyDXzvufRAyuI4xR6xPu+oXFCBmoFZatUd6fBlrTg7B+qHqLrG+rprxg4saVlTh1kLDWmFsPSIiQYBPa8RXAiaWSp6EnszzN/obQ5CzgnHEnK6TzKRtDdZ6eCcW/ARKcenofx+j+uWI95CkG7tpgK5LllPIWVOaNdAIcCBDGGng5JnS3g6PBTjU9aaHoJ9K0rccGW5SjqaZT6ucx/F2T8RosWibHi7O88SEoI5uwbm/2Ixqf1gy7feXFiJwIE9eBlCbD78khS8cyUrAXOH5CXZQWXMAeq0WDc1/+BPwpHVm5mf8VKUFUjH4T1C+54ir9lLmKQsv+nqk5yTAMZEYcIrxdLGtMNoaqN7kt3vZlfQBBLZNRdkO/pOY3N1wgS7npKEe7C2MG2NMp/Uw00y6YAL5yLBmCmfqDc6mZC+T2v1JsJoQJrXiPbThB1R8FKdqr6+uoBjCf7Txz9YfN1+zLwKlh4sGQAj9G/DPLoaiPRLgcJPZdzN8fMindBfmbGy8GztHpitRK6FTyDRgs1J19viwCBhMRvyBzVGZAha8ZoJjj2eUKykeUB4QkSHJGZCIwZQ2C7ehSh8xtAvxZqLdEJzDOgXIzicOeDe1BEV1w2H26mqxLvpDCieNq5zA5nyge27rmRlv6rUOhgiegjdge8xG/20FTYKZYdTGOAl/JVMNjgAe3451rBsNYnxqq4yFoKojGGO8+0J+kTBc2wlNvHIE5P9gc/vFvM3vv9zyq2pgOWx3JVkB+zAXqqxxxZeJStA+7iNH2d3P892NFTeCCNsckiumQgevRy3I0Z75tpKiQLBGCWNKw6Mo9/lyMUnEg1nJx3KKLFKMGyRrL+CF01uc75zrZEoXRViv2MtlcaT863VYvKk6ctxfpAtGGFZUBwKFaezEppWZkNPC6yHIBkDdwpq05gQdyhMXlLkk6pjTptz33eObepbqtNCaJl3X7luzGVkra2jZsI2HwHpeZH6QiQxglGlFKKGpeuO3XzYSJWnm6Y5KaNwm2Aoh4D1GiasOR2JKWPcoivMGxNu4GkJLKiD+x8mq2hEdynGQ6sP0dJVGpvghkcaf2xGb1Mh5HFCFAexx9MLzPUH85T5BN0EY02bYshWvbGR94AHimgdDttaRFbDno5tGLhDHd0BHEYkE9vWX8gJKgm5/TO9+yI6kU7xNqytlPESFm3HSq2b5dA2i6oqXZ4r53iLOl0zcXd6Vxz6z/IScUWsfcpebS2jQeiCvDEkJJslM6mStPSGegowFZzFqadsJuhpzus0AizJT50D1DxbtylmipkYO635di5aHU+snJwVCRUy3uGUhLkLUq9/J1ntwV8X7lo9Ovy2HrGyoIyjv0Y7SjZzQr3jQU2uE4zWyDzXzYtzinRxtilHu4fHnE9dqWss2uoSZboaZEAXmwBrdkLCxq3dsz70m9fjwPc7AZJ/IpT+c0mgQ", "layer_level": 2}, {"id": "ff08e9d4-86a8-487d-9f54-b48df2171714", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "Redis维护", "description": "redis-maintenance", "prompt": "开发关于Redis维护任务的详细内容。全面阐述RedisClean任务如何定期清理过期或无效的缓存数据，包括扫描策略（如按key pattern匹配）、批量删除操作的实现以及对缓存性能的影响评估。详细说明任务如何与CacheTimeoutConfig配置协同工作，确保缓存数据的时效性。解释任务如何处理大key的删除以避免Redis阻塞，以及如何监控缓存命中率、内存使用率等关键指标。提供配置建议，如清理窗口时间、并发度控制等，以平衡数据新鲜度和系统稳定性。", "parent_id": "5e77a8d5-d8b1-44ac-85da-7479b9b4a49f", "order": 3, "progress_status": "completed", "dependent_files": "uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/RedisClean.java,uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/HashCacheRepository.java,uac-common/src/main/java/cn/loveapp/uac/common/config/redis/CacheTimeoutConfig.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:38.212717+08:00", "gmt_modified": "2025-09-17T17:47:19.281809+08:00", "raw_data": "WikiEncrypted:4LmPMCPVPVr3sdKThDfkIbUWN9q0Ju9mp5SkzgKCKP8Dv9HBb3rmb73nLM4+KY8sdL+CkqofCrKZwxzCM5gP0WuXxLm7i2oApI6zOyYw0VCF2Svq8qXQPvdFRcoBm76Ny9dRzibTVhaGp9fQ2xl86Iyf65FFlBcJooWKye9X8GbWihIFI2gZNB4Vnbo67Zdzh6ao4lxxZAYueb4PbbgeePxdV0Gzt7GBNzSWiA3Fw1Gx/JzUzTHfBTu0LkgRqHrWcImHjoi5eqxweJyTrK9U+cWNzavkfAOQvS+gyxEQl6eNgz+W6UYrCFITvui1Epicn2vPoyOIrnsIFa/oo6gekVFkumCIMWTr2fL/E4OhiDWwY5Ola35tX7/uYD/yqtgkkTQ+VZRz84eayWtFmAto0GMLCUlCIxDW6GXC/NnddbtoAxdjlDnAovbZjCzkUHs742i1veUry8kP500uqpk7VQzQh6+WK0f5JOiXeLr0rhXTSSy5y3ijFDxB7B+v+eCWq7xFaSD5cPMnePPv5AXekvxG9caKF+CQBgWWgyoB5pngnlYWVfIp3NMGjCKR5y3NG2eeeahqJ4D86pszCix0vV0qDuofIfjCkgo48UcURdEw2DA2ZqxoGt8kgInt9rppNa83u6+/2oeLtDSmLKsRJLbzSef2lLaZo3X9bZp4pguflL9N09mouyEtOa8E6/W6LTxsAKtshJvVwLmb29MREvvqT3XAcwTKTECieEY3MbWNw/CT7/iNK9cByyW5yVxBjSifzb72w0hYdqNf2s32V+e+LVTqW0Zn9gdhVcxGeWKyG/NMTkLFi0tfMq58be3hFAW3XmTObj9U6VV9L/OJxUT/q1b0Qakxd1WR6/qwURKiKH7FE+Z/2tQykLtXqOW3d5XLOl0Jv6ajEhS9P7o56cPCJ9naQsXNFtJVmTg21nZt71cxxj1Zo4SCiJpRoPepKW71PBOnYGCMfFmJ/7vw9YGR03ARs+T7jCMpdMDyw2Ur7QuGhpZNEUFOCzxWdbHYXFW9ktUrTis1Tzc09yQkYSw/d03T8Sj0octlJhKq5G80mXhglh1p3RTzxB2GeR8eM3ok2fZJsrT2OLs8VtzLcTyNRIVx+BVNZ3Tc5M7V1BMGwpI5MIiXYoW0SWI0+W+a9iVBNWr/Nzx0lTAK47r5M2SnsPZIdqeVkQqtgaI371uVUx0W3oaK6eM7Lr4NtJrl2kETtW2ykYAL/ov14hS8LSpK0msuhiFFonPf3MF48+EFFcoWE0QNMfYD4VWxPimbLWA7yhLNoQDFZcc1lwFPpb/r5QOablXeVdLx4znm7L+oEPWXmXE+lPsEplUdyygyMh/gRIBm3fLGAVC5HdE+r8YUn3DuR2vzERmb4IrW22SFbcN9ZLEVC2ikGskOqTpND1bcbK9S+NosZCSHYb4BrsQjxR/s0NB3xQMInu23exwK/tilyOIVKl3TZiG6FFUwFCFYiGN5SEXa9ZFUzSuW8A==", "layer_level": 2}, {"id": "35a0355c-d49b-4b9a-943c-e53329999363", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "快手小店认证实现", "description": "kwaishop-auth-implementation", "prompt": "创建快手小店平台认证实现的详细文档，重点剖析KwaishopAuthServiceImpl的认证逻辑。详细说明快手开放平台的授权流程，包括redirect_uri配置、code获取、access_token交换及用户身份确认过程。解释KwaishopTradeAppConfig中各参数如client_id、client_secret、授权回调地址的配置要求。提供处理快手API返回的特殊格式数据和错误码（如invalid grant）的解析方法。通过实际调用示例展示KwaishopAuthServiceImpl的使用方式，并对比其与主流电商平台认证机制的异同。", "parent_id": "10e48e47-ef03-4f56-9d9f-4504a0820575", "order": 3, "progress_status": "completed", "dependent_files": "uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/KwaishopAuthServiceImpl.java,uac-common/src/main/java/cn/loveapp/uac/common/config/kwaishop/KwaishopTradeAppConfig.java,uac-common/src/main/java/cn/loveapp/uac/common/config/kwaishop/KwaishopTradeERPAppConfig.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:09:05.036+08:00", "gmt_modified": "2025-09-17T17:53:53.266884+08:00", "raw_data": "WikiEncrypted:IPSs2yF8hfty7NcZULVYaGqF4eLm0RxHed5F0rgQXoKIg0A1zwSe9JSDpBICW7wfgkUCta2rdTaqQHGr6JHghSQX0C45vy7R1YkiHwPbiwXLefKSzQNu4YXgv3CqLCjpDXIuQ80wlQZs/8zfKZgllgs8Z12j7N+cApaK9DuN8LJCV2WlrihHSMiJNihR+I/2vYjdtoFdmKZMvl1ptlCfQgdFs75AWnzoTQQQ3TTD0wR/CE6DTmB0rJ6D61NNIzL4kbLLAmT4UGDNSh98lxEEe7FAUuEvpmT+cLyzMn9nkJkt1vgTytgO7PoDkRKwpEzQm9kLMeAZ1rKTc0ksi+Oac2xpSAyWa3NWQpr9OXrDYUaZ8+yL2WovMIXtvZp3y4+pYMePwecsZqioVKweblWTCcMp4cnJBQBv9IoJQxRLFOU5b19mkj3AQGDy/DMsv/KFzMu3PYOuqhQhkranhW3rKDIkMazoP9FQML7PmNx1dTxIDJ3GmGkVXaHTaoSnust4fTHq3C6wyhIwtDhJh6a3fBzhfDypNzB73hfqhgR1L/rhOsRj/49zeLIMZWi2D65FibvaVlVQ5P+kjn6/S0uuEyVHR9bUnxam8f6lENLmO9VDTRzEeq1NB3Ib9iBDR4t7c0lkbvF849vBNeLKvp2NkMiJk+d0Qav8EeW927ep2VX+muNyU6BMlb8YGBGMdOBRdCY+dpEgimnEMKgWZgKKXFBhvX3993/uH6+l9MEji0b3FoLof47uNZiUOTxrKrTTeRxjz0aciWrhPsoWwwU+IhJ7k6BLfMGHlmAQQfWkUvF1z/Wmh4phRMeuceVyShOBb6hvYv6tnlenmtK9GO4lnelEBFM9sKphvxSm8OJcdQOJElv9Shm3kW28U+T2nbQYYLwettgJ58NB3jctLMl+9hnPFGGjf5QsDtGZuLbokZoYC2uZ4gRYUovaUYfX72iT/B3Lp+4ybiK5iH0Ol5FmF0n1D3wkxtf3DVGA5ne5XLREmxcujRRmM35LAIEcsvF9W6dPApY3JqldEw96uoynMnMTbLMFNU9J5je2cvw6UrcCE7YcYoEWm+uzCDGu0w5qQzH6XsIWSqqGsQ4QCsPLWyv0MYp2AZGTUDgxtmNhEBo5P8H8vOfaVur54ImXOrhsScs+yfR3J9HbJO2wLbuZoSsWjDH/prlHxXWJDqUPoIduqDicQA4qK5me9WolkyYqIanxrQxtWBOY/QFmvmBJiIu3RQzAIef8LGAuPGZkzNZBzGoZS0IovUKCGNcnmyePYdXqjwuPrc8GflACmNdC41HJRpy8/5WPMhRfyp705WbBsulsVk3QBeVJ28lm3+2tWtSuhKjgD5QtDyN6D7dLPkirdZuY59zKfIYYVifUyIRpDttSdT7wHgLdQggGnn9A9wae3rncBX39OUm8tklSGZn3IlP13OU/SRJAmmORJfo=", "layer_level": 3}, {"id": "103edcac-ed4f-4fff-b35f-73e2fafc3279", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "数据模型与数据库设计", "description": "data-model", "prompt": "创建全面的数据模型文档，涵盖usercenter-service-group的核心数据库实体及其关系。详细描述关键表如UserProductinfo（用户产品信息）、UserSettings（用户设置）、AyBusinessOpenUser（业务开通用户）、TradePddAuth（拼多多授权信息）、OrderSearch（订单搜索）等的字段定义、主外键关系、索引设计和业务含义。结合MyBatis的Mapper XML文件（如UserProductinfoTaoDao.xml）说明数据访问逻辑。解释分库分表策略（如果存在）和数据生命周期管理。提供ER图或类图来可视化实体间的关系，帮助开发者理解数据存储结构和查询逻辑。", "parent_id": "", "order": 4, "progress_status": "completed", "dependent_files": "uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity,uac-db-common/src/main/resources/mapper,uac-common/src/main/java/cn/loveapp/uac/common/entity,uac-api/src/main/java/cn/loveapp/uac/entity", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:06:38.913508+08:00", "gmt_modified": "2025-09-17T17:13:08.284719+08:00", "raw_data": "WikiEncrypted:klcgW2PbPxJambbKMzvFtzKMOkAiMygw2jhMWheBgWgOOP0gWU167f71vPfkb6/KodCEF7kh+/+AvQEZ1NPRWtR8LjulXoJQpIzBt4lHKgo6m8kPkdKHlMtSHozSD3K20C+udq7lFm6h3RPmiTa2UXI7JLG1fm/RAaVz2iI7VJV9SiN+T1aIKKG034fB9DYb23/6TF64fqWWVXkwZm2uggIgoR6vwOe2dZRzDJ123AqiG/WPpJCHDGnRIc0whiSNZddjfqArAVKTtH5H1ZWSOxAutXGnU3s7NLw/T0V3YKRpcP/IwTNw7arlTTKD9k8eCR36HigkLHy5b25tb5s2nFrtkrdl7arTpbJDg/Glmiwa/eSOMyVaZ0Xut4OqYWoXXhPwudA5KPmA67ZekXVmR9l5clocu1fpW+YtKSZbL8kVixXq7bsT1PlpArxBYYw8iXikxLeEVxMdpXXNhoZeap+e6rG7qpdPHsKN6cLX3yF8eySqCBPjFx1hHGnHxG2WnrGYzG03F1PL1v+u3FLAAOiQrfg8q8y1NCwFIZPt6YA/uhdxk4R7HW6Rxyiw5+BsUCujNUvC4VKxmbOC5CTDhkOEU14M12zrAdpbgViK77D8Q2E9tSVeYBAM48rMiaeSAsulh0Mw5dYF4xp5zgUKcLV+yJsjdvoRZCqA3Xg2SGn2zorXHS4b0CDTd7DAcQEc8GaRZ+UrlbWyvBXlF19KirPq2p3mvD9N+h+rairUHviuXWP6yQuuaVL972vaqRkFpiHkI+x+mTYdOdmyFuMsQEiSmpHc+ykMJTFt6/sGNFwfQ4XUdqk2rWdm9smD5NqmIi3fy2/5HCJUdFjiUOoQ/YmvAvniUSH77qwMpW8sM9fvfos01NlCAT2oZqFmVPRTWb7VCSfUq3Sgaq5mGwE2hAKJ/3poHwLZ4xhbNmOuio9qXCa4DFs+r4mR61ae3EmDLqHl4M7ML623aUFabxC2GLNE082fGypan8fgEwj6cr0J7dRWqZNjf8NSaIK7F3f8GuxdZN4JMi5XIapwjDj3BD+FZ/89RZmpWaIeq8+bC9c4kMKpYPfE1eIX/4W/8OAOtR2NKbBxlysw9ryneE54L6wg4ibG6uGJ8SdIEc/6Fpl0l2fTJUwwdt6KL6QxLP3c6n/USuUWu5ahbEEhgQbdQq74b4z6c3J1TJqxQdtHfeAzb1VNWs1SjkTpyE9pdMESa1P3rnKkWRDifUO9wnRVguQcVmId3yKg0emBOb2Vu8vyqcvPZkkVWURrlnjUEhQd3N9Dj0YNPddVQpHib/1jB1tWxZbIpgSsAs1zdHermmMGfRZG/Ee5FmTOmgx1Y8QP3DxneKeIumK72HTef9ZprmPNhOYSFoBsT8secW+kwXrEl5lhr2+L4GFe8EYF5MVp4DDGMwgzdZ1q3Vb1mK8LEnWJl1Uffwh5fKCRTmNPPzV3LXEaN/burPP2Gl7CH/VcjALWGNP7eZ7nEZKi5VLTqyrtqwraVGgO7oT2HnHLTFLMuFAaSyPVtrNJFhV5SKXBmSapXr01wJp3YJazwGePPaK1yROdWygn+A5I1Wc81xcYeoj4vfgA5K3YcM8eUsblyNC4S66frPW9qEKJAaXb+w==", "layer_level": 0}, {"id": "f4d5c9dc-098e-4306-bbcf-0fb86498f79e", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "uac-newusers模块", "description": "uac-newusers-module", "prompt": "全面解析uac-newusers模块在新用户开通与管理方面的架构与流程。详细说明uac-newuser-scheduler子模块中的定时任务（task包）如ScanWaitOpenUser、ScanRetryUser、ScanPullUserDataTask如何驱动新用户开通的异步处理流程；uac-newuser-service子模块中的NewuserController如何提供新用户相关的API接口；uac-newuser-common子模块中的NewUserPlatformHandleService接口及其实现类（如PddNewUserPlatformHandleServiceImpl）如何适配不同电商平台的新用户开通逻辑。阐述该模块如何通过消息队列和定时任务解耦用户开通流程，实现高可靠性和可扩展性。分析其状态机设计（如等待开通、开通中、开通失败、重试等状态）和异常处理机制，确保新用户数据最终一致性。结合具体业务场景，说明从用户注册到完成平台授权的完整生命周期管理。", "parent_id": "84196b87-4eae-49d1-98d3-e8f06be3b022", "order": 4, "progress_status": "completed", "dependent_files": "uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task,uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller,uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform,uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/service", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:07:11.371315+08:00", "gmt_modified": "2025-09-17T17:29:25.050217+08:00", "raw_data": "WikiEncrypted: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", "layer_level": 1}, {"id": "c6dbc118-ee10-43df-b817-42927bb6fc29", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "平台扩展信息模型", "description": "platform-extension", "prompt": "创建平台扩展信息模型的架构文档，分析多平台用户产品信息的统一管理方案。详细描述UserProductinfoTao、UserProductinfoPdd等平台特化实体的设计模式，解释如何通过继承或组合实现共性与特性的平衡。说明平台特定字段的存储策略和数据同步机制。结合Mapper XML文件，分析跨平台数据查询和聚合的实现方式。文档应包含扩展性设计原则，指导如何为新接入平台（如抖音、快手）设计相应的数据模型，并提供实际的平台接入案例。", "parent_id": "103edcac-ed4f-4fff-b35f-73e2fafc3279", "order": 4, "progress_status": "completed", "dependent_files": "uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserProductinfoTao.java,uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserProductinfoPdd.java,uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserProductinfoKwaishop.java,uac-db-common/src/main/resources/mapper/UserProductinfoTaoDao.xml,uac-db-common/src/main/resources/mapper/UserProductinfoPddDao.xml", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:07:17.432851+08:00", "gmt_modified": "2025-09-17T17:30:54.799848+08:00", "raw_data": "WikiEncrypted:Djug3Ni1DAZ8Du2qShzHYtUrcn+/prOYsZQb1faIxlzXIUuvhveYSqPPPWhl0w/wBqqIYM0jKrxM0GS+bR5U+UEuvbZeHlev7bDGJiLaWdsjHG/iPaju/0fASNWJBBtIuwAVkIQLQJq4es+nOcLajfYaxg8JWCAIflUqXbbbAjtwF08ugy/kEmGp9NZjXH1XJ1jGYGcGtJ2stXkS+odrTQd5K4G1htnh2xkGc3pGqIwzfip8WcuQHIriSs4sla/zFq/TkfbdzP9q68blt0A9+HJF4UpoNwH2GBqOn42ai0vw+4JvzLNPorgr6sOkUPTwAqBITBrJ9eIJk2EnmREdvBCDFMU+kOPGgR9V7Q0LDL1M9vC3Kc/WokKGUuT4JbZ/HLBpELD17Uu5LG5Htw4gRoHv2N2MskTLPVM8+AXnhE4fow+Ax7dgp/LjOkafuADZ9KCjYMzKK4x3JP1xAZ+qFMGjalfAxoym8sZ9RrUQe3BfYc4akc+g7Ci9J0ybAUo9N+IP4QecFvc3OYDRGax3mdWjRY48BGBUH7uv3QMXUQWy0CQL9Y98pAk88iMALOxH+FRWc8gyNlqtJ9rPLI+BqrV2r9V4J6RVvUoIcqG+8GWpSwR8JAA2oSuXPPQ907q7ciihVBub/rQ5cv72yFmLwiG/FbRHe/1ANAF9q3ErBvHKBDu45q69Vb2AdC0Be99BQmMiE2JA6gBIjP4tp4y1XazsD0fUysSn0RBpG9wsjCQhJMa4tZL+EiuMwuHxuhCsC6ckeALfMIRfHCvi8a+wUpu7m9XuQgexe2OP4Qkf0IEkWlDXHN/paYF3IZNQtB/seairR1Rv+CZ0VFiysKuTyineDCzuARmeZ62oC6rDY8+yoOtyYKAgwbkXgq38GXxSpgqf6xBYlKzAAqyhgqXbr99pfiXRQHaURE43SLnRWNfsUGVXh1Uy+mxK0oQekxYq0ScI2efr72ON3Xskq9cdl60e2oW5KXBiYFRw4y5bp8YCRYwv5JWR7JWvVZpsngVh4TtP+dF4aamXGR3l1/Z2SxhLNWWaEWMJ8yzRa1zKPCqQHXfHg+jDD8UE6ysjlv5cXP6/Pr4mJqy/oF60IUtvV9XdbLz21m4FOvJw4pw2FPiRarvdeDEskCf+WTm4teRqWG3EQ+ppM9NdUqsDP9QAa6vN7oU0Wjy7jCpTP6cPkjWYMBWCHEz1ZbH1KLjUvzklGP5X6xiZO5GTkDMmZjdU5ACpvtz1fnTUQGjSswmyXzX/mCx2+0XoYugUuA7buh7EoZSECtRMyk2WnQR9Lut70/CZEWBTnnTAcV4yA6KWZLcBEckD64egYv7uDFNzpDzKA1UrjXTRnaudVgMm/LhezV0oYkIR09D2/JpjwPNA0PWQmz7OhRnVURtvukY3A/0H5w67ZLHOzAM3XWhb+vx+sIJdRIl94dE5DCzY/zFMgXj1dmzs25D5YMk44t4RhT531BIDjT1b4vN88wxxHQFVLt2VOOlZjyJIrPatkHTXI9d8fez8tQAepJyU2M4pULh8G41vEHusuvnBA+zdB3uLUmrPcm9Bhdi/Yc/bBSXWE/Gf1+RNYR6YoN7FOTbgRySUBB4iQ31PqmSCp/DT0eNgp+s8s9jLEIk7pbaKdg3nPw8VJJWYOg/Feaf+ywBQm96rtVBv22mFxbeCfl7Rlf5lOtt0O82BWbIGadQ6SxODf40=", "layer_level": 1}, {"id": "1a36ea21-8a00-459b-8805-d0c53e572fc8", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "Redis数据访问", "description": "uac-common-redis-access", "prompt": "编写Redis数据访问层的详细实现文档。深入解析dao/redis包中基于Spring Data Redis的分层封装设计，从基础的BaseHashRedisRepository和BaseValueRedisRepository抽象类，到具体的HashCacheRepository和ValueRedisRepository实现。说明如何通过泛型和模板方法模式提供类型安全的Redis操作，包括字符串、哈希、列表等数据结构的CRUD操作。详细描述OpenUserRedisRepository等业务专用仓库类的设计，如何封装特定业务场景的缓存操作。文档化缓存键的命名规范、序列化策略（如JSON、JDK序列化）、过期时间管理机制。提供性能优化建议，包括管道操作、批量处理、连接池配置等。通过实际代码示例展示缓存读写、缓存穿透/击穿/雪崩的防护策略，以及缓存与数据库的一致性保证方案。", "parent_id": "5dc01eb8-7e2d-47b2-96e2-f5337e92784e", "order": 4, "progress_status": "completed", "dependent_files": "uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseHashRedisRepository.java,uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseValueRedisRepository.java,uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/HashCacheRepository.java,uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/ValueRedisRepository.java,uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/repository/OpenUserRedisRepository.java,uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/repository/UserManageRedisRepositoryHashRedisRepository.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:08:07.060637+08:00", "gmt_modified": "2025-09-17T17:48:39.02256+08:00", "raw_data": "WikiEncrypted: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", "layer_level": 2}, {"id": "a03dbef6-5a8e-477a-b7cf-24dafd96615a", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "京东认证实现", "description": "jd-auth-implementation", "prompt": "编写京东平台认证实现的技术文档，深入解析JdAuthServiceImpl的实现原理。详细描述京东OAuth2.0流程中授权链接构造、临时code获取、access_token换取及用户信息拉取的全过程。说明JdTradeERPAppConfig中appKey、appSecret、授权scope等配置项的作用与安全设置建议。提供处理京东API返回的加密响应、时间戳校验及常见错误码（如invalid token）的解决方案。通过代码示例展示JdAuthServiceImpl的调用流程，并分析其在高并发场景下的性能优化策略。", "parent_id": "10e48e47-ef03-4f56-9d9f-4504a0820575", "order": 4, "progress_status": "completed", "dependent_files": "uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/JdAuthServiceImpl.java,uac-common/src/main/java/cn/loveapp/uac/common/config/jd/JdTradeERPAppConfig.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:09:05.036649+08:00", "gmt_modified": "2025-09-17T17:54:07.550741+08:00", "raw_data": "WikiEncrypted:cKWQo/ErbGc1c1vIgMSfjxJR1nEDV2OY7nOn85S7bJyMnAKLOXUIuWR47LhGIlARZLiV0gSjty6ZeGmDcqtjFYTXJ9kJ/lPXCIj31d5tZoaCf4WJ3b+RnS/8j3zCtck72jovxnMmZonAtwEDMiBYe77DOt8weaKu+YzGB3dc6AWPOTkkb3brhpB/GUe2Vu0gNJWrekF6qWOICVzJw3WG9kuicQG9iKEJIOa2eQAZRRHbw4167fasFMN4ZZHowt/ssLdut2kFysDztJBOgLqzdqaF93uZgoAB3B1E5ucFsrk9O60qLNBseXO0r2gzKn6TJJpHSlUowNFIVZ8Xbv1AY2D6f2T6eQ/DVbxO2gcq6CjmqFBrLPg8trqTFGohXKYzi1vEICY/CfCyPuUqqASyR76gmm5rFiSF12eGgCAtlOK9rnbzqDS6AJ43n42JSREygda/MnDy/jwChpZh+vh7fAXA2Lonjj0t2rdhgljVfxprkglsesct+J4zs67M7gaBIvl+2fp79q5aK1yhkchYubEJuMEA3uFUBxuWEvgAr60NJWfGuRwXveDRzGTMLfBEh6Yz7sv01f9Wat9CcrbK0j8FQkcBBB0Hgfzl9EoKDn0XN8DhMUNAFpNuq+GDHeM+7RgXqGGqIIAHU1b0Xxs72g/t+wWEABqTiKCh2DcaHVWAx8Ly89K9KGN1Gwp1a7198EXAI524ULaz8ZC/Y0I4cC0+rqQEdsmSFpGObvIUHQgQD7qZkJXkB0Pim2iBdVprmBCoRQqiSiTycyjiBP/DR0R1oL0Eng3HTH/53TJk0UqrkPwzL2ErqQkxcqVTT1+DbjewCJv711M9MRvmvfSAY0Z2x7UeMILqaJZSe173b011TDxvWi+b9d8Mx2lm/FdHRIbHXGLaLlstHuaXk0GXqo9Cnt3uNHPSzkPckIg7Qu39c7fTIlX2Bbs6JtTtfhg/KdG0cEOGcSOQzr8QoTXOq2hmvKZwQfau9RIVioc/tmK5Ca3ZgyNHkiraW9PCEEhdVEzvAiolaTKkAa8q70TMhPorAQ4Zih50dNx2PmCpa44ADYORNgyzCCyEwc8D3zv4dtW3HzEGWIFx2ChWzb3B997fINYDquzWU0G4xfA7MBwk9wUJi/nzfyYxYuYpDlEz0O/H4ZoybRjd2B2Nd9m6HeABYKiIUYc9Y6OwDM8V4UMnH+1bNankqibWeR6ctZddtkW6bA3iaC+gS+gwWZ4IHg1hCs/WkjR8/f7WLTUBviVk6l7hcVguwOD/dzp2ZrJs", "layer_level": 3}, {"id": "484d4aea-c0df-44d0-9b9e-c951ee96a3dd", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "核心业务逻辑", "description": "business-logic", "prompt": "深入解析usercenter-service-group的核心业务流程。详细说明用户认证与授权流程，包括OAuth2.0的实现、Token的获取与刷新机制（参考RefreshAccessTokenTask）。阐述新用户注册与开通的完整生命周期，从请求接收、异步调度（OpenUserConsumer）、到各平台（淘宝、拼多多等）的开通处理（NewUserPlatformHandleService）。描述用户信息变更事件（UserChangedEvent）的发布与消费流程。解释多平台适配器模式的实现，如何通过不同的PlatformHandleService处理各电商平台的特定逻辑。为开发者提供业务流程图和关键代码路径。", "parent_id": "", "order": 5, "progress_status": "completed", "dependent_files": "uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java,uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task,uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl,uac-service-common/src/main/java/cn/loveapp/uac/service/platform/biz/impl", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:06:38.914394+08:00", "gmt_modified": "2025-09-17T17:13:48.324594+08:00", "raw_data": "WikiEncrypted:mHLzoBKW/ak+KVdbda4A2Zn67KoTeQt1oMP9weY0rUfPsH/u9SvESj3b5puwRAUcEjoXKZAdC/Seful9IR6HtQHRJ7m7M00HNNexsDbm/SwP8kOPrHSqgQYY2GQzKl4ElhbY1AILfOfd+798x6t8RL0kMam3eLyGB+iZMn63eXH+X21+BzUaSv97I3U6LRhrZALDORSruWY8izvjQWXE+9WedT7i9IK3Zt2sU3FRQw1BbO6J7uBx7F77PGtC7HNygP8klZEkfFQ1zM6U/dGCKi56M/3YWf0E3kmKo4cqWyTRl0aiQpeWDXes+e1kr+NdsNCkIrMwgIeKmvIcqSUnzwNf8g5b5L3Bt+uhVaZaqv3/e5G1RDNqftFlo3xSnEUZuDOWPTop7iGz540vAjjcYn+MJAKRMfevLAz1S9cmBx+H6XxlsrPFWGTI35gtGXVr1WmpTAh1DcO7qhVUnihu9N5BUQjib+z2GEn+JFCpKTAbpsQw6l+rI1UflG1Kv8HnzeCd8NI6C1d0LGY1j7TpqZ5EUw6qoWgXK5L6gsFKHjhJdz5nPJGlKUKeI8Eue7xHeb31xod2yNSXar2b2rl8ygIkJcunIaieHS+nGnzd5H+XoHHtYBWRl2TbLpCiFWllDVLiF/itCyayG7Tc2xW2i+3u4mlZ+twI8JzH0FU1/UkIOM0YWVU5f4PSLElGMbCjgvwUgNsN7iC+BWDpAgxgouTe6DFYIqvok/k4tuLLrY/adus5c1XeBSjJwErF2yAF8OfHYQpeoJiEAEWSbYeVdjecGHiv5WKXCKbmbNd2zFS/ASqCVnKizXLWaRuCtWvTyACv6485IK75BpF4HimKGl6ywTj9ZGFYB+MjXnTILP3TOSAmdmNDUHJE2vCaRYRtp3khCyRNZdCder5gK5fj92SfmIaxsyZmmaA7lSm8aOylG5Oe4/nF400MhMgp49lxosxsxy2cN0e76Eku3dGt10gAwYv+qEiCHbyj366aNU+q9D0WorHbTeNXI5cOfbiYVQJ09ZqACjxNYml7Sg5XdKLkTInVd0bNg8i6smjsc5SU4m9/8Swl5gd093+hZ3D8lWyQE98ORTJYf8ue/oFpK0NataG3LWYFCIGqqcjCFm64s0Guesdpua65J+v2XVJeMADEWLyhSmjU4w9/MsB1HdhaAQKIr5Nee8y/f/qOwthX6Ib1SSkZnCHCl71hnGBn04PFL7wC0jFLyPdv9wo6WrXiXnJYwLhCMq1HyuK864tHRkP1rNXgQrNYSSDidtFVMEPgfaOG0z0i1XwDv6/5wFBseRyh9eWNM4hUOAcY0za5EVNw1bcfY0Ih+e/QyPACU8MqUxr6qjUhEU/TgAe64mZpC3JvAOR0PHMcBZ/M0JtzyNuUKuhEjKHh7tMhaom2w1YGXLNguWvt/Jxe3vKk+DGA523dorFsxx8AGZIRtqc1sLZgqPOyR8arii1oGjTc8uwJ5MhhNX3NYmmduIxjzyJ223Ki5cAPd2fUSZSpNe2EEFIPGnKqN/brNBVhtlz5YW9p9x61FVYubdpf9NUvGke8QGLt5dJDF96vr1bO00vWAdo00Mnd9pwnbCbfeb9wdCb3BSaI+ZlJ/YhDrYrSDii5j7FP7r9gxCW+uHwhcbBLCLpqsEIdP0nue3aua9HsD4/bUa0vYiHj8t6WxUfs6hiL6YSXAdN8o0xi4rexkch5jgODHZlhfT2kNGSEEC4YC/OpxlzOO54xRXVVienKxw/wg5aemTpPQd/0Wc1mhFo=", "layer_level": 0}, {"id": "0d3c57ee-26f0-46be-89f4-823d083e6e77", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "uac-job模块", "description": "uac-job-module", "prompt": "深入分析uac-job模块中授权令牌刷新任务的实现原理与运行机制。重点解析RefreshAccessTokenTask定时任务如何周期性地扫描即将过期的用户访问令牌（Access Token），并通过调用各电商平台提供的刷新接口（如淘宝、拼多多的refresh_token机制）实现令牌的自动续期。说明RefreshAccessTokenTaskConfig配置类如何定义任务的执行周期、线程池配置和容错策略。阐述该任务在保障用户授权状态持续有效中的关键作用，避免因令牌过期导致的服务中断。讨论任务执行的幂等性设计、失败重试机制以及监控告警方案。结合实际代码，展示任务从触发到完成的完整执行链路，并分析其对系统稳定性和用户体验的影响。", "parent_id": "84196b87-4eae-49d1-98d3-e8f06be3b022", "order": 5, "progress_status": "completed", "dependent_files": "uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java,uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/config/RefreshAccessTokenTaskConfig.java,uac-job/uac-authorization-job/src/main/resources/application.properties", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:07:11.37227+08:00", "gmt_modified": "2025-09-17T17:30:37.077194+08:00", "raw_data": "WikiEncrypted:50HTGzl5JiRxCmJg5JpdKo/ciqNECe0WHzOtZlV+Pe36eupGXfpXe/LWk9ittUaHIU+Sjjw3QnIOK2Cx80rrYNXckNpD3AHTmuY3ljlZtEi+adBA7xIyUZyh2vDmDnXhoq+47gYy673qn5C1MaEmO4TWMLLeualFcCCvM5+XAZIfDoL7SkwNUPIMP/qhWHSj6AStkNVXm7bMEWKLBmxS1W4h/3Af8FuydgRWac1qDFNodJOQO47wej6sT0FdUS5fu99hclr0OHGjB8sYdFV0bC1j6lVUWPsas+bzFqdYqyigK1X5ieg3sFZnrLAIfTq01BL2BhX0znRAMQe24Q+BiZfm8zeMxPhDDVw2vqcOgaMh/VtTi/69rK/fErYPzd9wyajnmO7d9i3WoUxx4FeKublm4VQ5E80UrQIDYebRsNa6S6DIjNjJ5/rL5QP42iGst/a90vt/aJGuPPN4zekUNhHbFrZF4CSNqstSJZ5LYwIpBG8Lgvlb5WJ3O/cFSM9fsC1oreBfNeKLfh+BXOfvx7/NL6UpH5OgFkxRBjoniayzik2IwH2VSuaBkC7Y43KY40Yo+7YHhiAlbKXYUpdPtb5XVw6Uw7Vnzuif4FR8YIzUKT4PrQIkR/Hb/kEtFJMFePHZEvrhtS6BtiDws92+lAo+kJ6MjakF0KNiaFRPr/F7/tWC25bNbKk/yAr83eadHUAbNRw24b2UxHZoeOkZuXysrmri7WXtNND7+HWZ/C+pyw96BaehXXphMX8rQ4TywBCn0+d9YWaygEArSyzGipIJv2oumg5oVqhfFHEbTWfNEdaxygG//llqOAwotGEPIG3AQtzSKmuCWZtRX7JH25UMhhZLQ1W4YRgU3RTUIoS5SZtJCiRg2DJbCzAp/oxojVDA4xNTMYIBBaAkouz7Ayz697sDM3swotpm/hQjEdh4p5VL30VQ4B2CWySYbqHBy+00mkSn/VmDz+3y28dZfGp0boW+Vl8YX1omGaKyFYq/DLs5d5vQv0eEZ4iZB2LNRMq0Kr9LyMwJKQUg2IYL3GRqgxSGVdXvJ9hQwhbXPfKhFh/i/Yvju7OWgulq4yiuCZW6H8Y7QBp/4bWUxHnGtsYBZPw+tuxUunwvIOrnZLf8DHqBRNq4xSbN1SKKRd4y1G4B0vWtMGor043qQQ/uLeVPGmOZcObeLfcwCcQDXfN9UQMJHcinFisjF+RRLd7i7JYiFx5zzGr8iXrGXmd3C5drfDDqDwb2awMtJEgirLz9jO4pVR1nnfdC+H3W/c0IwobZ8y5BXUp0agu1fBSRrimsMqNwTzVwi1Bmm/hRshjDG4f/XHmpUXxY+Z7y7RSYF41okhY3z57wyflBnMdrAg/S6Cu9wtCOndHZDx3IofViM9RulVOZQjYElHo3iH3Gt9v4fEufoD5A4wIjT9dsrogq9fDQPoxFEiU2dci9sesdlFfYEfrRYxtcQXy3m+mwh71t5J/VvUQUbsfP47mWYhhwRpX+a4s/rAbJU4PpA0DFKRDh8uwFlpCJsGcuAnmmkqQ+xoU5Z6Cv+OW49z7ZBMPLOOgV2ltiduyFm576xeWkwYZ/LyI9ZKMtW3pGbEylTcdDnN1JQ7lRvQiYF0eHWMlNJJ697CCRs3BVz4JJB3RBT5vHYzP6dZx/lTbyVd815keB4Qod1bjQoMxwQKICpBrXvTFdTvMFVilWXZw4hGSOr2jAjQZkycFJF03cLUVPIzoXFiB+xTAxPZNhCUJjGb6F3n87XcHelHM4q/SU1X8i1slBK5UiPnJ4mZjLRlY86CP3gb4q+wDQ3S6WS1J8ZOV7TuDETqx2NRnCtiEZX1/9DdqPLgWLkVov0Ht+5MB0qKyk32JxOz+AH8H46t/X0Q==", "layer_level": 1}, {"id": "df434262-7398-402d-a4d2-cd897b1ce94d", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "微信小店认证实现", "description": "wxshop-auth-implementation", "prompt": "开发微信小店平台认证实现的详细文档，重点分析WxshopAuthServiceImpl的认证机制。详细说明微信开放平台的授权流程，包括公众号/小程序授权URL生成、code换取access_token、用户openid获取及unionid关联逻辑。解释WxshopTradeAppConfig中appid、secret、token等配置项的用途与安全规范。提供处理微信API返回的JSON结构、access_token过期刷新及错误码（如invalid appid）的应对策略。通过实际代码示例展示WxshopAuthServiceImpl的集成方式，并说明其在多端场景下的适配方案。", "parent_id": "10e48e47-ef03-4f56-9d9f-4504a0820575", "order": 5, "progress_status": "completed", "dependent_files": "uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/WxshopAuthServiceImpl.java,uac-common/src/main/java/cn/loveapp/uac/common/config/wxshop/WxshopTradeAppConfig.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:09:05.037239+08:00", "gmt_modified": "2025-09-17T17:54:54.89355+08:00", "raw_data": "WikiEncrypted:/5iv36bvAaXMgc380eD5KsNCebs1tvIQD4wU4mvrfevwcoPxBwMCf8rWI6t+f5ezsxsffa4dZfg1PfRXt9n76CEQ+u4c/i50nefn32HIRM37GTWS7u23xXVlGuREZmKpQMTFfSgwV6FXzkoFGJUN1D5Ag8L7gU3yWPdSVMee6GQK/E30FoD1UGswxiuI4K7VoN1CYsVwQMI6V4Nqw3WFZ4p6ZqtQgIC47N68v3XHUWDFHd2d4sjiEVBsVfM4eYqUc521xi6sJXGRZ3B/RECNBLKLJlG0ZS6LEh6cvNtVffwOm5AI6UADhBmPdJOyi+Ra8Y4JXdy09RelkRWxAtUU04ex046gGGVYmvp0hRYfFl9s7wR0K/JM+5V3h0FuPtACaG2aSkDM6PZYqzmu65DquS+lIkxhpnMc4bMqg/df+Dnj2lbxZN6TTNZiW6OivfTa79FAQZhG8nA4Bsp5VQXzY1cR+p97qcno2Ai8rQvJb+9103aTdTyWFXql5Th3UHaCI2LB5P+S+SL7fPbJk6Zos7e6oV8h2jhzRhAcpPh5vWZxx2Tv5Gn87SMNpVFRtc3VQFr41NNdudNIpJpD2yQR8jp/pcbc/har7aWp7+d1JVK8sJFdweHZqdE9Q0PxSiXi0mUWWgNAeEXZI06BUOKoo8mIMOXJbTG0lch/1AqeXePM53uvtu/UES+GF1LsKTl3ycFiF556nWzxH02k6VtqfvibeAc0Wx1E+jI/Lrw4sMYblvjdlUvC+Rw4A8hLmN9dfuDITlh67FxDMTX5B47Oz7KfCKb4nWHijeNcQphC55wrvKrI16n2v7w5YkAk4lJB0BcfvSH/JROdkko3x6oka4gZdR6Q5e6Fhtz/G1BWgJIsN2ZOYW5HCJ7wwY41Z+GlBa+BAoVBgR/Vn9WHxzokMAM2Knh4hOGuFOOM2YcmsOjGQP1jZNsJhc9y3I+Tml5ftEeh0KfCguVlX5/YT1NBZGEmcYh2AinYK3I7B0ci9oAParWkO3HWhq5T4qZnYJmzNYCzLKWFid3ftk6EXcEKsGgsSKAAoU9q63BEycWa2Rrz0461r2UmX2AZkiccii6+zV/oXjscWcUACq5Yef6SCQORCjdz2jiuXvoCGoiSdhMBAXO1VPVNYZ59FFiG4hkGmvM+uu4Z3MK1kGtjgHaPQhjSVy83z/A3pgg87VdWJhIy3xl+MIDDxEzF5fU9wYtH5+hXjDRv5lDmzhu3niMPy+wj8KjoLHpAUMdDW5JJGsm4rihLa6jCrUvLUg2SJZIY12iwcgKDyf3zDLMglmE4W0K2DB39gljP9WXycyiXvmQ=", "layer_level": 3}, {"id": "5e961b9e-e3d5-495f-aaef-bf1b593cdda0", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "配置管理", "description": "configuration-management", "prompt": "文档化项目的配置管理体系。说明如何使用Apollo配置中心管理不同环境（dev、pretest、prod）的配置。详细列出关键配置项，如数据库连接、Redis连接、RocketMQ生产者/消费者组、各电商平台的AppKey/Secret、定时任务调度表达式、缓存超时时间等。解释配置的加载机制和优先级。描述灰度发布（VersionGrayHandler）和功能开关（SwitchCacheKeyConstant）的实现方式。提供配置项清单及其默认值、作用范围和修改影响，确保运维和开发人员能正确管理配置。", "parent_id": "", "order": 6, "progress_status": "completed", "dependent_files": "uac-service/src/main/resources/application.properties,uac-service/src/main/resources/application-dev.properties,uac-common/src/main/java/cn/loveapp/uac/common/config,uac-service/src/main/java/cn/loveapp/uac/service/web/VersionGrayHandler.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:06:38.915217+08:00", "gmt_modified": "2025-09-17T17:14:19.856991+08:00", "raw_data": "WikiEncrypted:aC5ZtUyEKPSxjzg//aVllPgfgMGT84WysP5aU7jVGgMDPSKvWZp7xsAQzGtWF2TB4Rm8ZVH4zR7QDXSHsvp7GUnrnl7eeJjHTgKV5avCfSte/Lq1B1z4rUUkByft6EgT5ZdndBhPCvA1tUnOEBFfituGC1v1jsdYbf0ewEhZl+m8z79vLNnYD9kalFdNx4hpZpIbRgMS/vmUWqGMW6MROuXd5EuJa5QZrB4vldQcwZQ+gIf5E70AUNqv6fb5TTgJ/RU1fSlzV/m1IQ/qWy2hQVNHEgZIlX4lWAqgMKRLx/yIiZeBCamQ8/jB/+esTpoIE/d4jp9ohebN4kwyqr8h3W1w44RDAmLDznCOTL30jQVX44uRZcdbbCfEcqTvk+7N9E33Sq8k6/WeyEaZYq8RvcxCUHciMGbrJQoZMTZjzrLHY3nzA/pKBeDmUkNkzKSuYO4ENKWHeark6DtDBCJxySuxx/9dggEYtdG+sm9pEy4rfiA+yzBCSfMA7SAw4QbtZnGVMJSodHg6etdG+WIEkFCmTkYQyeRucVBwzRIjAPXldJmPYUvaNqfqYFiRXEWvX7eY/lWGs8aq0Hox4cSAVQEZk2s/Ks+wtoYwM+624Kqw/lJPLubHMznQzlYepa7rbID+5S5xoF+aoV3at30LhxpW1ZaLf1ewCjdWetJdunXVkUhNPHmmoUKma3XL3Tm09/oRq4olCd5j1HKdfSqL8a1gtPik0cK9Ke+r0LgMRdvQ2yXK92teoQsILj+8fRZuVyq5tgQI8chYS6Nj3E4/QYyBDHoBg4c9AfI9AlScV5JpU3cObk4iYRMb/9IFhJJFETL3R5lCS5+N5sI3o2jhhwmBZNspgqPPrBx/UdEZ/CD+AjvIvnZUVnIHIgnaGdoqdFVTpaKVyZk9VX/dORAaPSX8VHAD0QxwNsMLrD55egz7cljNm71QGedcdJL8nouCIcAtVYB1kH8jXSc1UExr55o4n0hKR4lpWABQt1YJhXCkq/36lsuYZMcW8fYr/lfnQLaIj5gYGS2qa1scuQVW1w+PESO2ZROlg6Qt1GdvoqtIJHsj2cyoBMqx0ScyS81irR2sbIQXuRL5gZ2Zgjs+3n8obsCQP3bYG2HQ4cInLN1OmzVg/Ce/QStNO3mnzNROrOEK+NzmTioikwM4VSqtMs+5oMWcRr50hVrSFLZZoQlILRxEaa/+OXfQ3NmElOiiDA3+iMGfNlo9uQtstlQpI8rM+LiulUPJQcjAFSqJbuKBugtbGLlfrmqTDtIcH2NiPKV/USWaGF5ifCQI9z6z9Lco3gIIGMPzsRViS7W32CrMstjId1k4+To7mKe47Kzx4L8jp6FNAAhxcHWKCTHBdsSAYLR9rMZrx7HMf0W3HxHExcwoaMahr72slRekGejw2zxg/7bRz7p1FF+rNTYHfQ2YC/9Kve4z9jATXgixB6qyN6pIeG73SysLKl9C5ECdOzip20fBkGMFOeGRICb+5fOSu3ynbgk87LNs3rj0vG4=", "layer_level": 0}, {"id": "8d6f023f-2a96-4cb5-a609-9829002dad5e", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "抖音电商认证实现", "description": "tiktok-auth-implementation", "prompt": "编写抖音电商平台认证实现的技术文档，深入解析TikTokAuthServiceImpl的实现细节。详细阐述抖音开放平台的OAuth2.0授权流程，包括授权链接构建、临时code获取、access_token与refresh_token交换机制。说明TikTokAppConfig中client_key、client_secret、redirect_uri等配置项的作用与最佳实践。提供处理抖音API返回的分页数据、频率限制错误及token失效问题的解决方案。通过代码示例展示TikTokAuthServiceImpl的调用方法，并分析其在直播电商场景下的特殊处理逻辑。", "parent_id": "10e48e47-ef03-4f56-9d9f-4504a0820575", "order": 6, "progress_status": "completed", "dependent_files": "uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TikTokAuthServiceImpl.java,uac-common/src/main/java/cn/loveapp/uac/common/config/tiktok/TikTokAppConfig.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:09:05.037831+08:00", "gmt_modified": "2025-09-17T17:55:24.641064+08:00", "raw_data": "WikiEncrypted:6HKC8o18nzNcaXmecicPheObPO+EECbuhsWUXk+YB7oNmHyZ1cZ7FF+LBrRGsO9xAT+YInBXDXjkETLBrXynRGYkC3SqzXwKq/kE3BEWeN7lUXdydSePbjN9RG4hw6CzCu4dGhXSrHhVABkZchC3UFD3MN8TQy0HMKIRUXxzqKQmh5Pg5xtoZaxbwtuet/MHjF1HKHNs2Cp/7pDC7/V9g6ixzaZlg8yKzk+BJEwYZUvhwap/HmGrCZ2tu5n2vYIXKIWHEdRMnf1q6mlfipyOuYudF9Z1CVh1Hezm1cUq+pYrA+JNseUtazHbMqAHva+8x7JafSQi60ZbUr7Swy4zAViJOJSNkqjW/K1MGTSK0Eg8m6DNSIKR5LzynlzIuneTXEyjj5sCm+jrSs300n79EyVdQgWAGQcbDMRBblRVExPIzMbvO2uk7UzdClLpEgwh5kf5nPUSp01zuK7UIwj8hzFOMl+5kLlVz5nT7PIpiBclx8qQlzFFH6l0pBjb7gPlrhaNV1fMjmhyA/mprlkcqbbyf6piSyS4Eybof4iSfU3eQjnKSPUHdMVaxH7lD+h7V6nYXFL7/gw+oCq3wn3wBsUbAv3GWzojlJKDirc7xipnoU33aAujOUyzAL7ywBlxB5eiaj3AwTJQ4onMYf6HMQobiDCZHnvpNuvCQaNJrrrhwfoZHwj3L53KnH4wZC46CAXAcztGwUeyOnOIaRxfZuybVjUx1YCPuv6ioessnRszGIptIGlf/3hO4umO8/IuBq7GByI9B8v7bvLyZ/CaZpqffPztg+dcLQtLUaDPgsONHMi38b9b6rq3weLSFR2VY1uoEJe8Jqaaq4GcpmxkQh61IuiwBw+Sk6tzZtqLK7+2gYmGnN5CGZZ804cT6qrq8wCXT4eMFtPY1dWifJK7HoRdPqg7yKY1iEjAwopbl836ZUsdWrqNWkiFuer6PhmolWgn4ZDOrqBPJrNyzd4DY4d2h+a+zkHQg/XkDjkcoaiEovMHXb1bLb3Ft1+VLWOMgxda2BKbSL5z28FgMyIxlhSv62s+DfdHbWad8yNQgYLUb8cqrYTNp6I+CD86UlFRyTUC314clNTa8GHsRrE9sEW63NPizNRuufzniJtJTrYTcPu7J8RwS33fR6+boZjQ385oJMYiVoLW41ecNCyLq6Gv7eEjLTFCLq++RvOER2b34BOUbuiewszXRgC6MQx7ML002vmVsn2vMGyoUoKAAc2s49K2t7NYYXUwSxSDXRoo7lQcHYCU++5ZDsgyMNfKhZF2hbXKMrfXYKYmR59weUq/lTSfUURyK4XX5SDXPnY=", "layer_level": 3}, {"id": "830291be-201b-4882-bf19-a089732d40e1", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "缓存策略", "description": "cache-strategy", "prompt": "详细说明系统中Redis缓存的设计与使用策略。描述缓存的层级结构，如用户基本信息缓存、用户设置缓存、授权Token缓存等。解释缓存的Key设计规范、数据序列化方式（如JSON或自定义序列化）和过期策略（TTL）。分析缓存与数据库的一致性保障机制，包括缓存更新、失效的触发条件（如用户信息变更事件）。介绍提供的缓存工具类（如UserCacheUtils）和基础Repository（如BaseHashRedisRepository）的使用方法。讨论缓存穿透、雪崩、击穿的预防措施，确保系统的高性能和高可用性。", "parent_id": "", "order": 7, "progress_status": "completed", "dependent_files": "uac-common/src/main/java/cn/loveapp/uac/common/config/redis,uac-common/src/main/java/cn/loveapp/uac/common/dao/redis,uac-api/src/main/java/cn/loveapp/uac/utils/UserCacheUtils.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:06:38.915872+08:00", "gmt_modified": "2025-09-17T17:15:15.484142+08:00", "raw_data": "WikiEncrypted:dAMSC70bWva9SabC1uic9sH5U4o3eDQETHk4CxcvcSlikxGyhLiNWwYJORZZOlbOyEbF968sV/SELsvKWfI+RYnwvYHhD3XZ3OtYNCH+8Z04gnd1vyQVY2Pjne93i6Nd++jzFz/UcJzPBuJYdJo9hYP/q20+yd+8S94Rz1vXDZ9qze1ecClkU7BI9Mni9BMcMCtvDCKPdMwKxABj5pS76zSbGghRB/UqzclfeTuFZXjHO7d6MK6YnKFcxxz8A4QW+Xb1xa5MhjH2X7Uk9FKUksLyj/wmzrK/547ZHhBqtpBPGo4ctgrMTerU3GTadRwKDGprtEDP7R2dV1nmQ7K45DtaaDMxX5T4H9M+p10eFXM2BEI3VO6cmt8MndH3PI1itt/xomGI6ZrO2Vk5NLoow72qHNIUMzYuSp2YUC9TTwB6sbEMiuMPVf9Pt6qwer1UWdFhXCX2a3KsiHZzFovWqoEeWMP4MAyK/U/ZP61q9XDSz3a5tOjW+drP6Ne4PObKS3UCJT9axj/4DfwDvag40En8xm+vDpP3xVvmvBWb4Bip/9w2Uk0hqjSDkjbuTR/Tt3O69GUGVFlTlRdtd2hsrBqJKfeEH7X1fWNVRoBNw2DuGByNafQIrm7z3hYH/yBoVj6S0BoxeiigQMSaAmpF4nC+hD3fbEP619QoJsLFrndjyfat2M5NzNcBMsMZLdQxRlFZ45RfDXLnoYo7mHIWuqLJab6TEAToyX8YLEegguzHUC8v8JDIjcitS4d5nMIvz5Co0tVGm31GS3pBJLKk6Yw/CQ2eS/vKnrKWpIrvLBObHdGYiyy7M2ILGFLORAWBYUjbEm5Ux9yaEYWCkdQu+XeK1g1IwBR3kiEvNUucsvLD5DeBF/Me/IVEAYHqiX34S1/IYMatJb+pL9w07bivfA/XJlLhPNdhKhIKSQb0yadz3xKyHAY0Otq7VbK92+sOaftj82JPtT8Ts0D0XjKuP3UQMH4tUOD/tyeN+slnu+VzB9qD/Fm6maMo9eMcn/KZwEMXvKr4ULQszY6KaV5Yh3BfS8g85EHLMMbC+JzLfDI3O0jap3i3AJnmvyJg3Gf08BlisQHNYoYvqhQ/60wzO0nDVpTSAL55u/4Oh2QtoCbb4svuG2LIKmid4RWzkj+CYTBXdGGaaWO8dB1ftApG+ryMto2Q4JwaL5GS8xTFyvWkVEQPtJr747zlk+bOZHYfBkhk9SGzecDEtJknX8ugJhvxGnCeZZRRXrLNFNSnKrGoZBSFOgiEG7HD57fEuyJLCOJbSQWjLiXsR8ewiPHAjefTm3A5BUxNx/Eq2jBP+ZIWfLNJ44W2V0SPRGXtsEhwja4YoQ0A2+lPYWJi074MBA2FcrT1qPz6rydPpgDLaLJDVpOOLK+ESc+Vk9cReIqx", "layer_level": 0}, {"id": "8de7ee1c-dbcb-4dfa-962e-dd2bd4914cab", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "阿里1688认证实现", "description": "ali1688-auth-implementation", "prompt": "创建阿里1688平台认证实现的详细文档，重点分析Ali1688AuthServiceImpl的认证逻辑。详细说明1688开放平台的授权流程，包括授权URL生成、code获取、access_token换取及用户身份验证过程。解释Ali1688AppConfig中appKey、appSecret、授权回调地址等配置项的功能与安全要求。提供处理1688 API特有的签名算法、时间戳校验及错误码（如invalid signature）的解决方案。通过实际调用示例展示Ali1688AuthServiceImpl的使用方式，并说明其在B2B电商场景下的特殊处理机制。", "parent_id": "10e48e47-ef03-4f56-9d9f-4504a0820575", "order": 7, "progress_status": "completed", "dependent_files": "uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/Ali1688AuthServiceImpl.java,uac-common/src/main/java/cn/loveapp/uac/common/config/ali1688/Ali1688AppConfig.java,uac-common/src/main/java/cn/loveapp/uac/common/config/ali1688/Ali1688DistributeAppConfig.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:09:05.03885+08:00", "gmt_modified": "2025-09-17T17:56:03.7767+08:00", "raw_data": "WikiEncrypted:k4saLPHRvxolN9ePPvhCT85bla9Kr55EjtYHEZ0SL7mlTcv8FAw83PVvYOy4koTXgqNKO6YSlsxO0poNRiOZOkR2n9l5+IDE4wakhmpQR0wDPj2gqVThvRhcCebKlMXjQrI3z1QbM3bg18/rhyU/QI5cr9KfTB1byfuLTR602dahXFSgCJ/k5BH3THLWrgbYXKPquJxiv1tTrEpIHJkrNdHR17/jbj5jslJ4DkwCGZNw/96jHtk8aSdvNf7yx9g8K7RwJUIeRilQK18RXk5eXAVm4uG+SspbMa3hucRpiG/K6Mx9vn5kvDbYmx+r6MojD3eoQLfWZyMVglGHC5KGxELw2idHqkwE2sDnUWII1aCxWZq4yJ0NTiyTeELMl4ioLZp6AHiGdnVM0HKYouVH/OT3sSaYmwF0vEKCFgrdhxDWrSZg3KwhcDGCnNL3/CJdZ+X990QymgwjCXldfkeIsuOpW5Ysriy9erfg9neQ5mmo+x66C+gsZyzeEozl9sF5GD8HwghiJhqqNgdBcCywooMNHtTX7BVN7yk9TAuNrN5NuBVHoZt7uK5E+LkTLIeZ9MS/00x04wiwNrB9e3gBYYQVqTPYTkayyRHKziFz9NH7QJJNheBTlFTwCfX9rWuTGObIYtC+vUYqv8LlfPwWatKqic5D6FNEd8tyOo3KwhXcaEOpV9M1mOFqC6Lio49PYBjmEqE+BOvyUBfzu+/TAdHQll3eB/wV9VkVKjsEHHbVHOp1CA60gaBqRiBbI5tAQ5pL5guqYZvkgqPg0+4HztKRmFymeU1VRi+U8NeX6PVefxhTD/WMEXmC8/f/r/0WDoZOjVvQya7EtNMbvSvkMkVZrubp7brAtTSD4w82JwA5bH6ulSBV/TcgaulvS2LF/CYR6f4GDT21rx0FkRmrBODdCYTqbsfLjs7ZY1BwozZ4RBtZJtArlD5kRAPFr7YvSmUSzQPZSo30XfUHBraFsIatjaP7ZdRJsg4MQUTbCzsFowvd4agD7OPgnoGIJEClXnK6+4W1JGDiCq76VOPGeLQSPrqkj3l49tz3P/XoKfEKaNmgiHGAiYSRWhISJY9z/g6rAYPEsa+ni4usU8Bogkk2uCdJEkBYXLLTI/YCVa4ctk3nYpQlxzn27/CfUQ1NLiDIyURLvOoBjQGdcLO9DkXkYAJ29MTT3Yi9IC188ynXlCUTTmHrBYm23o4KufJif71jPW8mYDYrVEkrcll/s8UuGvP+FeX1nYESPQUaT4/04XQBW7Hba8AmPP4EL+9XBewGVdN4qp3YN+COc4MVXR3VJXkK08Cx7CZA5PJzCBp99a/OFjjkL6usFimXHTK3LECcA8Sc6I5VOhnfsrjbm7T2iE8LQEDRZKoLSRT+lmxPyoNzI0x8sNb/bmIi+LeYEIRjo6A4bSar9fqROKiCwA==", "layer_level": 3}, {"id": "0e94e284-f9d1-4416-aee3-a464ceb58a16", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "消息队列", "description": "messaging-queue", "prompt": "创建关于RocketMQ消息队列集成的详细文档。说明消息队列在系统中的作用，如解耦用户信息变更通知、异步处理耗时操作。定义核心消息主题（Topic）和标签（Tag），例如用户变更事件（UserChangedEvent）的发布。描述消息生产者的配置与使用（RocketMqQueueHelper），以及消费者的实现模式（BaseOnsConsumer）。解释消息的序列化格式（Protobuf或JSON）、消息重试机制、死信队列处理和消费幂等性保证。提供监控消息积压和消费延迟的最佳实践。", "parent_id": "", "order": 8, "progress_status": "completed", "dependent_files": "uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq,uac-common/src/main/java/cn/loveapp/uac/common/consumer/BaseOnsConsumer.java,uac-common/src/main/java/cn/loveapp/uac/common/utils/RocketMqQueueHelper.java,uac-api/src/main/java/cn/loveapp/uac/proto/event/UserChangedEvent.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:06:38.916503+08:00", "gmt_modified": "2025-09-17T17:15:55.212467+08:00", "raw_data": "WikiEncrypted:IaaHBhg4aKVK2Z27STIpf04Yi1bGsg+84nmHA3ekUPQQPGV8G91NoCROjhJfvWYJDW3jZXTQRYZaWnJN5ZHMS8emGAuFq2ZMpPWXmyuQnGjyJ3xHDDNV//zGkVuKtMLSFcj83RpUpWO++KVY11NDlX1waspRB+teZVKiD363KdghnLcZqa6IK8ylqnKtH76naPAra0/16Q4lqGNtYyExmsmHSLZp6Xq7SrMqLN3NhTFIsl2vwW/SW9CotmFk3slGEuawmt/kNFe/5pVVGW3QKidT7X3uQ/q2VxczuAAZ3k34pYW72XHR2ivLXN/s5d4CaTj7DMfXnn2jYYF1PkiGySJjA14uhCk7jMq1m2uY9NYmZZx6gfYDiEC4fEfXT/LRgYXCUUdA/xnmX8vyUt2XmDOaeU5TItSnI6470PTOuK7agqCbM4shon/BA1sq2aWwLG0LwLjsExMsXBfyWdMnHui96FPRSeC6DitSmk7dD9xI65Viyn8HFnjM3IXYofb5DABWEArKDy1eGMCV25FBTgfon9EjWjc6PGaYgc+iu9mtFDwslFVc+zLrEiVhEXqEntpVrh5DssCdI41fnH6x+x48SCGszbaQYNXqwrbUkn7vTjwk8041kmsFoopZAvqYwwzD0uKZDUyFMnljfCj59SBrm+nCarzw6WQj1qh+86/n8wxG6FirBBWr+z6H0Wp67HxTeQnp9F0uBQ5eBCDPM6vguoMovx9oDcCd/v1t4A4KneJJfF6drouGDNUS4myZ4K3ThO1QMdQMfM8eHl4rFTvGLXD7OdV3VUx5KAkhWxODO13gATMqGAJXfzLfun10j7KQs8OmQA4Ug9MkvMpk0pH7+QZN5BQUUlO1OjJEi1vvyc3bgVv/DT0d25FX3uLova/9Efsu0JxO7ywbWHLUiBNkYDRVD6W+46R2dlui6VcZbQXnxmWp7jPYfy/K7ZMarf1B61lzwhKUPG9HsebRDV9ekYSleCAq3kWTm2HUQwKjYFz92vj/h/fYKKVxqBCF/lgGvUG85ALRhGCJXzXbrPH+Kr37hSMrZ0tXUXbxnsxuqh5+mkjKP4hC4ko9YaH/2xxlhvMFQOKJxdHjoF5iHHZ7SJ+RuHYvnR/9tOuJHJbINbGrK/E+TTb1OZ79Z2g+tfQfCGrhKG07jEdKg68uhkpHdEJesL0zwKw60kjfl5EMBY2pjZp2Dyi9wUMDs9nB8KPpqmpFRs2f26Z4X0qdlhdYXHfQzqROrdxpgpTquHfaQhZJhPnQi7mRRmiAl/4AvIKrSzwch2YlJjjTSmyZ/rDwvwY8Q0V539u7jIgrcL9R7x6I5GeV/BHMMgDviMQs+EOTFFW7kNiYtrkN4Ss5WQ9eveCwd3nrV3atIDvJvbfdsYWRgFxw1cmRf+jib9sl7au9yZE9eHIJWFnHpFrGcW+DMRS5YQdX3YhEQewlBsmdMUMxiqougXHQY5dzaXG9WPzyqdamMqPNYk++dIIZAhJf3ikV9CDjlqJ5k6RFTHRaLX8STTwHK9Yr+v0TMRQn", "layer_level": 0}, {"id": "11640edb-f5f8-4737-a6af-0ab226c39804", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "定时任务", "description": "scheduling-tasks", "prompt": "文档化系统中的所有定时任务。重点描述uac-authorization-job模块中的RefreshAccessTokenTask，该任务定期刷新即将过期的平台授权Token，确保服务的持续可用性。同时，说明uac-newuser-scheduler模块中的各类任务，如ScanWaitOpenUser（扫描待开通用户）、ScanRetryUser（扫描重试用户）、RedisClean（Redis维护）等，这些任务驱动新用户开通流程的自动化。解释任务的调度配置（Cron表达式）、执行逻辑、异常处理和监控告警机制。", "parent_id": "", "order": 9, "progress_status": "completed", "dependent_files": "uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java,uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task,uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/config/RefreshAccessTokenTaskConfig.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:06:38.917269+08:00", "gmt_modified": "2025-09-17T17:16:43.497183+08:00", "raw_data": "WikiEncrypted:Ixm4kI/Z7v0V3JxitGlz9Rn/Pzfj6n6O2QUIkPATduqUuCXqV7ALZ5QSAxoDD9d2M9MUDDdCEXeF6Z6nJ7XHHDnydC2i954EKolYQcloTE6s5TqRi2ykOjPy2vo85c4jSMGbCpBSKohZWP+9dRr1RlbED821cqmzo5ciTqGdZYt+oiRcwttMJdqqjydb6TggqIVOuDKe3CyqujcIyZ5wn3YqMJHGaC3hK1nHAR0hwuUsYoJWpRIyVnXdiCEv3GTy3OUQuNEaMNRd2Wx5eVWUKI0+YDRH8oQoB5Vk4maoFwrNAreEjzV+bpG9gYq1SfzExOoFy1xxhfE/tERXsP5Ll8mkqkFiEKrZk+Ep2cuashEppRVhVxmF3UdsrmEBlMrFkZg8lh9qeDQ8BQj7rc+w6AWtOyq66ftTjPG+0/DIRz+909aIyqDhq2tFWKvzw8aiUQQE6HawZCgAxLQ0kOcVHHddgBD8+kLeLmL31vTN5Fm3vkcPSRwYhohkU6nHI1DtnP0UwZEx+ESgzC+Y192RNltATxo6pZ9hj5QT6AdOMezAx+c5ANcY79VVvfaHEhECXlJi1kN8tCMUsTylcScvrnfl/IrSlkPBNw1hCCxqLM7xoGkmGRFwo4+4KomPcFLdXdZPJKH72vDZPCcdUDVJ7M6v6Q0NHIRwj0joU+5WRZAok1iRECkZgvTPCa5cCjjj6OKwfKTV1ZMrHKnmiq/q7kDjQIc59EQkQArZh0Svn+GY/6Ntm3iLX8Pp1ElupAMU58nxtJ/eaDU3UPf7F3cbkRSE98DDkqWxSqjTFPxGPKJ2lFHsnvhhw3dYGZzyMiim083hA6wzfA9+ZBYSpBLS/vYIDuLhAiqaZ8cIPUApJ63YOX+P+LiPtOKSaWOtWekknGK+o+CYJzgFV8HEdgSYq3oVRl1fPWSxzj6AeaTEKhTS6oISoEPuxpsswnghJaT/DBlxOktQ2aEl6TdE3K/OhwEo9jVO1rnl2KB/p4/thTodJREB/5FDtzfekutvOVNDLUOH/KikG5zwz0DbM8sOCiX/jYunw7IwbogGjkoH5d5iemR6YRilYtxAiT4ufl9IEcgFAolUaQzp0lcKahDzy5svhI7wxbaoDNlMuqPzQ4u/W62iSKx/DPK+E6FAEZUTPkfH5rFi8VMTfZROw7WaE5OgF4AXCY/1lMWvMBAOOI/lbAZ/D5Niy9m1guK8tbeWRP7vV8dNrfcpGBEDPAVL0UDhx5fCZTFoZKfDXRQeatATWQ/ghHfbtpM/9rotI1UJI3ggo/T1UHwZg5ilY9IzgQr5clWBaxFbHhFAukf87dGk6zeLI8OdCpPOCmXSm5fpeWZbLswkRBN/iIR6c6k3b0yz4u8WwCetUx2aZItsC6m8e3l0Gyrlk0Bb3YRPaOIRFCTHClEElRQ95/mNj4Z+GF86+bwVZ7CAs0iyYpWxqQRk68p2FBnFgpwRu1zXju5UzjPg1xXtHRiVDXpo/hw4ZTuEFv4TrECstIEx6iFnel9bFEUCSYetbB+W1v+ZUTZ9", "layer_level": 0}, {"id": "e9d672e9-3573-4c60-98f4-9d3dbe8c36d1", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "name": "快速开始", "description": "getting-started", "prompt": "为新开发者编写一份详细的快速入门指南。指导如何克隆代码仓库、安装必要的开发工具（JDK 8+、Maven、Redis、RocketMQ、Apollo）。提供详细的本地环境搭建步骤，包括配置Apollo的本地配置文件、启动依赖的中间件。演示如何使用Maven命令编译和打包各个模块（如mvn clean install）。指导如何在IDE中导入项目并运行主应用（UacServiceApplication）。提供一个简单的API调用示例，验证本地环境是否配置成功。列出常见的环境配置问题及其解决方案。", "parent_id": "", "order": 10, "progress_status": "completed", "dependent_files": "README.md,uac-service/src/main/java/cn/loveapp/uac/service/UacServiceApplication.java,uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/UacNewUserServiceApplication.java", "keywords": "", "workspace_path": "", "gmt_create": "2025-09-17T17:06:38.917954+08:00", "gmt_modified": "2025-09-17T17:16:57.933961+08:00", "raw_data": "WikiEncrypted:qfgbutC7oyxR6nMxrwk1ODnNMBEQ3/sG78fQT1yXWju83LC7H/jAM8CEAvWT8huE5OClmQiIfNYNxHQ9s7RRS0qKOV1Xow/Q40Lf5w81YutjQomHRTxd4K8zW3BSOYZ8e0bYAraAxZAgHwuZ91hiG9JV+kc69uqvK8lrXtjPXjReGpQcU4vIIEk5YP29+o9Dp8ae6O5BQgMGQOk9vAiTEiwaUUtAMIE8MlPuFNU4G6lmmB4AW3c9y7T00tPlqKCoHxSgxPNXANbq73NYIAhFwjlHlGxFsAvAhiiB9CXyE+ewhe4MRCOB6SIsLady/6auEUOxcbRx7dr9yoNSk/wCDSzkNElDU2R8ZIfP9wKIj3ijqKB9zhaoFrsGxct8mVjPb3iv6H8vIuqJBRllvXHODXc2Qvkrs4KUGt7YyKE9WP34VEBU1VoZyNYAVlG5k2KrE6UxwO8ASCLMlLhpjeM6iHGJD4drLlZmWfK2w7WnYNdPCsPBXEGbZ2TLFY/9HuIBbUqIHHosdcX6pM9sVy3LZRkrQOPK8yj/1S9ZKgGGwtYm0IHtFyJd4kZ3f+KO0XZ+uJMd6nAQqvkcOnLXbbgVz35qcH4tk/1XKkZg7WF3J0eSPQAkb36unCbtQDXu+Jl08T2859meXY0CUcaEa8jt987Bgy8EW7f5fPu2svW9qiKBF8ahlbejKSa1N2rpTRNzON4Y+KBzJttizw15lKuUlUyr04JqCs2h879D3cfuRpxdIqCQA09/U/9XM+q7Z3TrXfHKgQVQG0iygF/t/3GXNPsX98Pi1TdtJlDNbIavbczcUTlGhsAplNMPj1h8pb+XpqnwVNksSfXQ2lpYlmjpVyBMYVehc3s6CA/+fUyjK9B3X3Qp8H/lmAo8gP53mmyr1st8sMcB64nPs0kesufjW96bE9ITfs3m1ftS2ARUfBja2dJWl8/5czcsX1Z9ZJugfULb3kaPFxHrsGkhwCCcVytFFmRjuX9CNjcZHNxATc3q9RzTGWY9az3KqQ5BEFO7HH3ZA/Q0vk5Xs5KsstHiMZKJxiXvbDJ54gU8mzq4K3pKILQ/jKuLbd1cEIcsl0zBy676r6a+0kyf+6XTlZo6AK74JSnPr57c9yoCUqZMt7qaMLVGVsPRC3lNpBmJ5zzcglIWJ/9e9nMJrUNzW3ZHm27HMToZdiwwM4Q5LSd/pBCv2FsEXiXJ+fjROjWtgBrCjCB2qfmgpjOhyLrsQjkz2samOXNAH6ovtTBHJZ7iihkeIfZnp70p9S+qGrYKCz/CSonfeHzUHxAW9EkL0pq9ulfJluUnrfSXWZTvDB7sIcBa/Gma2XYGJ7yJFBHc7tbQ+h60/zkYNSG+9w0//R0pU2Nkxmtd3eflK3FeWZJeBzuAL4WqRk+xYk0AW9T3LxVV", "layer_level": 0}], "wiki_items": [{"catalog_id": "452c29a5-8639-4bf8-8b75-8cc0d4a9ffe5", "content": "", "title": "技术栈与依赖", "description": "technology-stack", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "d4c0137f-9d7f-4edc-9b6c-83a79eaa62da", "gmt_create": "2025-09-17T17:10:26.514138+08:00", "gmt_modified": "2025-09-17T17:10:26.516163+08:00"}, {"catalog_id": "c6ee37c4-9b2c-4cb9-ab24-5a4456d3894b", "content": "", "title": "项目概述", "description": "project-overview", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "68837d99-fde7-4a1c-879b-e0f399afafe9", "gmt_create": "2025-09-17T17:10:32.593498+08:00", "gmt_modified": "2025-09-17T17:10:32.59755+08:00"}, {"catalog_id": "84196b87-4eae-49d1-98d3-e8f06be3b022", "content": "", "title": "模块架构", "description": "module-architecture", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "06d759c0-fab3-40f1-b848-cff676f413fb", "gmt_create": "2025-09-17T17:12:02.402534+08:00", "gmt_modified": "2025-09-17T17:12:02.404959+08:00"}, {"catalog_id": "21f95d6f-85b1-496a-9f5c-1fbcb3015ee0", "content": "", "title": "API参考", "description": "api-reference", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "70c2a73f-5176-4fe7-b5cf-ba46ce23c4aa", "gmt_create": "2025-09-17T17:12:02.9991+08:00", "gmt_modified": "2025-09-17T17:12:03.003299+08:00"}, {"catalog_id": "103edcac-ed4f-4fff-b35f-73e2fafc3279", "content": "", "title": "数据模型与数据库设计", "description": "data-model", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "9a9810fa-53a4-4d83-b0bb-a247aee8c025", "gmt_create": "2025-09-17T17:13:08.282839+08:00", "gmt_modified": "2025-09-17T17:13:08.284902+08:00"}, {"catalog_id": "484d4aea-c0df-44d0-9b9e-c951ee96a3dd", "content": "", "title": "核心业务逻辑", "description": "business-logic", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "bcef76a3-bc7d-4611-ac7f-add7e716945f", "gmt_create": "2025-09-17T17:13:48.321385+08:00", "gmt_modified": "2025-09-17T17:13:48.324944+08:00"}, {"catalog_id": "5e961b9e-e3d5-495f-aaef-bf1b593cdda0", "content": "", "title": "配置管理", "description": "configuration-management", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "dd5ddac4-028a-4e4b-bd6a-e3fd821dc0d6", "gmt_create": "2025-09-17T17:14:19.854274+08:00", "gmt_modified": "2025-09-17T17:14:19.857179+08:00"}, {"catalog_id": "830291be-201b-4882-bf19-a089732d40e1", "content": "", "title": "缓存策略", "description": "cache-strategy", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "79fad8a4-326c-42ba-ab8c-32a294200362", "gmt_create": "2025-09-17T17:15:15.481461+08:00", "gmt_modified": "2025-09-17T17:15:15.484333+08:00"}, {"catalog_id": "0e94e284-f9d1-4416-aee3-a464ceb58a16", "content": "", "title": "消息队列", "description": "messaging-queue", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "29d3c200-eb17-4a81-bde2-8876527b0df5", "gmt_create": "2025-09-17T17:15:55.208694+08:00", "gmt_modified": "2025-09-17T17:15:55.212793+08:00"}, {"catalog_id": "11640edb-f5f8-4737-a6af-0ab226c39804", "content": "", "title": "定时任务", "description": "scheduling-tasks", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "fe26aef8-465a-4b42-8938-ad10d3676344", "gmt_create": "2025-09-17T17:16:43.494523+08:00", "gmt_modified": "2025-09-17T17:16:43.497426+08:00"}, {"catalog_id": "e9d672e9-3573-4c60-98f4-9d3dbe8c36d1", "content": "", "title": "快速开始", "description": "getting-started", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "43d7eb92-0beb-4d2a-81bc-3bc5679bfb02", "gmt_create": "2025-09-17T17:16:57.931186+08:00", "gmt_modified": "2025-09-17T17:16:57.934186+08:00"}, {"catalog_id": "cec3aeef-d7fa-4435-b007-54a9231d0f79", "content": "", "title": "用户管理API", "description": "user-management-api", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "0b03ac96-a1cd-4f6d-9530-03c09a3957f8", "gmt_create": "2025-09-17T17:17:57.664928+08:00", "gmt_modified": "2025-09-17T17:17:57.668343+08:00"}, {"catalog_id": "77188613-0ed6-4eaa-9f3f-d3be974a3939", "content": "", "title": "uac-api模块", "description": "uac-api-module", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "f312f940-527b-4bd8-a705-fe52b29f61bc", "gmt_create": "2025-09-17T17:18:34.369463+08:00", "gmt_modified": "2025-09-17T17:18:34.37311+08:00"}, {"catalog_id": "f48da353-a1b9-4f38-8e40-abc721e82e2c", "content": "", "title": "用户核心信息模型", "description": "user-core-info", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "fc80cc6d-be28-47f5-a9ce-6019f1687d68", "gmt_create": "2025-09-17T17:19:08.104256+08:00", "gmt_modified": "2025-09-17T17:19:08.108615+08:00"}, {"catalog_id": "082c46c4-157b-48e3-b828-14eaa03bd875", "content": "", "title": "Token刷新任务", "description": "token-refresh-task", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "7d1c9fc9-6f16-4121-88d8-169389ec2d82", "gmt_create": "2025-09-17T17:19:59.094367+08:00", "gmt_modified": "2025-09-17T17:19:59.096593+08:00"}, {"catalog_id": "6692730f-9a40-41a5-b8b3-3dd1a8e4e6e1", "content": "", "title": "用户认证与授权", "description": "user-authentication-authorization", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "d68ff3f0-fa40-43ee-9370-1561e6858574", "gmt_create": "2025-09-17T17:20:40.368383+08:00", "gmt_modified": "2025-09-17T17:20:40.370426+08:00"}, {"catalog_id": "5923fd82-9366-4eef-9b14-e914245f96d0", "content": "", "title": "新用户管理API", "description": "new-user-management-api", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "95c293e0-0fd1-41b3-b790-59f2769f6fe5", "gmt_create": "2025-09-17T17:21:10.143948+08:00", "gmt_modified": "2025-09-17T17:21:10.146093+08:00"}, {"catalog_id": "5dc01eb8-7e2d-47b2-96e2-f5337e92784e", "content": "", "title": "uac-common模块", "description": "uac-common-module", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "b5e3ed1e-e731-42c6-af40-ca0a39f962a0", "gmt_create": "2025-09-17T17:22:11.796676+08:00", "gmt_modified": "2025-09-17T17:22:11.798863+08:00"}, {"catalog_id": "0bea1de8-40fd-4f12-97b0-62216b323eab", "content": "", "title": "用户设置模型", "description": "user-settings", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "2eaf7d03-7395-4eeb-b0c4-c31f27b48e6a", "gmt_create": "2025-09-17T17:22:16.26413+08:00", "gmt_modified": "2025-09-17T17:22:16.266929+08:00"}, {"catalog_id": "5e77a8d5-d8b1-44ac-85da-7479b9b4a49f", "content": "", "title": "新用户开通调度任务", "description": "new-user-scheduling-tasks", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "75e8cdab-8d3e-40d1-8eee-afb52d94e098", "gmt_create": "2025-09-17T17:23:30.047317+08:00", "gmt_modified": "2025-09-17T17:23:30.051513+08:00"}, {"catalog_id": "5193f333-bf85-4c97-8cc5-71fec6f2785f", "content": "", "title": "新用户开通流程", "description": "new-user-onboarding", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "9fa0b0b0-68de-4a92-a069-c0a95a17a508", "gmt_create": "2025-09-17T17:24:02.593978+08:00", "gmt_modified": "2025-09-17T17:24:02.596679+08:00"}, {"catalog_id": "8ad78008-869e-48a0-ae54-58673875c3bc", "content": "", "title": "认证与回调API", "description": "authentication-callback-api", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "93d9e201-8453-46c5-86bf-73d3bd849f75", "gmt_create": "2025-09-17T17:24:51.690841+08:00", "gmt_modified": "2025-09-17T17:24:51.694453+08:00"}, {"catalog_id": "89fda65d-105d-42d5-a727-d1e38d49ba61", "content": "", "title": "uac-db-common模块", "description": "uac-db-common-module", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "ed49d354-ccf6-4fa5-a92d-4bc87c19df96", "gmt_create": "2025-09-17T17:25:20.700912+08:00", "gmt_modified": "2025-09-17T17:25:20.703459+08:00"}, {"catalog_id": "f138caf7-70d4-4a22-96c7-6c32e8165d57", "content": "", "title": "用户信息变更事件处理", "description": "user-info-change-event", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "5da84cfc-95f9-4e70-9ffb-6f4f2a15916f", "gmt_create": "2025-09-17T17:26:29.526462+08:00", "gmt_modified": "2025-09-17T17:26:29.529061+08:00"}, {"catalog_id": "3ab1acc2-e81a-457f-ac28-1d6f4e6cf248", "content": "", "title": "授权信息模型", "description": "auth-info", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "d6f1dd95-54d9-47f8-8f5b-e4d0d30ea54a", "gmt_create": "2025-09-17T17:26:36.424684+08:00", "gmt_modified": "2025-09-17T17:26:36.426593+08:00"}, {"catalog_id": "de8c6980-4198-46c9-9021-65354ec8ffcf", "content": "", "title": "uac-service模块", "description": "uac-service-module", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "e215b524-7be0-4940-9086-dfe2b9084dd8", "gmt_create": "2025-09-17T17:27:46.381237+08:00", "gmt_modified": "2025-09-17T17:27:46.383401+08:00"}, {"catalog_id": "176bfe95-5219-4c07-84da-a5e320f1ddfd", "content": "", "title": "订单与交易模型", "description": "order-transaction", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "3a7a5ca3-06b0-4b57-ace1-633ac40c9c9c", "gmt_create": "2025-09-17T17:27:49.01384+08:00", "gmt_modified": "2025-09-17T17:27:49.017011+08:00"}, {"catalog_id": "73c7f169-179d-4efc-a8d8-c9a87f47449c", "content": "", "title": "多平台集成逻辑", "description": "multi-platform-integration", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "a4ac4b17-76c1-4a78-8c74-7f84a77ea9ae", "gmt_create": "2025-09-17T17:29:21.623277+08:00", "gmt_modified": "2025-09-17T17:29:21.625122+08:00"}, {"catalog_id": "f4d5c9dc-098e-4306-bbcf-0fb86498f79e", "content": "", "title": "uac-newusers模块", "description": "uac-newusers-module", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "21105de4-86fd-4545-82bb-e94c5d4152fe", "gmt_create": "2025-09-17T17:29:25.046106+08:00", "gmt_modified": "2025-09-17T17:29:25.050635+08:00"}, {"catalog_id": "0d3c57ee-26f0-46be-89f4-823d083e6e77", "content": "", "title": "uac-job模块", "description": "uac-job-module", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "aa13f8b4-5727-4769-8537-9ea1b7956b93", "gmt_create": "2025-09-17T17:30:37.075356+08:00", "gmt_modified": "2025-09-17T17:30:37.077361+08:00"}, {"catalog_id": "c6dbc118-ee10-43df-b817-42927bb6fc29", "content": "", "title": "平台扩展信息模型", "description": "platform-extension", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "ddbd353e-7c3d-4a90-8262-9307931d29b0", "gmt_create": "2025-09-17T17:30:54.79612+08:00", "gmt_modified": "2025-09-17T17:30:54.800217+08:00"}, {"catalog_id": "61b178e3-66ba-49d0-8907-1d2633827fd2", "content": "", "title": "用户信息查询API", "description": "user-info-query-api", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "ab93395f-84bc-46a5-9d79-64c69f4c30b4", "gmt_create": "2025-09-17T17:31:58.632634+08:00", "gmt_modified": "2025-09-17T17:31:58.636288+08:00"}, {"catalog_id": "360d690b-4d6e-4228-a247-41edf79b71f5", "content": "", "title": "回调接口规范", "description": "callback-api-specification", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "f695c05e-9f3e-4dd4-8c8d-1040ab861344", "gmt_create": "2025-09-17T17:32:03.478798+08:00", "gmt_modified": "2025-09-17T17:32:03.48257+08:00"}, {"catalog_id": "0a61f2b9-4e69-4e0f-b457-f68f18ecb285", "content": "", "title": "数据访问对象（DAO）", "description": "uac-db-common-dao", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "ccd6398f-c947-4b00-9ff8-3725c1ae5da8", "gmt_create": "2025-09-17T17:33:22.614009+08:00", "gmt_modified": "2025-09-17T17:33:22.618006+08:00"}, {"catalog_id": "7d20823d-8c4b-4dbb-8c6e-4b27e04e1b79", "content": "", "title": "配置管理", "description": "uac-common-config", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "7000795d-9985-4e8b-afee-2ae2babba0d7", "gmt_create": "2025-09-17T17:33:24.753636+08:00", "gmt_modified": "2025-09-17T17:33:24.756342+08:00"}, {"catalog_id": "745fc134-e9f2-4109-9501-785b2a9f7f0c", "content": "", "title": "新用户调度器", "description": "newuser-scheduler", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "ac387d32-2baf-4c44-801c-9787d0b549fd", "gmt_create": "2025-09-17T17:34:58.393738+08:00", "gmt_modified": "2025-09-17T17:34:58.395828+08:00"}, {"catalog_id": "f4ec820f-4858-4667-8931-8e350a37c018", "content": "", "title": "控制器层", "description": "uac-service-controller", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "da193803-a81a-42a9-9dc7-3376df9c0db5", "gmt_create": "2025-09-17T17:35:01.592106+08:00", "gmt_modified": "2025-09-17T17:35:01.596372+08:00"}, {"catalog_id": "0004d29d-a159-443a-9afb-5b629204114e", "content": "", "title": "用户设置管理API", "description": "user-setting-management-api", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "65dd2915-10b7-4fd9-b132-7c486d1de4ac", "gmt_create": "2025-09-17T17:36:24.684744+08:00", "gmt_modified": "2025-09-17T17:36:24.686647+08:00"}, {"catalog_id": "cc6a6177-7fe2-4f3d-8c9f-a0f9494635ec", "content": "", "title": "待开通用户扫描", "description": "scan-waiting-users", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "4ab5c311-e98f-416e-b1c8-6d4735c6c996", "gmt_create": "2025-09-17T17:36:27.924762+08:00", "gmt_modified": "2025-09-17T17:36:27.927914+08:00"}, {"catalog_id": "154f2366-1b1e-4afc-9c66-95e40f4a353a", "content": "", "title": "实体模型", "description": "uac-db-common-entity", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "2ad98ad7-95ab-483b-baa5-523e6217f099", "gmt_create": "2025-09-17T17:37:47.098847+08:00", "gmt_modified": "2025-09-17T17:37:47.103339+08:00"}, {"catalog_id": "10e48e47-ef03-4f56-9d9f-4504a0820575", "content": "", "title": "多平台认证实现", "description": "multi-platform-auth-implementation", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "948857b0-d5f3-4098-91a2-9f99f950ebcb", "gmt_create": "2025-09-17T17:37:54.075268+08:00", "gmt_modified": "2025-09-17T17:37:54.079141+08:00"}, {"catalog_id": "412f6f46-b685-4245-b70c-153986faedb4", "content": "", "title": "工具类", "description": "uac-common-utils", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "1f6a7944-333a-4feb-ac62-70657eb06a51", "gmt_create": "2025-09-17T17:39:02.803375+08:00", "gmt_modified": "2025-09-17T17:39:02.807154+08:00"}, {"catalog_id": "fe90e6f6-d714-41eb-993e-136cf1422460", "content": "", "title": "服务实现层", "description": "uac-service-export", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "d5cd5afe-72c1-4dc6-8e64-e9709c061d6b", "gmt_create": "2025-09-17T17:39:24.950662+08:00", "gmt_modified": "2025-09-17T17:39:24.95367+08:00"}, {"catalog_id": "52680662-3e75-42a3-bc4c-85865400c3e8", "content": "", "title": "新用户服务接口", "description": "newuser-service-api", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "486bfbc8-d4c9-40b5-b9be-3773fac3a342", "gmt_create": "2025-09-17T17:40:14.102472+08:00", "gmt_modified": "2025-09-17T17:40:14.105016+08:00"}, {"catalog_id": "419e8996-5ae1-4523-a06f-daab4846c399", "content": "", "title": "重试用户处理", "description": "retry-user-processing", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "5d54ebaf-e073-4b6f-a5fa-654c15ea6437", "gmt_create": "2025-09-17T17:40:49.944367+08:00", "gmt_modified": "2025-09-17T17:40:49.946102+08:00"}, {"catalog_id": "5a9d6d59-14ab-4e62-a64d-72d415e04c6e", "content": "", "title": "异常处理", "description": "uac-common-exception", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "99e2f7f6-c3f7-452e-901a-dea94bbeaa5c", "gmt_create": "2025-09-17T17:41:58.650931+08:00", "gmt_modified": "2025-09-17T17:41:58.655949+08:00"}, {"catalog_id": "71443bf4-7dc3-4627-be01-75ff8e1327fe", "content": "", "title": "仓储层（Repository）", "description": "uac-db-common-repository", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "5b58902c-93b9-4ff2-bd92-bfa869e7b0ec", "gmt_create": "2025-09-17T17:42:10.480289+08:00", "gmt_modified": "2025-09-17T17:42:10.484711+08:00"}, {"catalog_id": "79c37b58-5b2d-4a6b-a941-c2079e7d6f92", "content": "", "title": "拦截器与过滤器", "description": "uac-service-interceptor", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "9148d765-a91a-4adc-a27a-d5e83fccc7fb", "gmt_create": "2025-09-17T17:43:07.365598+08:00", "gmt_modified": "2025-09-17T17:43:07.369335+08:00"}, {"catalog_id": "5a85234d-4ba7-404b-8cb2-dfc9651f6e3e", "content": "", "title": "新用户平台适配逻辑", "description": "newuser-platform-adaptation", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "8158c7a9-bd07-4240-9fc7-1ca1cb23e3fd", "gmt_create": "2025-09-17T17:43:36.535279+08:00", "gmt_modified": "2025-09-17T17:43:36.539815+08:00"}, {"catalog_id": "e2d66b84-37c4-45e0-a6bc-01d619147bbc", "content": "", "title": "用户状态清理", "description": "user-status-cleanup", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "137d498b-7e77-4492-b642-8b30ba0173d3", "gmt_create": "2025-09-17T17:44:29.078413+08:00", "gmt_modified": "2025-09-17T17:44:29.082074+08:00"}, {"catalog_id": "dd2f2057-ef1c-4a70-a759-714ea943efd4", "content": "", "title": "平台服务实现", "description": "uac-db-common-platform-service", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "f1ddc595-0bcd-408b-8948-a5357d2a361e", "gmt_create": "2025-09-17T17:44:56.357944+08:00", "gmt_modified": "2025-09-17T17:44:56.360248+08:00"}, {"catalog_id": "70d95884-8792-4d05-952e-d4a89215b20a", "content": "", "title": "平台适配服务", "description": "uac-common-platform-service", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "a3bb7e92-3bb3-44af-a8de-4746ab7b87a4", "gmt_create": "2025-09-17T17:45:54.662536+08:00", "gmt_modified": "2025-09-17T17:45:54.666823+08:00"}, {"catalog_id": "d2da99e8-0dca-4ccc-aaf5-f96abeef6c63", "content": "", "title": "Web配置与灰度发布", "description": "uac-service-web-config", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "9ed314ab-9722-4389-9f9e-60dd0ec874ef", "gmt_create": "2025-09-17T17:46:18.20192+08:00", "gmt_modified": "2025-09-17T17:46:18.205412+08:00"}, {"catalog_id": "61264e82-7f3a-428f-b1c8-9230d8fcc015", "content": "", "title": "新用户业务处理流程", "description": "newuser-business-process", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "0f9637e6-7361-4a60-b217-92d20890abf9", "gmt_create": "2025-09-17T17:47:09.000622+08:00", "gmt_modified": "2025-09-17T17:47:09.005461+08:00"}, {"catalog_id": "ff08e9d4-86a8-487d-9f54-b48df2171714", "content": "", "title": "Redis维护", "description": "redis-maintenance", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "af738f88-c6ae-4bd3-ba1e-57cb09d8b1c0", "gmt_create": "2025-09-17T17:47:19.279679+08:00", "gmt_modified": "2025-09-17T17:47:19.281997+08:00"}, {"catalog_id": "1a36ea21-8a00-459b-8805-d0c53e572fc8", "content": "", "title": "Redis数据访问", "description": "uac-common-redis-access", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "2ad69cac-8d40-45eb-8e4c-beb5645e7b9b", "gmt_create": "2025-09-17T17:48:39.01823+08:00", "gmt_modified": "2025-09-17T17:48:39.022876+08:00"}, {"catalog_id": "8dd29984-02fc-4f77-97d5-25106f3f8d63", "content": "", "title": "用户中心服务实现", "description": "user-center-service-impl", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "cc299b89-1600-420e-84f5-114ce4c47a50", "gmt_create": "2025-09-17T17:49:51.493444+08:00", "gmt_modified": "2025-09-17T17:49:51.498187+08:00"}, {"catalog_id": "ed0be98c-9e64-41b3-b287-626ccc09cd14", "content": "", "title": "拼多多认证实现", "description": "pdd-auth-implementation", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "3bb22d81-0a32-4ad4-87f3-87b7af4047ac", "gmt_create": "2025-09-17T17:50:01.189824+08:00", "gmt_modified": "2025-09-17T17:50:01.192793+08:00"}, {"catalog_id": "085433df-07aa-43bd-80bc-2d5e9c5d6ad5", "content": "", "title": "订单搜索服务实现", "description": "order-search-service-impl", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "079c59f7-f623-4f18-a490-5eeaf6777e5b", "gmt_create": "2025-09-17T17:51:24.597702+08:00", "gmt_modified": "2025-09-17T17:51:24.602683+08:00"}, {"catalog_id": "69c43034-95a7-4965-9ceb-e8d76a867654", "content": "", "title": "淘宝认证实现", "description": "taobao-auth-implementation", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "646ad3f7-0c4c-4e95-a67c-d65e2c3a0515", "gmt_create": "2025-09-17T17:51:29.539844+08:00", "gmt_modified": "2025-09-17T17:51:29.543729+08:00"}, {"catalog_id": "be4f2411-f8de-413b-8e4f-280f3568e62d", "content": "", "title": "抖店认证实现", "description": "doudian-auth-implementation", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "d49706cc-94fc-41e2-8484-39e2702268c4", "gmt_create": "2025-09-17T17:52:32.355234+08:00", "gmt_modified": "2025-09-17T17:52:32.356668+08:00"}, {"catalog_id": "8cf50346-63a5-4e23-aa18-311edf07400d", "content": "", "title": "用户产品信息扩展服务实现", "description": "user-product-info-ext-service-impl", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "56e8e822-4c90-4f12-bd1b-f19b908725c1", "gmt_create": "2025-09-17T17:52:48.179416+08:00", "gmt_modified": "2025-09-17T17:52:48.182198+08:00"}, {"catalog_id": "35a0355c-d49b-4b9a-943c-e53329999363", "content": "", "title": "快手小店认证实现", "description": "kwaishop-auth-implementation", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "d9100272-9dbe-4ebc-99d3-c967dbdb9740", "gmt_create": "2025-09-17T17:53:53.263917+08:00", "gmt_modified": "2025-09-17T17:53:53.267178+08:00"}, {"catalog_id": "a03dbef6-5a8e-477a-b7cf-24dafd96615a", "content": "", "title": "京东认证实现", "description": "jd-auth-implementation", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "c0e285be-5113-4647-be96-a1d2042a8229", "gmt_create": "2025-09-17T17:54:07.547261+08:00", "gmt_modified": "2025-09-17T17:54:07.551186+08:00"}, {"catalog_id": "df434262-7398-402d-a4d2-cd897b1ce94d", "content": "", "title": "微信小店认证实现", "description": "wxshop-auth-implementation", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "288ba19a-eb52-4d89-a7b1-201770fcc0ac", "gmt_create": "2025-09-17T17:54:54.892107+08:00", "gmt_modified": "2025-09-17T17:54:54.893681+08:00"}, {"catalog_id": "8d6f023f-2a96-4cb5-a609-9829002dad5e", "content": "", "title": "抖音电商认证实现", "description": "tiktok-auth-implementation", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "075d6f05-9008-477d-abc5-2e18a0ccd5bb", "gmt_create": "2025-09-17T17:55:24.637659+08:00", "gmt_modified": "2025-09-17T17:55:24.641344+08:00"}, {"catalog_id": "8de7ee1c-dbcb-4dfa-962e-dd2bd4914cab", "content": "", "title": "阿里1688认证实现", "description": "ali1688-auth-implementation", "extend": "{}", "progress_status": "completed", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "id": "5da24188-e100-4a57-948d-1b508c336931", "gmt_create": "2025-09-17T17:56:03.774738+08:00", "gmt_modified": "2025-09-17T17:56:03.776944+08:00"}], "wiki_overview": {"content": "<blog>\n# 爱用UAC用户中心服务组项目分析\n\n## 1. 项目介绍\n\n### 项目概述\n爱用UAC（User Access Control）用户中心服务组是爱用科技的核心用户管理系统，提供用户认证、授权、账户管理等功能。该项目采用微服务架构，基于Spring Boot框架构建，支持多平台用户统一管理。\n\n### 核心目标\n- 实现多平台用户统一认证和授权管理\n- 提供高性能的用户信息查询和更新服务\n- 支持新用户注册、开通和维护的完整生命周期管理\n- 保障用户数据安全和系统稳定性\n\n### 目标用户\n- 内部系统开发者和维护人员\n- 需要集成用户认证功能的业务系统\n- 平台运营和管理人员\n\n## 2. 技术架构\n\n### 组件分解\n项目采用Maven多模块架构，包含以下核心模块：\n\n```mermaid\ngraph TD\n    A[uac-service-group] --> B[uac-api]\n    A --> C[uac-newuser-api]\n    A --> D[uac-common]\n    A --> E[uac-service]\n    A --> F[uac-job]\n    A --> G[uac-db-common]\n    A --> H[uac-service-common]\n    A --> I[uac-newusers]\n    A --> J[uac-domain]\n    \n    B --> K[二方接口定义]\n    E --> L[主服务模块]\n    F --> M[uac-authorization-job]\n    I --> N[uac-newuser-common]\n    I --> O[uac-newuser-scheduler]\n    I --> P[uac-newuser-service]\n```\n\n### 设计模式\n项目采用了典型的分层架构设计模式：\n- **接口层**：`uac-api`模块定义了服务接口\n- **服务层**：`uac-service`和`uac-newuser-service`提供核心业务逻辑\n- **数据访问层**：`uac-db-common`模块处理数据库操作\n- **公共组件层**：`uac-common`和`uac-service-common`提供共享功能\n\n### 系统关系\n```mermaid\nflowchart TD\n    Client[客户端] --> API[uac-api]\n    API --> Service[uac-service]\n    Service --> DB[uac-db-common]\n    Service --> Cache[Redis]\n    Service --> MQ[RocketMQ]\n    NewUserService[uac-newuser-service] --> Scheduler[uac-newuser-scheduler]\n    AuthorizationJob[uac-authorization-job] --> Token[Token刷新任务]\n    Common[uac-common] --> AllServices\n    DBCommon[uac-db-common] --> AllServices\n```\n\n### 数据流\n```mermaid\nflowchart TD\n    A[客户端请求] --> B[Controller]\n    B --> C[Service]\n    C --> D[DAO/Repository]\n    D --> E[(数据库)]\n    D --> F[Redis缓存]\n    C --> G[外部服务]\n    G --> H[RocketMQ消息队列]\n    H --> I[定时任务]\n    I --> J[数据同步]\n```\n\n## 3. 关键实现\n\n### 主要入口点\n- `uac-service`: `cn.loveapp.uac.service.UacServiceApplication.java`\n- `uac-newuser-service`: `cn.loveapp.uac.newuser.service.UacNewUserServiceApplication.java`\n- `uac-newuser-scheduler`: `cn.loveapp.uac.newuser.scheduler.UacNewUserSchedulerApplication.java`\n- `uac-authorization-job`: `cn.loveapp.uac.authorization.UacAuthorizationJobApplication.java`\n\n### 核心模块\n- **uac-api**: 定义服务接口和数据传输对象\n- **uac-service**: 实现用户认证、授权等核心功能\n- **uac-newusers**: 处理新用户相关业务逻辑\n- **uac-db-common**: 提供数据库访问通用组件\n- **uac-common**: 共享工具类和配置\n\n### 配置方法\n项目使用Spring Boot的标准配置方式，通过`application.properties`文件进行环境配置，并支持Apollo配置中心。\n\n### 外部依赖\n- **数据库**: MySQL\n- **缓存**: Redis\n- **消息队列**: RocketMQ\n- **框架**: Spring Boot 3.x\n- **JDK**: JDK 24+\n\n### 集成点\n- 多平台用户认证集成（淘宝、京东、拼多多等）\n- 消息队列事件处理\n- 定时任务调度\n- 分布式缓存管理\n\n### 组件关系\n```mermaid\ngraph LR\n    uac-common[公共组件] --> uac-service\n    uac-common --> uac-newuser-service\n    uac-common --> uac-newuser-scheduler\n    uac-common --> uac-authorization-job\n    uac-db-common[数据库组件] --> uac-service\n    uac-db-common --> uac-newuser-service\n    uac-db-common --> uac-newuser-scheduler\n    uac-api[API定义] --> uac-service\n    uac-api --> uac-newuser-service\n    uac-service-common[服务通用] --> uac-service\n    uac-newuser-common[新用户通用] --> uac-newuser-service\n    uac-newuser-common --> uac-newuser-scheduler\n```\n\n## 4. 关键特性\n\n### 功能概览\n- 用户认证和授权管理\n- 多平台用户信息同步\n- 新用户注册和开通流程\n- 定时任务处理（Token刷新、用户状态维护）\n- 分布式缓存管理\n- 消息队列事件处理\n\n### 实现亮点\n- **多平台支持**: 通过`uac-common`中的`platform/api`包实现多平台认证服务\n- **定时任务**: `uac-job`和`uac-newuser-scheduler`模块提供定时任务支持\n- **缓存策略**: 使用Redis进行用户信息缓存，提高访问性能\n- **异常处理**: 统一的异常处理机制，通过`exception`包定义各类异常\n\n### 特性架构\n```mermaid\nstateDiagram-v2\n    [*] --> 启动\n    启动 --> 初始化: 加载配置\n    初始化 --> 等待请求: 服务就绪\n    等待请求 --> 处理请求: 接收API调用\n    处理请求 --> 验证: 身份认证\n    验证 --> 失败: 返回错误\n    验证 --> 成功: 执行业务逻辑\n    成功 --> 数据访问: 查询/更新\n    数据访问 --> 响应: 返回结果\n    响应 --> 等待请求: 循环处理\n    失败 --> 等待请求: 错误处理\n    等待请求 --> 定时任务: 触发定时操作\n    定时任务 --> 数据同步: 更新用户状态\n    数据同步 --> 等待请求: 任务完成\n```\n\nSources:\n- [UacServiceApplication.java](uac-service/src/main/java/cn/loveapp/uac/service/UacServiceApplication.java)\n- [UacNewUserServiceApplication.java](uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/UacNewUserServiceApplication.java)\n- [UacNewUserSchedulerApplication.java](uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/UacNewUserSchedulerApplication.java)\n- [UacAuthorizationJobApplication.java](uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/UacAuthorizationJobApplication.java)\n- [README.md](README.md)\n</blog>", "gmt_create": "2025-09-17T17:03:53.493523+08:00", "gmt_modified": "2025-09-17T17:03:53.493523+08:00", "id": "02a43e51-1e19-4867-9d79-9199b8ab3f13", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": ""}, "wiki_readme": {"content": "No readme file", "gmt_create": "2025-09-17T17:03:09.732652+08:00", "gmt_modified": "2025-09-17T17:03:09.732652+08:00", "id": "af380e45-ac46-442e-9254-1f7db23c9f63", "repo_id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": ""}, "wiki_repo": {"id": "92d6563b-7f7a-4100-9e69-0cb814c5073b", "workspace_path": "", "name": "usercenter-service-group", "progress_status": "completed", "wiki_present_status": "COMPLETED", "optimized_catalog": "\".\\n├── uac-api\\n│   ├── src/main/java/cn/loveapp/uac\\n│   │   ├── annotation\\n│   │   │   ├── processor\\n│   │   │   │   ├── AppValidator.java\\n│   │   │   │   ├── DateTimeValidator.java\\n│   │   │   │   ├── IdOrNickValidator.java\\n│   │   │   │   └── PlatformIdValidator.java\\n│   │   │   ├── CheckAppHasExist.java\\n│   │   │   ├── CheckDateTime.java\\n│   │   │   ├── CheckIdOrNick.java\\n│   │   │   └── CheckPlatformHasExist.java\\n│   │   ├── code\\n│   │   │   └── ApiCode.java\\n│   │   ├── contant\\n│   │   │   └── AyMultiTagType.java\\n│   │   ├── domain\\n│   │   │   ├── UserExtInfoDTO.java\\n│   │   │   ├── UserOrderSearchDTO.java\\n│   │   │   └── UserSettingDTO.java\\n│   │   ├── entity\\n│   │   │   ├── LevelCycleEndTime.java\\n│   │   │   └── UserProductInfoBusinessExt.java\\n│   │   ├── exception\\n│   │   │   ├── BaseException.java\\n│   │   │   └── UserException.java\\n│   │   ├── proto\\n│   │   │   ├── event\\n│   │   │   │   ├── EventTypeEnum.java\\n│   │   │   │   └── UserChangedEvent.java\\n│   │   │   ├── BaseRequestProto.java\\n│   │   │   ├── SubscribeUserMessageRequestProto.java\\n│   │   │   └── UserChangedRequestProto.java\\n│   │   ├── request\\n│   │   │   ├── BaseHttpRequest.java\\n│   │   │   ├── BatchGetUserCacheInfoRequest.java\\n│   │   │   ├── BatchGetUserFullInfoRequest.java\\n│   │   │   ├── BatchMultiUserTagUpdateRequest.java\\n│   │   │   ├── BatchRequest.java\\n│   │   │   ├── BatchSettingGetRequest.java\\n│   │   │   ├── BatchSettingUpdateRequest.java\\n│   │   │   ├── BatchUpdateUserCacheInfoRequest.java\\n│   │   │   ├── BatchUsersSettingGetRequest.java\\n│   │   │   ├── CallbackRequest.java\\n│   │   │   ├── GetUserInfoExtRequest.java\\n│   │   │   ├── ListUserByVipInfoRequest.java\\n│   │   │   ├── LoginInfoRequest.java\\n│   │   │   ├── MethodInterface.java\\n│   │   │   ├── RefreshUserInfoRequest.java\\n│   │   │   ├── UpdateUserInfoExtRequest.java\\n│   │   │   ├── UserFullInfoRequest.java\\n│   │   │   ├── UserInfoMemberRequest.java\\n│   │   │   ├── UserInfoRequest.java\\n│   │   │   ├── UserOrderSearchRequest.java\\n│   │   │   ├── UserSettingCopyRequest.java\\n│   │   │   ├── UserShopInfoGetRequest.java\\n│   │   │   ├── UserShopInfoGetResponse.java\\n│   │   │   └── UserinfoTopSessionMemberRequest.java\\n│   │   ├── response\\n│   │   │   ├── BatchUpdateUserCacheInfoResponse.java\\n│   │   │   ├── BatchUsersSettingGetResponse.java\\n│   │   │   ├── CallbackResponse.java\\n│   │   │   ├── GetUserInfoExtResponse.java\\n│   │   │   ├── ListUserByVipInfoResponse.java\\n│   │   │   ├── UpdateUserInfoExtResponse.java\\n│   │   │   ├── UserCacheInfoResponse.java\\n│   │   │   ├── UserFullInfoResponse.java\\n│   │   │   ├── UserInfoMemberResponse.java\\n│   │   │   ├── UserInfoResponse.java\\n│   │   │   ├── UserInfoTopSessionMemberResponse.java\\n│   │   │   └── UserOrderSearchResponse.java\\n│   │   ├── service\\n│   │   │   ├── UserCenterInnerApiService.java\\n│   │   │   ├── UserOrderSearchApiService.java\\n│   │   │   └── UserProductInfoExtApiService.java\\n│   │   └── utils\\n│   │       ├── UacRpcUtils.java\\n│   │       ├── UserCacheUtils.java\\n│   │       └── ValidatorUtils.java\\n│   └── pom.xml\\n├── uac-common\\n│   ├── src/main/java/cn/loveapp/uac/common\\n│   │   ├── api\\n│   │   │   ├── domain\\n│   │   │   │   ├── SellerArticleBizOrder.java\\n│   │   │   │   ├── SellerArticleSub.java\\n│   │   │   │   └── SellerArticleUserSubscribe.java\\n│   │   │   ├── request\\n│   │   │   │   ├── SellerVasOrderSearchRequest.java\\n│   │   │   │   ├── SellerVasSubscSearchRequest.java\\n│   │   │   │   └── SellerVasSubscribeGetRequest.java\\n│   │   │   └── response\\n│   │   │       ├── BaseResponse.java\\n│   │   │       ├── SellerVasOrderSearchResponse.java\\n│   │   │       ├── SellerVasSubscSearchResponse.java\\n│   │   │       └── SellerVasSubscribeGetResponse.java\\n│   │   ├── bo\\n│   │   │   ├── AuthBo.java\\n│   │   │   ├── CalculateBo.java\\n│   │   │   ├── LoginUserInfoBo.java\\n│   │   │   ├── UserAutoRenewBo.java\\n│   │   │   ├── UserBo.java\\n│   │   │   └── UserInfoBo.java\\n│   │   ├── code\\n│   │   │   ├── taobao\\n│   │   │   │   └── ApiCodeConstant.java\\n│   │   │   ├── ApiCode.java\\n│   │   │   ├── ErrorCode.java\\n│   │   │   ├── ErrorResponse.java\\n│   │   │   └── PDDApiCode.java\\n│   │   ├── config\\n│   │   │   ├── aiyong\\n│   │   │   │   └── AiyongAppConfig.java\\n│   │   │   ├── ali1688\\n│   │   │   │   ├── Ali1688AppConfig.java\\n│   │   │   │   └── Ali1688DistributeAppConfig.java\\n│   │   │   ├── app\\n│   │   │   │   └── ArticleCodeConfig.java\\n│   │   │   ├── biyao\\n│   │   │   │   └── BiyaoAppConfig.java\\n│   │   │   ├── cache\\n│   │   │   │   └── VasGetResult.java\\n│   │   │   ├── doudian\\n│   │   │   │   └── DoudianAppConfig.java\\n│   │   │   ├── jd\\n│   │   │   │   └── JdTradeERPAppConfig.java\\n│   │   │   ├── kwaishop\\n│   │   │   │   ├── KwaishopDistributeAppConfig.java\\n│   │   │   │   ├── KwaishopTradeAppConfig.java\\n│   │   │   │   └── KwaishopTradeERPAppConfig.java\\n│   │   │   ├── pdd\\n│   │   │   │   ├── PDDDistributeAppConfig.java\\n│   │   │   │   ├── PDDGuanDianAppConfig.java\\n│   │   │   │   ├── PDDItemAppConfig.java\\n│   │   │   │   ├── PDDTradeAppConfig.java\\n│   │   │   │   ├── PDDTradeERPAppConfig.java\\n│   │   │   │   └── PDDWaybillAppConfig.java\\n│   │   │   ├── redis\\n│   │   │   │   ├── CacheTimeoutConfig.java\\n│   │   │   │   └── RedisConfiguration.java\\n│   │   │   ├── rocketmq\\n│   │   │   │   ├── product\\n│   │   │   │   │   └── DefaultProducerConfig.java\\n│   │   │   │   ├── BaseRocketMQDefaultConfig.java\\n│   │   │   │   ├── RocketMQAppConfig.java\\n│   │   │   │   └── RocketMQDefaultProducerConfig.java\\n│   │   │   ├── taobao\\n│   │   │   │   ├── TaoBaoItemAppConfig.java\\n│   │   │   │   ├── TaoBaoTradeAppConfig.java\\n│   │   │   │   ├── TaoBaoTradeERPAppConfig.java\\n│   │   │   │   ├── TaoBaoTradeSupplierAppConfig.java\\n│   │   │   │   └── TaoBaoWaybillAppConfig.java\\n│   │   │   ├── tgc\\n│   │   │   │   └── TgcTradeERPAppConfig.java\\n│   │   │   ├── tiktok\\n│   │   │   │   └── TikTokAppConfig.java\\n│   │   │   ├── web\\n│   │   │   │   ├── WarmUpConfiguration.java\\n│   │   │   │   └── WarnUpRequestData.java\\n│   │   │   ├── wxshop\\n│   │   │   │   ├── WxshopTradeAppConfig.java\\n│   │   │   │   └── WxvideoshopTradeAppConfig.java\\n│   │   │   ├── xhs\\n│   │   │   │   └── XhsAppConfig.java\\n│   │   │   ├── youzan\\n│   │   │   │   └── YouzanAppConfig.java\\n│   │   │   ├── AppConfig.java\\n│   │   │   ├── DistributeConfig.java\\n│   │   │   ├── SpringAsyncConfig.java\\n│   │   │   ├── TaskConfiguration.java\\n│   │   │   └── TradeConfig.java\\n│   │   ├── constant\\n│   │   │   ├── AuthInfoParamConstant.java\\n│   │   │   ├── CommonConstant.java\\n│   │   │   ├── MqUserPropertyConstant.java\\n│   │   │   ├── OnsRateLimitConstant.java\\n│   │   │   ├── PretestTopicConstant.java\\n│   │   │   ├── PromotionActFlag.java\\n│   │   │   └── SwitchCacheKeyConstant.java\\n│   │   ├── consumer\\n│   │   │   ├── AiyongMessageExt.java\\n│   │   │   └── BaseOnsConsumer.java\\n│   │   ├── controller\\n│   │   │   └── BaseCloudNativeController.java\\n│   │   ├── dao/redis\\n│   │   │   ├── base\\n│   │   │   │   ├── BaseHashRedisRepository.java\\n│   │   │   │   ├── BaseValueRedisRepository.java\\n│   │   │   │   └── RedisRepositoryBase.java\\n│   │   │   ├── repository\\n│   │   │   │   ├── OpenUserRedisRepository.java\\n│   │   │   │   ├── OperationManageRedisRepository.java\\n│   │   │   │   ├── SearchActiveRedisRepository.java\\n│   │   │   │   └── UserManageRedisRepositoryHashRedisRepository.java\\n│   │   │   ├── HashCacheRepository.java\\n│   │   │   └── ValueRedisRepository.java\\n│   │   ├── dto\\n│   │   │   ├── OrderSearchQueryDTO.java\\n│   │   │   └── QueryUserSettingsParam.java\\n│   │   ├── entity\\n│   │   │   ├── redis\\n│   │   │   │   └── UserRedisEntity.java\\n│   │   │   ├── taobao\\n│   │   │   │   └── UserAuthTokenEntity.java\\n│   │   │   ├── PromotionActivity.java\\n│   │   │   └── UserProductInfo.java\\n│   │   ├── exception\\n│   │   │   ├── CacheWriteException.java\\n│   │   │   ├── DbWriteException.java\\n│   │   │   ├── NetworkException.java\\n│   │   │   ├── ResendMessageException.java\\n│   │   │   ├── StorageException.java\\n│   │   │   ├── TaobaoException.java\\n│   │   │   └── UserNeedAuthException.java\\n│   │   ├── platform/api\\n│   │   │   ├── domain\\n│   │   │   │   ├── KwaishopRefreshTokenCallbackResult.java\\n│   │   │   │   ├── PddRefreshTokenCallbackResult.java\\n│   │   │   │   ├── RefreshTokenCallbackResult.java\\n│   │   │   │   ├── TaobaoRefreshTokenCallbackResult.java\\n│   │   │   │   └── WxshopRefreshTokenCallbackResult.java\\n│   │   │   ├── impl\\n│   │   │   │   ├── AiyongAuthServiceImpl.java\\n│   │   │   │   ├── Ali1688AppStoreServiceImpl.java\\n│   │   │   │   ├── Ali1688AuthServiceImpl.java\\n│   │   │   │   ├── BaseAuthServiceImpl.java\\n│   │   │   │   ├── DoudianAppStoreServiceImpl.java\\n│   │   │   │   ├── DoudianAuthServiceImpl.java\\n│   │   │   │   ├── JdAuthServiceImpl.java\\n│   │   │   │   ├── KwaishopAuthServiceImpl.java\\n│   │   │   │   ├── OfflineshopAuthServiceImpl.java\\n│   │   │   │   ├── PddAppStoreServiceImpl.java\\n│   │   │   │   ├── PddAuthServiceImpl.java\\n│   │   │   │   ├── TaoAppStoreServiceImpl.java\\n│   │   │   │   ├── TaoAuthServiceImpl.java\\n│   │   │   │   ├── TgcAuthServiceImpl.java\\n│   │   │   │   ├── TikTokAuthServiceImpl.java\\n│   │   │   │   ├── WxshopAuthServiceImpl.java\\n│   │   │   │   ├── WxvideoshopAuthServiceImpl.java\\n│   │   │   │   ├── XhsAppStoreServiceImpl.java\\n│   │   │   │   ├── XhsAuthServiceImpl.java\\n│   │   │   │   ├── YouzanAppStoreServiceImpl.java\\n│   │   │   │   └── YouzanAuthServiceImpl.java\\n│   │   │   ├── AppStoreService.java\\n│   │   │   └── AuthService.java\\n│   │   ├── request\\n│   │   │   └── MessageRequest.java\\n│   │   ├── response\\n│   │   │   └── MessageResponse.java\\n│   │   ├── service\\n│   │   │   ├── impl\\n│   │   │   │   ├── Ali1688PlatformFuwuItemCodeServiceImpl.java\\n│   │   │   │   ├── DistributeUserProcessServiceImpl.java\\n│   │   │   │   ├── KwaishopPlatformFuwuItemCodeServiceImpl.java\\n│   │   │   │   ├── PddPlatformFuwuItemCodeServiceImpl.java\\n│   │   │   │   └── TaoPlatformFuwuItemCodeServiceImpl.java\\n│   │   │   ├── DistributeUserProcessService.java\\n│   │   │   └── PlatformFuwuItemCodeService.java\\n│   │   └── utils\\n│   │       ├── DateUtil.java\\n│   │       ├── HttpUtil.java\\n│   │       ├── MathUtil.java\\n│   │       ├── RocketMqQueueHelper.java\\n│   │       └── SerializedPhpParser.java\\n│   └── pom.xml\\n├── uac-db-common\\n│   ├── src/main\\n│   │   ├── java/cn/loveapp/uac/db/common\\n│   │   │   ├── cache\\n│   │   │   │   └── DefaultSettingLocalCache.java\\n│   │   │   ├── config\\n│   │   │   │   └── UserProductinfoTableConfig.java\\n│   │   │   ├── convert\\n│   │   │   │   └── CommonConvertMapper.java\\n│   │   │   ├── dao/dream\\n│   │   │   │   ├── AyMultiUserTagDao.java\\n│   │   │   │   ├── AyTradeShopsDao.java\\n│   │   │   │   ├── BaseAyBusinessOpenUserDao.java\\n│   │   │   │   ├── BaseAyBusinessOpenUserLogDao.java\\n│   │   │   │   ├── BasePlatformUserProductinfoDao.java\\n│   │   │   │   ├── BlackIpDao.java\\n│   │   │   │   ├── BlackIsvDao.java\\n│   │   │   │   ├── CommonUserProductionInfoExtDao.java\\n│   │   │   │   ├── OrderSearchDao.java\\n│   │   │   │   ├── PddUserInfoDao.java\\n│   │   │   │   ├── PddUserInfoItemDao.java\\n│   │   │   │   ├── PddUserInfoTradeDao.java\\n│   │   │   │   ├── PromotionActivityDao.java\\n│   │   │   │   ├── ReturnMoneyInfoDao.java\\n│   │   │   │   ├── ReturnMoneyProjectInfoDao.java\\n│   │   │   │   ├── TradePddAuthDao.java\\n│   │   │   │   ├── TradePddAuthPayDao.java\\n│   │   │   │   ├── UserAlipayAccountInfoDao.java\\n│   │   │   │   ├── UserProductinfo1688Dao.java\\n│   │   │   │   ├── UserProductinfoAiyongDao.java\\n│   │   │   │   ├── UserProductinfoBiyaoDao.java\\n│   │   │   │   ├── UserProductinfoDoudianDao.java\\n│   │   │   │   ├── UserProductinfoJdDao.java\\n│   │   │   │   ├── UserProductinfoKwaishopDao.java\\n│   │   │   │   ├── UserProductinfoOfflineShopDao.java\\n│   │   │   │   ├── UserProductinfoPddDao.java\\n│   │   │   │   ├── UserProductinfoTaoDao.java\\n│   │   │   │   ├── UserProductinfoTgcDao.java\\n│   │   │   │   ├── UserProductinfoTikTokDao.java\\n│   │   │   │   ├── UserProductinfoWxshopDao.java\\n│   │   │   │   ├── UserProductinfoWxvideoshopDao.java\\n│   │   │   │   ├── UserProductinfoXhsDao.java\\n│   │   │   │   ├── UserProductinfoYouzanDao.java\\n│   │   │   │   ├── UserSettingsDao.java\\n│   │   │   │   ├── UserShopInfoMappingDao.java\\n│   │   │   │   ├── UserTagDao.java\\n│   │   │   │   └── UserTaobaoSellerinfoDao.java\\n│   │   │   ├── entity\\n│   │   │   │   ├── AyBusinessOpenUser.java\\n│   │   │   │   ├── AyBusinessOpenUserLog.java\\n│   │   │   │   ├── AyMultiUserTag.java\\n│   │   │   │   ├── AyTradeShops.java\\n│   │   │   │   ├── BlackIp.java\\n│   │   │   │   ├── BlackIsv.java\\n│   │   │   │   ├── OrderSearch.java\\n│   │   │   │   ├── PddUserInfo.java\\n│   │   │   │   ├── PddUserInfoItem.java\\n│   │   │   │   ├── PddUserInfoTrade.java\\n│   │   │   │   ├── PromotionActivityItem.java\\n│   │   │   │   ├── PromotionActivityTrade.java\\n│   │   │   │   ├── ReturnMoneyInfo.java\\n│   │   │   │   ├── ReturnMoneyProjectInfo.java\\n│   │   │   │   ├── TaobaoOrderSearchItem.java\\n│   │   │   │   ├── TaobaoOrderSearchTrade.java\\n│   │   │   │   ├── TradePddAuth.java\\n│   │   │   │   ├── TradePddAuthPay.java\\n│   │   │   │   ├── UserAlipayAccountInfo.java\\n│   │   │   │   ├── UserProductinfoTradeExt.java\\n│   │   │   │   ├── UserSettings.java\\n│   │   │   │   ├── UserShopInfoMapping.java\\n│   │   │   │   ├── UserTag.java\\n│   │   │   │   └── UserTaobaoSellerinfo.java\\n│   │   │   ├── repository\\n│   │   │   │   ├── impl\\n│   │   │   │   │   ├── AyBusinessOpenUserLogRepositoryImpl.java\\n│   │   │   │   │   ├── AyBusinessOpenUserRepositoryImpl.java\\n│   │   │   │   │   ├── OrderSearchRepositoryImpl.java\\n│   │   │   │   │   ├── PromotionActivityRepositoryImpl.java\\n│   │   │   │   │   ├── UserProductionInfoExtRepositoryImpl.java\\n│   │   │   │   │   ├── UserRepositoryImpl.java\\n│   │   │   │   │   └── UserSettingsRepositoryImpl.java\\n│   │   │   │   ├── AyBusinessOpenUserLogRepository.java\\n│   │   │   │   ├── AyBusinessOpenUserRepository.java\\n│   │   │   │   ├── OrderSearchRepository.java\\n│   │   │   │   ├── PromotionActivityRepository.java\\n│   │   │   │   ├── UserProductionInfoExtRepository.java\\n│   │   │   │   ├── UserRepository.java\\n│   │   │   │   └── UserSettingsRepository.java\\n│   │   │   └── service\\n│   │   │       ├── impl\\n│   │   │       │   ├── AiyongPlatformUserProductInfoServiceImpl.java\\n│   │   │       │   ├── Ali1688PlatformUserProductInfoServiceImpl.java\\n│   │   │       │   ├── BiyaoPlatformUserProductInfoServiceImpl.java\\n│   │   │       │   ├── DefaultPlatformUserProductInfoService.java\\n│   │   │       │   ├── PddPlatformUserProductInfoServiceImpl.java\\n│   │   │       │   ├── TaoPlatformUserProductInfoServiceImpl.java\\n│   │   │       │   ├── TgcPlatformUserProductInfoServiceImpl.java\\n│   │   │       │   ├── TikTokPlatformUserProductInfoServiceImpl.java\\n│   │   │       │   └── WxvideoshopPlatformUserProductInfoServiceImpl.java\\n│   │   │       └── PlatformUserProductInfoService.java\\n│   │   └── resources/mapper\\n│   │       ├── AyMultiUserTagDao.xml\\n│   │       ├── AyTradeShopsDao.xml\\n│   │       ├── BaseAyOpenUserDao.xml\\n│   │       ├── BaseAyOpenUserLogDao.xml\\n│   │       ├── BasePlatformUserProductinfoDao.xml\\n│   │       ├── BlackIpDao.xml\\n│   │       ├── BlackIsvDao.xml\\n│   │       ├── CommonUserProductionInfoExtDao.xml\\n│   │       ├── OrderSearchDao.xml\\n│   │       ├── PddUserInfoDao.xml\\n│   │       ├── PddUserInfoItemDao.xml\\n│   │       ├── PddUserInfoTradeDao.xml\\n│   │       ├── PromotionActivityDao.xml\\n│   │       ├── ReturnMoneyInfoDao.xml\\n│   │       ├── ReturnMoneyProjectInfoDao.xml\\n│   │       ├── TradePddAuthDao.xml\\n│   │       ├── TradePddAuthPayDao.xml\\n│   │       ├── UserAlipayAccountInfoDao.xml\\n│   │       ├── UserProductinfo1688Dao.xml\\n│   │       ├── UserProductinfoAiyongDao.xml\\n│   │       ├── UserProductinfoBiyaoDao.xml\\n│   │       ├── UserProductinfoDoudianDao.xml\\n│   │       ├── UserProductinfoJdDao.xml\\n│   │       ├── UserProductinfoKwaishopDao.xml\\n│   │       ├── UserProductinfoOfflineShopDao.xml\\n│   │       ├── UserProductinfoPddDao.xml\\n│   │       ├── UserProductinfoTaoDao.xml\\n│   │       ├── UserProductinfoTgcDao.xml\\n│   │       ├── UserProductinfoTikTokDao.xml\\n│   │       ├── UserProductinfoWxshopDao.xml\\n│   │       ├── UserProductinfoWxvideoshopDao.xml\\n│   │       ├── UserProductinfoXhsDao.xml\\n│   │       ├── UserProductinfoYouzanDao.xml\\n│   │       ├── UserSettingsDao.xml\\n│   │       ├── UserShopInfoMappingDao.xml\\n│   │       ├── UserTagDao.xml\\n│   │       └── UserTaobaoSellerinfoDao.xml\\n│   └── pom.xml\\n├── uac-domain\\n│   ├── src/main/java/cn/loveapp/uac/domain\\n│   │   ├── ComLoveRpcInnerprocessRequestHead.java\\n│   │   ├── PullDataRequestBody.java\\n│   │   ├── PullDataRequestProto.java\\n│   │   ├── PullTypeConstants.java\\n│   │   └── RpcRequestHead.java\\n│   └── pom.xml\\n├── uac-job\\n│   ├── uac-authorization-job\\n│   │   ├── src/main\\n│   │   │   ├── java/cn/loveapp/uac/authorization\\n│   │   │   │   ├── config\\n│   │   │   │   │   └── RefreshAccessTokenTaskConfig.java\\n│   │   │   │   ├── task\\n│   │   │   │   │   └── RefreshAccessTokenTask.java\\n│   │   │   │   └── UacAuthorizationJobApplication.java\\n│   │   │   └── resources\\n│   │   │       ├── application-dev.properties\\n│   │   │       ├── application-prod.properties\\n│   │   │       ├── application.properties\\n│   │   │       └── logback-spring.xml\\n│   │   └── pom.xml\\n│   └── pom.xml\\n├── uac-newuser-api\\n│   ├── src/main/java/cn/loveapp\\n│   │   ├── items/api/service\\n│   │   │   └── ItemsNewUserRpcInnerApiService.java\\n│   │   └── uac/newuser/dto\\n│   │       ├── request\\n│   │       │   ├── GetPullDataProgressRequest.java\\n│   │       │   └── GetSaveDataTotalResultRequest.java\\n│   │       └── response\\n│   │           ├── GetPullDataProgressResponse.java\\n│   │           └── GetSaveDataTotalResultResponse.java\\n│   └── pom.xml\\n├── uac-newusers\\n│   ├── uac-newuser-common\\n│   │   ├── src/main/java/cn/loveapp/uac/newuser/common\\n│   │   │   ├── bo\\n│   │   │   │   └── OpenUserBo.java\\n│   │   │   ├── business\\n│   │   │   │   ├── impl\\n│   │   │   │   │   ├── AbstractUserSaveDataBusinessHandleService.java\\n│   │   │   │   │   └── ItemUserSaveDataBusinessHandleServiceImpl.java\\n│   │   │   │   └── UserSaveDataBusinessHandleService.java\\n│   │   │   ├── config\\n│   │   │   │   ├── BaseNewUserQueueConfig.java\\n│   │   │   │   ├── NewUserQueueConfig.java\\n│   │   │   │   ├── NewUserQueueConfigMap.java\\n│   │   │   │   ├── OpenUserCommonConfig.java\\n│   │   │   │   └── OpenUserDispatcherConfig.java\\n│   │   │   ├── constant\\n│   │   │   │   ├── LevelConstant.java\\n│   │   │   │   ├── OpenResult.java\\n│   │   │   │   └── UserProductionInfoExtConst.java\\n│   │   │   ├── dto\\n│   │   │   │   ├── request\\n│   │   │   │   │   └── CheckDistributeUserRequest.java\\n│   │   │   │   ├── response\\n│   │   │   │   │   └── CheckDistributeUserResponse.java\\n│   │   │   │   ├── ExportSaveDataCourseRequest.java\\n│   │   │   │   ├── NewUserPlatformHandleDTO.java\\n│   │   │   │   ├── ReOpenSaveDataDTO.java\\n│   │   │   │   ├── SaveDataCourseDTO.java\\n│   │   │   │   ├── SaveDataDTO.java\\n│   │   │   │   └── UserInfoDTO.java\\n│   │   │   ├── entity\\n│   │   │   │   └── TargetSellerInfo.java\\n│   │   │   ├── helper\\n│   │   │   │   └── RateLimitHelper.java\\n│   │   │   ├── platform\\n│   │   │   │   ├── impl\\n│   │   │   │   │   ├── AbstractNewUserPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── AiyongMessageApiPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── AiyongNewUserPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── Ali1688MessageApiPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── Ali1688NewUserPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── DefaultNewUserPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── DoudianMessageApiPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── DoudianNewUserPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── JdNewUserPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── KwaishopMessageApiPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── KwaishopNewUserPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── PddMessageApiPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── PddNewUserPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── TaoMessageApiPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── TaoNewUserPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── TgcNewUserPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── WxshopMessageApiPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── WxvideoshopMessageApiPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── WxvideoshopNewUserPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── XhsMessageApiPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── XhsNewUserPlatformHandleServiceImpl.java\\n│   │   │   │   │   └── YouzanMessageApiPlatformHandleServiceImpl.java\\n│   │   │   │   ├── MessageApiPlatformHandleService.java\\n│   │   │   │   └── NewUserPlatformHandleService.java\\n│   │   │   ├── proto\\n│   │   │   │   └── OpenUserRequest.java\\n│   │   │   ├── service\\n│   │   │   │   ├── external\\n│   │   │   │   │   └── Distribute1688GuleService.java\\n│   │   │   │   ├── impl\\n│   │   │   │   │   ├── DistributeDataHandleServiceServiceImpl.java\\n│   │   │   │   │   ├── UserCenterServiceImpl.java\\n│   │   │   │   │   └── UserServiceImpl.java\\n│   │   │   │   ├── DistributeDataHandleService.java\\n│   │   │   │   ├── UserCenterService.java\\n│   │   │   │   └── UserService.java\\n│   │   │   └── utils\\n│   │   │       ├── MemcacheUtil.java\\n│   │   │       ├── PlatformAndAppNameUtil.java\\n│   │   │       └── SessionValidateUtil.java\\n│   │   └── pom.xml\\n│   ├── uac-newuser-scheduler\\n│   │   ├── src/main\\n│   │   │   ├── java/cn/loveapp/uac/newuser/scheduler\\n│   │   │   │   ├── config\\n│   │   │   │   │   ├── NewUserTaskConfig.java\\n│   │   │   │   │   └── NewuserConsumerConfig.java\\n│   │   │   │   ├── consumer\\n│   │   │   │   │   └── OpenUserConsumer.java\\n│   │   │   │   ├── service\\n│   │   │   │   │   ├── impl\\n│   │   │   │   │   │   └── NewUserTaskServiceImpl.java\\n│   │   │   │   │   └── NewUserTaskService.java\\n│   │   │   │   ├── task\\n│   │   │   │   │   ├── close\\n│   │   │   │   │   │   ├── ScanAuthCancelledUserTask.java\\n│   │   │   │   │   │   └── ScanOfflineUser.java\\n│   │   │   │   │   ├── maintain\\n│   │   │   │   │   │   └── RedisClean.java\\n│   │   │   │   │   ├── open\\n│   │   │   │   │   │   ├── ScanOpenExceptionUser.java\\n│   │   │   │   │   │   ├── ScanRetryUser.java\\n│   │   │   │   │   │   └── ScanWaitOpenUser.java\\n│   │   │   │   │   └── pull\\n│   │   │   │   │       └── ... 1 files, 0 dirs not shown\\n│   │   │   │   └── UacNewUserSchedulerApplication.java\\n│   │   │   └── resources\\n│   │   │       ├── application-dev.properties\\n│   │   │       ├── application-pretest.properties\\n│   │   │       ├── application-prod.properties\\n│   │   │       ├── application.properties\\n│   │   │       └── logback-spring.xml\\n│   │   └── pom.xml\\n│   ├── uac-newuser-service\\n│   │   ├── src/main\\n│   │   │   ├── java/cn/loveapp/uac/newuser/service\\n│   │   │   │   ├── controller\\n│   │   │   │   │   └── NewuserController.java\\n│   │   │   │   ├── export\\n│   │   │   │   │   └── ExportNewUserController.java\\n│   │   │   │   └── UacNewUserServiceApplication.java\\n│   │   │   └── resources\\n│   │   │       ├── application-dev.properties\\n│   │   │       ├── application-pretest.properties\\n│   │   │       ├── application-prod.properties\\n│   │   │       ├── application.properties\\n│   │   │       └── logback-spring.xml\\n│   │   └── pom.xml\\n│   └── pom.xml\\n├── uac-service\\n│   ├── src\\n│   │   ├── main\\n│   │   │   ├── java/cn/loveapp/uac/service\\n│   │   │   │   ├── controller\\n│   │   │   │   │   ├── CallbackController.java\\n│   │   │   │   │   ├── CloudNativeController.java\\n│   │   │   │   │   └── UserController.java\\n│   │   │   │   ├── export\\n│   │   │   │   │   ├── UserCenterServiceImpl.java\\n│   │   │   │   │   ├── UserOrderSearchApiServiceImpl.java\\n│   │   │   │   │   └── UserProductInfoExtApiServiceImpl.java\\n│   │   │   │   ├── interceptor\\n│   │   │   │   │   ├── RequestInterceptorConfig.java\\n│   │   │   │   │   └── UserRequestInterceptor.java\\n│   │   │   │   ├── util\\n│   │   │   │   │   └── RoutingDelegateUtils.java\\n│   │   │   │   ├── web\\n│   │   │   │   │   ├── VersionGrayHandler.java\\n│   │   │   │   │   └── WebConfiguration.java\\n│   │   │   │   └── UacServiceApplication.java\\n│   │   │   └── resources\\n│   │   │       ├── application-dev.properties\\n│   │   │       ├── application-prod.properties\\n│   │   │       ├── application.properties\\n│   │   │       └── logback-spring.xml\\n│   │   └── test/java/cn/loveapp/uac/service\\n│   │       ├── controller\\n│   │       │   ├── CallbackControllerTest.java\\n│   │       │   └── UserControllerTest.java\\n│   │       └── export\\n│   │           ├── UserCenterServiceImplTest.java\\n│   │           └── UserProductInfoExtApiServiceImplTest.java\\n│   └── pom.xml\\n├── uac-service-common\\n│   ├── src\\n│   │   ├── main/java/cn/loveapp/uac/service\\n│   │   │   ├── base\\n│   │   │   │   ├── BaseOAuthService.java\\n│   │   │   │   ├── BaseOperationService.java\\n│   │   │   │   └── BaseSellerService.java\\n│   │   │   ├── cache\\n│   │   │   │   └── ReminderCache.java\\n│   │   │   ├── config\\n│   │   │   │   ├── AbstractConfig.java\\n│   │   │   │   ├── ActivityConfig.java\\n│   │   │   │   ├── UserInfoChangedConfig.java\\n│   │   │   │   └── VersionGrayConfig.java\\n│   │   │   ├── event\\n│   │   │   │   ├── SubscribeUserMessageEventHandler.java\\n│   │   │   │   └── SubscribeUserMessageEventHandlerImpl.java\\n│   │   │   ├── platform/biz\\n│   │   │   │   ├── impl\\n│   │   │   │   │   ├── Alibaba1688CallbackPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── DoudianCallbackPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── PddCallbackPlatformHandleServiceImpl.java\\n│   │   │   │   │   ├── TaoCallbackPlatformHandleServiceImpl.java\\n│   │   │   │   │   └── UserPlatformHandleServiceImpl.java\\n│   │   │   │   ├── CallbackPlatformHandleService.java\\n│   │   │   │   └── UserPlatformHandleService.java\\n│   │   │   └── service\\n│   │   │       ├── impl\\n│   │   │       │   ├── ActivityServiceImpl.java\\n│   │   │       │   ├── CalculateServiceImpl.java\\n│   │   │       │   ├── OAuthDecorationServiceImpl.java\\n│   │   │       │   ├── OperationServiceImpl.java\\n│   │   │       │   ├── SellerOrderSearchServiceImpl.java\\n│   │   │       │   └── SellerServiceImpl.java\\n│   │   │       ├── ActivityService.java\\n│   │   │       ├── CalculateService.java\\n│   │   │       ├── OAuthDecorationService.java\\n│   │   │       ├── OperationService.java\\n│   │   │       ├── SellerOrderSearchService.java\\n│   │   │       └── SellerService.java\\n│   │   └── test/java/cn/loveapp/uac/service/service/impl\\n│   │       ├── ActivityServiceImplTest.java\\n│   │       ├── CalculateServiceImplTest.java\\n│   │       ├── OAuthDecorationServiceImplTest.java\\n│   │       ├── OperationServiceImplTest.java\\n│   │       ├── SellerServiceImplTest.java\\n│   │       └── UserManageRedisRepositoryHashRedisRepositoryTest.java\\n│   └── pom.xml\\n├── README.md\\n└── pom.xml\\n\"", "current_document_structure": "WikiEncrypted: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", "catalogue_think_content": "WikiEncrypted: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", "recovery_checkpoint": "wiki_generation_completed", "last_commit_id": "f06131ffdd227196457d4cb16854af63512c107b", "last_commit_update": "2025-07-25T18:36:39+08:00", "gmt_create": "2025-09-17T17:02:58.400024+08:00", "gmt_modified": "2025-09-17T17:56:03.899492+08:00", "extend_info": "{\"language\":\"zh\",\"active\":true,\"branch\":\"master\",\"shareStatus\":\"\",\"server_error_code\":\"\",\"cosy_version\":\"\"}"}}