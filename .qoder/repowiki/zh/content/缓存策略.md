# 缓存策略

<cite>
**本文档引用文件**  
- [UserCacheUtils.java](file://uac-api/src/main/java/cn/loveapp/uac/utils/UserCacheUtils.java)
- [BaseHashRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseHashRedisRepository.java)
- [BaseValueRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseValueRedisRepository.java)
- [HashCacheRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/HashCacheRepository.java)
- [ValueRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/ValueRedisRepository.java)
- [CacheTimeoutConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/CacheTimeoutConfig.java)
- [BatchGetUserCacheInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserCacheInfoRequest.java)
- [BatchUpdateUserCacheInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchUpdateUserCacheInfoRequest.java)
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java)
- [RedisCleanTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/maintain/RedisCleanTask.java)
</cite>

## 目录
1. [缓存层级结构](#缓存层级结构)
2. [缓存Key设计规范](#缓存key设计规范)
3. [数据序列化方式](#数据序列化方式)
4. [过期策略（TTL）](#过期策略ttl)
5. [缓存与数据库一致性保障机制](#缓存与数据库一致性保障机制)
6. [缓存工具类与基础Repository使用方法](#缓存工具类与基础repository使用方法)
7. [缓存穿透、雪崩、击穿的预防措施](#缓存穿透雪崩击穿的预防措施)

## 缓存层级结构

系统中Redis缓存主要分为以下几类：

- **用户基本信息缓存**：存储用户的核心身份信息，如sellerNick、sellerId、platformId等，用于快速识别和定位用户。
- **用户设置缓存**：存储用户的个性化配置信息，如界面偏好、功能开关等，提升用户体验。
- **授权Token缓存**：存储用户的访问令牌，支持无状态认证，减少数据库查询压力。
- **用户扩展信息缓存**：通过`userExtCacheKey`生成的键存储特定业务场景下的用户扩展数据，如多标签信息（ayMultiTags）、店铺标签（shops_tag）等。

这些缓存按需分布在不同的Redis实例中，以实现资源隔离和性能优化。

**本节来源**  
- [UserCacheUtils.java](file://uac-api/src/main/java/cn/loveapp/uac/utils/UserCacheUtils.java#L25-L88)
- [BaseHashRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseHashRedisRepository.java#L0-L147)

## 缓存Key设计规范

缓存Key的设计遵循统一的命名规则，确保可读性和唯一性：

- 对于**旧应用**（如淘宝、拼多多的老版本），使用`sellerNick`作为Redis Key，并进行URL编码处理。
- 对于**新应用**，采用统一格式：`platformId:appName:sellerNick`，例如`wxvideoshop:distribute:gh_4ebc6c1c889a`。
- 特殊情况下（如代发应用），不直接拼接Key，而是返回null，由具体业务逻辑决定。
- 用户扩展信息使用独立前缀`userext:`，格式为：`userext:{businessId}:{platformId}:{appName}:{sellerId}`。

Key生成过程中会进行必要的参数校验，若缺少关键字段（如sellerNick或sellerId），将记录错误日志并返回null。

```mermaid
flowchart TD
Start([开始生成缓存Key]) --> CheckPlatform{"是否为旧平台?"}
CheckPlatform --> |是| CheckSellerNick{"sellerNick是否为空?"}
CheckSellerNick --> |是| LogError["记录错误日志"]
CheckSellerNick --> |否| EncodeNick["URL编码sellerNick"]
EncodeNick --> ReturnKey["返回编码后的Key"]
CheckPlatform --> |否| CheckApp{"是否为代发应用?"}
CheckApp --> |是| ReturnNull["返回null"]
CheckApp --> |否| CheckPlatformType{"是否为抖音?"}
CheckPlatformType --> |是| CheckSellerId{"sellerId是否为空?"}
CheckPlatformType --> |否| CheckSellerNick2{"sellerNick是否为空?"}
CheckSellerId --> |是| LogError2["记录错误日志"]
CheckSellerId --> |否| BuildKeyWithId["构建 platformId:appName:sellerId"]
CheckSellerNick2 --> |是| LogError3["记录错误日志"]
CheckSellerNick2 --> |否| BuildKeyWithNick["构建 platformId:appName:sellerNick"]
BuildKeyWithId --> ReturnKey
BuildKeyWithNick --> ReturnKey
LogError --> ReturnNull
LogError2 --> ReturnNull
LogError3 --> ReturnNull
```

**图示来源**  
- [UserCacheUtils.java](file://uac-api/src/main/java/cn/loveapp/uac/utils/UserCacheUtils.java#L25-L88)

## 数据序列化方式

系统采用JSON格式进行对象序列化，具体实现如下：

- 所有Java对象通过`OBJECT_MAPPER`（Jackson ObjectMapper）转换为JSON字符串后存入Redis。
- `OBJECT_MAPPER`配置为忽略null值字段（`JsonInclude.Include.NON_NULL`），并设置时区为GMT+8。
- 对于字符串类型的数据，直接存储原始值，无需序列化。
- 反序列化时，根据目标类类型自动将JSON字符串转换回Java对象。

该机制保证了数据的可读性和跨语言兼容性，同时减少了网络传输体积。

**本节来源**  
- [BaseHashRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseHashRedisRepository.java#L29-L34)
- [BaseValueRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseValueRedisRepository.java#L47-L52)
- [RedisRepositoryBase.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/RedisRepositoryBase.java#L28-L32)

## 过期策略（TTL）

缓存的过期策略通过配置类`CacheTimeoutConfig`集中管理：

- **用户设置缓存**默认过期时间为7天（604800秒），可通过配置项`uac.redis.usersettings.timeout`动态调整。
- 其他类型的缓存未在配置中显式定义，其TTL由具体业务调用时传入。
- 支持在调用`add`方法时指定超时时间和时间单位（TimeUnit），实现灵活的生命周期控制。
- 提供`setIfAbsent`方法，可在设置值的同时指定过期时间，适用于分布式锁或限流场景。

```mermaid
classDiagram
class CacheTimeoutConfig {
+long userSettingsCacheTimeout
}
class BaseValueRedisRepository {
+add(collection, object, appName, timeout, timeUnit)
+setIfAbsent(collection, object, appName, timeout, timeUnit)
}
class BaseHashRedisRepository {
+add(collection, hkey, object, appName)
}
CacheTimeoutConfig --> BaseValueRedisRepository : "提供TTL配置"
BaseValueRedisRepository --> BaseHashRedisRepository : "继承基础功能"
```

**图示来源**  
- [CacheTimeoutConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/CacheTimeoutConfig.java#L0-L22)
- [BaseValueRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseValueRedisRepository.java#L0-L118)

## 缓存与数据库一致性保障机制

系统通过事件驱动和定时任务相结合的方式维护缓存与数据库的一致性：

- **变更触发**：当用户信息发生变更（如用户信息更新、授权取消、店铺下线等）时，系统会触发相应的事件（如`UserChangedEvent`），由事件处理器更新或删除对应缓存。
- **批量操作接口**：提供`batchUpdateUserCacheInfo`和`batchGetUserCacheInfo`接口，支持批量修改和获取缓存值，确保多节点间的数据同步。
- **定期清理任务**：通过`RedisCleanTask`定时扫描数据库中`order_cycle_end`、`lastupdatetime`、`w1_deadline`等字段早于两个月前的用户记录，并清理其缓存，防止陈旧数据占用内存。

一致性保障流程如下：

```mermaid
sequenceDiagram
participant DB as 数据库
participant Service as 业务服务
participant Redis as Redis缓存
participant Task as 定时任务
DB->>Service : 用户数据变更
Service->>Redis : 更新/删除缓存
Redis-->>Service : 操作结果
Note over Service,Redis : 实时一致性保障
Task->>DB : 分页查询过期用户
loop 每页数据
DB-->>Task : 返回用户列表
Task->>Task : 判断是否满足清理条件
alt 满足条件
Task->>Redis : 清理用户缓存
end
end
Note over Task,Redis : 定期一致性维护
```

**图示来源**  
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java#L645-L661)
- [RedisCleanTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/maintain/RedisCleanTask.java#L117-L177)

## 缓存工具类与基础Repository使用方法

### UserCacheUtils 工具类

`UserCacheUtils`提供了缓存Key的生成方法：

- `userCacheKey(sellerNick, sellerId, platformId, appName)`：生成标准用户缓存Key。
- `userExtCacheKey(sellerId, platformId, appName, businessId)`：生成用户扩展信息缓存Key。
- 内置日志记录，便于排查Key生成失败原因。

### BaseHashRedisRepository 与 BaseValueRedisRepository

两者均为抽象基类，封装了Redis操作的公共逻辑：

- **BaseHashRedisRepository**：用于操作Hash类型缓存，支持按field存取数据。
  - `add(collection, hkey, object, appName)`：新增一个field。
  - `put(collection, hkey, v, appName)`：更新或新增一个field。
  - `find(collection, hkey, appName)`：查询指定field的值。
  - `findAll(collection, tClass, platformId, appName)`：查询整个hash并反序列化为对象。
- **BaseValueRedisRepository**：用于操作String类型缓存。
  - `add(collection, object, appName, timeout, timeUnit)`：带过期时间的新增操作。
  - `setIfAbsent(collection, object, appName, timeout, timeUnit)`：原子性地设置值（若不存在）。
  - `find(collection, tClass, appName)`：查询并反序列化值。

所有操作均包含异常捕获和日志记录，确保系统稳定性。

**本节来源**  
- [UserCacheUtils.java](file://uac-api/src/main/java/cn/loveapp/uac/utils/UserCacheUtils.java#L0-L89)
- [BaseHashRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseHashRedisRepository.java#L0-L147)
- [BaseValueRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseValueRedisRepository.java#L0-L118)
- [HashCacheRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/HashCacheRepository.java#L0-L83)
- [ValueRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/ValueRedisRepository.java#L0-L67)

## 缓存穿透、雪崩、击穿的预防措施

### 缓存穿透预防

- **参数校验**：在生成缓存Key前，严格校验`sellerNick`、`sellerId`等关键参数，避免无效Key查询。
- **空值缓存**：对于查询结果为空的情况，可考虑缓存一个特殊标记（如`_REDIS_EMPTY_`），防止重复查询数据库。

### 缓存雪崩预防

- **差异化过期时间**：虽然当前配置为固定7天，但建议在实际使用中为同类缓存设置随机的过期时间偏移量，避免大量缓存同时失效。
- **多级缓存**：结合本地缓存（如Caffeine）作为一级缓存，Redis作为二级缓存，降低对Redis的直接冲击。

### 缓存击穿预防

- **互斥锁**：在缓存失效的瞬间，使用`setIfAbsent`配合过期时间实现分布式锁，确保只有一个线程去加载数据，其他线程等待并重试读取缓存。
- **永不过期策略**：对于热点数据，可采用“逻辑过期”方式，后台异步更新缓存，前台始终返回旧值，避免并发查询压垮数据库。

此外，系统通过`distributeUserProcessService.appendUserRedisField`方法对用户Redis字段进行统一处理，增强了字段的可维护性和一致性。

**本节来源**  
- [UserCacheUtils.java](file://uac-api/src/main/java/cn/loveapp/uac/utils/UserCacheUtils.java#L25-L88)
- [BaseHashRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseHashRedisRepository.java#L40-L45)
- [BaseValueRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseValueRedisRepository.java#L70-L75)
- [UacRpcUtils.java](file://uac-api/src/main/java/cn/loveapp/uac/utils/UacRpcUtils.java#L40-L42)