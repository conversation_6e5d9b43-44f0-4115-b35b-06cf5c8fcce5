# 数据模型与数据库设计

<cite>
**本文档引用的文件**  
- [AyBusinessOpenUser.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/AyBusinessOpenUser.java)
- [TradePddAuth.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/TradePddAuth.java)
- [OrderSearch.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/OrderSearch.java)
- [UserSettings.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserSettings.java)
- [UserProductinfoTableConfig.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/config/UserProductinfoTableConfig.java)
- [UserProductinfoTaoDao.xml](file://uac-db-common/src/main/resources/mapper/UserProductinfoTaoDao.xml)
- [UserSettingsDao.xml](file://uac-db-common/src/main/resources/mapper/UserSettingsDao.xml)
- [BasePlatformUserProductinfoDao.xml](file://uac-db-common/src/main/resources/mapper/BasePlatformUserProductinfoDao.xml)
</cite>

## 目录
1. [引言](#引言)
2. [核心数据实体](#核心数据实体)
3. [实体关系与ER图](#实体关系与er图)
4. [关键表结构详解](#关键表结构详解)
5. [MyBatis数据访问逻辑](#mybatis数据访问逻辑)
6. [分库分表策略](#分库分表策略)
7. [数据生命周期管理](#数据生命周期管理)
8. [结论](#结论)

## 引言
本文档旨在全面描述 usercenter-service-group 服务的核心数据模型，涵盖主要数据库实体及其相互关系。重点分析 `UserProductinfo`（用户产品信息）、`UserSettings`（用户设置）、`AyBusinessOpenUser`（业务开通用户）、`TradePddAuth`（拼多多授权信息）、`OrderSearch`（订单搜索）等关键表的字段定义、主外键关系、索引设计和业务含义。通过结合 MyBatis 的 Mapper XML 文件，阐明数据访问层的实现逻辑，并解释系统的分库分表策略和数据生命周期管理机制。

## 核心数据实体

本系统围绕用户中心服务，构建了多个核心数据实体，用于管理用户在不同电商平台的授权、产品使用、订单搜索和个性化设置等信息。

**Section sources**
- [AyBusinessOpenUser.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/AyBusinessOpenUser.java)
- [TradePddAuth.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/TradePddAuth.java)
- [OrderSearch.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/OrderSearch.java)
- [UserSettings.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserSettings.java)

## 实体关系与ER图

```mermaid
erDiagram
AyBusinessOpenUser {
bigint id PK
varchar platformId
varchar userId
varchar appId
datetime openTime
datetime closeTime
int status
varchar openSource
datetime createTime
datetime updateTime
}
TradePddAuth {
bigint id PK
varchar pddUserId
varchar accessToken
varchar refreshToken
datetime accessTokenExpireTime
datetime refreshTokenExpireTime
varchar authSource
datetime createTime
datetime updateTime
int status
}
OrderSearch {
bigint id PK
varchar userId
varchar platformId
varchar searchKeyword
text searchCondition
datetime lastSearchTime
int searchCount
datetime createTime
datetime updateTime
}
UserSettings {
bigint id PK
varchar userId
varchar settingKey
text settingValue
varchar settingType
datetime createTime
datetime updateTime
}
AyBusinessOpenUser ||--o{ OrderSearch : "用户开通 -> 订单搜索"
AyBusinessOpenUser ||--o{ TradePddAuth : "用户开通 -> 拼多多授权"
AyBusinessOpenUser }|--|| UserSettings : "用户开通 -> 用户设置"
```

**Diagram sources**
- [AyBusinessOpenUser.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/AyBusinessOpenUser.java)
- [TradePddAuth.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/TradePddAuth.java)
- [OrderSearch.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/OrderSearch.java)
- [UserSettings.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserSettings.java)

## 关键表结构详解

### AyBusinessOpenUser (业务开通用户)
该表记录了用户在各个平台（如淘宝、拼多多、抖音等）的业务开通状态和基本信息。

- **主键**: `id`
- **核心字段**:
  - `platformId`: 平台标识符，区分不同电商平台。
  - `userId`: 系统内用户唯一ID。
  - `appId`: 开通的应用ID。
  - `openTime`: 业务开通时间。
  - `closeTime`: 业务关闭时间。
  - `status`: 当前状态（如：开通中、已关闭、已过期）。
- **业务含义**: 作为用户与平台服务关联的核心纽带，是其他业务数据（如授权、订单）的前置条件。

**Section sources**
- [AyBusinessOpenUser.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/AyBusinessOpenUser.java)

### TradePddAuth (拼多多授权信息)
该表专门存储用户在拼多多平台的OAuth授权凭证。

- **主键**: `id`
- **核心字段**:
  - `pddUserId`: 拼多多平台的用户ID。
  - `accessToken`: 访问令牌，用于调用拼多多API。
  - `refreshToken`: 刷新令牌，用于获取新的`accessToken`。
  - `accessTokenExpireTime`: 访问令牌过期时间。
  - `refreshTokenExpireTime`: 刷新令牌过期时间。
  - `status`: 授权状态（有效、已过期、已撤销）。
- **业务含义**: 安全地管理用户的第三方平台授权信息，是实现数据同步和自动化操作的基础。

**Section sources**
- [TradePddAuth.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/TradePddAuth.java)

### OrderSearch (订单搜索)
该表记录用户的订单搜索行为，用于个性化推荐和搜索历史功能。

- **主键**: `id`
- **核心字段**:
  - `userId`: 发起搜索的用户ID。
  - `platformId`: 搜索的平台。
  - `searchKeyword`: 搜索关键词。
  - `searchCondition`: 搜索条件的序列化JSON。
  - `lastSearchTime`: 最后一次搜索时间。
  - `searchCount`: 该关键词的搜索次数。
- **业务含义**: 分析用户搜索行为，优化搜索体验，并为运营提供数据支持。

**Section sources**
- [OrderSearch.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/OrderSearch.java)

### UserSettings (用户设置)
该表存储用户的个性化配置信息。

- **主键**: `id`
- **核心字段**:
  - `userId`: 用户ID。
  - `settingKey`: 设置项的唯一键（如 `theme`, `language`）。
  - `settingValue`: 设置项的值，通常为JSON格式。
  - `settingType`: 设置类型，用于分类管理。
- **业务含义**: 实现用户界面和功能的个性化，提升用户体验。

**Section sources**
- [UserSettings.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserSettings.java)

## MyBatis数据访问逻辑

系统的数据访问层采用MyBatis框架，通过XML映射文件定义SQL语句。

### UserProductinfoTaoDao.xml (淘宝用户产品信息数据访问)
此文件定义了针对淘宝平台用户产品信息的CRUD操作。虽然`UserProductinfo`类未直接找到，但存在针对不同平台的DAO实现（如`UserProductinfoTaoDao`），表明采用了分表策略。

- **核心SQL**:
  - `insertSelective`: 选择性插入新记录。
  - `updateByPrimaryKeySelective`: 根据主键选择性更新。
  - `selectByUserIdAndPlatform`: 根据用户ID和平台ID查询，是高频查询。
- **动态SQL**: 大量使用`<if>`标签构建动态查询条件，提高SQL的灵活性。

**Section sources**
- [UserProductinfoTaoDao.xml](file://uac-db-common/src/main/resources/mapper/UserProductinfoTaoDao.xml)
- [BasePlatformUserProductinfoDao.xml](file://uac-db-common/src/main/resources/mapper/BasePlatformUserProductinfoDao.xml)

### UserSettingsDao.xml (用户设置数据访问)
此文件定义了用户设置的访问逻辑。

- **核心SQL**:
  - `insertOrUpdate`: 通过`ON DUPLICATE KEY UPDATE`实现插入或更新，保证设置项的唯一性。
  - `selectByUserIdAndKey`: 根据用户ID和设置键精确查询。
- **设计考量**: `settingValue`字段为`TEXT`类型，支持存储复杂对象，体现了灵活性。

**Section sources**
- [UserSettingsDao.xml](file://uac-db-common/src/main/resources/mapper/UserSettingsDao.xml)

## 分库分表策略

根据项目结构和命名规范，系统采用了明确的分库分表策略以应对海量用户数据。

- **分表策略**:
  - **按平台分表**: 存在`UserProductinfoTaoDao`、`UserProductinfoPddDao`等多个DAO，表明`UserProductinfo`表按平台（如淘宝、拼多多）进行了垂直分表。
  - **按用户ID分片**: `UserProductinfoTableConfig.java`文件的存在，强烈暗示了根据`userId`进行水平分片（Sharding）的逻辑，可能通过取模或哈希算法将数据分散到多个物理表中。
- **优势**: 该策略有效分散了单表的读写压力，提升了数据库的并发处理能力和查询性能。

**Section sources**
- [UserProductinfoTableConfig.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/config/UserProductinfoTableConfig.java)
- [UserProductinfoTaoDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/UserProductinfoTaoDao.java)

## 数据生命周期管理

系统通过多种机制管理数据的生命周期：

- **状态字段**: 如`AyBusinessOpenUser`和`TradePddAuth`中的`status`字段，明确标识数据的有效性，便于进行软删除和状态过滤。
- **时间戳**: 所有核心表均包含`createTime`和`updateTime`字段，为数据审计和过期清理提供依据。
- **后台任务**: `uac-job`模块中的`RefreshAccessTokenTask`等定时任务，负责定期清理过期的授权信息（如`accessTokenExpireTime`已过期的`TradePddAuth`记录），确保数据的时效性和安全性。

**Section sources**
- [AyBusinessOpenUser.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/AyBusinessOpenUser.java)
- [TradePddAuth.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/TradePddAuth.java)
- [uac-job](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java)

## 结论
usercenter-service-group 的数据模型设计清晰，围绕用户中心化思想，通过`AyBusinessOpenUser`作为核心枢纽，关联各平台的授权、订单和设置信息。系统采用了按平台垂直分表和按用户ID水平分片的复合分库分表策略，具备良好的可扩展性。MyBatis的使用提供了灵活的数据访问能力，而状态管理和定时任务则确保了数据的完整性和生命周期可控性。此设计为支撑大规模用户和多平台业务提供了坚实的数据基础。