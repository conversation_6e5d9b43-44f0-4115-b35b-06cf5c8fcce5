# 授权信息模型

<cite>
**本文档引用文件**  
- [TradePddAuth.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/TradePddAuth.java)
- [TradePddAuthPay.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/TradePddAuthPay.java)
- [UserAuthTokenEntity.java](file://uac-common/src/main/java/cn/loveapp/uac/common/entity/taobao/UserAuthTokenEntity.java)
- [TradePddAuthDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/TradePddAuthDao.java)
- [TradePddAuthPayDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/TradePddAuthPayDao.java)
- [TradePddAuthDao.xml](file://uac-db-common/src/main/resources/mapper/TradePddAuthDao.xml)
- [TradePddAuthPayDao.xml](file://uac-db-common/src/main/resources/mapper/TradePddAuthPayDao.xml)
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java)
- [RefreshAccessTokenTaskConfig.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/config/RefreshAccessTokenTaskConfig.java)
</cite>

## 目录
1. [引言](#引言)
2. [核心实体模型分析](#核心实体模型分析)
3. [授权数据持久化机制](#授权数据持久化机制)
4. [授权令牌安全存储与管理](#授权令牌安全存储与管理)
5. [授权生命周期管理](#授权生命周期管理)
6. [定时任务与令牌刷新机制](#定时任务与令牌刷新机制)
7. [安全最佳实践](#安全最佳实践)
8. [结论](#结论)

## 引言
本文档旨在深入解析用户中心服务系统中的授权信息模型，重点分析多平台授权数据结构的设计与实现。文档将详细阐述 `TradePddAuth`、`TradePddAuthPay` 和 `UserAuthTokenEntity` 等核心实体类的结构与用途，解析访问令牌（access_token）、刷新令牌（refresh_token）和令牌过期时间（token_expires）等关键安全字段的存储机制与加密策略。同时，文档将说明授权信息的完整生命周期管理流程，包括令牌刷新、过期处理和安全审计，并结合 MyBatis Mapper XML 文件说明授权数据的查询与更新操作，特别关注与定时任务模块的交互逻辑。最后，提供数据脱敏、访问控制和防重放攻击等安全最佳实践指南。

## 核心实体模型分析

本节深入分析系统中用于存储多平台授权信息的核心 Java 实体类。

### TradePddAuth 实体
`TradePddAuth` 实体类用于存储拼多多交易授权的基本信息。该实体主要记录了与拼多多平台授权相关的店铺标识和元数据。

**关键字段说明**：
- `id`：主键，唯一标识一条授权记录。
- `pddId`：拼多多平台分配的用户 ID。
- `pddName`：拼多多平台的用户昵称。
- `nickTrade`：交易店铺的昵称，用于标识具体的授权店铺。
- `createTime`：授权创建的日期。
- `remark`：备注信息，可用于存储额外的说明。

该实体不直接存储敏感的访问令牌，而是作为授权关系的主记录，可能与其他存储敏感信息的表进行关联。

**Section sources**
- [TradePddAuth.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/TradePddAuth.java#L12-L28)

### TradePddAuthPay 实体
`TradePddAuthPay` 实体类的结构与 `TradePddAuth` 完全一致，用于存储拼多多支付相关的授权信息。这表明系统将交易授权和支付授权进行了分离，以实现更细粒度的权限控制和管理。

其字段定义与 `TradePddAuth` 相同，同样不包含敏感令牌，体现了系统在数据模型设计上对敏感信息的隔离原则。

**Section sources**
- [TradePddAuthPay.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/TradePddAuthPay.java#L12-L28)

### UserAuthTokenEntity 实体
`UserAuthTokenEntity` 实体类专门用于存储与淘宝平台授权相关的令牌信息，是系统中处理敏感认证数据的核心模型。

**关键字段说明**：
- `accessToken`：访问令牌，用于调用淘宝开放平台 API 的主要凭证。
- `refreshToken`：刷新令牌，用于在 `accessToken` 过期后获取新的访问令牌。
- `originAcessToken` 和 `originRefreshToken`：原始令牌，可能用于记录令牌的初始值或用于特定的业务逻辑（如对比变更）。
- `w1DeadLine`：令牌的过期时间戳，用于判断 `accessToken` 是否有效。

此实体明确地将令牌信息与用户身份信息分离，集中管理，便于实施统一的安全策略和加密措施。

**Section sources**
- [UserAuthTokenEntity.java](file://uac-common/src/main/java/cn/loveapp/uac/common/entity/taobao/UserAuthTokenEntity.java#L10-L17)

## 授权数据持久化机制

本节分析授权数据的数据库访问层（DAO）设计和 MyBatis 映射文件，揭示数据的查询与更新操作。

### 数据访问接口 (DAO)
系统通过 `TradePddAuthDao` 和 `TradePddAuthPayDao` 两个接口分别定义了对 `TradePddAuth` 和 `TradePddAuthPay` 实体的数据库操作。

**核心操作方法**：
- `queryById(Integer id)`：根据主键查询单条记录。
- `queryAllByLimit(int offset, int limit)`：分页查询所有记录。
- `queryAll(TradePddAuth tradePddAuth)`：根据实体对象中的非空字段进行动态条件查询。
- `queryAllBySellerNick(String sellerNick)`：根据店铺昵称查询所有相关授权记录。
- `insert(TradePddAuth tradePddAuth)`：插入一条新的授权记录。
- `update(TradePddAuth tradePddAuth)`：根据主键更新记录。
- `deleteById(Integer id)`：根据主键删除记录。

这些接口遵循了标准的 CRUD 模式，为上层业务逻辑提供了清晰的数据访问契约。

**Section sources**
- [TradePddAuthDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/TradePddAuthDao.java#L13-L73)
- [TradePddAuthPayDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/TradePddAuthPayDao.java#L13-L73)

### MyBatis Mapper XML 文件
Mapper XML 文件是 MyBatis 框架的核心，它将 DAO 接口中的方法映射到具体的 SQL 语句。

**`TradePddAuthDao.xml` 关键分析**：
- **`<resultMap>`**：定义了数据库字段（如 `pdd_id`）与 Java 实体属性（如 `pddId`）之间的映射关系。
- **`<select>`**：包含了多个查询语句，例如 `queryById` 使用了简单的 `WHERE id = #{id}` 条件；`queryAll` 使用了 `<where>` 和 `<if>` 标签实现了动态 SQL，仅对非空参数进行条件拼接，避免了 SQL 注入风险。
- **`<insert>`**：`insert` 语句使用了 `useGeneratedKeys="true"` 和 `keyProperty="id"`，确保在插入后能获取到数据库自动生成的主键 ID。
- **`<update>`**：`update` 语句同样使用了 `<set>` 和 `<if>` 标签，动态生成 `SET` 子句，只更新非空字段，避免了不必要的字段覆盖。

`TradePddAuthPayDao.xml` 的结构和内容与 `TradePddAuthDao.xml` 完全相同，体现了代码的复用性。

```mermaid
erDiagram
TRADE_PDD_AUTH {
int id PK
bigint pdd_id
varchar pdd_name
varchar nick_trade
date create_time
varchar remark
}
TRADE_PDD_AUTH_PAY {
int id PK
bigint pdd_id
varchar pdd_name
varchar nick_trade
date create_time
varchar remark
}
```

**Diagram sources**
- [TradePddAuthDao.xml](file://uac-db-common/src/main/resources/mapper/TradePddAuthDao.xml#L10-L108)
- [TradePddAuthPayDao.xml](file://uac-db-common/src/main/resources/mapper/TradePddAuthPayDao.xml)

## 授权令牌安全存储与管理

系统对敏感的授权令牌（如 `accessToken` 和 `refreshToken`）采取了谨慎的管理策略。

### 存储机制
从现有代码分析，`UserAuthTokenEntity` 实体直接以明文 `String` 类型存储令牌。这表明在应用层，令牌是以原始字符串形式处理的。然而，这并不意味着令牌在数据库中也是明文存储。

**推断的安全策略**：
1.  **应用层加密**：最可能的情况是，在将 `UserAuthTokenEntity` 对象持久化到数据库之前，系统会有一个预处理步骤，对 `accessToken` 和 `refreshToken` 字段进行加密（如 AES 加密），然后将密文存入数据库。反之，在从数据库读取后，会进行解密。
2.  **数据库透明加密 (TDE)**：数据库层面可能启用了透明数据加密，对整个表或特定列进行加密，应用层代码无需感知。
3.  **环境隔离**：生产环境的数据库访问受到严格控制，只有授权的服务才能访问包含敏感信息的表。

### 关键安全字段
- **`accessToken`**：是短期有效的凭证，应设置较短的过期时间（如 2 小时）。系统通过 `w1DeadLine` 字段来跟踪其有效期。
- **`refreshToken`**：是长期有效的凭证，安全性要求极高。一旦泄露，攻击者可以无限期地获取新的 `accessToken`。因此，`refreshToken` 必须被严格保护，且系统应具备在发现泄露时主动使其失效（revoke）的能力。
- **`w1DeadLine`**：通过时间戳而非相对时间存储过期时间，便于进行精确的过期判断和定时任务调度。

**Section sources**
- [UserAuthTokenEntity.java](file://uac-common/src/main/java/cn/loveapp/uac/common/entity/taobao/UserAuthTokenEntity.java#L10-L17)

## 授权生命周期管理

授权信息的生命周期涵盖了从创建、使用、刷新到最终失效的全过程。

### 生命周期阶段
1.  **授权创建**：用户通过 OAuth 流程完成授权后，系统调用 `TradePddAuthDao.insert()` 和 `UserAuthTokenEntity` 的持久化方法，将授权信息存入数据库。
2.  **令牌使用**：业务服务在调用第三方平台 API 时，从数据库加载 `UserAuthTokenEntity`，获取有效的 `accessToken`。
3.  **令牌刷新**：当 `accessToken` 即将过期或已过期时，系统使用 `refreshToken` 向第三方平台发起刷新请求，获取新的 `accessToken` 和 `refreshToken`（有时刷新后旧的 `refreshToken` 会失效，需用新的替换）。
4.  **过期处理**：如果 `refreshToken` 也已过期或失效，用户需要重新进行授权流程。
5.  **授权撤销**：用户或系统管理员可以主动撤销授权，系统调用 `TradePddAuthDao.deleteById()` 删除相关记录，并清理缓存中的令牌。

### 安全审计
虽然当前代码未直接体现，但一个完整的授权系统应记录关键操作日志，例如：
- 授权创建时间、IP 地址。
- 令牌刷新操作记录。
- 频繁的失败 API 调用尝试（可能为暴力破解）。
- 授权撤销操作。

这些日志对于安全审计和问题排查至关重要。

## 定时任务与令牌刷新机制

系统通过独立的定时任务模块来自动化管理令牌的刷新，确保服务的持续可用性。

### 定时任务模块
`uac-job` 模块下的 `uac-authorization-job` 专门负责授权相关的后台任务。

**核心组件**：
- `RefreshAccessTokenTaskConfig`：配置类，定义了定时任务的执行周期（例如，每 30 分钟检查一次即将过期的令牌）。
- `RefreshAccessTokenTask`：任务实现类，是定时任务的执行主体。

### 任务执行流程
```mermaid
sequenceDiagram
participant Scheduler as 定时任务调度器
participant Task as RefreshAccessTokenTask
participant Service as AuthService
participant DB as 数据库
participant Platform as 第三方平台
Scheduler->>Task : 触发任务 (每30分钟)
Task->>Service : 获取所有有效授权
Service->>DB : queryAll() from TradePddAuth
DB-->>Service : 返回授权列表
Service->>DB : 查询 UserAuthTokenEntity
DB-->>Service : 返回令牌信息
loop 遍历每个授权
Service->>Service : 检查 w1DeadLine < 当前时间 + 阈值
alt 令牌即将过期
Service->>Platform : 使用 refreshToken 请求新令牌
Platform-->>Service : 返回新的 accessToken 和 refreshToken
Service->>DB : update() UserAuthTokenEntity
DB-->>Service : 更新成功
else 令牌有效
无需操作
end
end
Task-->>Scheduler : 任务执行完毕
```

**Diagram sources**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java)
- [RefreshAccessTokenTaskConfig.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/config/RefreshAccessTokenTaskConfig.java)

**Section sources**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java)

## 安全最佳实践

基于对系统架构的分析，提出以下安全最佳实践指南。

### 数据脱敏
- **日志脱敏**：在任何日志输出中，必须对 `accessToken` 和 `refreshToken` 进行脱敏处理（例如，只显示前几位和后几位，中间用 `***` 代替）。
- **接口响应脱敏**：对外提供的 API 接口，除非必要，绝不应返回完整的 `refreshToken`。

### 访问控制
- **最小权限原则**：访问包含令牌信息的数据库表或服务接口，必须经过严格的权限认证和授权（如基于角色的访问控制 RBAC）。
- **网络隔离**：存放敏感数据的数据库应部署在内网，限制外部直接访问。

### 防重放攻击
- **时间戳和Nonce**：在调用第三方平台 API 时，请求中应包含时间戳和一次性随机数（Nonce），防止请求被截获后重复发送。
- **HTTPS**：所有涉及令牌传输的通信必须使用 HTTPS 协议进行加密。

### 其他建议
- **定期轮换密钥**：用于加密令牌的应用层密钥应定期轮换。
- **监控与告警**：建立对令牌刷新失败、数据库访问异常等关键事件的监控和告警机制。

## 结论
本文档详细解析了用户中心服务中的授权信息模型。系统通过 `TradePddAuth` 和 `TradePddAuthPay` 等实体管理多平台授权关系，并通过 `UserAuthTokenEntity` 集中管理敏感的令牌信息。数据持久化通过 MyBatis DAO 和 XML 映射实现，支持灵活的查询和更新。系统通过独立的定时任务模块 `uac-authorization-job` 自动化执行令牌刷新，保障了服务的稳定性。尽管代码层面未直接体现加密逻辑，但通过合理的架构设计（如数据分离）和遵循安全最佳实践（如数据脱敏、访问控制），可以构建一个安全可靠的授权管理体系。未来可进一步在代码中明确加密解密的实现细节，并加强安全审计日志的记录。