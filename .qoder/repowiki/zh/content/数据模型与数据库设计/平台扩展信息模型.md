# 平台扩展信息模型

<cite>
**本文档引用文件**  
- [UserProductinfoTableConfig.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/config/UserProductinfoTableConfig.java)
- [UserProductinfoTaoDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/UserProductinfoTaoDao.java)
- [UserProductinfoPddDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/UserProductinfoPddDao.java)
- [BasePlatformUserProductinfoDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/BasePlatformUserProductinfoDao.java)
- [UserProductinfoTradeExt.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserProductinfoTradeExt.java)
- [PddPlatformUserProductInfoServiceImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/impl/PddPlatformUserProductInfoServiceImpl.java)
- [TaoPlatformUserProductInfoServiceImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/impl/TaoPlatformUserProductInfoServiceImpl.java)
- [UserProductinfoTaoDao.xml](file://uac-db-common/src/main/resources/mapper/UserProductinfoTaoDao.xml)
- [UserProductinfoPddDao.xml](file://uac-db-common/src/main/resources/mapper/UserProductinfoPddDao.xml)
</cite>

## 目录
1. [引言](#引言)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 引言
本文档旨在深入分析用户中心服务系统中多平台用户产品信息的统一管理方案。系统通过灵活的数据模型设计，支持淘宝、拼多多、抖音、快手等多个电商平台的用户信息存储与查询。核心设计目标是实现共性与特性的平衡，确保系统具备良好的扩展性，能够快速接入新平台。

## 项目结构
系统采用模块化设计，核心数据访问逻辑位于 `uac-db-common` 模块中。该模块定义了统一的数据访问接口和实体类，为上层业务提供服务。

```mermaid
graph TB
subgraph "uac-db-common"
config[config]
dao[dao/dream]
entity[entity]
mapper[mapper]
service[service/impl]
end
config --> |提供配置| dao
dao --> |实现CRUD| mapper
entity --> |数据载体| dao
service --> |业务逻辑| dao
```

**图示来源**  
- [UserProductinfoTableConfig.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/config/UserProductinfoTableConfig.java)
- [BasePlatformUserProductinfoDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/BasePlatformUserProductinfoDao.java)
- [UserProductinfoTradeExt.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserProductinfoTradeExt.java)

**本节来源**  
- [UserProductinfoTableConfig.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/config/UserProductinfoTableConfig.java)
- [BasePlatformUserProductinfoDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/BasePlatformUserProductinfoDao.java)

## 核心组件
系统的核心在于 `UserProductInfo` 实体类和 `BasePlatformUserProductinfoDao` 接口。`UserProductInfo` 定义了所有平台共有的用户信息字段，如 `sellerId`、`sellerNick`、`level` 等。`BasePlatformUserProductinfoDao` 定义了基于这些共有字段的通用数据访问方法。

**本节来源**  
- [BasePlatformUserProductinfoDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/BasePlatformUserProductinfoDao.java)
- [UserProductinfoTradeExt.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserProductinfoTradeExt.java)

## 架构概述
系统采用“继承+配置”的混合模式来管理多平台数据。对于每个平台，如淘宝（Tao）、拼多多（Pdd），都存在一个特化的 DAO 接口（如 `UserProductinfoTaoDao`）继承自 `BasePlatformUserProductinfoDao`。这种设计复用了通用的 CRUD 操作，同时允许为特定平台添加独有的方法。

```mermaid
classDiagram
class BasePlatformUserProductinfoDao {
+queryByUserId(sellerId, tableName, extensionFields) UserProductInfo
+queryBySellerNick(sellerNick, tableName, extensionFields) UserProductInfo
+insert(userProductinfo, tableName) int
+update(userProductinfo, tableName, useUserId, isDistribute1688) int
}
class UserProductinfoTaoDao {
+updateSaveDataDB(nick, db, tableName) int
}
class UserProductinfoPddDao {
}
BasePlatformUserProductinfoDao <|-- UserProductinfoTaoDao
BasePlatformUserProductinfoDao <|-- UserProductinfoPddDao
```

**图示来源**  
- [BasePlatformUserProductinfoDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/BasePlatformUserProductinfoDao.java)
- [UserProductinfoTaoDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/UserProductinfoTaoDao.java)
- [UserProductinfoPddDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/UserProductinfoPddDao.java)

## 详细组件分析

### 平台特化实体设计模式
系统通过继承机制实现平台特化实体的设计。`UserProductinfoTaoDao` 和 `UserProductinfoPddDao` 继承了 `BasePlatformUserProductinfoDao` 的所有通用方法，确保了代码的复用性。同时，`UserProductinfoTaoDao` 可以根据需要扩展特定于淘宝平台的方法，如 `updateSaveDataDB`，用于更新数据存储库信息。这种设计模式在保持共性的同时，灵活地支持了各平台的特性。

#### 对象导向组件
```mermaid
classDiagram
class UserProductInfo {
+String sellerId
+String sellerNick
+Integer level
+String appId
+String memberId
}
class UserProductinfoTradeExt {
+Integer id
+String sellerId
+String sellerNick
+Integer dbStatus
+Integer pullStatus
+LocalDateTime gmtCreate
}
UserProductInfo <|-- UserProductinfoTradeExt : "扩展"
```

**图示来源**  
- [UserProductinfoTradeExt.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserProductinfoTradeExt.java)

**本节来源**  
- [UserProductinfoTaoDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/UserProductinfoTaoDao.java)
- [UserProductinfoPddDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/UserProductinfoPddDao.java)

### 平台特定字段存储策略
平台特定字段的存储策略通过配置驱动实现。`UserProductinfoTableConfig` 类定义了一个 `PlatformConfig` 内部类，其中包含 `extensionFields` 字段，用于配置各平台用户表的额外字段。在执行数据库查询时，`BasePlatformUserProductinfoDao` 的方法会接收 `extensionFields` 参数，并将其动态注入到 SQL 查询中，从而实现对平台特有字段的读取。

### 数据同步机制
数据同步机制主要通过 `UserProductinfoTradeExt` 实体类来跟踪。该实体记录了数据落库、拉单、Session、API 等多个状态，以及拉取的起止时间。这些状态字段（如 `dbStatus`, `pullStatus`, `sessionStatus`）为监控和管理数据同步过程提供了依据。

### 跨平台数据查询与聚合
跨平台数据查询和聚合的实现依赖于 MyBatis 的 Mapper XML 文件。以 `UserProductinfoTaoDao.xml` 和 `UserProductinfoPddDao.xml` 为例，它们为各自的 DAO 接口提供了 SQL 映射。查询时，通过 `tableName` 参数动态指定目标表，通过 `extensionFields` 参数动态拼接需要查询的字段。这种设计使得同一个服务方法可以操作不同平台的物理表，实现了数据的统一查询和聚合。

#### API/服务组件
```mermaid
sequenceDiagram
participant Service as "PlatformUserProductInfoService"
participant Impl as "TaoPlatformUserProductInfoServiceImpl"
participant Repo as "UserRepository"
participant Dao as "UserProductinfoTaoDao"
participant Mapper as "UserProductinfoTaoDao.xml"
Service->>Impl : getUserInfo(userBo, platformId)
Impl->>Repo : queryBySellerNick(nick, hasReadTag, platformId, appType)
Repo->>Dao : queryBySellerNick(nick, tableName, extensionFields)
Dao->>Mapper : 执行SQL (动态表名和字段)
Mapper-->>Dao : 返回UserProductInfo
Dao-->>Repo : 返回结果
Repo-->>Impl : 返回结果
Impl-->>Service : 返回UserProductInfo
```

**图示来源**  
- [TaoPlatformUserProductInfoServiceImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/impl/TaoPlatformUserProductInfoServiceImpl.java)
- [UserProductinfoTaoDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/UserProductinfoTaoDao.java)
- [UserProductinfoTaoDao.xml](file://uac-db-common/src/main/resources/mapper/UserProductinfoTaoDao.xml)

**本节来源**  
- [UserProductinfoTableConfig.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/config/UserProductinfoTableConfig.java)
- [UserProductinfoTaoDao.xml](file://uac-db-common/src/main/resources/mapper/UserProductinfoTaoDao.xml)
- [UserProductinfoPddDao.xml](file://uac-db-common/src/main/resources/mapper/UserProductinfoPddDao.xml)

## 依赖分析
系统各组件间依赖关系清晰。`uac-service` 和 `uac-api` 模块依赖 `uac-db-common` 提供的数据访问能力。`uac-db-common` 内部，`service` 层依赖 `repository` 层，`repository` 层依赖 `dao` 层，`dao` 层依赖 `mapper` XML 文件和 `entity` 实体类。`config` 模块为 `dao` 层提供必要的配置信息。

```mermaid
graph TD
uac_api[uac-api] --> uac_db_common[uac-db-common]
uac_service[uac-service] --> uac_db_common
uac_db_common_service[uac-db-common/service] --> uac_db_common_repository[uac-db-common/repository]
uac_db_common_repository --> uac_db_common_dao[uac-db-common/dao]
uac_db_common_dao --> uac_db_common_mapper[uac-db-common/mapper]
uac_db_common_dao --> uac_db_common_entity[uac-db-common/entity]
uac_db_common_config[uac-db-common/config] --> uac_db_common_dao
```

**图示来源**  
- [PddPlatformUserProductInfoServiceImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/impl/PddPlatformUserProductInfoServiceImpl.java)
- [UserProductinfoTableConfig.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/config/UserProductinfoTableConfig.java)

**本节来源**  
- [PddPlatformUserProductInfoServiceImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/impl/PddPlatformUserProductInfoServiceImpl.java)
- [TaoPlatformUserProductInfoServiceImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/impl/TaoPlatformUserProductInfoServiceImpl.java)

## 性能考虑
系统的性能优化主要体现在以下几个方面：
1.  **动态SQL**：通过 MyBatis 动态 SQL 实现按需查询字段，减少不必要的数据传输。
2.  **分页查询**：`BasePlatformUserProductinfoDao` 提供了多种分页查询方法，避免一次性加载大量数据。
3.  **索引优化**：虽然未在代码中直接体现，但 `queryByUserId`, `queryBySellerNick` 等高频查询方法暗示了数据库层面应建立相应的索引。
4.  **配置化**：将表名和扩展字段配置化，避免了硬编码，提高了灵活性和可维护性。

## 故障排除指南
当遇到跨平台数据查询失败时，应按以下步骤排查：
1.  检查 `UserProductinfoTableConfig` 中对应平台的 `tableName` 和 `extensionFields` 配置是否正确。
2.  检查对应的 Mapper XML 文件（如 `UserProductinfoTaoDao.xml`）中的 SQL 语句是否存在语法错误。
3.  确认数据库中对应的物理表是否存在，且表结构与 `UserProductInfo` 实体类及 `extensionFields` 配置相匹配。
4.  查看 `UserProductinfoTradeExt` 表中的状态字段，判断数据同步是否成功。

**本节来源**  
- [UserProductinfoTableConfig.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/config/UserProductinfoTableConfig.java)
- [UserProductinfoTradeExt.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserProductinfoTradeExt.java)

## 结论
该平台扩展信息模型通过“继承+配置”的设计模式，成功实现了多平台用户信息的统一管理。系统在保证核心功能复用的同时，具备了极强的扩展性。为新平台（如抖音、快手）接入，只需遵循以下步骤：
1.  在 `uac-db-common` 模块中创建新的 DAO 接口（如 `UserProductinfoDouyinDao`），继承 `BasePlatformUserProductinfoDao`。
2.  在 `resources/mapper` 目录下创建对应的 Mapper XML 文件。
3.  在 `UserProductinfoTableConfig` 中配置新平台的表名和扩展字段。
4.  实现 `PlatformUserProductInfoService` 的具体服务类（如 `DouyinPlatformUserProductInfoServiceImpl`），并重写 `getPlatformId` 方法。
此设计模式清晰、高效，为系统的长期演进提供了坚实的基础。