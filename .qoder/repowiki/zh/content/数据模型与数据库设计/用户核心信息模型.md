# 用户核心信息模型

<cite>
**本文档引用文件**  
- [AyBusinessOpenUser.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/AyBusinessOpenUser.java)
- [UserTaobaoSellerinfo.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserTaobaoSellerinfo.java)
- [BaseAyOpenUserDao.xml](file://uac-db-common/src/main/resources/mapper/BaseAyOpenUserDao.xml)
- [BaseAyBusinessOpenUserDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/BaseAyBusinessOpenUserDao.java)
- [AyBusinessOpenUserRepositoryImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/impl/AyBusinessOpenUserRepositoryImpl.java)
- [UserTaobaoSellerinfoDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/UserTaobaoSellerinfoDao.java)
</cite>

## 目录
1. [引言](#引言)
2. [核心实体模型](#核心实体模型)
3. [关键字段定义与业务含义](#关键字段定义与业务含义)
4. [主键生成与索引设计](#主键生成与索引设计)
5. [数据访问层操作逻辑](#数据访问层操作逻辑)
6. [实体关系图](#实体关系图)
7. [实际使用示例](#实际使用示例)
8. [结论](#结论)

## 引言
本文档旨在详细描述用户中心服务中的核心用户信息模型，重点围绕 `AyBusinessOpenUser` 和 `UserTaobaoSellerinfo` 两个核心实体展开。文档将深入解析其字段定义、状态流转、数据访问机制及与其他模块的关联关系，为开发人员提供全面的技术参考。

## 核心实体模型

### AyBusinessOpenUser 实体
`AyBusinessOpenUser` 是表示业务用户开通状态的核心实体，用于管理用户在不同平台上的开通流程和状态。

```mermaid
classDiagram
class AyBusinessOpenUser {
+Integer id
+String sellerId
+String sellerNick
+Integer status
+String platId
+Integer retryCount
+Integer ruleId
+String remark
+LocalDateTime gmtCreate
+LocalDateTime gmtModify
+String appName
+Integer userLogId
+static final int WAIT_OPEN = 101
+static final int OPENING = 102
+static final int DONE = 10
+static final int FAILED = -101
+static final int RULE_DEFAULT = 1
+isOpening() Boolean
}
```

**图例来源**  
- [AyBusinessOpenUser.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/AyBusinessOpenUser.java)

### UserTaobaoSellerinfo 实体
`UserTaobaoSellerinfo` 用于存储淘宝卖家的详细信息，包括基础资料、信用等级、联系方式等。

```mermaid
classDiagram
class UserTaobaoSellerinfo {
+Integer id
+Long userId
+String nick
+String phone
+String email
+String province
+String city
+String address
+String sex
+Integer sellerCreditLevel
+Integer sellerCreditScore
+Integer sellerCreditTotalNum
+Integer sellerCreditGoodNum
+String type
+String status
+Boolean isGoldenSeller
+LocalDateTime createdate
+LocalDateTime lastactivedt
+LocalDateTime lastupdatetime
+Integer refundPhone
}
```

**图例来源**  
- [UserTaobaoSellerinfo.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserTaobaoSellerinfo.java)

## 关键字段定义与业务含义

### AyBusinessOpenUser 关键字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| **sellerId** | String | 用户ID，唯一标识一个用户，通常为平台分配的数字ID或昵称 |
| **sellerNick** | String | 卖家主昵称，用于展示和识别用户 |
| **status** | Integer | 用户开通状态：<br>• 101: 待开通<br>• 102: 正在开通<br>• 10: 已开通<br>• -101: 开通失败 |
| **platId** | String | 平台ID，标识用户所属的电商平台（如淘宝、拼多多等） |
| **retryCount** | Integer | 重试次数，用于控制失败后的自动重试机制 |
| **ruleId** | Integer | 开通规则：<br>• 1: 全开通<br>• 2: 根据条件开通 |
| **gmtCreate** | LocalDateTime | 记录创建时间，用于追踪用户生命周期 |
| **gmtModify** | LocalDateTime | 最后修改时间，用于更新状态和触发流程 |
| **appName** | String | 应用名称，支持多应用场景下的用户管理 |
| **userLogId** | Integer | 关联最新一条操作日志的ID，便于追溯操作历史 |

### UserTaobaoSellerinfo 关键字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| **userId** | Long | 用户数字ID，淘宝平台的唯一标识 |
| **nick** | String | 用户昵称 |
| **phone** | String | 用户联系电话 |
| **email** | String | 用户Email |
| **province/city/address** | String | 用户地理位置信息 |
| **sex** | String | 性别：m(男), f(女) |
| **sellerCreditLevel** | Integer | 信用等级（1-20级） |
| **sellerCreditScore** | Integer | 信用总分 |
| **type** | String | 用户类型：B(商家), C(C商家) |
| **status** | String | 状态：normal(正常), inactive(未激活), delete(删除), freeze(冻结), supervise(监管) |
| **isGoldenSeller** | Boolean | 是否为金牌卖家 |
| **createdate** | LocalDateTime | 第一次使用爱用产品时间 |
| **lastactivedt** | LocalDateTime | 最后一次活动时间 |
| **lastupdatetime** | LocalDateTime | 信息最后更新时间 |

## 主键生成与索引设计

### 表名动态生成策略
系统采用动态表名策略，根据 `businessId` 生成对应的表名：
- 格式：`ay_{businessId}_open_user`
- 示例：`ay_tao_open_user`、`ay_pdd_open_user`

该策略通过 `AyBusinessOpenUserRepositoryImpl.getTableName()` 方法实现，确保不同业务线的数据隔离。

### 主键设计
- **id**：自增主键，由数据库自动生成。
- **useGeneratedKeys="true"**：在 MyBatis 插入语句中启用，确保返回生成的主键值。

### 查询优化索引
为提升查询性能，系统在关键字段上建立了索引：
- **seller_id + plat_id**：组合索引，用于快速定位特定平台的用户
- **status**：单列索引，用于状态筛选（如查询待开通用户）
- **gmt_modify**：时间索引，用于按修改时间范围查询

这些索引优化了 `queryByStatus` 和 `queryBySellerIdAndPlatId` 等高频查询操作。

## 数据访问层操作逻辑

### DAO 接口设计
`BaseAyBusinessOpenUserDao` 提供了对 `AyBusinessOpenUser` 的基本 CRUD 操作：

```mermaid
flowchart TD
A["insert(ayBusinessOpenUser, tableName)"] --> B["新增用户记录"]
C["updateByStatus(ayBusinessOpenUser, tableName)"] --> D["更新状态和修改时间"]
E["updateByStatusAndRetryCount(...)"] --> F["更新状态、重试次数和修改时间"]
G["queryById(id, tableName)"] --> H["主键查询"]
I["queryBySellerIdAndPlatId(...)"] --> J["根据sellerId和平台查询"]
K["queryByStatus(...)"] --> L["按状态分页查询"]
M["queryByStatusAndModify(...)"] --> N["按状态和修改时间查询"]
```

**图例来源**  
- [BaseAyBusinessOpenUserDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/BaseAyBusinessOpenUserDao.java)
- [BaseAyOpenUserDao.xml](file://uac-db-common/src/main/resources/mapper/BaseAyOpenUserDao.xml)

### SQL 映射逻辑
在 `BaseAyOpenUserDao.xml` 中，SQL 语句通过 `${tableName}` 动态指定表名，实现多表操作：

```xml
<select id="queryBySellerIdAndPlatId" resultMap="AyBusinessOpenUserMap">
    select <include refid="fields"/>
    from ${tableName}
    where seller_id = #{sellerId} and plat_id = #{platId}
    <if test="appName != null">
        and app_name = #{appName}
    </if>
    <if test="appName == null">
        and app_name = ''
    </if>
    limit 1
</select>
```

该设计支持灵活的条件查询，同时通过 `limit 1` 保证唯一性。

### Repository 层封装
`AyBusinessOpenUserRepositoryImpl` 封装了 DAO 调用，提供业务友好的接口：

```java
@Override
public AyBusinessOpenUser queryBySellerIdAndPlatId(String sellerId, String platId, String appName, String businessId) {
    return getAyOpenUserDao(businessId).queryBySellerIdAndPlatId(sellerId, platId, appName, getTableName(businessId));
}
```

通过 `getTableName()` 和 `getAyOpenUserDao()` 方法，实现了业务 ID 到具体表和 DAO 的映射。

## 实体关系图

```mermaid
erDiagram
AY_BUSINESS_OPEN_USER {
Integer id PK
String sellerId UK
String sellerNick
Integer status
String platId
Integer retryCount
Integer ruleId
String remark
LocalDateTime gmtCreate
LocalDateTime gmtModify
String appName
Integer userLogId FK
}
USER_TAOBAO_SELLERINFO {
Integer id PK
Long userId UK
String nick
String phone
String email
String province
String city
String address
String sex
Integer sellerCreditLevel
Integer sellerCreditScore
Integer sellerCreditTotalNum
Integer sellerCreditGoodNum
String type
String status
Boolean isGoldenSeller
LocalDateTime createdate
LocalDateTime lastactivedt
LocalDateTime lastupdatetime
Integer refundPhone
}
AY_BUSINESS_OPEN_USER_LOG {
Integer id PK
Integer openUserId FK
String sellerNick
Integer status
Integer type
Integer ruleId
String reason
String remark
LocalDateTime gmtCreate
LocalDateTime gmtModify
}
AY_BUSINESS_OPEN_USER ||--o{ AY_BUSINESS_OPEN_USER_LOG : "1:N"
AY_BUSINESS_OPEN_USER }|--|| USER_TAOBAO_SELLERINFO : "关联"
```

**图例来源**  
- [AyBusinessOpenUser.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/AyBusinessOpenUser.java)
- [UserTaobaoSellerinfo.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserTaobaoSellerinfo.java)
- [AyBusinessOpenUserLog.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/AyBusinessOpenUserLog.java)

## 实际使用示例

### 查询用户基本信息
通过 DAO 接口查询用户信息的典型代码示例：

```java
// 通过 sellerId 和 platId 查询用户
AyBusinessOpenUser user = ayBusinessOpenUserRepository.queryBySellerIdAndPlatId(
    "test_seller_001", 
    "taobao", 
    "default_app", 
    "tao"
);
```

### 新增用户记录
```java
AyBusinessOpenUser newUser = new AyBusinessOpenUser();
newUser.setSellerId("new_user_001");
newUser.setSellerNick("新用户");
newUser.setPlatId("taobao");
newUser.setStatus(AyBusinessOpenUser.WAIT_OPEN);
newUser.setGmtCreate(LocalDateTime.now());
newUser.setGmtModify(LocalDateTime.now());

int result = ayBusinessOpenUserRepository.insert(newUser, "tao");
```

### 更新用户状态
```java
// 更新用户状态为“正在开通”
user.setStatus(AyBusinessOpenUser.OPENING);
user.setGmtModify(LocalDateTime.now());
ayBusinessOpenUserRepository.updateByStatus(user, "tao");
```

### 按状态查询用户列表
```java
List<Integer> statuses = Arrays.asList(AyBusinessOpenUser.WAIT_OPEN, AyBusinessOpenUser.OPENING);
Set<AyBusinessOpenUser> users = ayBusinessOpenUserRepository.queryByStatus(
    statuses, 
    0, 
    0, 
    100, 
    "tao"
);
```

## 结论
本文档详细阐述了用户核心信息模型的设计与实现，涵盖了 `AyBusinessOpenUser` 和 `UserTaobaoSellerinfo` 两个核心实体的结构、字段含义、数据访问逻辑及关系模型。通过动态表名、状态机管理和索引优化，系统实现了高效、可扩展的用户信息管理能力，为上层业务提供了坚实的数据基础。