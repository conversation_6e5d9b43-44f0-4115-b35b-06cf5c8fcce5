# uac-api模块

<cite>
**本文档引用文件**  
- [UserInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/UserInfoRequest.java)
- [BatchGetUserFullInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserFullInfoRequest.java)
- [UserInfoResponse.java](file://uac-api/src/main/java/cn/loveapp/uac/response/UserInfoResponse.java)
- [UserFullInfoResponse.java](file://uac-api/src/main/java/cn/loveapp/uac/response/UserFullInfoResponse.java)
- [UserExtInfoDTO.java](file://uac-api/src/main/java/cn/loveapp/uac/domain/UserExtInfoDTO.java)
- [UserSettingDTO.java](file://uac-api/src/main/java/cn/loveapp/uac/domain/UserSettingDTO.java)
- [UserCenterInnerApiService.java](file://uac-api/src/main/java/cn/loveapp/uac/service/UserCenterInnerApiService.java)
- [BaseException.java](file://uac-api/src/main/java/cn/loveapp/uac/exception/BaseException.java)
- [UserException.java](file://uac-api/src/main/java/cn/loveapp/uac/exception/UserException.java)
- [ApiCode.java](file://uac-api/src/main/java/cn/loveapp/uac/code/ApiCode.java)
</cite>

## 目录
1. [引言](#引言)
2. [核心数据传输对象设计](#核心数据传输对象设计)
3. [服务接口契约定义](#服务接口契约定义)
4. [异常体系设计](#异常体系设计)
5. [实际使用示例](#实际使用示例)
6. [多平台适配与版本兼容性](#多平台适配与版本兼容性)
7. [结论](#结论)

## 引言
uac-api模块作为用户中心服务的API契约层，承担着定义统一接口规范的核心职责。该模块通过标准化的请求与响应数据结构，确保了服务提供方与消费方之间通信的一致性，是微服务架构中关键的接口契约组件。本模块不仅为内部服务调用提供了清晰的远程方法定义，也为外部系统集成提供了稳定的API契约。

**文档引用文件**  
- [UserInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/UserInfoRequest.java)
- [UserCenterInnerApiService.java](file://uac-api/src/main/java/cn/loveapp/uac/service/UserCenterInnerApiService.java)

## 核心数据传输对象设计

### 请求对象设计
uac-api模块定义了丰富的请求数据传输对象（DTO），用于封装客户端向服务端发起的各种操作请求。这些请求对象继承自`BaseHttpRequest`，并根据具体业务场景进行扩展。

`UserInfoRequest`作为基础用户信息请求类，继承自`BaseHttpRequest`，用于获取用户基本信息。该类通过继承机制复用基础请求属性，确保了请求结构的一致性。

`BatchGetUserFullInfoRequest`则用于批量获取用户完整信息的场景，其核心设计包含一个`userFullInfoRequests`列表，用于封装多个`UserFullInfoRequest`对象，实现批量操作。该请求还包含`needCheckAndRefreshToken`标志位，用于控制是否需要检查并刷新访问令牌。

```mermaid
classDiagram
class BaseHttpRequest {
+String sellerNick
+String sellerId
+String platformId
+String appName
}
class UserInfoRequest {
}
class UserFullInfoRequest {
+String sellerNick
+String sellerId
+String platformId
+String appName
}
class BatchGetUserFullInfoRequest {
-UserFullInfoRequest[] userFullInfoRequests
-boolean needCheckAndRefreshToken
}
BaseHttpRequest <|-- UserInfoRequest
BaseHttpRequest <|-- UserFullInfoRequest
BatchGetUserFullInfoRequest --> UserFullInfoRequest : "包含"
```

**图示来源**  
- [UserInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/UserInfoRequest.java#L0-L17)
- [BatchGetUserFullInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserFullInfoRequest.java#L0-L27)

**本节来源**  
- [UserInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/UserInfoRequest.java#L0-L17)
- [BatchGetUserFullInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserFullInfoRequest.java#L0-L27)

### 响应对象设计
响应对象的设计遵循统一的规范，实现了`Serializable`接口以支持跨服务序列化传输。`UserInfoResponse`和`UserFullInfoResponse`分别代表用户基本信息和完整信息的响应结构。

`UserInfoResponse`包含用户的核心信息，如`sellerId`、`sellerNick`、`topSession`等，并包含多个时间字段如`orderCycleEnd`（订购到期时间）、`authDeadLine`（授权到期时间）等。该响应还包含`ayMultiTags`字段，用于支持多店铺场景下的标签管理。

`UserFullInfoResponse`则提供了更全面的用户数据，除了基本信息外，还包括登录统计（`logincountPc`、`mauMp`等）、授权状态（`topStatus`、`isNeedauth`）、店铺信息（`mallName`）等。该响应还包含专业版到期时间`professionalOrderCycleEnd`等高级特性字段。

```mermaid
classDiagram
class UserInfoResponse {
+String sellerId
+String sellerNick
+String topSession
+LocalDateTime orderCycleEnd
+LocalDateTime authDeadLine
+String[] ayMultiTags
+Boolean hasNeedAuth
}
class UserFullInfoResponse {
+String sellerId
+String sellerNick
+String accessToken
+LocalDateTime orderCycleEnd
+LocalDateTime subdatetime
+Integer logincountPc
+Integer mauMp
+String mallName
+String[] ayMultiTags
+LocalDateTime professionalOrderCycleEnd
}
UserInfoResponse <|-- UserFullInfoResponse : "扩展"
```

**图示来源**  
- [UserInfoResponse.java](file://uac-api/src/main/java/cn/loveapp/uac/response/UserInfoResponse.java#L0-L75)
- [UserFullInfoResponse.java](file://uac-api/src/main/java/cn/loveapp/uac/response/UserFullInfoResponse.java#L0-L147)

**本节来源**  
- [UserInfoResponse.java](file://uac-api/src/main/java/cn/loveapp/uac/response/UserInfoResponse.java#L0-L75)
- [UserFullInfoResponse.java](file://uac-api/src/main/java/cn/loveapp/uac/response/UserFullInfoResponse.java#L0-L147)

### 领域对象设计
领域对象位于`domain`包下，用于在服务间传递业务数据。`UserExtInfoDTO`和`UserSettingDTO`是两个核心的领域对象。

`UserExtInfoDTO`用于封装用户的扩展信息，包含`sellerId`、`sellerNick`、`storeId`等标识信息，以及`pullStatus`（拉数据状态）、`apiStatus`（API状态）等运行时状态。该对象还包含`products`列表，用于记录用户开通的产品业务。

`UserSettingDTO`采用键值对结构设计，包含`key`（设置名）、`value`（设置值）和`description`（描述）三个字段，为用户配置管理提供了灵活的数据结构。

```mermaid
classDiagram
class UserExtInfoDTO {
+String sellerId
+String sellerNick
+String storeId
+String topStatus
+Integer pullStatus
+Integer apiStatus
+LocalDateTime pullStartDateTime
+LocalDateTime pullEndDateTime
+String[] products
}
class UserSettingDTO {
+String key
+String value
+String description
}
```

**图示来源**  
- [UserExtInfoDTO.java](file://uac-api/src/main/java/cn/loveapp/uac/domain/UserExtInfoDTO.java#L0-L86)
- [UserSettingDTO.java](file://uac-api/src/main/java/cn/loveapp/uac/domain/UserSettingDTO.java#L0-L27)

**本节来源**  
- [UserExtInfoDTO.java](file://uac-api/src/main/java/cn/loveapp/uac/domain/UserExtInfoDTO.java#L0-L86)
- [UserSettingDTO.java](file://uac-api/src/main/java/cn/loveapp/uac/domain/UserSettingDTO.java#L0-L27)

## 服务接口契约定义
`UserCenterInnerApiService`接口是uac-api模块的核心服务契约，使用Spring Cloud OpenFeign注解定义了远程调用的API。该接口通过`@FeignClient`注解指定服务名称、主机地址和路径前缀，实现了声明式的HTTP客户端。

该接口定义了多种用户信息操作方法，包括：
- `getUserInfo`：获取用户基本信息
- `getUserFullInfo`：获取用户全部信息
- `batchGetUserInfo`：批量获取用户基本信息
- `batchGetUserFullInfo`：批量获取用户全部信息
- `getTopSession`：获取用户的访问令牌

所有方法均返回`CommonApiResponse<T>`泛型包装类型，确保了统一的响应格式。请求参数使用`@RequestBody`注解标注，并配合`@Valid`实现参数校验。方法签名清晰地表达了业务意图，如`rebuildUserInfo`用于重建用户信息，`batchSettingUpdate`用于批量更新用户配置。

```mermaid
classDiagram
class UserCenterInnerApiService {
+getTopSession(UserInfoRequest) CommonApiResponse~UserInfoResponse~
+listUserByVipInfo(ListUserByVipInfoRequest) CommonApiResponse~ListUserByVipInfoResponse~
+batchGetTopSession(BatchRequest~UserInfoRequest~) CommonApiResponse~UserInfoResponse[]~
+rebuildUserInfo(UserInfoRequest) CommonApiResponse~UserInfoResponse~
+batchUpdateUserCacheInfo(BatchUpdateUserCacheInfoRequest) CommonApiResponse~BatchUpdateUserCacheInfoResponse~
+batchGetUserCacheInfo(BatchGetUserCacheInfoRequest) CommonApiResponse~UserCacheInfoResponse[]~
+getTopSessionByMemberId(UserinfoTopSessionMemberRequest) CommonApiResponse~UserInfoTopSessionMemberResponse~
+getUserInfo(UserInfoRequest) CommonApiResponse~UserInfoResponse~
+getUserFullInfo(UserFullInfoRequest) CommonApiResponse~UserFullInfoResponse~
+batchGetUserFullInfo(BatchGetUserFullInfoRequest) CommonApiResponse~UserFullInfoResponse[]~
+getUserInfoByMemberId(UserInfoMemberRequest) CommonApiResponse~UserInfoMemberResponse~
+batchGetUserInfo(UserInfoRequest[]) CommonApiResponse~UserInfoResponse[]~
+batchSettingUpdate(BatchSettingUpdateRequest) CommonApiResponse~Void~
+batchSettingGet(BatchSettingGetRequest) CommonApiResponse~UserSettingDTO[]~
+batchUsersSettingGet(BatchUsersSettingGetRequest) CommonApiResponse~BatchUsersSettingGetResponse[]~
+userSettingCopy(UserSettingCopyRequest) CommonApiResponse~Void~
+batchMultiuserTagUpdate(BatchMultiUserTagUpdateRequest) CommonApiResponse~Void~
+getUserShopInfo(UserShopInfoGetRequest) CommonApiResponse~UserShopInfoGetResponse~
+getUserByMallName(UserInfoRequest) CommonApiResponse~UserFullInfoResponse~
+batchGetUserLoginInfo(UserInfoRequest[]) CommonApiResponse~UserInfoResponse[]~
}
```

**图示来源**  
- [UserCenterInnerApiService.java](file://uac-api/src/main/java/cn/loveapp/uac/service/UserCenterInnerApiService.java#L0-L192)

**本节来源**  
- [UserCenterInnerApiService.java](file://uac-api/src/main/java/cn/loveapp/uac/service/UserCenterInnerApiService.java#L0-L192)

## 异常体系设计

### 异常层次结构
uac-api模块定义了清晰的异常继承体系。`BaseException`作为所有业务异常的基类，继承自Java的`Exception`类，并扩展了`code`字段用于表示错误码。该类提供了两个构造函数，支持仅包含错误码和消息的异常创建，以及包含底层异常的链式异常创建。

`UserException`继承自`BaseException`，专门用于处理用户相关的业务异常。这种分层设计使得异常处理更加结构化，便于在服务调用链中进行统一的异常捕获和处理。

```mermaid
classDiagram
class Exception {
}
class BaseException {
-int code
+BaseException(int, String)
+BaseException(int, String, Throwable)
+int getCode()
+void setCode(int)
}
class UserException {
+UserException(int, String)
}
Exception <|-- BaseException
BaseException <|-- UserException
```

**图示来源**  
- [BaseException.java](file://uac-api/src/main/java/cn/loveapp/uac/exception/BaseException.java#L0-L29)
- [UserException.java](file://uac-api/src/main/java/cn/loveapp/uac/exception/UserException.java#L0-L13)

### 错误码定义
`ApiCode`枚举类定义了系统级的错误码和对应的消息。目前包含`NO_EXIST_USER`（用户不存在）和`AUTH_EXPIRE_USER`（授权过期）两个错误码。每个错误码包含一个整数型的`code`和字符串型的`message`，通过`code()`和`message()`方法对外提供访问。

这种枚举式错误码设计确保了错误信息的统一管理和类型安全，避免了魔法数字的使用，提高了代码的可维护性。

```mermaid
classDiagram
class ApiCode {
+NO_EXIST_USER(40001, "用户不存在")
+AUTH_EXPIRE_USER(40002, "授权过期")
+int code()
+String message()
}
```

**图示来源**  
- [ApiCode.java](file://uac-api/src/main/java/cn/loveapp/uac/code/ApiCode.java#L0-L29)

**本节来源**  
- [BaseException.java](file://uac-api/src/main/java/cn/loveapp/uac/exception/BaseException.java#L0-L29)
- [UserException.java](file://uac-api/src/main/java/cn/loveapp/uac/exception/UserException.java#L0-L13)
- [ApiCode.java](file://uac-api/src/main/java/cn/loveapp/uac/code/ApiCode.java#L0-L29)

## 实际使用示例
以下是一个典型的API使用场景：服务消费者通过`UserCenterInnerApiService`接口获取用户信息。

```mermaid
sequenceDiagram
participant Consumer as "服务消费者"
participant Feign as "Feign客户端"
participant Service as "用户中心服务"
Consumer->>Feign : 调用getUserInfo(request)
Feign->>Service : POST /export/uac/user/getUserInfo
Service->>Service : 校验request参数
Service->>Service : 查询用户数据
alt 用户存在
Service-->>Feign : 返回200及UserInfoResponse
Feign-->>Consumer : 返回CommonApiResponse<UserInfoResponse>
else 用户不存在
Service-->>Feign : 返回200及错误码NO_EXIST_USER
Feign-->>Consumer : 抛出UserException
end
Note over Service : 使用ApiCode定义错误码
```

在实际代码中，调用方只需注入`UserCenterInnerApiService`实例，构造`UserInfoRequest`对象，即可通过简单的接口调用获取用户信息。当用户不存在时，服务端会返回`NO_EXIST_USER`错误码，由Feign客户端自动转换为`UserException`抛出，调用方可通过捕获该异常进行相应处理。

**本节来源**  
- [UserCenterInnerApiService.java](file://uac-api/src/main/java/cn/loveapp/uac/service/UserCenterInnerApiService.java#L0-L192)
- [UserControllerTest.java](file://uac-service/src/test/java/cn/loveapp/uac/service/controller/UserControllerTest.java#L158-L184)
- [ApiCode.java](file://uac-api/src/main/java/cn/loveapp/uac/code/ApiCode.java#L0-L29)

## 多平台适配与版本兼容性
uac-api模块通过`platformId`和`appName`字段实现了多平台适配。`platformId`用于标识不同的电商平台（如淘宝、京东、拼多多等），`appName`用于标识不同的应用。这种设计使得同一套API可以服务于多个平台和应用，提高了代码的复用性。

在版本兼容性方面，模块采用了渐进式演进策略。新增字段采用可选方式添加，确保旧版本客户端仍能正常工作。例如，`UserFullInfoResponse`中的`professionalOrderCycleEnd`字段为新增的专业版到期时间，不影响旧版本对基础字段的读取。

接口设计遵循RESTful原则，使用标准的HTTP方法和状态码。同时，通过`@Validated`注解和`MethodInterface`校验分组实现了请求参数的灵活校验，支持不同调用场景下的参数验证需求。

**本节来源**  
- [UserFullInfoResponse.java](file://uac-api/src/main/java/cn/loveapp/uac/response/UserFullInfoResponse.java#L0-L147)
- [MethodInterface.java](file://uac-api/src/main/java/cn/loveapp/uac/request/MethodInterface.java#L0-L20)

## 结论
uac-api模块作为用户中心服务的API契约层，通过精心设计的请求/响应DTO、清晰的服务接口契约和结构化的异常体系，为微服务架构提供了稳定可靠的通信基础。该模块不仅实现了接口的标准化，还通过多平台适配和版本兼容性设计，支持了业务的持续演进。其设计模式可作为其他服务API契约设计的参考范例。