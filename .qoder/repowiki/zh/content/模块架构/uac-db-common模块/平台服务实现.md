# 平台服务实现

<cite>
**本文档中引用的文件**  
- [PlatformUserProductInfoService.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/PlatformUserProductInfoService.java)
- [PddPlatformUserProductInfoServiceImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/impl/PddPlatformUserProductInfoServiceImpl.java)
- [TaoPlatformUserProductInfoServiceImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/impl/TaoPlatformUserProductInfoServiceImpl.java)
- [Ali1688PlatformUserProductInfoServiceImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/impl/Ali1688PlatformUserProductInfoServiceImpl.java)
- [DefaultPlatformUserProductInfoService.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/impl/DefaultPlatformUserProductInfoService.java)
- [UserRepository.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/UserRepository.java)
- [UserBo.java](file://uac-common/src/main/java/cn/loveapp/uac/common/bo/UserBo.java)
- [UserProductInfo.java](file://uac-common/src/main/java/cn/loveapp/uac/common/entity/UserProductInfo.java)
</cite>

## 目录
1. [引言](#引言)
2. [核心接口设计](#核心接口设计)
3. [多平台实现类分析](#多平台实现类分析)
4. [默认实现的作用](#默认实现的作用)
5. [数据访问协调机制](#数据访问协调机制)
6. [业务场景调用链路](#业务场景调用链路)
7. [设计模式应用](#设计模式应用)
8. [结论](#结论)

## 引言
本文档深入探讨`uac-db-common`模块中`PlatformUserProductInfoService`接口及其针对不同电商平台的实现类的设计与实现机制。重点分析拼多多、淘宝、1688等平台的具体实现方式，阐述其如何通过统一接口实现差异化数据持久化逻辑，并展示策略模式在多平台适配中的实际应用。

## 核心接口设计

`PlatformUserProductInfoService`是多平台用户产品信息处理的核心接口，定义了跨平台一致的数据访问契约。该接口继承自`CommonPlatformHandler`，具备平台识别能力。

```mermaid
classDiagram
class PlatformUserProductInfoService {
<<interface>>
+getUserInfo(UserBo, UserRepository, String, String) UserProductInfo
+getUserShopInfo(String, String, String) UserShopInfoMapping
+getUserShopInfo(String, String, String, String) UserShopInfoMapping[]
}
class CommonPlatformHandler {
<<interface>>
+getPlatformId() String
}
PlatformUserProductInfoService --|> CommonPlatformHandler
```

**图示来源**  
- [PlatformUserProductInfoService.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/PlatformUserProductInfoService.java#L15-L48)

**本节来源**  
- [PlatformUserProductInfoService.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/PlatformUserProductInfoService.java#L1-L50)

## 多平台实现类分析

各电商平台通过继承`DefaultPlatformUserProductInfoService`并重写`getUserInfo`和`getPlatformId`方法，实现平台专属逻辑。

### 拼多多实现类

`PddPlatformUserProductInfoServiceImpl`针对拼多多平台特性，优先使用`sellerNick`或`sellerId`进行用户信息查询，并调用`queryBySellerNick`或`queryBySellerIdStr`等DAO方法。

```mermaid
flowchart TD
Start([获取用户信息]) --> CheckNick{"sellerNick存在?"}
CheckNick --> |是| QueryByNick["userRepository.queryBySellerNick()"]
CheckNick --> |否| CheckSellerId{"sellerId存在?"}
CheckSellerId --> |是| QueryBySellerId["userRepository.queryBySellerIdStr()"]
CheckSellerId --> |否| CheckAppId{"sellerAppId存在?"}
CheckAppId --> |是| QueryByAppId["userRepository.queryByAppId()"]
CheckAppId --> |否| CheckMemberId{"memberId存在?"}
CheckMemberId --> |是| QueryByMemberId["userRepository.queryByMemberId()"]
CheckMemberId --> |否| ReturnNull["返回 null"]
QueryByNick --> End([返回 UserProductInfo])
QueryBySellerId --> End
QueryByAppId --> End
QueryByMemberId --> End
ReturnNull --> End
```

**图示来源**  
- [PddPlatformUserProductInfoServiceImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/impl/PddPlatformUserProductInfoServiceImpl.java#L15-L45)

**本节来源**  
- [PddPlatformUserProductInfoServiceImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/impl/PddPlatformUserProductInfoServiceImpl.java#L1-L45)

### 淘宝实现类

`TaoPlatformUserProductInfoServiceImpl`与拼多多实现逻辑相似，但使用`queryBySellerId`而非`queryBySellerIdStr`，体现平台间细微差异。

**本节来源**  
- [TaoPlatformUserProductInfoServiceImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/impl/TaoPlatformUserProductInfoServiceImpl.java#L1-L40)

### 1688实现类

`Ali1688PlatformUserProductInfoServiceImpl`同样遵循相同模式，使用标准`queryBySellerId`方法，适用于1688及淘工厂等平台。

**本节来源**  
- [Ali1688PlatformUserProductInfoServiceImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/impl/Ali1688PlatformUserProductInfoServiceImpl.java#L1-L43)

## 默认实现的作用

`DefaultPlatformUserProductInfoService`作为所有平台实现的基类，提供了通用的用户信息查询逻辑，并根据应用类型（如分销应用）调整查询策略。

```mermaid
flowchart TD
Start([获取用户信息]) --> CheckApp{"appName为分销?"}
CheckApp --> |是| ByDistributeLogic["按分销逻辑查询"]
CheckApp --> |否| ByStandardLogic["按标准逻辑查询"]
subgraph ByDistributeLogic
A1{"sellerId存在?"} --> |是| A2["queryBySellerIdStr"]
A1 --> |否| A3{"sellerNick存在?"} --> |是| A4["queryByShopName"]
A3 --> |否| A5{"sellerAppId存在?"} --> |是| A6["queryByAppId"]
A5 --> |否| A7{"memberId存在?"} --> |是| A8["queryByMemberId"]
A7 --> |否| A9["返回 null"]
end
subgraph ByStandardLogic
B1{"sellerNick存在?"} --> |是| B2["queryBySellerNick"]
B1 --> |否| B3{"sellerId存在?"} --> |是| B4["queryBySellerIdStr"]
B3 --> |否| B5{"sellerAppId存在?"} --> |是| B6["queryByAppId"]
B5 --> |否| B7{"memberId存在?"} --> |是| B8["queryByMemberId"]
B7 --> |否| B9["返回 null"]
end
A2 --> End([返回 UserProductInfo])
A4 --> End
A6 --> End
A8 --> End
A9 --> End
B2 --> End
B4 --> End
B6 --> End
B8 --> End
B9 --> End
```

**图示来源**  
- [DefaultPlatformUserProductInfoService.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/impl/DefaultPlatformUserProductInfoService.java#L15-L73)

**本节来源**  
- [DefaultPlatformUserProductInfoService.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/impl/DefaultPlatformUserProductInfoService.java#L1-L74)

## 数据访问协调机制

服务层通过`UserRepository`接口协调底层DAO组件，实现跨平台数据访问。`UserRepository`定义了多种查询方法，支持按卖家昵称、ID、会员ID、应用ID等多种方式检索用户信息。

```mermaid
classDiagram
class UserRepository {
<<interface>>
+queryBySellerNick(String, boolean, String, String) UserProductInfo
+queryBySellerId(String, boolean, String, String) UserProductInfo
+queryBySellerIdStr(String, boolean, String, String) UserProductInfo
+queryByMemberId(String, String, String) UserProductInfo
+queryByAppId(String, String, String) UserProductInfo
+queryByShopName(String, String, String) UserProductInfo
+insert(UserProductInfo, String, String) int
+update(UserProductInfo, String, String) int
}
class UserProductInfo {
+String sellerNick
+String sellerId
+String memberId
+String appId
+Integer level
+LocalDateTime w1Deadline
}
class UserBo {
+String sellerNick
+String sellerId
+String memberId
+String sellerAppId
+Boolean hasReadTag
+String platformId
+String appType
}
PlatformUserProductInfoService --> UserRepository : "依赖"
DefaultPlatformUserProductInfoService --> UserRepository : "使用"
UserBo --> UserRepository : "参数"
UserRepository --> UserProductInfo : "返回"
```

**图示来源**  
- [UserRepository.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/UserRepository.java#L13-L210)

**本节来源**  
- [UserRepository.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/UserRepository.java#L1-L211)
- [UserBo.java](file://uac-common/src/main/java/cn/loveapp/uac/common/bo/UserBo.java)
- [UserProductInfo.java](file://uac-common/src/main/java/cn/loveapp/uac/common/entity/UserProductInfo.java)

## 业务场景调用链路

以“保存新用户信息”为例，展示完整的服务调用链路：

```mermaid
sequenceDiagram
participant Controller as "控制器层"
participant Service as "UserCenterServiceImpl"
participant PlatformService as "PlatformUserProductInfoService"
participant Impl as "具体实现类"
participant Repository as "UserRepository"
participant DAO as "DAO组件"
participant DB as "数据库"
Controller->>Service : 调用保存用户信息
Service->>PlatformService : 根据platformId获取实现
PlatformService->>Impl : 调用getUserInfo()
Impl->>Repository : 调用queryByXxx()
Repository->>DAO : 执行SQL查询
DAO->>DB : 访问数据库
DB-->>DAO : 返回结果
DAO-->>Repository : 返回UserProductInfo
Repository-->>Impl : 返回结果
Impl-->>PlatformService : 返回用户信息
PlatformService-->>Service : 返回信息
Service->>Repository : 调用insert()
Repository->>DAO : 执行插入SQL
DAO->>DB : 插入数据
DB-->>DAO : 返回影响行数
DAO-->>Repository : 返回结果
Repository-->>Service : 返回成功
Service-->>Controller : 返回响应
```

**图示来源**  
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java)
- [PlatformUserProductInfoService.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/PlatformUserProductInfoService.java)
- [UserRepository.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/UserRepository.java)

**本节来源**  
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java)
- [PlatformUserProductInfoService.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/PlatformUserProductInfoService.java#L1-L50)
- [UserRepository.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/UserRepository.java#L1-L211)

## 设计模式应用

系统通过**策略模式**实现多平台适配。`PlatformUserProductInfoService`作为策略接口，各平台实现类为具体策略，`DefaultPlatformUserProductInfoService`提供通用算法骨架。

此外，结合Spring的`@Service`注解和依赖注入机制，实现了运行时的策略选择，无需显式工厂类即可完成实例化与注入，提升了代码的可扩展性和可维护性。

**本节来源**  
- [PlatformUserProductInfoService.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/PlatformUserProductInfoService.java)
- [DefaultPlatformUserProductInfoService.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/impl/DefaultPlatformUserProductInfoService.java)
- [PddPlatformUserProductInfoServiceImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/impl/PddPlatformUserProductInfoServiceImpl.java)
- [TaoPlatformUserProductInfoServiceImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/service/impl/TaoPlatformUserProductInfoServiceImpl.java)

## 结论
`PlatformUserProductInfoService`及其多平台实现类通过策略模式实现了高度可扩展的多平台适配架构。各实现类继承默认逻辑并重写平台标识，结合`UserRepository`统一数据访问接口，有效协调了DAO与Repository组件，确保了跨平台数据操作的一致性与灵活性。该设计显著提升了系统的可维护性，便于未来新增电商平台支持。