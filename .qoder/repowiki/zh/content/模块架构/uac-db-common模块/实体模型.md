# 实体模型

<cite>
**本文档中引用的文件**  
- [UserProductInfo.java](file://uac-common/src/main/java/cn/loveapp/uac/common/entity/UserProductInfo.java)
- [AyMultiUserTag.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/AyMultiUserTag.java)
- [OrderSearch.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/OrderSearch.java)
- [TradePddAuth.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/TradePddAuth.java)
- [UserProductinfo1688Dao.xml](file://uac-db-common/src/main/resources/mapper/UserProductinfo1688Dao.xml)
- [AyMultiUserTagDao.xml](file://uac-db-common/src/main/resources/mapper/AyMultiUserTagDao.xml)
- [OrderSearchDao.xml](file://uac-db-common/src/main/resources/mapper/OrderSearchDao.xml)
</cite>

## 目录
1. [引言](#引言)
2. [核心实体类设计原则](#核心实体类设计原则)
3. [UserProductInfo 实体类详解](#userproductinfo-实体类详解)
4. [AyMultiUserTag 实体类详解](#aymultiusertag-实体类详解)
5. [OrderSearch 实体类详解](#ordersearch-实体类详解)
6. [TradePddAuth 实体类详解](#tradepddauth-实体类详解)
7. [实体类与数据库映射关系](#实体类与数据库映射关系)
8. [实体间关联关系分析](#实体间关联关系分析)
9. [Lombok 注解应用](#lombok-注解应用)
10. [多平台扩展支持设计](#多平台扩展支持设计)
11. [序列化与缓存应用](#序列化与缓存应用)

## 引言
本文档旨在全面阐述 `uac-db-common` 模块中核心实体类的设计原则与实现细节。重点分析 `UserProductInfo`、`AyMultiUserTag`、`OrderSearch` 和 `TradePddAuth` 等实体类的字段定义、数据类型、与数据库的映射关系，以及它们在系统中的作用。通过分析 MyBatis 映射文件，揭示实体类与数据库表结构的对应关系，并探讨实体类如何通过设计模式支持多电商平台的扩展需求。

## 核心实体类设计原则
`uac-db-common` 模块中的实体类遵循统一的设计原则，以确保数据一致性、可维护性和可扩展性。这些原则包括：
- **单一职责**：每个实体类专注于表示一个特定的业务概念或数据库表。
- **数据封装**：使用私有字段和公共访问器（通过 Lombok 生成）来封装数据。
- **注解驱动映射**：利用 MyBatis 的 XML 映射文件将实体字段与数据库列进行精确映射。
- **可扩展性**：通过字段预留和组合模式支持不同电商平台的特有需求。
- **业务逻辑内聚**：在实体类中封装简单的业务逻辑和数据转换方法。

**本文档中引用的文件**  
- [UserProductInfo.java](file://uac-common/src/main/java/cn/loveapp/uac/common/entity/UserProductInfo.java)
- [AyMultiUserTag.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/AyMultiUserTag.java)
- [OrderSearch.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/OrderSearch.java)
- [TradePddAuth.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/TradePddAuth.java)

## UserProductInfo 实体类详解
`UserProductInfo` 实体类是用户中心服务的核心，用于存储用户在各个电商平台上的产品订购和授权信息。

### 字段含义与数据类型
该实体类包含大量字段，主要分为以下几类：
- **用户标识**：`userId`（字符串）、`nick`（用户昵称）。
- **平台与应用**：`platformId`（平台标识）、`appName`（应用名称）。
- **时间信息**：`createDate`（创建时间）、`lastactivedt`（最后活跃时间）、`orderCycleEnd`（订购到期时间）、`w1Deadline`（授权到期时间）等，均使用 `LocalDateTime` 类型。
- **用户等级与状态**：`vipflag`（用户等级）、`isNeedauth`（是否需要授权）、`isSilent`（是否沉默用户）。
- **授权信息**：`topsessionkey`（TOP会话密钥）、`toprefreshkey`（TOP刷新密钥）。
- **登录统计**：`logincountPc`（PC登录次数）、`mauPc`（30天内PC登录次数）等。
- **多店铺信息**：`isMany`（是否多店铺）、`isMain`（是否主店铺）、`corpId`（主店铺ID）。
- **平台特有字段**：如 `memberId`（1688会员ID）、`jdUid`（京东UID）、`shopId`（TikTok店铺ID）等，体现了对多平台的支持。

### 业务常量与方法
该类定义了大量静态常量，如用户等级常量（`LEVEL_ZERO`, `LEVEL_ONE`）、活跃点常量（`ACTIVE_POINT_PC`, `ACTIVE_POINT_WW`）等。同时提供了多个静态工厂方法（如 `of`）和实例方法，用于对象创建、状态更新和数据转换（如 `toUserRedisEntity` 方法用于生成缓存实体）。

**实体来源**  
- [UserProductInfo.java](file://uac-common/src/main/java/cn/loveapp/uac/common/entity/UserProductInfo.java#L19-L650)

## AyMultiUserTag 实体类详解
`AyMultiUserTag` 实体类用于管理爱用多店用户的标签信息。

### 字段含义与数据类型
- **主键与标识**：`id`（自增主键）、`sellerId`（用户ID）、`storeId`（平台）、`appName`（应用）。
- **标签信息**：`tags`（标签字符串，用逗号分隔）、`type`（标签类型，关联 `AyMultiTagType` 枚举）。
- **时间戳**：`gmtCreate`（创建时间）、`gmtModified`（修改时间），使用 `LocalDateTime` 类型。

### 业务方法
该类提供了 `addTags` 和 `deleteTags` 两个核心方法，用于安全地添加和删除标签。这些方法处理了空值检查和字符串拼接逻辑，确保了数据的完整性。

**实体来源**  
- [AyMultiUserTag.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/AyMultiUserTag.java#L1-L97)

## OrderSearch 实体类详解
`OrderSearch` 实体类用于存储用户的订单搜索记录。

### 字段含义与数据类型
- **订单标识**：`id`（主键）、`orderId`（子订单号）、`bizOrderId`（订单号）。
- **用户信息**：`userId`（用户ID）、`nick`（用户昵称）。
- **商品与应用信息**：`articleName`（应用名称）、`articleCode`（应用收费代码）、`itemCode`（收费项目代码）。
- **时间信息**：`createdate`（订单创建时间）、`orderCycleStart`（订购周期开始时间）、`orderCycleEnd`（订购周期结束时间）、`maturitydt`（服务到期时间）。
- **费用信息**：`fee`（原价）、`promFee`（优惠）、`refundFee`（退款）、`totalPayFee`（实付），单位均为分。
- **订单类型与来源**：`bizType`（订单类型）、`from`（来自PC、WW、Mobile等）、`tjplatform`（统计平台）。

**实体来源**  
- [OrderSearch.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/OrderSearch.java#L1-L86)

## TradePddAuth 实体类详解
`TradePddAuth` 实体类用于存储拼多多平台的授权信息。

### 字段含义与数据类型
- **主键**：`id`（整型主键）。
- **拼多多信息**：`pddId`（拼多多ID）、`pddName`（拼多多昵称）。
- **交易信息**：`nickTrade`（交易店铺）。
- **时间与备注**：`createTime`（创建时间，使用 `LocalDate` 类型）、`remark`（备注）。

该实体类结构相对简单，专注于拼多多平台的特定授权数据。

**实体来源**  
- [TradePddAuth.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/TradePddAuth.java#L1-L29)

## 实体类与数据库映射关系
实体类通过 MyBatis 的 XML 映射文件与数据库表进行映射。

### UserProductInfo 映射示例
在 `UserProductinfo1688Dao.xml` 文件中，`insert` 语句将 `UserProductInfo` 对象的字段精确映射到数据库表的列。例如，Java 字段 `createDate` 映射到数据库列 `createdate`，`logincountPc` 映射到 `logincount_pc`。这体现了驼峰命名法到下划线命名法的转换。

```mermaid
erDiagram
USER_PRODUCTINFO_1688 {
string user_id
string user_id_str
string nick
datetime createdate
datetime lastactivedt
datetime order_cycle_end
string topsessionkey
string toprefreshkey
integer vipflag
integer logincount_pc
integer logincount_mp
string otherroleid
string memberid
}
```

**图示来源**  
- [UserProductinfo1688Dao.xml](file://uac-db-common/src/main/resources/mapper/UserProductinfo1688Dao.xml#L1-L24)

### AyMultiUserTag 映射分析
在 `AyMultiUserTagDao.xml` 文件中，`resultMap` 明确定义了 Java 字段与数据库列的映射关系，如 `sellerId` -> `seller_id`，`gmtCreate` -> `gmt_create`。其 `insertOrUpdate` 语句使用了 MySQL 的 `ON DUPLICATE KEY UPDATE` 语法，实现了插入或更新的原子操作。

```mermaid
erDiagram
AY_MULTI_USER_TAG {
bigint id PK
string seller_id UK
string store_id UK
string app_name UK
string tags
int type
datetime gmt_create
datetime gmt_modified
}
```

**图示来源**  
- [AyMultiUserTagDao.xml](file://uac-db-common/src/main/resources/mapper/AyMultiUserTagDao.xml#L1-L70)

### OrderSearch 映射分析
`OrderSearchDao.xml` 文件中的 `resultMap` 同样定义了详细的字段映射。其查询语句（如 `queryBySellerNickAndOrderCycleStartAndOrderCycleEndAndItemCode`）展示了如何根据复杂的业务条件（用户昵称、订购周期、商品代码）进行数据检索。

```mermaid
erDiagram
ORDER_SEARCH {
int id PK
string user_id
string nick
string article_code
string item_code
datetime order_cycle_start
datetime order_cycle_end
int biz_type
int total_pay_fee
string order_id
string biz_order_id
datetime maturitydt
}
```

**图示来源**  
- [OrderSearchDao.xml](file://uac-db-common/src/main/resources/mapper/OrderSearchDao.xml#L1-L265)

## 实体间关联关系分析
虽然这些实体类在代码层面没有通过 JPA 注解建立显式的外键关联，但它们在业务逻辑上存在紧密的联系：
- **一对多关系**：一个 `UserProductInfo` 可以对应多个 `OrderSearch` 记录（一个用户有多笔订单）。
- **一对一关系**：一个 `UserProductInfo` 可能对应一个 `TradePddAuth` 记录（如果用户在拼多多平台有授权）。
- **聚合关系**：`UserProductInfo` 可以包含多个 `AyMultiUserTag`（一个用户有多个标签）。

这些关系主要通过共享的 `userId` 或 `nick` 字段在业务层进行关联查询。

## Lombok 注解应用
所有实体类都广泛使用了 Lombok 注解来简化代码：
- **@Data**：自动生成 getter、setter、`toString`、`equals` 和 `hashCode` 方法，极大地减少了样板代码。
- **@ToString**：明确生成 `toString` 方法，便于调试和日志输出。
- **@Builder**：虽然在提供的代码片段中未直接看到，但此类设计通常会使用 `@Builder` 来支持流畅的构建模式。

这些注解使得实体类的代码更加简洁、易读，同时降低了因手动编写方法而引入错误的风险。

**实体来源**  
- [UserProductInfo.java](file://uac-common/src/main/java/cn/loveapp/uac/common/entity/UserProductInfo.java#L19)
- [AyMultiUserTag.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/AyMultiUserTag.java#L15)
- [OrderSearch.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/OrderSearch.java#L15)
- [TradePddAuth.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/TradePddAuth.java#L15)

## 多平台扩展支持设计
实体类的设计巧妙地支持了多平台扩展：
- **字段预留**：`UserProductInfo` 类中包含了大量平台特有字段（如 `jdUid`、`shopId`），通过在同一个表中存储所有平台的数据，避免了为每个平台创建独立表的复杂性。
- **组合模式**：通过 `platformId` 和 `appName` 字段，可以区分不同平台和应用的数据，实现了数据的逻辑隔离。
- **继承替代**：没有为每个平台创建 `UserProductInfo` 的子类，而是采用组合和字段扩展的方式，这简化了 DAO 层的设计，使得一个通用的 DAO 可以操作所有平台的数据。

这种设计在灵活性和复杂性之间取得了良好的平衡。

## 序列化与缓存应用
实体类在序列化和缓存中扮演着关键角色：
- **序列化**：`UserProductInfo` 类实现了 `Serializable` 接口（通过 `TradePddAuth` 的示例推断），使其可以被序列化，便于在网络中传输或持久化。
- **缓存转换**：`UserProductInfo` 提供了多个 `toXXXRedisEntity` 方法（如 `toUserRedisEntity`、`toPddUserRedisEntity`），将复杂的业务实体转换为专为 Redis 缓存设计的扁平化实体 `UserRedisEntity`。这优化了缓存的存储结构和访问效率，是实体类在缓存层应用的典型示例。