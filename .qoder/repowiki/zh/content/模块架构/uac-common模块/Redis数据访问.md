# Redis数据访问

<cite>
**本文档引用文件**   
- [BaseHashRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseHashRedisRepository.java)
- [BaseValueRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseValueRedisRepository.java)
- [RedisRepositoryBase.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/RedisRepositoryBase.java)
- [HashCacheRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/HashCacheRepository.java)
- [ValueRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/ValueRedisRepository.java)
- [OpenUserRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/repository/OpenUserRedisRepository.java)
- [RedisConfiguration.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/RedisConfiguration.java)
- [CacheTimeoutConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/CacheTimeoutConfig.java)
</cite>

## 目录
1. [简介](#简介)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 简介
本文档详细描述了基于Spring Data Redis的Redis数据访问层实现。重点分析了`dao/redis`包中的分层封装设计，包括基础抽象类`BaseHashRedisRepository`和`BaseValueRedisRepository`，以及具体实现`HashCacheRepository`和`ValueRedisRepository`。文档说明了如何通过泛型和模板方法模式提供类型安全的Redis操作，涵盖字符串、哈希、列表等数据结构的CRUD操作。同时，详细描述了业务专用仓库类如`OpenUserRedisRepository`的设计，以及缓存键命名规范、序列化策略、过期时间管理机制等。

## 项目结构
`uac-common`模块中的`dao/redis`包实现了完整的Redis数据访问层，采用分层设计模式。该层分为基础抽象层、接口定义层和业务实现层三个主要部分。

```mermaid
graph TD
subgraph "Redis数据访问层"
Base[RedisRepositoryBase]
BHRR[BaseHashRedisRepository]
BVRR[BaseValueRedisRepository]
HCR[HashCacheRepository]
VRR[ValueRedisRepository]
OUPR[OpenUserRedisRepository]
OMRR[OperationManageRedisRepository]
SARR[SearchActiveRedisRepository]
UMRR[UserManageRedisRepositoryHashRedisRepository]
end
Base --> BHRR
Base --> BVRR
BHRR --> HCR
BVRR --> VRR
HCR --> OUPR
HCR --> OMRR
HCR --> SARR
HCR --> UMRR
VRR --> OUPR
```

**图示来源**
- [BaseHashRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseHashRedisRepository.java)
- [BaseValueRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseValueRedisRepository.java)
- [HashCacheRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/HashCacheRepository.java)
- [ValueRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/ValueRedisRepository.java)

**本节来源**
- [BaseHashRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseHashRedisRepository.java)
- [BaseValueRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseValueRedisRepository.java)

## 核心组件
Redis数据访问层的核心组件包括基础抽象类、接口定义和具体实现。`RedisRepositoryBase`提供了通用的Redis操作基础，`BaseHashRedisRepository`和`BaseValueRedisRepository`分别封装了哈希和值类型的操作。`HashCacheRepository`和`ValueRedisRepository`接口定义了统一的操作契约，而`OpenUserRedisRepository`等具体实现类则针对特定业务场景提供了专用的缓存操作。

**本节来源**
- [BaseHashRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseHashRedisRepository.java#L19-L146)
- [BaseValueRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseValueRedisRepository.java#L17-L117)
- [RedisRepositoryBase.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/RedisRepositoryBase.java)

## 架构概述
Redis数据访问层采用分层架构设计，实现了关注点分离和代码复用。该架构分为四个层次：基础层、抽象层、接口层和实现层。

```mermaid
graph TD
subgraph "实现层"
OUPR[OpenUserRedisRepository]
OMRR[OperationManageRedisRepository]
SARR[SearchActiveRedisRepository]
UMRR[UserManageRedisRepositoryHashRedisRepository]
end
subgraph "接口层"
HCR[HashCacheRepository]
VRR[ValueRedisRepository]
end
subgraph "抽象层"
BHRR[BaseHashRedisRepository]
BVRR[BaseValueRedisRepository]
end
subgraph "基础层"
Base[RedisRepositoryBase]
end
Base --> BHRR
Base --> BVRR
BHRR --> HCR
BVRR --> VRR
HCR --> OUPR
HCR --> OMRR
HCR --> SARR
HCR --> UMRR
VRR --> OUPR
```

**图示来源**
- [BaseHashRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseHashRedisRepository.java)
- [BaseValueRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseValueRedisRepository.java)
- [HashCacheRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/HashCacheRepository.java)
- [ValueRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/ValueRedisRepository.java)

## 详细组件分析
### 基础组件分析
#### 基础抽象类分析
`RedisRepositoryBase`类提供了Redis操作的基础功能，包括获取实际的`StringRedisTemplate`实例、检查集合是否存在和清除集合等通用方法。该类通过`stringRedisTemplateMap`映射管理多个Redis连接模板，支持根据应用名称选择不同的Redis实例。

```mermaid
classDiagram
class RedisRepositoryBase {
+Map~String, StringRedisTemplate~ stringRedisTemplateMap
+static final ObjectMapper OBJECT_MAPPER
+RedisRepositoryBase(Map~String, StringRedisTemplate~)
+Boolean hasExistCollection(String, String)
+Boolean cleanCollection(String, String)
+StringRedisTemplate getRealStringRedisTemplate(String)
}
RedisRepositoryBase <|-- BaseHashRedisRepository
RedisRepositoryBase <|-- BaseValueRedisRepository
```

**图示来源**
- [RedisRepositoryBase.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/RedisRepositoryBase.java#L19-L87)

**本节来源**
- [RedisRepositoryBase.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/RedisRepositoryBase.java#L19-L87)

#### 哈希操作抽象类分析
`BaseHashRedisRepository`类封装了Redis哈希数据结构的操作，实现了`HashCacheRepository`接口。该类提供了添加、更新、删除和查找哈希条目的方法，支持单个条目和批量操作。

```mermaid
classDiagram
class BaseHashRedisRepository {
-static LoggerHelper LOGGER
-DistributeUserProcessService distributeUserProcessService
+BaseHashRedisRepository(Map~String, StringRedisTemplate~)
+boolean add(String, String, T, String)
+boolean putAll(String, T, String, String)
+boolean put(String, String, String, String)
+boolean delete(String, String, String)
+String find(String, String, String)
+String[] find(String, String[], String)
+T findAll(String, Class~T~, String, String)
}
BaseHashRedisRepository --|> HashCacheRepository
BaseHashRedisRepository --> RedisRepositoryBase
```

**图示来源**
- [BaseHashRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseHashRedisRepository.java#L19-L146)

**本节来源**
- [BaseHashRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseHashRedisRepository.java#L19-L146)

#### 值操作抽象类分析
`BaseValueRedisRepository`类封装了Redis值数据结构的操作，实现了`ValueRedisRepository`接口。该类提供了添加、删除和查找值条目的方法，支持设置过期时间和条件写入。

```mermaid
classDiagram
class BaseValueRedisRepository {
-static final LoggerHelper LOGGER
+BaseValueRedisRepository(Map~String, StringRedisTemplate~)
+boolean add(String, T, String)
+boolean add(String, T, String, long, TimeUnit)
+Boolean setIfAbsent(String, T, String, long, TimeUnit)
+boolean delete(String, String)
+T find(String, Class~T~, String)
}
BaseValueRedisRepository --|> ValueRedisRepository
BaseValueRedisRepository --> RedisRepositoryBase
```

**图示来源**
- [BaseValueRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseValueRedisRepository.java#L17-L117)

**本节来源**
- [BaseValueRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseValueRedisRepository.java#L17-L117)

### 业务专用仓库分析
#### OpenUserRedisRepository分析
`OpenUserRedisRepository`是针对用户开通场景的专用Redis仓库实现。该类继承自`BaseValueRedisRepository`，提供了设置和删除用户开通信息的方法，支持条件写入和过期时间设置。

```mermaid
classDiagram
class OpenUserRedisRepository {
-static final LoggerHelper LOGGER
-StringRedisTemplate stringRedisTemplate
+OpenUserRedisRepository(StringRedisTemplate, StringRedisTemplate)
+Boolean setIfAbsent(String, String, String, String, String, String, long, TimeUnit)
+Boolean delete(String, String, String, String, String)
-String initRedisOpenUserKey(String, String, String, String, String)
+String initCollection(String, String, String)
}
OpenUserRedisRepository --> BaseValueRedisRepository
```

**图示来源**
- [OpenUserRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/repository/OpenUserRedisRepository.java#L19-L94)

**本节来源**
- [OpenUserRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/repository/OpenUserRedisRepository.java#L19-L94)

## 依赖分析
Redis数据访问层的依赖关系清晰，体现了良好的分层设计原则。基础组件不依赖上层组件，而上层组件依赖基础组件。

```mermaid
graph TD
RedisConfiguration[RedisConfiguration] --> StringRedisTemplate[stringTradeRedisTemplate]
RedisConfiguration --> StringRedisTemplate[stringItemRedisTemplate]
RedisConfiguration --> StringRedisTemplate[stringShophelperRedisTemplate]
RedisConfiguration --> StringRedisTemplate[stringDistributeRedisTemplate]
RedisRepositoryBase --> ObjectMapper[OBJECT_MAPPER]
BaseHashRedisRepository --> RedisRepositoryBase
BaseValueRedisRepository --> RedisRepositoryBase
BaseHashRedisRepository --> DistributeUserProcessService[distributeUserProcessService]
OpenUserRedisRepository --> BaseValueRedisRepository
OpenUserRedisRepository --> StringRedisTemplate[stringRedisTemplate]
```

**图示来源**
- [RedisConfiguration.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/RedisConfiguration.java)
- [BaseHashRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseHashRedisRepository.java)
- [BaseValueRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseValueRedisRepository.java)
- [OpenUserRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/repository/OpenUserRedisRepository.java)

**本节来源**
- [RedisConfiguration.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/RedisConfiguration.java)
- [BaseHashRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseHashRedisRepository.java)
- [BaseValueRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseValueRedisRepository.java)

## 性能考虑
Redis数据访问层在设计时考虑了性能优化。通过使用`StringRedisTemplate`和`ObjectMapper`进行高效的序列化和反序列化，减少了数据转换的开销。连接池配置通过`RedisConfiguration`类进行优化，支持多个Redis实例的连接管理。对于批量操作，提供了`putAll`方法以减少网络往返次数。过期时间管理通过`CacheTimeoutConfig`类集中配置，避免了硬编码的过期时间。

**本节来源**
- [RedisConfiguration.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/RedisConfiguration.java)
- [CacheTimeoutConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/CacheTimeoutConfig.java)

## 故障排除指南
在使用Redis数据访问层时，可能遇到以下常见问题：

1. **连接问题**：检查`RedisConfiguration`中的连接配置是否正确，包括主机、端口、密码和数据库索引。
2. **序列化问题**：确保要存储的对象可以被`ObjectMapper`正确序列化，避免包含无法序列化的字段。
3. **空值处理**：在调用`find`方法时，应检查返回值是否为null，以处理缓存未命中的情况。
4. **并发问题**：使用`setIfAbsent`方法可以避免并发写入时的数据覆盖问题。
5. **内存溢出**：合理设置缓存过期时间，避免缓存无限增长导致内存溢出。

**本节来源**
- [BaseHashRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseHashRedisRepository.java)
- [BaseValueRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseValueRedisRepository.java)
- [RedisConfiguration.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/RedisConfiguration.java)

## 结论
Redis数据访问层通过分层设计和抽象封装，提供了类型安全、易于使用的Redis操作接口。基础抽象类和接口定义实现了代码复用和关注点分离，而业务专用仓库类则针对特定场景提供了定制化的缓存操作。该设计不仅提高了代码的可维护性和可扩展性，还通过合理的性能优化和错误处理机制，确保了系统的稳定性和高效性。