# uac-common模块

<cite>
**本文档引用文件**  
- [AppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/AppConfig.java)
- [DateUtil.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/DateUtil.java)
- [HttpUtil.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/HttpUtil.java)
- [MathUtil.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/MathUtil.java)
- [NetworkException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/NetworkException.java)
- [CacheWriteException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/CacheWriteException.java)
- [AuthService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AuthService.java)
- [AppStoreService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AppStoreService.java)
- [BaseHashRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseHashRedisRepository.java)
</cite>

## 目录
1. [简介](#简介)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概览](#架构概览)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考量](#性能考量)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 简介
uac-common模块是爱用UAC用户中心服务组的基础公共组件库，为整个系统提供统一的基础设施支持。该模块通过封装多平台配置、通用工具类、异常体系、平台适配接口和Redis数据访问层，实现了跨业务模块的代码复用与标准化，有效降低了各业务模块的开发复杂度并保证了系统一致性。

## 项目结构
uac-common模块采用分层包结构组织代码，主要包括配置管理、工具类、异常处理、平台适配和数据访问等核心功能模块。

```mermaid
graph TD
subgraph "uac-common模块"
config[config<br>配置管理]
utils[utils<br>工具类]
exception[exception<br>异常体系]
platform[platform/api<br>平台适配]
dao[dao/redis<br>数据访问]
end
```

**图示来源**  
- [AppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/AppConfig.java)
- [DateUtil.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/DateUtil.java)
- [NetworkException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/NetworkException.java)
- [AuthService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AuthService.java)
- [BaseHashRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseHashRedisRepository.java)

**本节来源**  
- [uac-common/pom.xml](file://uac-common/pom.xml#L0-L38)
- 项目结构信息

## 核心组件
uac-common模块提供五大核心能力：多电商平台配置管理（config包）、通用工具类（utils包）、业务异常体系（exception包）、多平台认证授权抽象（platform/api包）以及Redis数据访问封装（dao/redis包）。这些组件共同构成了系统的基础设施层，为上层业务模块提供稳定可靠的支持。

**本节来源**  
- [AppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/AppConfig.java#L0-L19)
- [DateUtil.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/DateUtil.java#L0-L197)
- [NetworkException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/NetworkException.java#L0-L21)
- [AuthService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AuthService.java#L0-L60)
- [BaseHashRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseHashRedisRepository.java#L0-L147)

## 架构概览
uac-common模块作为基础公共库，被多个上层模块依赖，形成统一的技术栈。

```mermaid
graph TD
subgraph "上层模块"
uac_service[uac-service]
uac_service_common[uac-service-common]
uac_job[uac-authorization-job]
end
subgraph "公共组件"
uac_common[uac-common<br>基础公共组件库]
end
uac_service --> uac_common
uac_service_common --> uac_common
uac_job --> uac_common
style uac_common fill:#f9f,stroke:#333
```

**图示来源**  
- [uac-service/pom.xml](file://uac-service/pom.xml#L0-L50)
- [uac-service-common/pom.xml](file://uac-service-common/pom.xml#L0-L32)
- [uac-job/pom.xml](file://uac-job/pom.xml#L0-L20)
- [uac-common/pom.xml](file://uac-common/pom.xml#L0-L38)

## 详细组件分析

### 配置管理组件分析
uac-common模块通过config包实现了对多电商平台的统一配置管理，包括淘宝、京东、拼多多等主流平台。

```mermaid
classDiagram
class AppConfig {
+String appkey
+String appSecret
+String sessionkey
+String refreshTokenUrl
+String authCodeTokenUrl
+String redirectUrl
}
class PDDTradeAppConfig
class TaoBaoTradeAppConfig
class JdTradeERPAppConfig
class Ali1688AppConfig
class DoudianAppConfig
AppConfig <|-- PDDTradeAppConfig
AppConfig <|-- TaoBaoTradeAppConfig
AppConfig <|-- JdTradeERPAppConfig
AppConfig <|-- Ali1688AppConfig
AppConfig <|-- DoudianAppConfig
```

**图示来源**  
- [AppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/AppConfig.java#L0-L19)
- 项目结构中的config包内容

**本节来源**  
- [AppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/AppConfig.java#L0-L19)

### 工具类组件分析
uac-common模块提供了丰富的工具类，涵盖日期处理、HTTP通信和数学运算等常用功能。

#### 日期工具类
```mermaid
classDiagram
class DateUtil {
+String SECOND_TIME_TYPE
+String MILLISECOND_TIME_TYPE
+DateTimeFormatter FORMATTER_DATETIME
+DateTimeFormatter FORMATTER_DATE
+parseDateString(String) Date
+parseString(String) LocalDateTime
+parseLocalDateTime(LocalDateTime, String) Long
+convertLocalDateTimetoString(LocalDateTime) String
+compareTwoDatetimeOfDay(LocalDateTime, LocalDateTime) int
}
```

**图示来源**  
- [DateUtil.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/DateUtil.java#L0-L197)

#### HTTP工具类
```mermaid
classDiagram
class HttpUtil {
-LoggerHelper LOGGER
+sendJsonPost(String, HashMap~String,String~, Map~String,String~) String
}
```

**图示来源**  
- [HttpUtil.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/HttpUtil.java#L0-L72)

#### 数学工具类
```mermaid
classDiagram
class MathUtil {
+parseDouble(String) Double
+parseLong(Long) String
+parseStringByLong(String) Long
+parseInteger(Long) Integer
+parseLongByDouble(Long) Double
+parseString(String) Integer
}
```

**图示来源**  
- [MathUtil.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/MathUtil.java#L0-L55)

**本节来源**  
- [DateUtil.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/DateUtil.java#L0-L197)
- [HttpUtil.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/HttpUtil.java#L0-L72)
- [MathUtil.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/MathUtil.java#L0-L55)

### 异常体系组件分析
uac-common模块建立了完善的业务异常体系，用于统一处理各类业务场景下的异常情况。

```mermaid
classDiagram
class Exception
class NetworkException
class CacheWriteException
class DbWriteException
class StorageException
class UserNeedAuthException
class TaobaoException
class ResendMessageException
Exception <|-- NetworkException
Exception <|-- CacheWriteException
Exception <|-- DbWriteException
Exception <|-- StorageException
Exception <|-- UserNeedAuthException
Exception <|-- TaobaoException
Exception <|-- ResendMessageException
class BaseException
BaseException <|-- CacheWriteException
```

**图示来源**  
- [NetworkException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/NetworkException.java#L0-L21)
- [CacheWriteException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/CacheWriteException.java#L0-L20)
- 项目结构中的exception包内容

**本节来源**  
- [NetworkException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/NetworkException.java#L0-L21)
- [CacheWriteException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/CacheWriteException.java#L0-L20)

### 平台适配层组件分析
uac-common模块通过平台适配层实现了多平台认证授权的统一抽象。

```mermaid
classDiagram
class CommonPlatformHandler
class AuthService {
+decryptToken(String, String, String) String
+encryptToken(String, String, String) String
+getCallbackResultByCode(String, String, String) T
+refreshToken(UserInfoBo, String, String, String) T
+convertUserRedisEntity2UserInfoBo(UserInfoBo, UserRedisEntity, String, String) void
}
class AppStoreService {
+vasSubscribeGet(SellerVasSubscribeGetRequest, String, String) SellerVasSubscribeGetResponse
+vasSubscribeSearch(SellerVasSubscSearchRequest, String, String) SellerVasSubscSearchResponse
+vasOrderSearch(SellerVasOrderSearchRequest, String, String) SellerVasOrderSearchResponse
}
CommonPlatformHandler <|-- AuthService
CommonPlatformHandler <|-- AppStoreService
class PddAuthServiceImpl
class TaoAuthServiceImpl
class JdAuthServiceImpl
class KwaishopAuthServiceImpl
class WxshopAuthServiceImpl
AuthService <|-- PddAuthServiceImpl
AuthService <|-- TaoAuthServiceImpl
AuthService <|-- JdAuthServiceImpl
AuthService <|-- KwaishopAuthServiceImpl
AuthService <|-- WxshopAuthServiceImpl
```

**图示来源**  
- [AuthService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AuthService.java#L0-L60)
- [AppStoreService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AppStoreService.java#L0-L40)
- 项目结构中的platform/api/impl包内容

**本节来源**  
- [AuthService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AuthService.java#L0-L60)
- [AppStoreService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AppStoreService.java#L0-L40)

### Redis数据访问组件分析
uac-common模块提供了基于Spring Data Redis的Redis数据访问封装。

```mermaid
classDiagram
class RedisRepositoryBase {
+Map~String, StringRedisTemplate~ stringRedisTemplateMap
+StringRedisTemplate getRealStringRedisTemplate(String)
}
class HashCacheRepository {
+add(String, String, T, String) boolean
+putAll(String, T, String, String) boolean
+put(String, String, String, String) boolean
+delete(String, String, String) boolean
+find(String, String, String) String
+findAll(String, Class~T~, String, String) T
}
class BaseHashRedisRepository~T~ {
-LoggerHelper LOGGER
-DistributeUserProcessService distributeUserProcessService
+add(String, String, T, String) boolean
+putAll(String, T, String, String) boolean
+put(String, String, String, String) boolean
+delete(String, String, String) boolean
+find(String, String, String) String
+find(String, String[], String) String[]
+findAll(String, Class~T~, String, String) T
}
RedisRepositoryBase <|-- BaseHashRedisRepository
HashCacheRepository <|-- BaseHashRedisRepository
```

**图示来源**  
- [BaseHashRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseHashRedisRepository.java#L0-L147)

**本节来源**  
- [BaseHashRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseHashRedisRepository.java#L0-L147)

## 依赖分析
uac-common模块作为基础公共库，被多个上层模块所依赖，形成了清晰的依赖关系。

```mermaid
graph TD
uac_common[uac-common]
uac_api[uac-api] --> uac_common
uac_domain[uac-domain] --> uac_common
common_spring_boot_web_starter[common-spring-boot-web-starter] --> uac_common
common_platformsdk_starter[common-platformsdk-starter] --> uac_common
rocketmq_client[rocketmq-client] --> uac_common
style uac_common fill:#f9f,stroke:#333
```

**图示来源**  
- [uac-common/pom.xml](file://uac-common/pom.xml#L0-L38)

**本节来源**  
- [uac-common/pom.xml](file://uac-common/pom.xml#L0-L38)

## 性能考量
uac-common模块在设计时充分考虑了性能因素：
- 工具类采用静态方法实现，避免对象创建开销
- Redis操作封装了异常处理，确保缓存访问的稳定性
- HTTP工具类设置了合理的超时配置，防止请求堆积
- 日期处理使用了线程安全的DateTimeFormatter
- 提供了批量操作接口以减少网络往返次数

## 故障排除指南
当使用uac-common模块遇到问题时，可参考以下排查步骤：
1. 检查配置类是否正确继承AppConfig并设置了必要参数
2. 查看日志中是否有NetworkException等异常记录
3. 确认Redis连接是否正常，检查BaseHashRedisRepository相关日志
4. 验证平台适配实现类是否正确实现了AuthService和AppStoreService接口
5. 检查工具类方法的输入参数是否符合预期格式

**本节来源**  
- [NetworkException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/NetworkException.java#L0-L21)
- [CacheWriteException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/CacheWriteException.java#L0-L20)
- [BaseHashRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/base/BaseHashRedisRepository.java#L0-L147)

## 结论
uac-common模块作为爱用UAC用户中心服务组的基础公共组件库，通过提供配置管理、工具类、异常体系、平台适配和数据访问等多维度公共能力，有效降低了各业务模块的开发复杂度，保证了系统的一致性和稳定性。该模块的设计体现了良好的分层架构思想和代码复用原则，为整个系统的可维护性和可扩展性奠定了坚实基础。