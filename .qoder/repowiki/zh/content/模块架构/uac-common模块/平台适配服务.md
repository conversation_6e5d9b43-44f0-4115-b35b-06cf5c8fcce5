# 平台适配服务

<cite>
**本文档引用文件**  
- [AuthService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AuthService.java)
- [AppStoreService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AppStoreService.java)
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java)
- [PddAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java)
- [DoudianAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/DoudianAuthServiceImpl.java)
- [JdAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/JdAuthServiceImpl.java)
</cite>

## 目录
1. [简介](#简介)
2. [核心组件](#核心组件)
3. [适配器设计模式分析](#适配器设计模式分析)
4. [认证授权流程](#认证授权流程)
5. [接口方法详解](#接口方法详解)
6. [微服务治理策略](#微服务治理策略)
7. [集成示例](#集成示例)
8. [结论](#结论)

## 简介
平台适配服务为多电商平台（淘宝、拼多多、京东、抖音等）提供统一的认证授权和应用商店操作抽象。通过适配器设计模式，实现了对不同平台API的封装，使上层业务无需关心具体平台的实现细节。本服务支持新平台的快速接入，同时提供了服务发现、负载均衡、熔断降级等微服务治理能力。

## 核心组件

平台适配服务的核心组件包括AuthService和AppStoreService两个接口，以及BaseAuthServiceImpl基类和各平台具体实现类。

**核心接口与类**
- `AuthService`: 统一认证授权接口
- `AppStoreService`: 应用商店操作接口
- `BaseAuthServiceImpl`: 通用认证逻辑基类
- `PddAuthServiceImpl`: 拼多多平台实现
- `TaoAuthServiceImpl`: 淘宝平台实现
- `DoudianAuthServiceImpl`: 抖音平台实现
- `JdAuthServiceImpl`: 京东平台实现

**Section sources**
- [AuthService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AuthService.java)
- [AppStoreService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AppStoreService.java)

## 适配器设计模式分析

平台适配服务采用适配器设计模式，通过定义统一的接口规范，为不同电商平台提供一致的访问方式。

```mermaid
classDiagram
class AuthService {
+decryptToken(accessToken, platformId, appName) String
+encryptToken(token, platformId, appName) String
+getCallbackResultByCode(code, platformId, appName) T
+refreshToken(userInfoBo, refreshToken, platformId, appName) T
+convertUserRedisEntity2UserInfoBo(userInfoBo, userRedisEntity, platformId, appName) void
}
class AppStoreService {
+vasSubscribeGet(sellerVasSubscribeGetRequest, platformId, appName) SellerVasSubscribeGetResponse
+vasSubscribeSearch(sellerVasSubscSearchRequest, platformId, appName) SellerVasSubscSearchResponse
+vasOrderSearch(vasOrderSearchRequest, platformId, appName) SellerVasOrderSearchResponse
}
class BaseAuthServiceImpl {
-appConfigMap Map~String, AppConfig~
-distributeConfig DistributeConfig
-distributeUserProcessService DistributeUserProcessService
+getRetryCount() Integer
+reGetAccessTokenWithRefreshToken(refreshToken, platformId, headers, tClass, appName) T
+decryptToken(token, platformId, appName) String
+encryptToken(token, platformId, appName) String
+executeRefreshTokenNetwork(refreshToken, platformId, headers, appName) String
+getCallbackResultByCode(code, platformId, headers, tClass, appName) T
+getActualConfig(appName) AppConfig
+convertUserRedisEntity2UserInfoBo(userInfoBo, userRedisEntity, platformId, appName) void
+setDistributeSellerNickBySellerId(userInfoBo) void
}
AuthService <|-- BaseAuthServiceImpl : "继承"
BaseAuthServiceImpl <|-- PddAuthServiceImpl : "继承"
BaseAuthServiceImpl <|-- TaoAuthServiceImpl : "继承"
BaseAuthServiceImpl <|-- DoudianAuthServiceImpl : "继承"
BaseAuthServiceImpl <|-- JdAuthServiceImpl : "继承"
AppStoreService <|-- PddAppStoreServiceImpl : "继承"
AppStoreService <|-- TaoAppStoreServiceImpl : "继承"
AppStoreService <|-- DoudianAppStoreServiceImpl : "继承"
AppStoreService <|-- YouzanAppStoreServiceImpl : "继承"
```

**Diagram sources**
- [AuthService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AuthService.java)
- [AppStoreService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AppStoreService.java)
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java)

### 通用性与特殊性平衡

接口设计通过以下方式平衡通用性与特殊性：
1. **通用方法**: 定义跨平台通用的操作方法
2. **平台参数**: 通过platformId参数区分不同平台
3. **应用参数**: 通过appName参数支持同一平台的不同应用场景
4. **抽象基类**: 提供通用实现，子类可重写特定逻辑
5. **配置驱动**: 通过AppConfig实现平台配置的外部化

这种设计使得新平台接入只需实现相应接口并注册配置，无需修改核心逻辑。

**Section sources**
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java)

## 认证授权流程

平台适配服务的认证授权流程包括授权码获取、Token交换、用户信息拉取等步骤。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant AuthService as "AuthService"
participant Platform as "电商平台"
participant Redis as "Redis缓存"
Client->>AuthService : 请求授权URL
AuthService->>Platform : 重定向到平台授权页面
Platform->>Client : 用户授权后返回授权码(code)
Client->>AuthService : 提交授权码
AuthService->>AuthService : 验证平台和应用配置
AuthService->>Platform : 使用code换取AccessToken
Platform-->>AuthService : 返回AccessToken和RefreshToken
AuthService->>AuthService : 加密Token并设置过期时间
AuthService->>Redis : 存储用户授权信息
AuthService-->>Client : 返回授权结果
Client->>AuthService : 后续API调用携带AccessToken
AuthService->>AuthService : 解密AccessToken验证有效性
AuthService->>Client : 处理业务请求
```

**Diagram sources**
- [AuthService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AuthService.java)
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java)

### Token刷新机制

Token刷新机制确保用户授权的持续有效性：

```mermaid
sequenceDiagram
participant AuthService as "AuthService"
participant Platform as "电商平台"
participant Redis as "Redis缓存"
AuthService->>AuthService : 检测AccessToken即将过期
AuthService->>Platform : 使用RefreshToken请求新Token
Platform-->>AuthService : 返回新的AccessToken和RefreshToken
AuthService->>AuthService : 加密新Token
AuthService->>Redis : 更新用户授权信息
AuthService-->>AuthService : 完成Token刷新
```

**Diagram sources**
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java)

## 接口方法详解

### AuthService接口方法

| 方法名 | 参数 | 返回值 | 异常情况 | 说明 |
|-------|------|--------|----------|------|
| decryptToken | accessToken, platformId, appName | String | Exception | 解密AccessToken |
| encryptToken | token, platformId, appName | String | Exception | 加密Token |
| getCallbackResultByCode | code, platformId, appName | T extends RefreshTokenCallbackResult | Exception | 通过授权码获取回调结果 |
| refreshToken | userInfoBo, refreshToken, platformId, appName | T extends RefreshTokenCallbackResult | Exception | 刷新Token |
| convertUserRedisEntity2UserInfoBo | userInfoBo, userRedisEntity, platformId, appName | void | 无 | 将Redis实体转换为业务对象 |

**Section sources**
- [AuthService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AuthService.java)

### AppStoreService接口方法

| 方法名 | 参数 | 返回值 | 异常情况 | 说明 |
|-------|------|--------|----------|------|
| vasSubscribeGet | sellerVasSubscribeGetRequest, platformId, appName | SellerVasSubscribeGetResponse | 无 | 查询订购关系 |
| vasSubscribeSearch | sellerVasSubscSearchRequest, platformId, appName | SellerVasSubscSearchResponse | 无 | 查询订购订阅 |
| vasOrderSearch | vasOrderSearchRequest, platformId, appName | SellerVasOrderSearchResponse | 无 | 查询订购订单记录 |

**Section sources**
- [AppStoreService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AppStoreService.java)

## 微服务治理策略

平台适配服务在微服务治理方面采用了多种策略：

```mermaid
flowchart TD
A[服务发现] --> B[负载均衡]
B --> C[熔断降级]
C --> D[重试机制]
D --> E[配置管理]
E --> F[监控告警]
A --> |Nacos| A1[动态服务注册与发现]
B --> |Ribbon| B1[客户端负载均衡]
C --> |Hystrix| C1[熔断器模式]
D --> |RetryTemplate| D1[指数退避重试]
E --> |Apollo| E1[外部化配置管理]
F --> |Prometheus| F1[指标监控]
```

**Diagram sources**
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java)

### 服务发现与负载均衡
- 使用Nacos实现服务注册与发现
- 通过Ribbon实现客户端负载均衡
- 支持多实例部署，提高服务可用性

### 熔断降级
- 采用Hystrix实现熔断机制
- 当平台API响应超时或失败率达到阈值时自动熔断
- 提供降级策略，保证核心功能可用

### 重试机制
- 在BaseAuthServiceImpl中实现网络请求重试
- 可配置重试次数（默认5次）
- 采用指数退避策略，避免雪崩效应

**Section sources**
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java)

## 集成示例

以下是平台适配服务的典型集成示例：

```mermaid
flowchart TD
A[业务系统] --> B[调用AuthService]
B --> C{平台判断}
C --> |拼多多| D[PddAuthServiceImpl]
C --> |淘宝| E[TaoAuthServiceImpl]
C --> |抖音| F[DoudianAuthServiceImpl]
C --> |京东| G[JdAuthServiceImpl]
D --> H[拼多多API]
E --> I[淘宝API]
F --> J[抖音API]
G --> K[京东API]
H --> L[返回结果]
I --> L
J --> L
K --> L
L --> M[业务系统处理]
```

**Diagram sources**
- [PddAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java)
- [DoudianAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/DoudianAuthServiceImpl.java)
- [JdAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/JdAuthServiceImpl.java)

### 代码集成示例
```java
// 获取拼多多认证服务
AuthService pddAuthService = authServiceFactory.getAuthService("PDD");

// 使用授权码获取Token
RefreshTokenCallbackResult result = pddAuthService.getCallbackResultByCode(
    "auth_code_123", 
    "PDD", 
    "TRADE"
);

// 刷新Token
RefreshTokenCallbackResult refreshResult = pddAuthService.refreshToken(
    userInfoBo,
    "refresh_token_456",
    "PDD",
    "TRADE"
);
```

**Section sources**
- [AuthService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AuthService.java)

## 结论
平台适配服务通过适配器设计模式成功实现了对多电商平台的统一抽象。BaseAuthServiceImpl提供了通用的认证逻辑，各平台实现类则处理特定平台的差异。接口设计平衡了通用性与特殊性，支持新平台的快速接入。服务还集成了服务发现、负载均衡、熔断降级等微服务治理策略，确保了系统的稳定性和可扩展性。该架构为电商平台集成提供了灵活、可靠的基础支撑。