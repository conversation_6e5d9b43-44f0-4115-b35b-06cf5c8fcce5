# 异常处理

<cite>
**本文档引用的文件**  
- [CacheWriteException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/CacheWriteException.java)
- [DbWriteException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/DbWriteException.java)
- [NetworkException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/NetworkException.java)
- [ResendMessageException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/ResendMessageException.java)
- [StorageException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/StorageException.java)
- [TaobaoException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/TaobaoException.java)
- [UserNeedAuthException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/UserNeedAuthException.java)
- [ErrorCode.java](file://uac-common/src/main/java/cn/loveapp/uac/common/code/ErrorCode.java)
- [ApiCode.java](file://uac-common/src/main/java/cn/loveapp/uac/common/code/ApiCode.java)
</cite>

## 目录
1. [引言](#引言)
2. [异常体系结构](#异常体系结构)
3. [核心异常类分析](#核心异常类分析)
4. [错误码体系设计](#错误码体系设计)
5. [异常处理最佳实践](#异常处理最佳实践)
6. [全局异常处理器实现](#全局异常处理器实现)
7. [跨服务调用异常传递](#跨服务调用异常传递)
8. [结论](#结论)

## 引言
本项目构建了一套分层、可扩展的异常处理体系，旨在统一管理服务运行过程中可能出现的各种异常情况。该体系以`BaseException`为基类，通过继承机制构建了涵盖缓存、数据库、网络、业务逻辑等多个维度的异常分类。结合`ApiCode`与`ErrorCode`枚举类，实现了错误码的标准化定义与国际化支持，并通过全局异常处理器将异常信息转化为一致的API响应格式。

## 异常体系结构
系统采用分层异常模型，所有自定义异常均继承自`cn.loveapp.uac.exception.BaseException`，形成统一的异常处理链。在`uac-common`模块中，`exception`包定义了多种具体异常类型，分别对应不同的故障场景和处理策略。

```mermaid
classDiagram
class BaseException {
+int code
+String message
+BaseException(int code, String msg)
+BaseException(int code, String msg, Throwable ex)
}
class CacheWriteException {
+CacheWriteException(int code, String msg)
+CacheWriteException(int code, String msg, Throwable ex)
}
class DbWriteException {
+DbWriteException(int code, String msg)
+DbWriteException(int code, String msg, Throwable ex)
}
class StorageException {
+StorageException(int code, String msg)
+StorageException(int code, String msg, Throwable ex)
}
class TaobaoException {
+TaobaoException(int code, String msg)
+TaobaoException(int code, String msg, Throwable ex)
}
class UserNeedAuthException {
+UserNeedAuthException(int code, String msg)
}
class NetworkException {
+NetworkException()
+NetworkException(String messages)
+NetworkException(String messages, Throwable cause)
}
class ResendMessageException {
-int delayLevel
-Boolean forceHandleFlag
+ResendMessageException(String message, int delayLevel)
+ResendMessageException(int delayLevel, String message, Throwable throwable)
+getDelayLevel() int
+isForceHandleFlag() Boolean
+setForceHandleFlag(Boolean forceHandleFlag) void
}
BaseException <|-- CacheWriteException
BaseException <|-- DbWriteException
BaseException <|-- StorageException
BaseException <|-- TaobaoException
BaseException <|-- UserNeedAuthException
Exception <|-- NetworkException
RuntimeException <|-- ResendMessageException
```

**图示来源**  
- [CacheWriteException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/CacheWriteException.java)
- [DbWriteException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/DbWriteException.java)
- [StorageException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/StorageException.java)
- [TaobaoException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/TaobaoException.java)
- [UserNeedAuthException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/UserNeedAuthException.java)
- [NetworkException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/NetworkException.java)
- [ResendMessageException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/ResendMessageException.java)

**本节来源**  
- [uac-common/src/main/java/cn/loveapp/uac/common/exception](file://uac-common/src/main/java/cn/loveapp/uac/common/exception)

## 核心异常类分析

### 基础写操作异常
此类异常继承自`BaseException`，用于标识数据持久化过程中的失败。

#### CacheWriteException
表示缓存写入失败的异常。当系统尝试将数据写入Redis或其他缓存系统时发生错误，应抛出此异常。其设计意图是明确区分缓存层与数据库层的故障。

**本节来源**  
- [CacheWriteException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/CacheWriteException.java)

#### DbWriteException
表示数据库写入失败的异常。适用于INSERT、UPDATE、DELETE等操作执行失败的场景，通常由数据库约束冲突、连接中断等原因引起。

**本节来源**  
- [DbWriteException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/DbWriteException.java)

#### StorageException
广义的存储异常，可用于文件系统、对象存储等非数据库存储介质的写入失败场景。

**本节来源**  
- [StorageException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/StorageException.java)

### 业务特定异常
针对特定业务流程定义的异常类型，便于上层进行精细化处理。

#### TaobaoException
淘宝平台相关操作失败时抛出的异常。例如调用淘宝开放平台API返回错误码或网络请求失败时使用。

**本节来源**  
- [TaobaoException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/TaobaoException.java)

#### UserNeedAuthException
用户授权相关异常，表示当前用户未完成授权或授权已过期，需要重新授权。常用于OAuth流程中判断用户状态。

**本节来源**  
- [UserNeedAuthException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/UserNeedAuthException.java)

### 系统级异常
不继承自`BaseException`，而是直接继承自Java标准异常类，用于系统底层或框架级错误。

#### NetworkException
网络通信异常，继承自`Exception`。用于封装HTTP请求超时、连接拒绝等网络层问题。

**本节来源**  
- [NetworkException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/NetworkException.java)

#### ResendMessageException
消息重发异常，继承自`RuntimeException`。包含`delayLevel`（延迟等级）和`forceHandleFlag`（强制处理标志）两个附加属性，用于控制消息队列的重试策略。

**本节来源**  
- [ResendMessageException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/ResendMessageException.java)

## 错误码体系设计
错误码体系由`ErrorCode`和`ApiCode`两个核心枚举类构成，遵循统一的编码规范。

### ErrorCode 枚举结构
`ErrorCode`接口内嵌两个枚举：`BaseCode`和`SubCode`，分别代表基础错误码和子错误码。

```java
public interface ErrorCode {
    enum BaseCode implements ErrorCode {
        SUCCESS(0, "成功"),
        SYS_ERR(1, "程序异常"),
        PARAMS_ERR(2, "参数错误"),
        REQUEST_ERR(3, "请求错误"),
        SIGN_PRIAVET_KEY_ERR(4, "签名秘钥错误"),
        PACK_PRIAVET_KEY_ERR(5, "数据加密密钥错误"),
        SIGN_ERR(6, "签名错误"),
        SYS_MAINTAIN(7, "系统维护"),
        SYS_DB_ERR(8, "DB错误"),
        SYS_CACHE_ERR(9, "Cache错误");
        
        private Integer code;
        private String message;
        // getter/setter
    }

    enum SubCode implements ErrorCode {
        RECORD_NOEXIST("record.noexist", "记录不存在"),
        FULLINFO_ERR("fullinfo.err", "fullinfo结果未获取到"),
        TRADE_ERR("trade.err", "trade结果异常"),
        SUCCESS("success", "成功");
        
        private String code;
        private String message;
        // getter/setter
    }
}
```

### 设计原则
1. **分层编码**：`BaseCode`使用整型数字码，`SubCode`使用字符串形式的点分命名，便于分类管理。
2. **国际化支持**：每个错误码都配有中文描述，可通过资源文件扩展多语言支持。
3. **HTTP状态码映射**：
   - `SUCCESS(0)` → HTTP 200
   - `PARAMS_ERR(2)` → HTTP 400
   - `SIGN_ERR(6)` → HTTP 401
   - `SYS_ERR(1)` → HTTP 500
4. **可扩展性**：通过接口+枚举的组合方式，允许其他模块定义自己的错误码并实现`ErrorCode`接口。

**本节来源**  
- [ErrorCode.java](file://uac-common/src/main/java/cn/loveapp/uac/common/code/ErrorCode.java)

## 异常处理最佳实践

### 异常抛出规范
- 业务逻辑中应使用`throw new XxxException(code, message)`形式抛出异常。
- 包装底层异常时，应传递原始`Throwable`，如`new DbWriteException(8, "数据库写入失败", e)`。
- 对于需要重试的消息场景，使用`ResendMessageException`并设置合适的`delayLevel`。

### 异常捕获策略
- 在服务层应捕获具体业务异常并做相应处理。
- 控制器层不建议捕获异常，应交由全局异常处理器统一处理。
- 日志记录应在异常抛出点或全局处理器中完成，避免重复记录。

### 异常日志记录
- 所有`BaseException`子类异常应在全局处理器中记录ERROR级别日志。
- 日志内容应包含错误码、错误信息、堆栈跟踪及上下文参数。
- 对于`ResendMessageException`，还需记录`delayLevel`和`forceHandleFlag`。

### API响应转换
异常应被转换为统一的`ErrorResponse`格式：
```json
{
  "code": 1,
  "msg": "程序异常",
  "subCode": "sys.err",
  "subMsg": "系统内部错误"
}
```

## 全局异常处理器实现
通过Spring的`@ControllerAdvice`注解实现全局异常拦截，典型实现逻辑如下：

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Controller as "业务控制器"
participant GlobalHandler as "全局异常处理器"
participant Logger as "日志系统"
participant Response as "API响应"
Client->>Controller : 发起请求
Controller-->>GlobalHandler : 抛出异常
GlobalHandler->>Logger : 记录异常日志
Logger-->>GlobalHandler : 完成记录
GlobalHandler->>Response : 转换为ErrorResponse
Response-->>Client : 返回标准化错误响应
```

**图示来源**  
- [uac-common/src/main/java/cn/loveapp/uac/common/exception](file://uac-common/src/main/java/cn/loveapp/uac/common/exception)
- [uac-common/src/main/java/cn/loveapp/uac/common/code/ErrorCode.java](file://uac-common/src/main/java/cn/loveapp/uac/common/code/ErrorCode.java)

**本节来源**  
- [CacheWriteException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/CacheWriteException.java)
- [DbWriteException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/DbWriteException.java)
- [TaobaoException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/TaobaoException.java)
- [UserNeedAuthException.java](file://uac-common/src/main/java/cn/loveapp/uac/common/exception/UserNeedAuthException.java)
- [ErrorCode.java](file://uac-common/src/main/java/cn/loveapp/uac/common/code/ErrorCode.java)

## 跨服务调用异常传递
在微服务架构中，异常信息应在服务间保持一致性：
1. 服务A调用服务B，若B返回错误响应，A应解析其`code`和`msg`。
2. 根据业务需要决定是否封装为本地异常（如`new TaobaoException(response.getCode(), response.getMsg())`）。
3. 避免将底层异常直接暴露给前端，应进行适当的抽象和转换。
4. 使用`ErrorResponse`作为跨服务通信的标准错误格式，确保上下游系统兼容。

**本节来源**  
- [uac-common/src/main/java/cn/loveapp/uac/common/code/ErrorResponse.java](file://uac-common/src/main/java/cn/loveapp/uac/common/code/ErrorResponse.java)
- [uac-common/src/main/java/cn/loveapp/uac/common/exception](file://uac-common/src/main/java/cn/loveapp/uac/common/exception)

## 结论
uac-common模块构建了一个结构清晰、职责分明的异常处理体系。通过继承`BaseException`实现了异常的统一管理，结合`ErrorCode`枚举提供了标准化的错误码支持。该体系不仅满足了本地异常处理的需求，还通过`ErrorResponse`机制保障了跨服务调用时的错误信息一致性。建议在开发中严格遵循异常分类规范，合理使用错误码，确保系统具备良好的可观测性和可维护性。