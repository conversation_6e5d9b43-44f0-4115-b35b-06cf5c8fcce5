# 工具类

<cite>
**本文档中引用的文件**  
- [DateUtil.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/DateUtil.java)
- [HttpUtil.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/HttpUtil.java)
- [MathUtil.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/MathUtil.java)
- [RocketMqQueueHelper.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/RocketMqQueueHelper.java)
- [SerializedPhpParser.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/SerializedPhpParser.java)
</cite>

## 目录
1. [简介](#简介)
2. [日期工具类 DateUtil](#日期工具类-dateutil)
3. [HTTP 工具类 HttpUtil](#http-工具类-httputil)
4. [数学工具类 MathUtil](#数学工具类-mathutil)
5. [RocketMQ 消息队列助手 RocketMqQueueHelper](#rocketmq-消息队列助手-rocketmqqueuehelper)
6. [PHP 序列化数据解析器 SerializedPhpParser](#php-序列化数据解析器-serializedphpparser)
7. [线程安全性与性能特征](#线程安全性与性能特征)
8. [最佳实践](#最佳实践)

## 简介
`uac-common` 模块中的 `utils` 包提供了多个通用工具类，用于简化日常开发中的常见操作。这些工具类涵盖了日期处理、HTTP 请求、数学计算、消息队列交互以及特殊格式数据解析等场景。本文档将系统性地介绍每个工具类的功能、API 使用方式、实际业务场景示例，并说明其线程安全性、性能特征和使用建议。

## 日期工具类 DateUtil

`DateUtil` 类提供了丰富的日期时间处理功能，包括日期格式化、时间戳转换、日期比较与计算等。该类基于 Java 8 的 `java.time` API 实现，避免了传统 `Date` 和 `Calendar` 类的线程安全问题。

### 主要功能
- **日期解析**：支持将字符串解析为 `LocalDateTime` 或 `Date` 对象。
- **格式化输出**：提供预定义的格式化器（如 `yyyy-MM-dd HH:mm:ss`）用于日期转字符串。
- **时间戳转换**：支持将 `LocalDateTime` 转换为秒级或毫秒级时间戳（基于东八区）。
- **日期计算**：支持对日期进行加减天数、秒数等操作。
- **日期比较**：判断一个时间是否在指定区间内，或是否在当天范围内。

### API 参考
| 方法签名 | 参数说明 | 返回值 | 异常抛出情况 |
|--------|--------|--------|------------|
| `parseString(String s)` | s: 待解析的日期字符串，支持 "yyyy-MM-dd HH:mm:ss" 或 "yyyy-MM-dd" 格式 | 解析后的 `LocalDateTime`，失败返回 null | 无 |
| `parseLocalDateTime(LocalDateTime d, String timeType)` | d: 日期时间对象；timeType: 时间类型（`SECOND_TIME_TYPE` 或 `MILLISECOND_TIME_TYPE`） | 对应的时间戳（Long） | 无 |
| `convertLocalDateTimetoString(LocalDateTime dt)` | dt: 日期时间对象 | 格式化后的字符串（"yyyy-MM-dd HH:mm:ss"） | 无 |
| `calculateCurrentDate(Long d)` | d: 偏移天数（正数为未来，负数为过去） | 当前时间加上偏移天数后的 `LocalDateTime` | 无 |
| `isWithInDay(LocalDateTime dt)` | dt: 待判断的日期时间 | 是否在当天（含零点和23:59:59.999999999） | 无 |

### 使用示例
```java
// 解析日期字符串
LocalDateTime dateTime = DateUtil.parseString("2023-10-01 12:00:00");

// 转换为毫秒时间戳
Long timestamp = DateUtil.parseLocalDateTime(dateTime, DateUtil.MILLISECOND_TIME_TYPE);

// 计算三天后的日期
LocalDateTime futureDate = DateUtil.calculateCustomDay(dateTime, 3L);

// 判断是否为今天
boolean isToday = DateUtil.isWithInDay(dateTime);
```

**Section sources**
- [DateUtil.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/DateUtil.java#L1-L198)

## HTTP 工具类 HttpUtil

`HttpUtil` 类封装了基于 Apache HttpClient 的 HTTP POST 请求功能，主要用于发送 JSON 格式的请求体。该类简化了连接配置、请求头设置和响应处理流程。

### 主要功能
- **JSON POST 请求**：发送 `application/json` 类型的 POST 请求。
- **连接配置**：设置连接超时、读取超时和请求超时均为 10 秒。
- **自定义请求头**：支持传入 `Map<String, String>` 类型的请求头。
- **异常处理**：对空 URL 参数抛出 `IOException`，并在日志中记录错误信息。
- **资源管理**：确保 `HttpClient` 和 `HttpResponse` 在使用后正确关闭。

### API 参考
| 方法签名 | 参数说明 | 返回值 | 异常抛出情况 |
|--------|--------|--------|------------|
| `sendJsonPost(String url, HashMap<String,String> postFields, Map<String, String> headers)` | url: 请求地址；postFields: 请求参数（将被 JSON 序列化）；headers: 自定义请求头 | 服务器返回的响应体字符串 | `IOException`（当 url 为空时） |

### 使用示例
```java
HashMap<String, String> params = new HashMap<>();
params.put("name", "test");
params.put("value", "123");

Map<String, String> headers = new HashMap<>();
headers.put("Authorization", "Bearer token123");

try {
    String response = HttpUtil.sendJsonPost("https://api.example.com/data", params, headers);
    // 处理响应
} catch (IOException e) {
    // 处理异常
}
```

**Section sources**
- [HttpUtil.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/HttpUtil.java#L1-L73)

## 数学工具类 MathUtil

`MathUtil` 类提供了一系列安全的数值类型转换方法，避免了原始类型转换时可能出现的 `NullPointerException` 或 `NumberFormatException`。

### 主要功能
- **字符串转数值**：安全地将字符串解析为 `Double`、`Long`、`Integer` 类型，空值或无效值返回默认值。
- **数值转字符串**：将 `Long` 类型转换为字符串，`null` 或 0 返回 `null`。
- **类型转换**：将 `Long` 转换为 `Integer` 或 `Double`，`null` 或 0 返回 0。

### API 参考
| 方法签名 | 参数说明 | 返回值 | 异常抛出情况 |
|--------|--------|--------|------------|
| `parseDouble(String s)` | s: 待解析的字符串 | 解析后的 `Double`，失败或空返回 0.0 | 无 |
| `parseStringByLong(String s)` | s: 待解析的字符串 | 解析后的 `Long`，失败或空返回 null | 无 |
| `parseInteger(Long l)` | l: 待转换的 `Long` 值 | 转换后的 `Integer`，`null` 或 0 返回 0 | 无 |

### 使用示例
```java
// 安全解析字符串为数字
Double price = MathUtil.parseDouble("99.9");
Integer count = MathUtil.parseInteger(100L);

// 避免空指针
String idStr = MathUtil.parseLong(12345L); // 返回 "12345"
```

**Section sources**
- [MathUtil.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/MathUtil.java#L1-L56)

## RocketMQ 消息队列助手 RocketMqQueueHelper

`RocketMqQueueHelper` 类封装了 RocketMQ 消息的发送逻辑，提供了消息发送、重试机制、延迟消息和用户属性设置等功能。

### 主要功能
- **消息发送**：支持发送任意对象（自动 JSON 序列化）或字符串消息。
- **重试机制**：发送失败时自动重试最多 10 次，每次重试间隔递增（200ms * 重试次数）。
- **延迟消息**：支持通过 `delayTimeLevel` 参数设置延迟级别。
- **用户属性**：支持为消息添加自定义属性（`userProperties`）。
- **日志记录**：发送成功或失败时记录详细日志，便于追踪。

### API 参考
| 方法签名 | 参数说明 | 返回值 | 异常抛出情况 |
|--------|--------|--------|------------|
| `push(String topic, String tag, T pushData, DefaultMQProducer producer)` | topic: 主题；tag: 标签；pushData: 消息内容；producer: 生产者实例 | 发送成功返回 `MessageId`，失败返回 null | 无（内部捕获异常并记录日志） |
| `push(String topic, String tag, T pushData, DefaultMQProducer producer, int retryCount, int delayTimeLevel, Map<String, String> userProperties)` | retryCount: 重试次数；delayTimeLevel: 延迟级别（0为立即发送）；userProperties: 用户自定义属性 | 同上 | 无 |

### 使用示例
```java
// 发送普通消息
String messageId = rocketMqQueueHelper.push("USER_TOPIC", "CREATE", userEvent, producer);

// 发送延迟消息（延迟级别3）
Map<String, String> props = new HashMap<>();
props.put("source", "web");
String delayedId = rocketMqQueueHelper.push("ORDER_TOPIC", "PAY_TIMEOUT", orderData, producer, 10, 3, props);
```

**Section sources**
- [RocketMqQueueHelper.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/RocketMqQueueHelper.java#L1-L168)

## PHP 序列化数据解析器 SerializedPhpParser

`SerializedPhpParser` 类用于解析特定格式的 PHP 序列化字符串，主要用于从遗留系统或第三方接口中提取会话或配置信息。

### 主要功能
- **字符串解析**：解析形如 `key1\|s:5:"value1";key2\|N;` 的 PHP 序列化字符串。
- **键值提取**：根据指定键名提取对应的值，支持 `null` 值（`N`）和字符串值（`s:length:"value"`）。
- **多键提取**：支持一次性提取多个键的值。

### API 参考
| 方法签名 | 参数说明 | 返回值 | 异常抛出情况 |
|--------|--------|--------|------------|
| `parse(String key)` | key: 要提取的键名 | 包含该键值的 `HashMap`，未找到返回 null | 无 |
| `getSessionValue(String keys)` | keys: 多个键名，用分号分隔 | 包含所有请求键值的 `HashMap` | 无 |

### 使用示例
```java
String phpSerialized = "username\\|s:8:\"johndoe\";status\\|N;";
SerializedPhpParser parser = new SerializedPhpParser(phpSerialized);

// 提取单个值
HashMap<String, Object> result = parser.parse("username");
String username = (String) result.get("username"); // "johndoe"

// 提取多个值
HashMap<String, Object> multiResult = parser.getSessionValue("username;status");
```

**Section sources**
- [SerializedPhpParser.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/SerializedPhpParser.java#L1-L76)

## 线程安全性与性能特征
- **DateUtil**：基于 Java 8 的不可变时间类，所有方法均为线程安全。
- **HttpUtil**：`LoggerHelper` 为静态字段，`sendJsonPost` 方法创建新的 `HttpClient` 实例，方法本身线程安全，但频繁调用可能影响性能（建议使用连接池）。
- **MathUtil**：所有方法为静态且无状态，完全线程安全。
- **RocketMqQueueHelper**：`LoggerHelper` 为静态字段，`push` 方法使用传入的 `DefaultMQProducer` 实例（RocketMQ 生产者本身线程安全），方法内部有重试逻辑，线程安全。
- **SerializedPhpParser**：实例化时持有输入字符串，`parse` 方法操作局部变量，线程安全。

## 最佳实践
1. **DateUtil**：优先使用 `LocalDateTime` 而非 `Date`，避免时区问题。
2. **HttpUtil**：对于高频请求，应考虑使用连接池优化性能，避免频繁创建销毁连接。
3. **MathUtil**：在处理可能为空的字符串或数值时，使用此类方法可有效防止 `NullPointerException`。
4. **RocketMqQueueHelper**：生产环境应监控重试次数和失败日志，及时发现消息发送问题。
5. **SerializedPhpParser**：仅用于解析特定格式的遗留数据，不建议在新系统中使用此格式。