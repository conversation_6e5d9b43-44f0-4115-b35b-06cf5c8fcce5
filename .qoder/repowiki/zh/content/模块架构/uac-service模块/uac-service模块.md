# uac-service模块

<cite>
**本文档引用的文件**   
- [UacServiceApplication.java](file://uac-service/src/main/java/cn/loveapp/uac/service/UacServiceApplication.java)
- [UserController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java)
- [CallbackController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/CallbackController.java)
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java)
- [UserRequestInterceptor.java](file://uac-service/src/main/java/cn/loveapp/uac/service/interceptor/UserRequestInterceptor.java)
- [VersionGrayHandler.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/VersionGrayHandler.java)
</cite>

## 目录
1. [简介](#简介)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 简介
uac-service模块作为用户中心服务的核心应用，承担着用户身份认证、信息管理、授权回调等关键业务功能。该模块通过Spring Boot框架构建，实现了基于RESTful API的微服务架构，为上层应用提供统一的用户中心服务能力。模块通过整合uac-api、uac-common、uac-db-common等依赖模块，构建了完整的业务闭环，实现了从请求接收、业务处理到数据持久化的完整流程。

## 项目结构
uac-service模块采用典型的Spring Boot项目结构，主要包含controller、export、interceptor、util、web等包，分别对应控制器层、服务导出层、拦截器、工具类和Web配置。模块通过UacServiceApplication作为Spring Boot启动类，实现了自动配置、组件扫描和依赖注入。

```mermaid
graph TD
subgraph "uac-service"
A[controller]
B[export]
C[interceptor]
D[util]
E[web]
F[UacServiceApplication]
end
A --> F
B --> F
C --> F
D --> F
E --> F
```

**图示来源**
- [UacServiceApplication.java](file://uac-service/src/main/java/cn/loveapp/uac/service/UacServiceApplication.java)

**章节来源**
- [UacServiceApplication.java](file://uac-service/src/main/java/cn/loveapp/uac/service/UacServiceApplication.java)

## 核心组件

uac-service模块的核心组件包括控制器层的UserController和CallbackController、服务导出层的UserCenterServiceImpl、拦截器层的UserRequestInterceptor以及Web配置中的VersionGrayHandler。这些组件协同工作，实现了用户中心服务的核心功能。

**章节来源**
- [UserController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java#L29-L107)
- [CallbackController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/CallbackController.java#L21-L43)
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java#L55-L822)
- [UserRequestInterceptor.java](file://uac-service/src/main/java/cn/loveapp/uac/service/interceptor/UserRequestInterceptor.java#L13-L37)
- [VersionGrayHandler.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/VersionGrayHandler.java)

## 架构概述

uac-service模块采用分层架构设计，包括控制器层、服务层、数据访问层。控制器层负责接收外部HTTP请求，服务层实现业务逻辑，数据访问层负责与数据库交互。模块通过Spring Boot的自动配置机制，实现了组件的自动扫描和依赖注入。

```mermaid
graph TD
A[客户端] --> B[UserController]
A --> C[CallbackController]
B --> D[UserCenterServiceImpl]
C --> D
D --> E[UserPlatformHandleService]
D --> F[UserSettingsRepository]
D --> G[AyMultiUserTagDao]
E --> H[数据库]
F --> H
G --> H
```

**图示来源**
- [UserController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java#L29-L107)
- [CallbackController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/CallbackController.java#L21-L43)
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java#L55-L822)

## 详细组件分析

### 控制器层分析
控制器层包含UserController和CallbackController，负责接收外部HTTP请求并调用后端服务。

#### UserController
UserController处理用户相关的HTTP请求，包括登录、快速登录、刷新用户信息、获取用户授权信息等。

```mermaid
classDiagram
class UserController {
+LoggerHelper LOGGER
-UserPlatformHandleService userPlatformHandleService
+CommonApiResponse~UserInfoResponse~ login(UserInfoRequest)
+CommonApiResponse quickLogin(LoginInfoRequest)
+CommonApiResponse refreshUserInfo(RefreshUserInfoRequest)
+CommonApiResponse~UserInfoResponse~ getTopSession(UserInfoRequest)
}
```

**图示来源**
- [UserController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java#L29-L107)

#### CallbackController
CallbackController处理用户授权回调请求。

```mermaid
classDiagram
class CallbackController {
+LoggerHelper LOGGER
-CallbackPlatformHandleService callbackPlatformHandleService
+CommonApiResponse~CallbackResponse~ authCallback(CallbackRequest)
}
```

**图示来源**
- [CallbackController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/CallbackController.java#L21-L43)

### 服务导出层分析
服务导出层包含UserCenterServiceImpl，实现了uac-api定义的服务接口。

#### UserCenterServiceImpl
UserCenterServiceImpl实现了UserCenterInnerApiService接口，提供了用户信息获取、用户设置管理、用户标签管理等服务。

```mermaid
classDiagram
class UserCenterServiceImpl {
+LoggerHelper LOGGER
-UserPlatformHandleService userPlatformHandleService
-UserRepository userRepository
-Boolean checkTaobaoVipflagIsExpired
-UserSettingsRepository userSettingsRepository
-AyMultiUserTagDao ayMultiUserTagDao
-PlatformUserProductInfoService platformUserProductInfoService
+CommonApiResponse~UserInfoResponse~ getUserInfo(UserInfoRequest)
+CommonApiResponse~UserFullInfoResponse~ getUserFullInfo(UserFullInfoRequest)
+CommonApiResponse~UserFullInfoResponse[]~ batchGetUserFullInfo(BatchGetUserFullInfoRequest)
+CommonApiResponse~UserInfoMemberResponse~ getUserInfoByMemberId(UserInfoMemberRequest)
+CommonApiResponse~UserInfoResponse[]~ batchGetUserInfo(UserInfoRequest[])
+CommonApiResponse~Void~ batchSettingUpdate(BatchSettingUpdateRequest)
+CommonApiResponse~UserSettingDTO[]~ batchSettingGet(BatchSettingGetRequest)
+CommonApiResponse~BatchUsersSettingGetResponse[]~ batchUsersSettingGet(BatchUsersSettingGetRequest)
+CommonApiResponse~Void~ userSettingCopy(UserSettingCopyRequest)
+CommonApiResponse~UserInfoResponse~ getTopSession(UserInfoRequest)
+CommonApiResponse~ListUserByVipInfoResponse~ listUserByVipInfo(ListUserByVipInfoRequest)
+CommonApiResponse~UserInfoResponse[]~ batchGetTopSession(BatchRequest~UserInfoRequest~)
+CommonApiResponse~UserInfoResponse~ rebuildUserInfo(UserInfoRequest)
+CommonApiResponse~UserInfoTopSessionMemberResponse~ getTopSessionByMemberId(UserinfoTopSessionMemberRequest)
+CommonApiResponse~BatchUpdateUserCacheInfoResponse~ batchUpdateUserCacheInfo(BatchUpdateUserCacheInfoRequest)
+CommonApiResponse~UserCacheInfoResponse[]~ batchGetUserCacheInfo(BatchGetUserCacheInfoRequest)
+CommonApiResponse~Void~ batchMultiuserTagUpdate(BatchMultiUserTagUpdateRequest)
+CommonApiResponse~UserShopInfoGetResponse~ getUserShopInfo(UserShopInfoGetRequest)
+CommonApiResponse~UserFullInfoResponse~ getUserByMallName(UserInfoRequest)
+CommonApiResponse~UserInfoResponse[]~ batchGetUserLoginInfo(UserInfoRequest[])
}
```

**图示来源**
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java#L55-L822)

### 拦截器分析
拦截器层包含UserRequestInterceptor，用于实现请求鉴权和日志记录。

#### UserRequestInterceptor
UserRequestInterceptor在请求处理前，从请求参数中提取sellerNick、subSellerNick、sellerId、subSellerId、platformId、app等信息，并将其放入MDC（Mapped Diagnostic Context）中，便于日志记录。

```mermaid
classDiagram
class UserRequestInterceptor {
+boolean preHandle(HttpServletRequest, HttpServletResponse, Object)
}
```

**图示来源**
- [UserRequestInterceptor.java](file://uac-service/src/main/java/cn/loveapp/uac/service/interceptor/UserRequestInterceptor.java#L13-L37)

### Web配置分析
Web配置层包含VersionGrayHandler，用于支持灰度发布。

#### VersionGrayHandler
VersionGrayHandler通过解析请求头中的版本信息，实现不同版本服务的路由，支持灰度发布功能。

```mermaid
classDiagram
class VersionGrayHandler {
+String getVersion(HttpServletRequest)
+boolean isGrayVersion(String)
}
```

**图示来源**
- [VersionGrayHandler.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/VersionGrayHandler.java)

**章节来源**
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java#L55-L822)
- [UserRequestInterceptor.java](file://uac-service/src/main/java/cn/loveapp/uac/service/interceptor/UserRequestInterceptor.java#L13-L37)
- [VersionGrayHandler.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/VersionGrayHandler.java)

## 依赖分析

uac-service模块依赖于uac-api、uac-common、uac-db-common等模块，通过Maven进行依赖管理。

```mermaid
graph TD
A[uac-service] --> B[uac-api]
A --> C[uac-common]
A --> D[uac-db-common]
B --> E[Spring Boot]
C --> E
D --> E
```

**图示来源**
- [pom.xml](file://uac-service/pom.xml)

**章节来源**
- [UacServiceApplication.java](file://uac-service/src/main/java/cn/loveapp/uac/service/UacServiceApplication.java#L23-L39)

## 性能考虑
uac-service模块在性能方面考虑了以下几点：
1. 使用批量操作接口，减少数据库访问次数
2. 使用缓存机制，减少重复计算
3. 使用异步处理，提高响应速度
4. 使用连接池，提高数据库连接效率

## 故障排除指南
常见问题及解决方案：
1. 请求超时：检查网络连接，优化数据库查询
2. 数据不一致：检查缓存一致性，确保数据同步
3. 接口调用失败：检查参数合法性，确保请求格式正确
4. 性能下降：检查系统资源使用情况，优化代码逻辑

**章节来源**
- [UserController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java#L29-L107)
- [CallbackController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/CallbackController.java#L21-L43)
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java#L55-L822)

## 结论
uac-service模块作为用户中心服务的核心应用，通过合理的架构设计和组件划分，实现了高效、稳定的用户中心服务能力。模块通过整合多个依赖模块，构建了完整的业务闭环，为上层应用提供了可靠的用户管理功能。未来可以通过引入更多缓存机制、优化数据库查询、增加监控告警等方式，进一步提升系统的性能和稳定性。