# 拦截器与过滤器

<cite>
**本文档引用的文件**  
- [UserRequestInterceptor.java](file://uac-service/src/main/java/cn/loveapp/uac/service/interceptor/UserRequestInterceptor.java)
- [RequestInterceptorConfig.java](file://uac-service/src/main/java/cn/loveapp/uac/service/interceptor/RequestInterceptorConfig.java)
- [VersionGrayHandler.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/VersionGrayHandler.java)
</cite>

## 目录
1. [简介](#简介)
2. [拦截器设计与实现](#拦截器设计与实现)
3. [拦截器注册机制](#拦截器注册机制)
4. [执行流程与上下文传递](#执行流程与上下文传递)
5. [异常处理与灰度控制](#异常处理与灰度控制)
6. [安全与可观测性作用](#安全与可观测性作用)
7. [总结](#总结)

## 简介
本文档详细说明 `uac-service` 模块中拦截器的设计与实现，重点解析 `UserRequestInterceptor` 如何在请求处理前、后及完成时执行特定逻辑。涵盖基于 AppKey/Secret 的 API 请求鉴权、会话有效性验证、关键请求参数的审计日志记录、请求耗时监控等横切关注点的处理机制。阐述其通过实现 `HandlerInterceptor` 接口并与 `RequestInterceptorConfig` 配置类配合，注册到 Spring MVC 拦截器链中的完整流程。

## 拦截器设计与实现

`UserRequestInterceptor` 是核心的请求拦截器，实现了 Spring MVC 的 `HandlerInterceptor` 接口，用于在请求处理的不同阶段插入自定义逻辑。

该拦截器主要在 `preHandle` 方法中执行前置处理逻辑，负责从 HTTP 请求中提取关键业务参数，包括：
- 卖家昵称（sellerNick）
- 子卖家昵称（subSellerNick）
- 卖家 ID（sellerId）
- 子卖家 ID（subSellerId）
- 平台标识（platformId）
- 应用名称（app）

这些参数通过 `request.getParameter()` 方法获取，并通过 `MDC`（Mapped Diagnostic Context）机制存入日志上下文中，为后续的日志输出提供上下文信息，实现请求级别的日志追踪。

```mermaid
classDiagram
class HandlerInterceptor {
<<interface>>
+preHandle(HttpServletRequest, HttpServletResponse, Object) boolean
+postHandle(HttpServletRequest, HttpServletResponse, Object, ModelAndView) void
+afterCompletion(HttpServletRequest, HttpServletResponse, Object, Exception) void
}
class UserRequestInterceptor {
-MDC
+preHandle(HttpServletRequest, HttpServletResponse, Object) boolean
}
UserRequestInterceptor ..|> HandlerInterceptor
```

**图示来源**  
- [UserRequestInterceptor.java](file://uac-service/src/main/java/cn/loveapp/uac/service/interceptor/UserRequestInterceptor.java#L13-L37)

**本节来源**  
- [UserRequestInterceptor.java](file://uac-service/src/main/java/cn/loveapp/uac/service/interceptor/UserRequestInterceptor.java#L13-L37)

## 拦截器注册机制

拦截器的注册由 `RequestInterceptorConfig` 配置类完成，该类实现了 `WebMvcConfigurer` 接口，通过 `addInterceptors` 方法将拦截器注册到 Spring MVC 的拦截器链中。

注册过程如下：
1. 通过 `@Bean` 注解定义 `userRequestInterceptor()` 方法，创建 `UserRequestInterceptor` 的 Spring Bean 实例。
2. 在 `addInterceptors` 方法中，调用 `registry.addInterceptor()` 注册该实例。
3. 使用 `.order(Ordered.HIGHEST_PRECEDENCE)` 设置其执行顺序为最高优先级，确保它在其他拦截器之前执行。
4. 使用 `.addPathPatterns("/**")` 指定该拦截器应用于所有请求路径。

此外，该配置还支持条件性地注册 `VersionGrayHandler` 拦截器，用于实现版本灰度发布功能，体现了配置的灵活性。

```mermaid
sequenceDiagram
participant Config as RequestInterceptorConfig
participant Registry as InterceptorRegistry
participant Interceptor as UserRequestInterceptor
Config->>Registry : addInterceptor(interceptor)
Registry->>Interceptor : 设置 order=HIGHEST_PRECEDENCE
Registry->>Interceptor : 设置 pathPatterns=/**
Note over Config,Interceptor : 拦截器注册流程
```

**图示来源**  
- [RequestInterceptorConfig.java](file://uac-service/src/main/java/cn/loveapp/uac/service/interceptor/RequestInterceptorConfig.java#L25-L38)

**本节来源**  
- [RequestInterceptorConfig.java](file://uac-service/src/main/java/cn/loveapp/uac/service/interceptor/RequestInterceptorConfig.java#L16-L38)

## 执行流程与上下文传递

`UserRequestInterceptor` 的执行流程严格遵循 Spring MVC 拦截器的生命周期：

1.  **请求前处理 (preHandle)**：在控制器方法执行前，`preHandle` 方法被调用。它首先清理 MDC 中可能存在的旧上下文，然后从当前请求中提取 `sellerNick`, `sellerId`, `platformId` 等关键参数，并将其放入 MDC。这使得在后续的任何日志输出中，都可以自动包含这些上下文信息，实现精准的请求追踪。
2.  **请求后处理 (postHandle)**：此拦截器未重写 `postHandle` 方法，因此在控制器执行后不执行额外逻辑。
3.  **请求完成 (afterCompletion)**：此拦截器未重写 `afterCompletion` 方法，因此在请求完成后不执行额外逻辑。

上下文传递的核心机制是 `MDC`。`MDC` 是 `SLF4J` 提供的一个工具，它基于 `ThreadLocal` 实现，为当前线程绑定一个上下文映射（Map）。在请求处理的整个生命周期中（通常在一个线程内完成），所有通过日志框架输出的日志都会自动包含 MDC 中的数据。这使得在海量日志中，可以通过 `sellerNick` 或 `requestId` 等字段快速过滤出与单个请求相关的所有日志，极大地提升了系统的可观测性。

**本节来源**  
- [UserRequestInterceptor.java](file://uac-service/src/main/java/cn/loveapp/uac/service/interceptor/UserRequestInterceptor.java#L13-L37)

## 异常处理与灰度控制

虽然 `UserRequestInterceptor` 本身不涉及复杂的异常处理，但与它并行注册的 `VersionGrayHandler` 拦截器展示了更高级的异常处理模式。

`VersionGrayHandler` 同样实现了 `HandlerInterceptor` 和 `RequestBodyAdvice` 接口。它在 `preHandle` 和 `afterBodyRead` 阶段检查请求是否满足灰度条件。如果满足，它会抛出一个自定义的 `VersionGrayHitException` 异常。

该异常通过 `@ExceptionHandler` 注解在 `VersionGrayHandler` 类内部被捕获。异常处理器 `catVersionGrayHitException` 会构造一个重定向请求，并通过 `RoutingDelegateUtils.redirect()` 方法将请求转发到灰度服务地址。这种“抛异常-捕获-执行逻辑”的模式，巧妙地利用了 Spring 的异常处理机制来实现非业务性的控制流跳转。

```mermaid
flowchart TD
A[请求进入] --> B{满足灰度条件?}
B --> |是| C[抛出 VersionGrayHitException]
C --> D[ExceptionHandler 捕获]
D --> E[构造重定向请求]
E --> F[转发至灰度服务]
B --> |否| G[正常处理流程]
```

**图示来源**  
- [VersionGrayHandler.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/VersionGrayHandler.java#L32-L156)

**本节来源**  
- [VersionGrayHandler.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/VersionGrayHandler.java#L32-L156)

## 安全与可观测性作用

`UserRequestInterceptor` 在保障系统安全性和可观测性方面发挥着基础而关键的作用：

-   **可观测性 (Observability)**：通过 `MDC` 机制，为所有日志注入了 `sellerNick`, `platformId` 等业务上下文。这使得运维和开发人员能够轻松地进行问题排查、性能分析和用户行为审计。例如，可以快速定位某个特定卖家的所有操作日志，或分析某个平台的请求量趋势。
-   **安全性 (Security)**：虽然该拦截器本身不直接进行鉴权，但它提取的 `sellerId`, `sellerNick` 等信息是后续安全校验（如会话验证、权限检查）的基础数据。它确保了这些关键身份信息在整个请求链路中是可访问的。
-   **审计 (Auditing)**：记录关键请求参数是审计日志的前提。`MDC` 中的信息可以被日志系统自动采集，用于生成用户操作审计报告，满足合规性要求。

## 总结

`uac-service` 模块通过 `UserRequestInterceptor` 和 `RequestInterceptorConfig` 的组合，实现了高效、灵活的横切关注点处理。`UserRequestInterceptor` 利用 `MDC` 机制，在请求入口处统一收集和传递业务上下文，为系统的日志追踪和可观测性奠定了坚实基础。`RequestInterceptorConfig` 则通过标准的 Spring 配置方式，将拦截器无缝集成到 MVC 框架中，并支持高优先级的执行顺序。整个设计体现了关注点分离的原则，将非业务逻辑从核心控制器中剥离，提升了代码的可维护性和可扩展性。同时，与 `VersionGrayHandler` 的协同工作，也展示了拦截器在实现复杂控制流（如灰度发布）方面的强大能力。