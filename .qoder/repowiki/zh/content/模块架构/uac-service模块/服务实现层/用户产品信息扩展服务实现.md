# 用户产品信息扩展服务实现

<cite>
**本文档引用文件**  
- [UserProductInfoExtApiServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserProductInfoExtApiServiceImpl.java)
- [UserSettingsRepository.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/UserSettingsRepository.java)
- [UserSettingsRepositoryImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/impl/UserSettingsRepositoryImpl.java)
- [UserSettingsDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/UserSettingsDao.java)
- [UserSettings.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserSettings.java)
- [AppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/AppConfig.java)
- [ValidatorUtils.java](file://uac-api/src/main/java/cn/loveapp/uac/utils/ValidatorUtils.java)
- [QueryUserSettingsParam.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dto/QueryUserSettingsParam.java)
</cite>

## 目录

1. [简介](#简介)
2. [核心组件分析](#核心组件分析)
3. [数据交互与持久化机制](#数据交互与持久化机制)
4. [缓存同步策略](#缓存同步策略)
5. [配置项与默认设置管理](#配置项与默认设置管理)
6. [数据校验逻辑](#数据校验逻辑)
7. [事务管理与并发控制](#事务管理与并发控制)
8. [序列化与反序列化过程](#序列化与反序列化过程)
9. [变更事件发布机制](#变更事件发布机制)
10. [总结](#总结)

## 简介

`UserProductInfoExtApiServiceImpl` 是用户中心服务中负责管理用户产品信息扩展字段的核心服务类。该服务主要提供用户设置的读取、批量获取和更新功能，通过与 `UserSettingsRepository` 的深度集成，实现了高效的数据持久化与缓存同步机制。服务在设计上充分考虑了高并发场景下的数据一致性问题，并结合本地缓存、Redis 缓存与数据库三层存储结构，确保了系统的高性能与可靠性。

## 核心组件分析

该服务的核心功能围绕 `UserSettings` 实体展开，通过 `UserSettingsRepository` 接口与其实现类 `UserSettingsRepositoryImpl` 完成对用户设置数据的 CRUD 操作。服务通过 `UserSettingsDao` 与底层数据库进行交互，使用 MyBatis 作为持久层框架，支持批量查询与批量插入/更新操作。

```mermaid
classDiagram
class UserProductInfoExtApiServiceImpl {
+getUserInfoExtBySellerId(request) CommonApiResponse~GetUserInfoExtResponse~
+updateUserInfoExtBySellerId(request) CommonApiResponse~UpdateUserInfoExtResponse~
}
class UserSettingsRepository {
<<interface>>
+queryAllSettings(userId, platformId, appName) UserSettings[]
+batchQueryUserSetting(param) UserSettingDTO[]
+batchUpsertUserSetting(settings) int
}
class UserSettingsRepositoryImpl {
-userSettingsDao UserSettingsDao
-stringTradeRedisTemplate StringRedisTemplate
-cacheTimeoutConfig CacheTimeoutConfig
-defaultSettingLocalCache DefaultSettingLocalCache
+batchQueryUserSetting(param) UserSettingDTO[]
+batchUpsertUserSetting(settings) int
}
class UserSettingsDao {
<<interface>>
+queryAllSettings(userId, platformId, appName) UserSettings[]
+batchQueryUserSetting(param) UserSettings[]
+batchUpsertUserSetting(settings) int
}
class UserSettings {
+id : Long
+settingKey : String
+settingValue : String
+userId : String
+platformId : String
+appName : String
+createTime : Date
+updateTime : Date
}
UserProductInfoExtApiServiceImpl --> UserSettingsRepository : "依赖"
UserSettingsRepository <|-- UserSettingsRepositoryImpl : "实现"
UserSettingsRepositoryImpl --> UserSettingsDao : "依赖"
UserSettingsRepositoryImpl --> UserSettings : "操作"
UserSettingsRepositoryImpl --> DefaultSettingLocalCache : "依赖"
UserSettingsRepositoryImpl --> CacheTimeoutConfig : "依赖"
```

**图示来源**  
- [UserProductInfoExtApiServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserProductInfoExtApiServiceImpl.java)
- [UserSettingsRepository.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/UserSettingsRepository.java)
- [UserSettingsRepositoryImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/impl/UserSettingsRepositoryImpl.java)
- [UserSettingsDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/UserSettingsDao.java)
- [UserSettings.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserSettings.java)

**本节来源**  
- [UserProductInfoExtApiServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserProductInfoExtApiServiceImpl.java#L30-L95)
- [UserSettingsRepository.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/UserSettingsRepository.java#L13-L32)
- [UserSettings.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserSettings.java#L11-L63)

## 数据交互与持久化机制

服务通过 `UserSettingsRepository` 与数据库进行交互，核心操作包括：

- **批量查询**：通过 `batchQueryUserSetting` 方法，支持按用户 ID、平台 ID、应用名和设置键列表批量获取用户设置。
- **批量插入/更新**：通过 `batchUpsertUserSetting` 方法，实现“存在则更新，不存在则插入”的原子操作，确保数据一致性。

数据库操作由 `UserSettingsDao` 接口定义，并通过 MyBatis 映射到 SQL 语句执行。`UserSettings` 实体类与数据库表 `user_settings` 一一对应，包含设置键、值、用户标识、平台及应用信息等核心字段。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Service as "UserProductInfoExtApiServiceImpl"
participant Repository as "UserSettingsRepositoryImpl"
participant Dao as "UserSettingsDao"
participant DB as "数据库"
Client->>Service : 调用 batchQueryUserSetting()
Service->>Repository : 转发 QueryUserSettingsParam
Repository->>Repository : 先查 Redis 缓存
alt 缓存命中
Repository-->>Service : 返回缓存结果
else 缓存未命中
Repository->>Dao : 调用 batchQueryUserSetting()
Dao->>DB : 执行 SQL 查询
DB-->>Dao : 返回结果集
Dao-->>Repository : 返回 UserSettings 列表
Repository->>Repository : 将结果写入 Redis
Repository-->>Service : 返回 DTO 列表
end
Service-->>Client : 返回响应
```

**图示来源**  
- [UserSettingsRepositoryImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/impl/UserSettingsRepositoryImpl.java#L29-L196)
- [UserSettingsDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/UserSettingsDao.java#L13-L51)

**本节来源**  
- [UserSettingsRepositoryImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/impl/UserSettingsRepositoryImpl.java#L29-L196)
- [UserSettingsDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/UserSettingsDao.java#L13-L51)

## 缓存同步策略

服务采用多级缓存策略提升性能：

1. **Redis 缓存**：用于存储热点用户设置，键格式为 `user:settings:{platformId}:{appName}:{userId}:{settingKey}`。
2. **本地缓存**：`DefaultSettingLocalCache` 用于缓存默认设置，避免频繁访问数据库。
3. **空值标记**：对数据库中不存在的设置项，Redis 中存储特殊标记 `@EMPTY_USER_SETTING`，防止缓存穿透。

在更新操作后，服务会主动删除 Redis 中对应的缓存键，确保下次读取时能从数据库获取最新数据，实现“更新后失效”的缓存策略。

```mermaid
flowchart TD
A[开始] --> B{Redis 是否存在?}
B --> |是| C[检查是否为空标记]
C --> |是| D[加载默认设置]
C --> |否| E[返回缓存数据]
B --> |否| F[查询数据库]
F --> G{数据库是否存在?}
G --> |是| H[写入 Redis 并返回]
G --> |否| I[写入空标记]
I --> J[加载默认设置]
J --> K[返回结果]
H --> K
E --> K
K --> L[结束]
```

**图示来源**  
- [UserSettingsRepositoryImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/impl/UserSettingsRepositoryImpl.java#L29-L196)

**本节来源**  
- [UserSettingsRepositoryImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/impl/UserSettingsRepositoryImpl.java#L29-L196)

## 配置项与默认设置管理

服务通过 `AppConfig` 类管理应用级别的配置信息，如 `appkey`、`appSecret` 等。对于用户设置的默认值，系统通过 `DefaultSettingLocalCache` 提供支持。当用户未设置某项配置时，系统会尝试从本地缓存中获取该应用的默认设置值，从而保证功能的可用性。

**本节来源**  
- [AppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/AppConfig.java#L10-L18)
- [UserSettingsRepositoryImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/impl/UserSettingsRepositoryImpl.java#L29-L196)

## 数据校验逻辑

服务在处理请求前，使用 `ValidatorUtils` 工具类对关键参数进行校验，确保平台 ID 和应用名的合法性。校验逻辑通过静态方法 `checkPlatform` 和 `checkApp` 实现，防止非法输入导致的数据异常。

**本节来源**  
- [ValidatorUtils.java](file://uac-api/src/main/java/cn/loveapp/uac/utils/ValidatorUtils.java#L12-L28)

## 事务管理与并发控制

`batchUpsertUserSetting` 操作在数据库层面通过 MyBatis 的批量操作实现原子性。虽然服务层未显式声明事务注解，但 DAO 层的批量操作通常在同一个数据库会话中执行，具备基本的事务保障。在高并发场景下，通过“先更新数据库，再删除缓存”的策略，结合 Redis 的单线程特性，有效避免了脏读和不一致问题。

**本节来源**  
- [UserSettingsRepositoryImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/impl/UserSettingsRepositoryImpl.java#L29-L196)

## 序列化与反序列化过程

服务使用 `FastJSON2` 进行对象与 JSON 字符串之间的转换。当数据从数据库加载到 Redis 时，`UserSettings` 对象被序列化为 JSON 字符串存储；从 Redis 读取时，再反序列化为对象。该过程由 `JSON.toJSONString()` 和 `JSON.parseObject()` 方法完成，确保了数据结构的完整性。

**本节来源**  
- [UserSettingsRepositoryImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/impl/UserSettingsRepositoryImpl.java#L29-L196)

## 变更事件发布机制

虽然当前代码未直接体现消息队列的发布逻辑，但系统架构中已集成 `RocketMQ` 支持。在用户设置更新后，可通过事件监听机制触发 `UserChangedEvent`，并通过 `RocketMqQueueHelper` 发布消息，通知下游系统用户信息已变更，实现解耦与异步处理。

**本节来源**  
- [UserSettingsRepositoryImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/impl/UserSettingsRepositoryImpl.java#L29-L196)
- [UserSettingsRepositoryImpl.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/impl/UserSettingsRepositoryImpl.java#L29-L196)

## 总结

`UserProductInfoExtApiServiceImpl` 服务通过清晰的分层设计与高效的缓存策略，实现了用户产品信息扩展字段的稳定管理。其核心在于 `UserSettingsRepository` 对数据访问的封装，结合 Redis 缓存与本地默认设置缓存，构建了高性能、高可用的数据读写体系。服务在数据校验、序列化、并发控制等方面均具备良好的工程实践，为系统的可维护性与扩展性奠定了坚实基础。