# Web配置与灰度发布

<cite>
**本文档引用的文件**
- [VersionGrayHandler.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/VersionGrayHandler.java)
- [WebConfiguration.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/WebConfiguration.java)
- [VersionGrayConfig.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/config/VersionGrayConfig.java)
- [RoutingDelegateUtils.java](file://uac-service/src/main/java/cn/loveapp/uac/service/util/RoutingDelegateUtils.java)
- [BaseHttpRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BaseHttpRequest.java)
</cite>

## 目录
1. [简介](#简介)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 简介
本文档系统性地介绍`uac-service`模块的Web层高级配置，重点阐述灰度发布功能的实现机制。文档深入解析`VersionGrayHandler`如何基于请求头、用户ID、AppKey等条件动态路由流量至不同版本的服务实例，以实现新功能的平滑上线与风险控制。同时说明其与`WebConfiguration`配置类的集成方式，并涵盖灰度策略的配置、匹配规则、分流算法、监控与回滚方案。

## 项目结构
`uac-service`模块是用户中心服务的核心部分，负责处理Web请求、实现灰度发布、日志优化等关键功能。其主要结构如下：

```mermaid
graph TD
subgraph "uac-service"
Web[web]
Util[util]
Interceptor[interceptor]
Controller[controller]
end
Web --> VersionGrayHandler[VersionGrayHandler]
Web --> WebConfiguration[WebConfiguration]
Util --> RoutingDelegateUtils[RoutingDelegateUtils]
Interceptor --> RequestInterceptorConfig[RequestInterceptorConfig]
Controller --> UserController[UserController]
VersionGrayHandler --> VersionGrayConfig[VersionGrayConfig]
VersionGrayHandler --> RoutingDelegateUtils
WebConfiguration --> HttpTraceLogJsonValueFilter[HttpTraceLogJsonValueFilter]
```

**图示来源**
- [VersionGrayHandler.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/VersionGrayHandler.java#L1-L155)
- [WebConfiguration.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/WebConfiguration.java#L1-L47)
- [RoutingDelegateUtils.java](file://uac-service/src/main/java/cn/loveapp/uac/service/util/RoutingDelegateUtils.java#L1-L132)

**本节来源**
- [VersionGrayHandler.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/VersionGrayHandler.java#L1-L155)
- [WebConfiguration.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/WebConfiguration.java#L1-L47)

## 核心组件
本模块的核心组件包括`VersionGrayHandler`（灰度处理器）、`WebConfiguration`（Web配置类）、`VersionGrayConfig`（灰度配置）和`RoutingDelegateUtils`（路由代理工具）。这些组件协同工作，实现了灵活的流量控制和请求处理优化。

**本节来源**
- [VersionGrayHandler.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/VersionGrayHandler.java#L1-L155)
- [WebConfiguration.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/WebConfiguration.java#L1-L47)
- [VersionGrayConfig.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/config/VersionGrayConfig.java#L1-L43)

## 架构概述
系统通过Spring的`@ControllerAdvice`和`HandlerInterceptor`机制，在请求处理的早期阶段介入，根据预设的灰度规则判断是否需要将流量转发至特定的服务实例。整个流程不依赖于外部网关，而是由服务自身完成路由决策。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Handler as "VersionGrayHandler"
participant Config as "VersionGrayConfig"
participant Utils as "RoutingDelegateUtils"
participant Target as "目标服务实例"
Client->>Handler : 发送HTTP请求
Handler->>Handler : preHandle() / afterBodyRead()
Handler->>Config : 查询灰度配置
Config-->>Handler : 返回users, platforms, serviceHost
Handler->>Handler : checkGrayConditions()
alt 满足灰度条件
Handler->>Handler : 抛出VersionGrayHitException
Handler->>Handler : @ExceptionHandler处理异常
Handler->>Utils : 构建RequestRedirectDTO
Utils->>Utils : redirect() 执行转发
Utils->>Target : 通过RestTemplate调用
Target-->>Utils : 返回响应
Utils-->>Handler : 返回ResponseEntity
Handler-->>Client : 返回最终响应
else 不满足灰度条件
Handler-->>Client : 继续正常流程
end
```

**图示来源**
- [VersionGrayHandler.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/VersionGrayHandler.java#L30-L155)
- [VersionGrayConfig.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/config/VersionGrayConfig.java#L14-L43)
- [RoutingDelegateUtils.java](file://uac-service/src/main/java/cn/loveapp/uac/service/util/RoutingDelegateUtils.java#L25-L132)

## 详细组件分析

### 灰度发布处理器分析
`VersionGrayHandler`是实现灰度发布的核心类，它同时实现了`HandlerInterceptor`和`RequestBodyAdvice`接口，确保能从请求参数和请求体两个维度捕获关键信息。

#### 类结构与交互
```mermaid
classDiagram
class VersionGrayHandler {
+static final String SELLER_NICK_REQUEST_PARAM
+static final String PLATFORM_ID_REQUEST_PARAM
+static final String APP_NAME_REQUEST_PARAM
+static final String SPLIT
-VersionGrayConfig versionGrayConfig
+preHandle(HttpServletRequest, HttpServletResponse, Object) boolean
+supports(MethodParameter, Type, Class) boolean
+beforeBodyRead(HttpInputMessage, MethodParameter, Type, Class) HttpInputMessage
+afterBodyRead(Object, HttpInputMessage, MethodParameter, Type, Class) Object
+handleEmptyBody(Object, HttpInputMessage, MethodParameter, Type, Class) Object
+catVersionGrayHitException(HttpServletRequest, VersionGrayHitException) ResponseEntity~String~
-checkGrayConditions(String, String, String) boolean
}
class VersionGrayHitException {
-BaseHttpRequest requestBody
+VersionGrayHitException()
+VersionGrayHitException(String)
+VersionGrayHitException(BaseHttpRequest)
}
class VersionGrayConfig {
+static final String CONFIGURATION_PREFIX
-boolean enable
-String[] users
-String[] platforms
-String serviceHost
}
class RoutingDelegateUtils {
+static ResponseEntity~String~ redirect(RequestRedirectDTO~T~)
+static MultiValueMap~String, String~ parseRequestHeader(HttpServletRequest)
+static MultiValueMap~String, String~ parseRequestParameter(HttpServletRequest)
+static String createRedirectUrl(String, String, String)
}
class RequestRedirectDTO~T~ {
-String method
-MultiValueMap~String, String~ headers
-String requestURI
-String queryString
-MultiValueMap~String, String~ parameters
-T body
-String redirectHost
}
VersionGrayHandler --> VersionGrayConfig : "依赖"
VersionGrayHandler --> RoutingDelegateUtils : "使用"
VersionGrayHandler --> VersionGrayHitException : "抛出"
RoutingDelegateUtils --> RequestRedirectDTO : "包含"
```

**图示来源**
- [VersionGrayHandler.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/VersionGrayHandler.java#L30-L155)
- [VersionGrayConfig.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/config/VersionGrayConfig.java#L14-L43)
- [RoutingDelegateUtils.java](file://uac-service/src/main/java/cn/loveapp/uac/service/util/RoutingDelegateUtils.java#L25-L132)

**本节来源**
- [VersionGrayHandler.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/VersionGrayHandler.java#L1-L155)

### Web配置分析
`WebConfiguration`类主要负责配置Web层的非功能性需求，当前实现了一个日志过滤器，用于优化请求响应日志的输出。

#### 日志过滤逻辑
```mermaid
flowchart TD
Start([开始 apply]) --> CheckTag{"name == 'tag'?"}
CheckTag --> |是| IsString{"value 是 String?"}
IsString --> |是| ContainsTos{"value 包含 'tos'?"}
ContainsTos --> |是| ReturnTos["返回 '**,tos,**'"]
ContainsTos --> |否| ReturnStar["返回 '***'"]
CheckTag --> |否| CheckSettings{"name == 'settings'?"}
CheckSettings --> |是| IsArrayList{"value 是 ArrayList且元素为String?"}
IsArrayList --> |是| MapSettings["对每个元素应用abbreviatedSettingsName"]
MapSettings --> ReturnList["返回缩写后的列表"]
CheckSettings --> |否| CheckKey{"name == 'key'?"}
CheckKey --> |是| IsStringForKey{"value 是 String?"}
IsStringForKey --> |是| ReturnAbbreviated["返回abbreviatedSettingsName(value)"]
CheckKey --> |否| ReturnOriginal["返回原值value"]
ReturnTos --> End([结束])
ReturnStar --> End
ReturnList --> End
ReturnAbbreviated --> End
ReturnOriginal --> End
```

**图示来源**
- [WebConfiguration.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/WebConfiguration.java#L11-L47)

**本节来源**
- [WebConfiguration.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/WebConfiguration.java#L1-L47)

## 依赖分析
`VersionGrayHandler`的正常运行依赖于多个关键组件，形成了一个清晰的依赖链。

```mermaid
graph TD
RequestInterceptorConfig --> VersionGrayHandler : "注入"
VersionGrayHandler --> VersionGrayConfig : "注入"
VersionGrayHandler --> RoutingDelegateUtils : "调用"
RoutingDelegateUtils --> RestTemplate : "使用"
BaseHttpRequest --> VersionGrayHandler : "作为请求体类型"
```

**图示来源**
- [VersionGrayHandler.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/VersionGrayHandler.java#L30-L155)
- [VersionGrayConfig.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/config/VersionGrayConfig.java#L14-L43)
- [RoutingDelegateUtils.java](file://uac-service/src/main/java/cn/loveapp/uac/service/util/RoutingDelegateUtils.java#L25-L132)

**本节来源**
- [VersionGrayHandler.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/VersionGrayHandler.java#L1-L155)
- [VersionGrayConfig.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/config/VersionGrayConfig.java#L1-L43)
- [RoutingDelegateUtils.java](file://uac-service/src/main/java/cn/loveapp/uac/service/util/RoutingDelegateUtils.java#L1-L132)

## 性能考虑
灰度发布机制在每次请求时都会进行条件判断，虽然逻辑简单，但在高并发场景下仍需关注其性能影响。`checkGrayConditions`方法使用了`List.contains()`操作，对于大型灰度名单，建议后续优化为`Set`数据结构以提升查找效率。此外，`RoutingDelegateUtils.redirect()`方法中创建了新的`RestTemplate`实例，这可能会带来不必要的资源开销，建议将其设计为单例或通过Spring管理。

## 故障排除指南
当灰度发布功能未按预期工作时，可按以下步骤排查：
1.  **确认配置**：检查Apollo或本地配置文件中`uac.version.gray.enable`是否设置为`true`。
2.  **检查灰度名单**：确认请求的`sellerNick`、`platformId`或`platformId_app`组合是否已正确配置在`users`或`platforms`列表中。
3.  **验证服务地址**：确保`uac.version.gray.serviceHost`配置了正确的、可访问的目标服务地址。
4.  **查看日志**：在`VersionGrayHandler`中，`LOGGER.logInfo`会在命中灰度时输出日志，这是判断是否进入灰度流程的关键依据。
5.  **网络连通性**：确认当前服务实例能够通过网络访问`serviceHost`指定的地址。

**本节来源**
- [VersionGrayHandler.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/VersionGrayHandler.java#L30-L155)
- [VersionGrayConfig.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/config/VersionGrayConfig.java#L14-L43)

## 结论
`uac-service`模块通过`VersionGrayHandler`和`WebConfiguration`等组件，实现了一套内嵌于应用自身的、基于Spring拦截器的灰度发布方案。该方案通过读取配置中心的动态配置，能够灵活地将特定流量路由到新版本的服务实例，为新功能的上线提供了有效的风险控制手段。同时，`WebConfiguration`中的日志过滤器也体现了对系统可观测性的关注。整体设计简洁有效，但在性能和资源管理上仍有优化空间。