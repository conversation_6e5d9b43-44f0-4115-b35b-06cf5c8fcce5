# 模块架构

<cite>
**本文档引用文件**  
- [uac-api](file://uac-api)
- [uac-common](file://uac-common)
- [uac-db-common](file://uac-db-common)
- [uac-service](file://uac-service)
- [uac-newusers](file://uac-newusers)
- [uac-job](file://uac-job)
- [RedisConfiguration.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/RedisConfiguration.java)
- [UserRepository.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/UserRepository.java)
- [UserController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java)
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java)
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java)
</cite>

## 目录
1. [引言](#引言)
2. [项目结构](#项目结构)
3. [核心模块职责分析](#核心模块职责分析)
4. [模块协同工作机制](#模块协同工作机制)
5. [松耦合设计原则](#松耦合设计原则)
6. [架构图示](#架构图示)
7. [可维护性与可扩展性](#可维护性与可扩展性)
8. [结论](#结论)

## 引言
用户中心服务（usercenter-service-group）采用多模块微服务架构设计，旨在实现高内聚、低耦合的系统结构。本文档深入分析其六大核心模块：`uac-api`、`uac-common`、`uac-db-common`、`uac-service`、`uac-newusers` 和 `uac-job`，阐述各模块的职责边界、协作机制及设计原则，说明如何通过模块化设计提升系统的可维护性和可扩展性。

## 项目结构
用户中心服务由多个Maven子模块组成，采用分层架构设计，每个模块承担特定职责，通过依赖管理实现功能解耦。整体结构清晰，遵循“单一职责”和“关注点分离”原则。

```mermaid
graph TB
subgraph "API契约层"
uac_api[uac-api]
end
subgraph "公共能力层"
uac_common[uac-common]
uac_db_common[uac-db-common]
end
subgraph "业务应用层"
uac_service[uac-service]
uac_newusers[uac-newusers]
uac_job[uac-job]
end
uac_api --> uac_service
uac_api --> uac_newusers
uac_common --> uac_service
uac_common --> uac_newusers
uac_common --> uac_job
uac_db_common --> uac_service
uac_db_common --> uac_newusers
```

**图示来源**  
- [uac-api](file://uac-api)
- [uac-common](file://uac-common)
- [uac-db-common](file://uac-db-common)
- [uac-service](file://uac-service)
- [uac-newusers](file://uac-newusers)
- [uac-job](file://uac-job)

**本节来源**  
- [uac-api](file://uac-api)
- [uac-common](file://uac-common)
- [uac-db-common](file://uac-db-common)
- [uac-service](file://uac-service)
- [uac-newusers](file://uac-newusers)
- [uac-job](file://uac-job)

## 核心模块职责分析

### uac-api：API契约层
`uac-api`模块作为服务的API契约层，定义了所有对外暴露的请求（Request）和响应（Response）数据传输对象（DTO），以及核心服务接口。该模块不包含任何业务逻辑，仅负责数据结构的标准化，确保服务调用方与实现方之间的协议一致性。

该模块包含：
- **请求对象**：如 `BatchGetUserCacheInfoRequest`、`UserInfoRequest` 等，用于封装客户端请求参数。
- **响应对象**：如 `UserInfoResponse`、`CallbackResponse` 等，用于定义API返回的数据结构。
- **服务接口**：如 `UserCenterInnerApiService`，定义了服务应提供的方法契约。
- **异常与校验**：定义了 `BaseException` 及其子类，并通过自定义注解（如 `@CheckAppHasExist`）实现参数校验。

此设计使得API接口与具体实现分离，便于版本管理和多服务复用。

**本节来源**  
- [uac-api](file://uac-api)

### uac-common：公共能力层
`uac-common`模块提供跨服务的公共配置、工具类、异常体系和平台适配接口，是整个服务群的基础支撑模块。

主要职责包括：
- **配置管理**：集中管理各电商平台（如淘宝、拼多多、抖音等）的应用配置 `AppConfig` 及其子类，以及Redis、RocketMQ等中间件的配置。
- **工具类**：提供 `HttpUtil`、`DateUtil`、`MathUtil` 等通用工具方法。
- **异常体系**：定义了统一的异常基类 `BaseException` 和业务异常 `UserException`，以及网络、数据库等特定异常，实现异常的标准化处理。
- **平台适配接口**：定义了 `AuthService` 和 `AppStoreService` 等接口，为不同电商平台的授权和应用商店操作提供统一的抽象，其具体实现在此模块的 `impl` 包中。

通过将公共能力下沉，避免了代码重复，提高了开发效率和系统一致性。

**本节来源**  
- [uac-common](file://uac-common)
- [RedisConfiguration.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/RedisConfiguration.java)

### uac-db-common：数据访问层
`uac-db-common`模块封装了MyBatis DAO、数据库实体和Repository数据访问逻辑，实现了数据访问的统一管理和抽象。

其核心组件为：
- **DAO接口**：基于MyBatis，为每个数据库表定义了数据访问接口，如 `UserProductinfoTaoDao` 用于操作淘宝平台的用户产品信息表。
- **实体类**：`entity` 包下的POJO类，如 `UserProductInfo`，与数据库表结构一一对应。
- **Repository接口**：提供更高层次的业务数据访问接口，如 `UserRepository`，它屏蔽了底层DAO的细节，为上层业务提供统一的数据操作入口。例如，`queryBySellerNick` 方法可以根据卖家昵称查询用户信息，无需关心具体是哪个平台的DAO。
- **Repository实现**：`repository.impl` 包下的类实现了Repository接口，内部调用具体的DAO进行数据库操作。

该模块通过Repository模式实现了数据访问逻辑与业务逻辑的解耦，便于数据库操作的复用和测试。

**本节来源**  
- [uac-db-common](file://uac-db-common)
- [UserRepository.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/UserRepository.java)
- [UserProductinfoTaoDao.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao/dream/UserProductinfoTaoDao.java)

### uac-service：主业务应用层
`uac-service`模块是用户中心服务的主应用，负责处理核心的HTTP请求，协调各模块完成业务逻辑。

其主要组成部分包括：
- **控制器（Controller）**：如 `UserController`，接收来自前端或其他服务的HTTP请求，调用服务层处理业务，并返回响应。它依赖于 `uac-api` 中定义的请求和响应对象。
- **服务实现（Service）**：包含核心业务逻辑，如用户登录、信息刷新等。这些服务依赖于 `uac-common` 提供的工具和异常，以及 `uac-db-common` 提供的数据访问能力。
- **拦截器（Interceptor）**：如 `UserRequestInterceptor`，用于在请求处理前后执行通用逻辑，如权限校验、日志记录等。
- **配置类**：如 `WebConfiguration`，用于配置Spring MVC相关设置。

`uac-service`作为API契约的实现者，是整个系统对外服务的入口。

**本节来源**  
- [uac-service](file://uac-service)
- [UserController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java)

### uac-newusers：新用户开通调度层
`uac-newusers`模块负责新用户开通的调度与处理，是一个独立的微服务，专注于用户开通流程。

该模块采用生产者-消费者模式：
- **调度器（Scheduler）**：`uac-newuser-scheduler` 子模块中的 `ScanWaitOpenUser` 任务，通过 `@Scheduled` 注解定时扫描待开通的用户，并将它们发送到消息队列。
- **消费者（Consumer）**：`OpenUserConsumer` 从队列中消费待开通用户的消息，触发具体的开通业务逻辑。
- **业务处理**：`platform` 包下的 `NewUserPlatformHandleService` 及其实现类，负责处理不同电商平台的新用户开通流程。

此设计将用户开通这一耗时操作异步化，避免阻塞主业务流程，提高了系统的响应速度和稳定性。

**本节来源**  
- [uac-newusers](file://uac-newusers)
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java)

### uac-job：定时任务层
`uac-job`模块独立运行定时任务，如Token刷新，确保系统后台任务的稳定执行。

以 `uac-authorization-job` 为例：
- **定时任务**：`RefreshAccessTokenTask` 类中的多个方法使用 `@Scheduled` 注解，根据Cron表达式定期执行。
- **任务逻辑**：任务会调用 `UserRepository` 查询需要刷新Token的用户列表，然后通过 `SellerService` 调用第三方平台API进行Token刷新。
- **配置驱动**：任务的开关、执行频率等均通过配置文件控制，如 `RefreshAccessTokenTaskConfig`，便于运维管理。

将定时任务独立部署，避免了与主应用相互影响，保证了任务的可靠性和可维护性。

**本节来源**  
- [uac-job](file://uac-job)
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java)

## 模块协同工作机制
各模块通过清晰的依赖关系协同工作，构成完整的用户中心服务。

以“用户登录”流程为例：
1. 客户端发起登录请求，携带 `UserInfoRequest` 对象。
2. `uac-service` 模块的 `UserController` 接收请求，该请求对象来自 `uac-api` 模块。
3. `UserController` 调用 `UserPlatformHandleService` 服务。
4. 该服务可能需要查询数据库，因此调用 `uac-db-common` 模块的 `UserRepository`。
5. 在查询或处理过程中，可能会使用 `uac-common` 模块的 `RedisConfiguration` 进行缓存操作，或使用其工具类进行数据处理。
6. 最终，服务将结果封装成 `UserInfoResponse`（来自 `uac-api`），由 `UserController` 返回给客户端。

对于新用户开通：
1. `uac-newusers` 的调度器扫描到新用户。
2. 它使用 `uac-common` 的消息队列配置将用户信息发送到队列。
3. 消费者接收消息，调用 `uac-db-common` 的DAO将用户信息持久化。
4. 整个过程与主应用 `uac-service` 解耦，通过消息中间件异步通信。

## 松耦合设计原则
该架构严格遵循松耦合设计原则，主要体现在：
- **接口隔离**：`uac-api` 模块定义了清晰的接口契约，`uac-service` 和 `uac-newusers` 作为实现者，可以独立演进，只要不破坏接口兼容性。
- **依赖倒置**：高层模块（如 `uac-service`）依赖于抽象（`uac-common` 和 `uac-db-common` 中的接口），而非低层模块的具体实现，这使得替换实现（如更换数据库）变得容易。
- **关注点分离**：每个模块只负责一个特定领域，如数据访问、公共配置、业务逻辑、定时任务等，降低了模块间的耦合度。
- **异步通信**：`uac-newusers` 模块通过消息队列与系统其他部分通信，实现了时间上的解耦和空间上的解耦。

## 架构图示
```mermaid
graph TD
Client[客户端] --> |HTTP请求| uac_service[uac-service]
uac_service --> |调用| uac_api[uac-api<br>请求/响应/接口]
uac_service --> |使用| uac_common[uac-common<br>配置/工具/异常]
uac_service --> |访问| uac_db_common[uac-db-common<br>DAO/实体/Repository]
subgraph "异步处理"
Scheduler[ScanWaitOpenUser] --> |发送消息| MQ[(消息队列)]
MQ --> |消费消息| Consumer[OpenUserConsumer]
Consumer --> uac_db_common
Consumer --> uac_common
end
subgraph "定时任务"
CronJob[RefreshAccessTokenTask] --> |定时执行| uac_db_common
CronJob --> |调用| uac_common
end
style uac_api fill:#e1f5fe,stroke:#039be5
style uac_common fill:#e8f5e8,stroke:#43a047
style uac_db_common fill:#f3e5f5,stroke:#8e24aa
style uac_service fill:#fff3e0,stroke:#fb8c00
style Scheduler fill:#ffecb3,stroke:#f57f17
style Consumer fill:#ffecb3,stroke:#f57f17
style CronJob fill:#b3e5fc,stroke:#0288d1
```

**图示来源**  
- [uac-api](file://uac-api)
- [uac-common](file://uac-common)
- [uac-db-common](file://uac-db-common)
- [uac-service](file://uac-service)
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java)
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java)

## 可维护性与可扩展性
该多模块架构显著提升了系统的可维护性和可扩展性：
- **可维护性**：代码职责清晰，修改一个模块（如修复一个DAO）通常不会影响其他模块。公共功能集中管理，减少了重复代码，降低了维护成本。
- **可扩展性**：添加新功能（如支持新的电商平台）时，只需在 `uac-common` 中添加新的配置和适配器实现，在 `uac-db-common` 中添加新的DAO和表，在 `uac-service` 中添加新的控制器即可，对现有代码侵入性小。独立的 `uac-job` 和 `uac-newusers` 模块也便于横向扩展。

## 结论
usercenter-service-group通过精心设计的多模块架构，实现了清晰的职责划分和松耦合的模块间关系。`uac-api`、`uac-common`、`uac-db-common` 作为基础层，为 `uac-service`、`uac-newusers` 和 `uac-job` 等业务层提供了稳定、可复用的能力。这种分层和模块化的设计，不仅使系统结构清晰、易于理解，而且极大地增强了系统的可维护性、可测试性和可扩展性，为用户中心服务的长期稳定运行和持续迭代奠定了坚实的基础。