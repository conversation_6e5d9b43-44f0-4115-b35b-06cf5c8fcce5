# 新用户调度器

<cite>
**本文档引用文件**  
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java)
- [ScanRetryUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanRetryUser.java)
- [ScanPullUserDataTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/pull/ScanPullUserDataTask.java)
- [OpenUserConsumer.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/consumer/OpenUserConsumer.java)
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java)
- [NewUserTaskConfig.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/config/NewUserTaskConfig.java)
- [NewuserConsumerConfig.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/config/NewuserConsumerConfig.java)
</cite>

## 目录
1. [引言](#引言)
2. [核心定时任务机制](#核心定时任务机制)
3. [任务调度配置与执行策略](#任务调度配置与执行策略)
4. [消息消费者与RocketMQ集成](#消息消费者与rocketmq集成)
5. [异常处理与监控机制](#异常处理与监控机制)
6. [最终一致性与高可用性保障](#最终一致性与高可用性保障)
7. [结论](#结论)

## 引言
新用户调度器（uac-newuser-scheduler）是用户中心服务中的关键子模块，负责驱动新用户开通流程的自动化执行。该模块通过Spring Scheduler实现定时任务调度，结合RocketMQ异步消息机制，确保新用户从待开通到数据拉取的全流程可靠执行。本文档深入解析其核心调度机制，涵盖任务扫描、重试处理、数据拉取及消息消费等关键环节。

## 核心定时任务机制

### ScanWaitOpenUser任务：扫描待开通用户
`ScanWaitOpenUser`任务负责周期性扫描数据库中状态为“待开通”的新用户记录，并将其状态更新为“开通中”，随后通过RocketMQ消息队列触发实际的开通流程。

该任务通过`@Scheduled(fixedDelayString = "${uac.newuser.task.scanWaitOpenUser.delay:#{1 * 60 * 1000}}")`注解配置，每分钟执行一次。其核心逻辑在`NewUserTaskServiceImpl.scanWaitOpenUser`方法中实现：
1. 分页查询所有状态为`WAIT_OPEN`的用户记录。
2. 对于非预发环境，根据灰度配置过滤用户。
3. 将每个待开通用户的状态更新为`OPENING`。
4. 构造`OpenUserRequest`消息，发送至指定的RocketMQ主题，交由`OpenUserConsumer`进行后续处理。

```mermaid
flowchart TD
A[开始定时任务] --> B{开关是否开启?}
B --> |否| C[跳过执行]
B --> |是| D[分页查询WAIT_OPEN状态用户]
D --> E{查询结果为空?}
E --> |是| F[结束]
E --> |否| G[更新用户状态为OPENING]
G --> H[构造OpenUserRequest消息]
H --> I[发送至RocketMQ队列]
I --> J[继续处理下一批]
J --> D
```

**图示来源**  
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java#L70-L90)
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L60-L100)

**章节来源**  
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java)
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java)

### ScanRetryUser任务：处理开通失败用户
`ScanRetryUser`任务用于处理因临时故障而开通失败的用户，实现自动重试机制，提升系统容错能力。

该任务通过`@Scheduled(fixedDelayString = "${uac.newuser.task.scanRetryUser.delay:#{1 * 60 * 1000}}")`配置，同样每分钟执行一次。其逻辑在`NewUserTaskServiceImpl.scanRetryUser`中：
1. 查询所有状态为`WAIT_RETRY`的用户。
2. 检查重试次数，若超过最大重试次数（默认480次），则标记为永久失败。
3. 否则，递增重试计数，更新状态为`OPENING`。
4. 重新发送`OpenUserRequest`消息到队列，触发重试。

此机制确保了因网络抖动、服务短暂不可用等瞬时问题导致的失败能够被自动恢复。

```mermaid
flowchart TD
A[开始定时任务] --> B{开关是否开启?}
B --> |否| C[跳过执行]
B --> |是| D[分页查询WAIT_RETRY状态用户]
D --> E{查询结果为空?}
E --> |是| F[结束]
E --> |否| G[检查重试次数]
G --> H{超过最大重试?}
H --> |是| I[标记为FAILED]
H --> |否| J[递增重试计数]
J --> K[更新状态为OPENING]
K --> L[重新发送消息至队列]
L --> M[继续处理下一批]
M --> D
```

**图示来源**  
- [ScanRetryUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanRetryUser.java#L70-L90)
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L102-L140)

**章节来源**  
- [ScanRetryUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanRetryUser.java)
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java)

### ScanPullUserDataTask任务：驱动用户数据拉取
`ScanPullUserDataTask`任务负责处理用户数据拉取流程中的异常情况，确保数据同步的完整性。

该任务包含多个子任务，均通过`@Scheduled`注解配置：
- `scanUserFixLongWaiting`: 每30分钟扫描超过30分钟仍处于“拉取中”状态的用户，标记为“待修复”。
- `scanUserFailedLongWaiting`: 每30分钟扫描超过2天处于“拉取失败”状态的用户，标记为“待修复”。
- `scanUserWaitFixFailed`: 每30分钟扫描“修复失败”的用户，重新标记为“待修复”。
- `scanUserWaitFix`: 每5分钟扫描所有“待修复”用户，触发数据拉取流程。

其核心逻辑在`NewUserTaskServiceImpl.scanUserWaitFix`中，通过线程池并行处理，调用`sendMessage2PullDataQueue`方法向拉取数据队列发送消息。

```mermaid
flowchart TD
subgraph "扫描异常状态"
A[scanUserFixLongWaiting] --> |每30分钟| B[标记超时拉取为待修复]
C[scanUserFailedLongWaiting] --> |每30分钟| D[标记长期失败为待修复]
E[scanUserWaitFixFailed] --> |每30分钟| F[重试修复失败]
end
subgraph "执行修复"
G[scanUserWaitFix] --> |每5分钟| H[扫描待修复用户]
H --> I[并行处理]
I --> J[发送PullDataRequest消息]
J --> K[更新状态为拉取中]
end
```

**图示来源**  
- [ScanPullUserDataTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/pull/ScanPullUserDataTask.java#L80-L180)
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L250-L400)

**章节来源**  
- [ScanPullUserDataTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/pull/ScanPullUserDataTask.java)
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java)

## 任务调度配置与执行策略

### 调度频率与开关配置
所有定时任务的执行频率和开关状态均通过`NewUserTaskConfig`类从配置文件注入，实现了灵活的运维控制。

关键配置项包括：
- **scanWaitOpenUser.delay**: 扫描待开通用户的间隔，默认60秒。
- **scanRetryUser.delay**: 扫描重试用户的间隔，默认60秒。
- **scanWaitOpenUser.enable**: 扫描待开通用户的开关，默认关闭。
- **scanRetryUser.enable**: 扫描重试用户的开关，默认关闭。
- **business.pool.size**: 业务线程池大小，默认10个线程。

```java
@Value("${uac.newuser.task.scanWaitOpenUser.delay:#{1 * 60 * 1000}}")
public void scanWaitOpenUser() { ... }
```

### 线程池执行策略
为避免任务阻塞主线程，所有任务均使用独立的线程池进行异步执行。`ScanWaitOpenUser`和`ScanRetryUser`使用`businessExecutorService`，在`@PostConstruct`方法中初始化，核心和最大线程数由`businessPoolSize`配置项决定。这种设计保证了任务的并行处理能力，提升了系统吞吐量。

**章节来源**  
- [NewUserTaskConfig.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/config/NewUserTaskConfig.java)
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java#L40-L60)

## 消息消费者与RocketMQ集成

### OpenUserConsumer消息消费者
`OpenUserConsumer`是处理新用户开通消息的核心消费者，继承自`BaseOnsConsumer`，实现了`execute`方法来处理接收到的`OpenUserRequest`消息。

其处理流程如下：
1. 反序列化消息内容。
2. 校验消息结构。
3. 查询用户开通记录，确认其状态为“开通中”。
4. 调用`UserSaveDataBusinessHandleService.openSaveData`执行实际的开通业务逻辑。
5. 根据返回结果更新用户状态（成功、失败、可重试）。

```mermaid
sequenceDiagram
participant Scheduler as 定时任务
participant MQ as RocketMQ
participant Consumer as OpenUserConsumer
participant Service as UserSaveDataBusinessHandleService
Scheduler->>MQ : 发送OpenUserRequest
MQ->>Consumer : 推送消息
Consumer->>Consumer : 校验消息
Consumer->>Consumer : 查询用户状态
Consumer->>Service : 调用openSaveData
Service-->>Consumer : 返回OpenResult
Consumer->>Consumer : 更新用户状态
```

**图示来源**  
- [OpenUserConsumer.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/consumer/OpenUserConsumer.java#L40-L100)
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L80-L90)

**章节来源**  
- [OpenUserConsumer.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/consumer/OpenUserConsumer.java)
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java)

### RocketMQ配置与集成
`NewuserConsumerConfig`类负责配置RocketMQ消费者。它通过`@Bean`定义`DefaultMQPushConsumer`，并利用`OnsLifeCycleManager`在应用启动时完成订阅和消息监听器的注册。

```java
commonNewuserConsumer.subscribe(queueConfig.getTopic(), queueConfig.getTag());
commonNewuserConsumer.registerMessageListener(openUserConsumer);
commonNewuserConsumer.start();
```

生产者则通过`DefaultMQProducer`注入，由`RocketMqQueueHelper.push`方法完成消息发送。

**章节来源**  
- [NewuserConsumerConfig.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/config/NewuserConsumerConfig.java)
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L80)

## 异常处理与监控机制

### 任务级异常处理
每个定时任务都包含完善的异常捕获机制。例如，在`scanWaitOpenUser`中，若更新用户状态失败，会记录错误日志并跳过该用户，确保不影响其他用户的处理。在`scanUserWaitFix`中，对每个用户的处理都包裹在`try-catch`块中，单个用户的失败不会导致整个任务中断。

### 状态更新与日志记录
系统通过`MDC`将`sellerNick`、`platformId`等关键信息注入日志上下文，便于问题追踪。所有关键操作，如任务开始、用户状态变更、消息发送成功/失败，都会通过`LoggerHelper`记录详细日志，为监控和审计提供数据支持。

**章节来源**  
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java#L80)
- [OpenUserConsumer.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/consumer/OpenUserConsumer.java#L33-L144)
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java)

## 最终一致性与高可用性保障

### 基于消息队列的最终一致性
系统采用“先更新状态，再发消息”的模式，确保了数据的一致性。即使消息发送失败，也会将用户状态回滚为“待开通”，由下一次定时任务重新处理。这种设计避免了因消息丢失导致的用户卡在“开通中”状态。

### 重试与降级机制
- **自动重试**: `ScanRetryUser`任务提供了强大的自动重试能力。
- **异常恢复**: `ScanOpenExceptionUser`任务会扫描长时间处于“开通中”状态的用户，将其重置为“待开通”，防止状态死锁。
- **配置化开关**: 所有任务均可通过配置开关动态启停，便于运维操作和故障隔离。

这些机制共同保障了新用户开通流程的高可用性和鲁棒性。

**章节来源**  
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L60-L140)
- [ScanOpenExceptionUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanOpenExceptionUser.java)

## 结论
uac-newuser-scheduler模块通过Spring Scheduler与RocketMQ的紧密结合，构建了一个健壮、可靠的新用户开通调度系统。其核心在于利用定时任务进行状态扫描和驱动，通过异步消息解耦核心业务逻辑，辅以完善的重试、异常恢复和监控机制，有效保障了新用户开通流程的最终一致性和高可用性。该设计模式为类似的后台批处理和状态机驱动场景提供了优秀的实践参考。