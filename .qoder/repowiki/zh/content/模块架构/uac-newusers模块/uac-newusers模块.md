# uac-newusers模块

<cite>
**本文档引用文件**  
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java)
- [ScanRetryUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanRetryUser.java)
- [ScanPullUserDataTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/pull/ScanPullUserDataTask.java)
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java)
- [NewUserPlatformHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/NewUserPlatformHandleService.java)
- [PddNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/PddNewUserPlatformHandleServiceImpl.java)
</cite>

## 目录
1. [引言](#引言)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 引言
本文档全面解析uac-newusers模块在新用户开通与管理方面的架构与流程。重点阐述该模块如何通过消息队列和定时任务解耦用户开通流程，实现高可靠性和可扩展性。分析其状态机设计和异常处理机制，确保新用户数据最终一致性，并结合具体业务场景说明从用户注册到完成平台授权的完整生命周期管理。

## 项目结构
uac-newusers模块采用分层架构设计，包含三个核心子模块：uac-newuser-scheduler（定时任务调度）、uac-newuser-service（API服务）和uac-newuser-common（通用业务逻辑）。这种设计实现了关注点分离，提高了系统的可维护性和可扩展性。

```mermaid
graph TB
subgraph "uac-newusers"
subgraph "uac-newuser-scheduler"
ScanWaitOpenUser["ScanWaitOpenUser定时任务"]
ScanRetryUser["ScanRetryUser定时任务"]
ScanPullUserDataTask["ScanPullUserDataTask定时任务"]
end
subgraph "uac-newuser-service"
NewuserController["NewuserController"]
end
subgraph "uac-newuser-common"
NewUserPlatformHandleService["NewUserPlatformHandleService接口"]
PddNewUserPlatformHandleServiceImpl["PddNewUserPlatformHandleServiceImpl实现类"]
end
ScanWaitOpenUser --> NewuserController
ScanRetryUser --> NewuserController
ScanPullUserDataTask --> NewuserController
NewuserController --> NewUserPlatformHandleService
NewUserPlatformHandleService --> PddNewUserPlatformHandleServiceImpl
end
```

**图示来源**  
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java)
- [ScanRetryUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanRetryUser.java)
- [ScanPullUserDataTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/pull/ScanPullUserDataTask.java)
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java)
- [NewUserPlatformHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/NewUserPlatformHandleService.java)
- [PddNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/PddNewUserPlatformHandleServiceImpl.java)

**本节来源**  
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java)
- [ScanRetryUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanRetryUser.java)
- [ScanPullUserDataTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/pull/ScanPullUserDataTask.java)

## 核心组件
uac-newusers模块的核心组件包括定时任务调度器、API控制器和服务适配器。这些组件协同工作，实现了新用户开通的异步处理流程。定时任务负责驱动状态机流转，API控制器提供外部接口，服务适配器处理不同电商平台的差异化逻辑。

**本节来源**  
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java#L25-L92)
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java#L22-L101)
- [NewUserPlatformHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/NewUserPlatformHandleService.java#L12-L109)

## 架构概述
uac-newusers模块采用事件驱动架构，通过消息队列和定时任务实现用户开通流程的异步处理。该架构将用户开通过程分解为多个阶段，每个阶段由独立的定时任务处理，确保了系统的高可靠性和可扩展性。

```mermaid
sequenceDiagram
participant 用户
participant NewuserController
participant 消息队列
participant ScanWaitOpenUser
participant ScanRetryUser
participant ScanPullUserDataTask
用户->>NewuserController : 调用/prepare接口
NewuserController->>NewuserController : 验证参数并设置待开户状态
NewuserController-->>用户 : 返回提交结果
ScanWaitOpenUser->>消息队列 : 定时扫描待开通用户
消息队列->>处理服务 : 发送开户消息
ScanRetryUser->>消息队列 : 定时扫描开通失败用户
消息队列->>处理服务 : 发起重试消息
ScanPullUserDataTask->>消息队列 : 定时扫描异常用户
消息队列->>处理服务 : 发送修复消息
```

**图示来源**  
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java)
- [ScanRetryUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanRetryUser.java)
- [ScanPullUserDataTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/pull/ScanPullUserDataTask.java)
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java)

## 详细组件分析
### 定时任务组件分析
uac-newuser-scheduler子模块中的定时任务是驱动新用户开通流程的核心。这些任务按照预定的时间间隔运行，扫描数据库中的用户记录，并根据用户状态执行相应的操作。

#### 定时任务类图
```mermaid
classDiagram
class ScanWaitOpenUser {
+scanWaitOpenUser() void
}
class ScanRetryUser {
+scanRetryUser() void
}
class ScanPullUserDataTask {
+scanUserFixLongWaiting() void
+scanUserFailedLongWaiting() void
+scanUserWaitFixFailed() void
+scanUserWaitFix() void
}
class NewUserTaskService {
+scanWaitOpenUser() void
+scanRetryUser() void
+scanUserFixLongWaiting() void
+scanUserFailedLongWaiting() void
+scanUserWaitFixFailed() void
+scanUserWaitFix() void
}
ScanWaitOpenUser --> NewUserTaskService : "委托"
ScanRetryUser --> NewUserTaskService : "委托"
ScanPullUserDataTask --> NewUserTaskService : "委托"
```

**图示来源**  
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java#L25-L92)
- [ScanRetryUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanRetryUser.java#L24-L89)
- [ScanPullUserDataTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/pull/ScanPullUserDataTask.java#L25-L182)

**本节来源**  
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java#L25-L92)
- [ScanRetryUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanRetryUser.java#L24-L89)
- [ScanPullUserDataTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/pull/ScanPullUserDataTask.java#L25-L182)

### API服务组件分析
uac-newuser-service子模块提供新用户相关的API接口，主要通过NewuserController类实现。该控制器处理用户开通准备和进度查询等核心功能。

#### API调用序列图
```mermaid
sequenceDiagram
participant Client as "客户端"
participant Controller as "NewuserController"
participant Service as "UserSaveDataBusinessHandleService"
participant Validator as "SessionValidateUtil"
Client->>Controller : POST /uac/newuser/prepare
Controller->>Validator : checkSessionInfo()
Validator-->>Controller : 验证结果
Controller->>Controller : 参数校验
Controller->>Service : prepareSaveData()
Service-->>Controller : 处理结果
Controller-->>Client : 返回响应
Client->>Controller : GET /uac/newuser/saveDataCourse
Controller->>Validator : checkSessionInfo()
Validator-->>Controller : 验证结果
Controller->>Service : getPullDataProgress()
Service-->>Controller : 进度信息
Controller-->>Client : 返回进度
```

**图示来源**  
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java#L22-L101)

**本节来源**  
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java#L22-L101)

### 平台适配组件分析
uac-newuser-common子模块中的NewUserPlatformHandleService接口及其实现类负责适配不同电商平台的新用户开通逻辑。通过策略模式，系统能够灵活支持多个电商平台。

#### 平台适配类图
```mermaid
classDiagram
class NewUserPlatformHandleService {
<<interface>>
+isNeedPullData() Boolean
+isNeedSubscribeMc() Boolean
+isNeedSubscribeRds() Boolean
+getRdsRule() String
+isNeedCompareAuthDeadline() Boolean
+handlePrepareOpenOnNormalCondition() void
+checkTokenAndSend2PullDataQueue() Boolean
+send2PullDataQueue() Boolean
}
class AbstractNewUserPlatformHandleServiceImpl {
<<abstract>>
}
class PddNewUserPlatformHandleServiceImpl {
+isNeedPullData() Boolean
+isNeedSubscribeMc() Boolean
+getDispatcherId() String
}
class TaoNewUserPlatformHandleServiceImpl {
+isNeedPullData() Boolean
+isNeedSubscribeMc() Boolean
+getDispatcherId() String
}
NewUserPlatformHandleService <|-- AbstractNewUserPlatformHandleServiceImpl
AbstractNewUserPlatformHandleServiceImpl <|-- PddNewUserPlatformHandleServiceImpl
AbstractNewUserPlatformHandleServiceImpl <|-- TaoNewUserPlatformHandleServiceImpl
```

**图示来源**  
- [NewUserPlatformHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/NewUserPlatformHandleService.java#L12-L109)
- [PddNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/PddNewUserPlatformHandleServiceImpl.java#L11-L28)

**本节来源**  
- [NewUserPlatformHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/NewUserPlatformHandleService.java#L12-L109)
- [PddNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/PddNewUserPlatformHandleServiceImpl.java#L11-L28)

## 依赖分析
uac-newusers模块依赖于多个内部和外部组件。内部依赖包括uac-common、uac-db-common等基础模块，外部依赖包括消息队列、Redis缓存等中间件服务。

```mermaid
graph TD
uac-newusers --> uac-common
uac-newusers --> uac-db-common
uac-newusers --> uac-domain
uac-newusers --> RocketMQ
uac-newusers --> Redis
uac-newusers --> MySQL
subgraph "uac-newusers"
uac-newuser-scheduler
uac-newuser-service
uac-newuser-common
end
```

**图示来源**  
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java)
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java)
- [NewUserPlatformHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/NewUserPlatformHandleService.java)

**本节来源**  
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java)
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java)
- [NewUserPlatformHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/NewUserPlatformHandleService.java)

## 性能考虑
uac-newusers模块在设计时充分考虑了性能因素。通过使用线程池处理并发任务，避免了创建过多线程带来的资源消耗。定时任务采用固定延迟执行，确保了任务调度的稳定性。同时，系统通过批量处理和异步执行机制，提高了整体处理效率。

## 故障排除指南
当新用户开通流程出现问题时，应首先检查定时任务是否正常运行，然后查看消息队列是否有积压。对于特定电商平台的问题，需要检查对应的平台适配实现类是否正确配置。日志分析是故障排除的重要手段，应重点关注定时任务执行日志和消息处理日志。

**本节来源**  
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java#L25-L92)
- [ScanRetryUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanRetryUser.java#L24-L89)
- [ScanPullUserDataTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/pull/ScanPullUserDataTask.java#L25-L182)

## 结论
uac-newusers模块通过精心设计的架构实现了新用户开通与管理的高效处理。定时任务与消息队列的结合使用，确保了系统的高可靠性和可扩展性。状态机设计和异常处理机制保证了新用户数据的最终一致性。平台适配器模式使得系统能够灵活支持多个电商平台，为业务发展提供了坚实的技术基础。