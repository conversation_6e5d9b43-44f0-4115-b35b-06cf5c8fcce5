# 新用户服务接口

<cite>
**本文档中引用的文件**  
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java)
- [ExportNewUserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/export/ExportNewUserController.java)
- [UserSaveDataBusinessHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/business/UserSaveDataBusinessHandleService.java)
- [SaveDataDTO.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/dto/SaveDataDTO.java)
- [SaveDataCourseDTO.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/dto/SaveDataCourseDTO.java)
- [UserInfoDTO.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/dto/UserInfoDTO.java)
- [ExportSaveDataCourseRequest.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/dto/request/ExportSaveDataCourseRequest.java)
- [ErrorCode.java](file://uac-common/src/main/java/cn/loveapp/uac/common/code/ErrorCode.java)
- [ApiCode.java](file://uac-common/src/main/java/cn/loveapp/uac/common/code/ApiCode.java)
- [RateLimitHelper.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/helper/RateLimitHelper.java)
- [OpenUserCommonConfig.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/config/OpenUserCommonConfig.java)
</cite>

## 目录
1. [简介](#简介)
2. [核心接口说明](#核心接口说明)
3. [数据导出接口](#数据导出接口)
4. [认证与安全](#认证与安全)
5. [限流策略](#限流策略)
6. [错误码定义](#错误码定义)
7. [版本管理](#版本管理)
8. [客户端调用示例](#客户端调用示例)
9. [调试技巧与常见问题](#调试技巧与常见问题)

## 简介
新用户服务API为系统提供用户开通与数据拉取进度管理功能，支持通过RESTful接口获取用户开通进度、提交开通/关闭请求。该服务主要由`NewuserController`和`ExportNewUserController`两个控制器提供，分别面向内部系统和外部导出场景。

**Section sources**
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java#L1-L102)
- [ExportNewUserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/export/ExportNewUserController.java#L1-L78)

## 核心接口说明

### 获取开通进度接口
获取当前登录用户的存数据进度信息。

**HTTP方法**: `GET`  
**URL路径**: `/uac/newuser/saveDataCourse`  
**认证方式**: Session校验（通过Cookie或Header传递）  
**请求头要求**: 无特殊要求  
**响应数据结构**:
```json
{
  "code": 200,
  "msg": "成功",
  "body": {
    "progress": 1000,
    "total": 5000,
    "status": "PROCESSING"
  }
}
```

**字段说明**:
- `progress`: 当前已拉取的数据量
- `total`: 预计总数据量
- `status`: 开通状态（如PROCESSING, SUCCESS, FAILED）

**Section sources**
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java#L25-L48)
- [SaveDataCourseDTO.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/dto/SaveDataCourseDTO.java)

### 提交开通/关闭请求接口
为指定用户准备开通或关闭存数据服务。

**HTTP方法**: `POST`  
**URL路径**: `/uac/newuser/prepare`  
**认证方式**: AppKey/Secret（通过请求参数或Header）  
**请求参数（Body）**:
```json
{
  "sellerId": "123456",
  "businessId": "aiyong",
  "platform": "taobao",
  "serviceName": "distribute",
  "closeSaveData": false
}
```

**字段说明**:
- `sellerId`: 卖家ID
- `businessId`: 业务ID（如aiyong）
- `platform`: 平台ID
- `serviceName`: 服务名称
- `closeSaveData`: 是否关闭存数据

**响应数据结构**:
```json
{
  "code": 200,
  "msg": "提交成功",
  "body": true
}
```

**Section sources**
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java#L50-L80)
- [SaveDataDTO.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/dto/SaveDataDTO.java)

## 数据导出接口

### 外部系统获取开通进度
供外部系统调用以获取指定用户的存数据进度。

**HTTP方法**: `POST`  
**URL路径**: `/export/uac/newuser/saveDataCourse`  
**认证方式**: AppKey/Secret + 参数签名  
**请求参数（Body）**:
```json
{
  "sellerNick": "卖家昵称",
  "sellerId": "123456",
  "appName": "distribute",
  "platformId": "taobao",
  "businessId": "aiyong"
}
```

**响应数据结构**: 同内部获取进度接口

**Section sources**
- [ExportNewUserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/export/ExportNewUserController.java#L25-L78)
- [ExportSaveDataCourseRequest.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/dto/request/ExportSaveDataCourseRequest.java)

## 认证与安全
- **内部接口**: 使用Session校验，通过`SessionValidateUtil`工具类验证用户会话信息。
- **外部接口**: 采用AppKey/Secret机制进行身份认证，请求需包含签名信息。
- 所有敏感操作均需进行参数校验和权限验证。

**Section sources**
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java#L82-L102)
- [SessionValidateUtil.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/utils/SessionValidateUtil.java)

## 限流策略
系统采用Redis实现两级限流控制：

### 准备请求限流
- **限流Key前缀**: `filterPrepareRequest`
- **有效期**: 5秒（可配置）
- **作用**: 防止短时间内重复提交开通请求

### 拉取数据限流
- **限流Key前缀**: `filterPullData`
- **有效期**: 12小时（可配置）
- **作用**: 控制数据拉取消息的发送频率

**配置参数**:
- `uac.newuser.openuser.lock.expire.time`: 准备请求锁过期时间
- `uac.newuser.openuser.sendPullDataQueue.expire.time`: 拉取数据队列过期时间

```mermaid
flowchart TD
Start([开始]) --> ValidateParams["验证请求参数"]
ValidateParams --> CheckRateLimit["检查限流"]
CheckRateLimit --> |已限流| ReturnError["返回限流错误"]
CheckRateLimit --> |未限流| ProcessRequest["处理业务逻辑"]
ProcessRequest --> UpdateRedis["更新Redis状态"]
UpdateRedis --> ReturnSuccess["返回成功"]
ReturnError --> End([结束])
ReturnSuccess --> End
```

**Diagram sources**
- [RateLimitHelper.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/helper/RateLimitHelper.java#L19-L114)
- [OpenUserCommonConfig.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/config/OpenUserCommonConfig.java#L14-L54)

**Section sources**
- [RateLimitHelper.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/helper/RateLimitHelper.java#L19-L114)

## 错误码定义
参考`uac-common`模块中的`ErrorCode`和`ApiCode`定义。

### 基础错误码 (BaseCode)
| 错误码 | 含义 | 说明 |
|--------|------|------|
| 0 | 成功 | 操作成功 |
| 1 | 程序异常 | 系统内部错误 |
| 2 | 参数错误 | 请求参数不合法 |
| 3 | 请求错误 | 请求格式或方法错误 |
| 4 | 签名秘钥错误 | AppKey/Secret不匹配 |
| 5 | 数据加密密钥错误 | 加密解密密钥错误 |
| 6 | 签名错误 | 签名验证失败 |
| 7 | 系统维护 | 系统正在维护中 |
| 8 | DB错误 | 数据库操作失败 |
| 9 | Cache错误 | 缓存操作失败 |

### API专用错误码
| 错误码 | 含义 | 说明 |
|--------|------|------|
| 20001 | 默认爱用Code | 通用成功码 |
| 20002 | 网络或数据库错误 | 可重试错误 |
| 20003 | 无效的用户信息 | 需重新登录 |
| 20004 | 用户授权失效 | Session过期 |
| 20005 | VIP信息无效 | 用户级别不支持 |
| 20006 | 接口暂时降级 | 服务临时不可用 |

**Section sources**
- [ErrorCode.java](file://uac-common/src/main/java/cn/loveapp/uac/common/code/ErrorCode.java#L8-L86)
- [ApiCode.java](file://uac-common/src/main/java/cn/loveapp/uac/common/code/ApiCode.java#L7-L76)

## 版本管理
- 接口版本通过URL路径前缀管理（如`/v1/uac/newuser`）
- 当前为默认版本（无版本号前缀）
- 未来升级将采用灰度发布策略，通过`GRAY_USER_CODE`区分灰度用户
- 版本兼容性保证向后兼容至少两个大版本

**Section sources**
- [OpenUserCommonConfig.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/config/OpenUserCommonConfig.java)

## 客户端调用示例

### Java调用示例
```java
// 获取进度
String url = "http://api.example.com/uac/newuser/saveDataCourse";
HttpHeaders headers = new HttpHeaders();
headers.set("Cookie", "JSESSIONID=xxx");
ResponseEntity<CommonApiResponse<SaveDataCourseDTO>> response = 
    restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(headers), 
    new ParameterizedTypeReference<CommonApiResponse<SaveDataCourseDTO>>() {});
```

### Python调用示例
```python
import requests

# 提交开通请求
url = "http://api.example.com/uac/newuser/prepare"
data = {
    "sellerId": "123456",
    "businessId": "aiyong",
    "platform": "taobao",
    "serviceName": "distribute",
    "closeSaveData": False
}
response = requests.post(url, json=data, headers={"AppKey": "your_key"})
print(response.json())
```

**Section sources**
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java)
- [ExportNewUserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/export/ExportNewUserController.java)

## 调试技巧与常见问题

### 调试技巧
1. **日志追踪**: 使用`LoggerHelper`记录关键操作，通过`sellerNick`和`businessId`定位问题
2. **Redis监控**: 检查限流Key是否存在，确认`openUserRedisRepository`状态
3. **参数验证**: 确保`PlatformAndAppNameUtil`正确处理平台和应用名称

### 常见问题排查
| 问题现象 | 可能原因 | 解决方案 |
|---------|--------|--------|
| 获取进度失败 | Session无效 | 重新登录获取新Session |
| 提交请求被限流 | 频繁调用 | 等待5秒后重试 |
| 用户信息获取失败 | 卖家ID不存在 | 检查sellerId是否正确 |
| 响应为空 | Redis连接异常 | 检查Redis服务状态 |
| 状态不更新 | 消息队列积压 | 检查OpenUserConsumer消费情况 |

**Section sources**
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java)
- [RateLimitHelper.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/helper/RateLimitHelper.java)
- [OpenUserCommonConfig.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/config/OpenUserCommonConfig.java)