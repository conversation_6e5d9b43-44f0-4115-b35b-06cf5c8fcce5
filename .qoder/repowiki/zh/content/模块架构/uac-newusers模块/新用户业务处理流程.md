# 新用户业务处理流程

<cite>
**本文档引用文件**  
- [UserService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/service/UserService.java)
- [UserServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/service/impl/UserServiceImpl.java)
- [AbstractUserSaveDataBusinessHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/business/impl/AbstractUserSaveDataBusinessHandleService.java)
- [ItemUserSaveDataBusinessHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/business/impl/ItemUserSaveDataBusinessHandleServiceImpl.java)
- [SaveDataDTO.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/dto/SaveDataDTO.java)
- [OpenUserConsumer.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/consumer/OpenUserConsumer.java)
- [NewUserPlatformHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/NewUserPlatformHandleService.java)
- [AbstractNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/AbstractNewUserPlatformHandleServiceImpl.java)
- [OpenUserRequest.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/proto/OpenUserRequest.java)
</cite>

## 目录
1. [引言](#引言)
2. [核心服务接口与实现](#核心服务接口与实现)
3. [用户数据保存业务处理](#用户数据保存业务处理)
4. [数据传输对象（DTO）设计](#数据传输对象dto设计)
5. [完整业务流程序列图](#完整业务流程序列图)
6. [状态转换机制](#状态转换机制)
7. [异常处理与回滚策略](#异常处理与回滚策略)
8. [幂等性保障机制](#幂等性保障机制)
9. [总结](#总结)

## 引言
本文档全面阐述新用户从注册到数据落地的完整业务处理流程。系统通过消息驱动的方式，基于RocketMQ异步处理新用户开通请求，确保高并发场景下的稳定性与可扩展性。核心流程涵盖用户信息校验、平台适配、数据持久化及多业务场景支持，涉及多个微服务模块协同工作。

## 核心服务接口与实现

`UserService` 接口定义了新用户开通的核心业务逻辑契约，其主要职责包括接收新用户开通请求、执行前置校验、触发数据拉取与保存流程，并管理用户开通状态的生命周期。该接口的实现类 `UserServiceImpl` 提供了具体逻辑处理，协调底层数据访问层与业务处理组件。

在用户开通流程中，`UserServiceImpl` 首先对传入的 `OpenUserRequest` 进行合法性校验，包括平台标识、用户身份、授权信息等。校验通过后，将用户状态标记为“待开通”，并发布新用户开通事件至消息队列，交由调度模块异步处理，实现请求响应与实际处理的解耦。

**本节来源**  
- [UserService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/service/UserService.java)
- [UserServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/service/impl/UserServiceImpl.java)
- [OpenUserRequest.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/proto/OpenUserRequest.java)

## 用户数据保存业务处理

`UserSaveDataBusinessHandleService` 是一个抽象业务处理接口，用于统一处理不同类型业务数据的保存逻辑。该接口支持多种业务场景（如商品、订单、用户信息等），通过策略模式实现不同业务类型的差异化处理。

其抽象实现类 `AbstractUserSaveDataBusinessHandleService` 提供了通用的数据处理骨架，包括数据预处理、校验、分页拉取、批量保存、进度更新等公共逻辑。具体的业务实现类（如 `ItemUserSaveDataBusinessHandleServiceImpl`）继承该抽象类，仅需实现特定业务的数据查询与转换逻辑，从而实现代码复用与职责分离。

此类设计支持灵活扩展，新增业务类型时只需新增实现类并注册至Spring容器，无需修改核心调度逻辑，符合开闭原则。

**本节来源**  
- [UserSaveDataBusinessHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/business/UserSaveDataBusinessHandleService.java)
- [AbstractUserSaveDataBusinessHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/business/impl/AbstractUserSaveDataBusinessHandleService.java)
- [ItemUserSaveDataBusinessHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/business/impl/ItemUserSaveDataBusinessHandleServiceImpl.java)

## 数据传输对象（DTO）设计

`SaveDataDTO` 是贯穿整个数据保存流程的核心数据传输对象，封装了数据拉取与保存所需的所有上下文信息。其主要字段包括：用户标识、平台类型、业务类型、当前处理页码、每页大小、总数据量、处理状态、错误信息等。

该DTO在消息传递、服务调用、状态更新等环节中作为统一的数据载体，确保各组件间数据结构的一致性。同时，通过序列化机制在消息队列中传输，支持跨服务的数据流转。

此外，`UserInfoDTO`、`NewUserPlatformHandleDTO` 等辅助DTO用于封装平台特定的用户信息与处理参数，增强系统的可配置性与灵活性。

**本节来源**  
- [SaveDataDTO.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/dto/SaveDataDTO.java)
- [UserInfoDTO.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/dto/UserInfoDTO.java)
- [NewUserPlatformHandleDTO.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/dto/NewUserPlatformHandleDTO.java)

## 完整业务流程序列图

```mermaid
sequenceDiagram
participant API as API接口
participant UserService as UserService
participant MQ as RocketMQ
participant Consumer as OpenUserConsumer
participant PlatformService as NewUserPlatformHandleService
participant BusinessService as UserSaveDataBusinessHandleService
participant DB as 数据库
API->>UserService : openUser(OpenUserRequest)
UserService->>UserService : 校验请求参数
UserService->>UserService : 更新状态为"待开通"
UserService->>MQ : 发送开通消息
MQ-->>Consumer : 消费消息
Consumer->>PlatformService : getPlatformHandle(platform)
PlatformService-->>Consumer : 返回具体实现
Consumer->>BusinessService : execute(SaveDataDTO)
BusinessService->>BusinessService : 分页拉取数据
loop 每一页数据
BusinessService->>PlatformService : pullData(page, size)
PlatformService-->>BusinessService : 返回原始数据
BusinessService->>BusinessService : 转换为统一格式
BusinessService->>DB : 批量保存
BusinessService->>DB : 更新处理进度
end
BusinessService-->>Consumer : 处理完成
Consumer->>UserService : 更新最终状态
UserService->>DB : 持久化用户状态
```

**图示来源**  
- [UserServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/service/impl/UserServiceImpl.java)
- [OpenUserConsumer.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/consumer/OpenUserConsumer.java)
- [AbstractNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/AbstractNewUserPlatformHandleServiceImpl.java)
- [AbstractUserSaveDataBusinessHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/business/impl/AbstractUserSaveDataBusinessHandleService.java)

## 状态转换机制

系统定义了清晰的用户开通状态机，关键状态包括：
- **待开通**：初始状态，请求已接收但未开始处理
- **开通中**：消息已被消费，正在执行数据拉取与保存
- **成功**：所有数据已成功保存，流程结束
- **失败**：处理过程中发生不可恢复错误
- **重试中**：发生可恢复错误，进入重试队列

状态转换由 `OpenUserConsumer` 在处理过程中驱动，每次状态变更均会持久化至数据库，并可能触发后续动作（如失败后发送告警）。状态信息也用于前端进度查询接口，提供实时开通进度反馈。

**本节来源**  
- [OpenUserConsumer.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/consumer/OpenUserConsumer.java)
- [UserServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/service/impl/UserServiceImpl.java)

## 异常处理与回滚策略

系统采用分层异常处理机制：
- **可恢复异常**（如网络超时、数据库锁冲突）：捕获后记录日志，将用户重新投递至延迟队列，等待重试
- **不可恢复异常**（如参数错误、权限不足）：标记为失败状态，终止流程并记录错误原因

对于部分已成功保存的数据，系统不提供自动回滚，而是通过“失败-分析-人工干预”模式处理。设计上倾向于“最终一致性”而非强一致性，避免复杂事务带来的性能损耗与复杂度上升。

重试机制由RocketMQ的重试队列和调度任务（如 `ScanRetryUser`）共同保障，支持指数退避策略，防止雪崩效应。

**本节来源**  
- [OpenUserConsumer.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/consumer/OpenUserConsumer.java)
- [ScanRetryUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanRetryUser.java)

## 幂等性保障机制

为确保消息重复消费或接口重复调用不会导致数据重复或状态错乱，系统在多个层面实现幂等性：

1. **消息消费幂等**：`OpenUserConsumer` 在处理前先检查用户当前状态，若已为“开通中”或“成功”，则直接跳过处理。
2. **数据保存幂等**：在数据库层面通过唯一索引（如用户ID+平台ID）防止重复插入；在业务逻辑中通过状态判断避免重复操作。
3. **状态更新幂等**：状态变更操作基于当前状态进行条件更新（如仅当状态为“待开通”时才允许改为“开通中”），防止状态倒退或越级变更。

这些机制共同确保了系统在高并发、网络不稳定等异常场景下的行为一致性。

**本节来源**  
- [OpenUserConsumer.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/consumer/OpenUserConsumer.java)
- [UserServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/service/impl/UserServiceImpl.java)

## 总结

新用户业务处理流程采用异步消息驱动架构，通过 `UserService` 接口接收请求，经由消息队列解耦，由 `OpenUserConsumer` 调度执行。流程中通过 `UserSaveDataBusinessHandleService` 抽象类支持多业务场景的数据保存，利用 `SaveDataDTO` 统一数据上下文。系统具备完善的状态管理、异常重试与幂等性保障机制，确保了业务流程的健壮性与可靠性。