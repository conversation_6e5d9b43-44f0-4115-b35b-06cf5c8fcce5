# uac-job模块

<cite>
**本文档引用的文件**   
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java)
- [RefreshAccessTokenTaskConfig.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/config/RefreshAccessTokenTaskConfig.java)
- [SellerService.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/service/SellerService.java)
- [SellerServiceImpl.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/service/impl/SellerServiceImpl.java)
- [BaseSellerService.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/base/BaseSellerService.java)
</cite>

## 目录
1. [项目结构](#项目结构)
2. [核心组件](#核心组件)
3. [架构概述](#架构概述)
4. [详细组件分析](#详细组件分析)
5. [依赖分析](#依赖分析)
6. [性能考虑](#性能考虑)
7. [故障排除指南](#故障排除指南)
8. [结论](#结论)

## 项目结构

```mermaid
graph TD
uac-job[uac-job模块] --> uac-authorization-job[uac-authorization-job]
uac-authorization-job --> config[config]
uac-authorization-job --> task[task]
config --> RefreshAccessTokenTaskConfig[RefreshAccessTokenTaskConfig]
task --> RefreshAccessTokenTask[RefreshAccessTokenTask]
```

**图表来源**
- [RefreshAccessTokenTaskConfig.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/config/RefreshAccessTokenTaskConfig.java)
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java)

**章节来源**
- [RefreshAccessTokenTaskConfig.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/config/RefreshAccessTokenTaskConfig.java#L1-L20)
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java#L1-L30)

## 核心组件

`RefreshAccessTokenTask` 是 uac-job 模块中的核心定时任务组件，负责周期性地扫描即将过期的用户访问令牌，并通过调用各电商平台提供的刷新接口实现令牌的自动续期。该任务通过 `RefreshAccessTokenTaskConfig` 配置类定义执行周期、线程池配置和容错策略。

`RefreshAccessTokenTaskConfig` 配置类通过 Spring 的 `@Configuration` 注解定义了多个配置项，包括线程池大小、核心线程数以及各平台令牌刷新开关等。这些配置项通过 `@Value` 注解从配置文件中读取，支持动态调整。

**章节来源**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java#L29-L290)
- [RefreshAccessTokenTaskConfig.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/config/RefreshAccessTokenTaskConfig.java#L12-L63)

## 架构概述

```mermaid
sequenceDiagram
participant Scheduler as 定时调度器
participant Task as RefreshAccessTokenTask
participant Service as SellerService
participant Auth as AuthService
participant DB as 数据库
participant Redis as Redis缓存
Scheduler->>Task : 触发定时任务
Task->>DB : 查询即将过期的用户令牌
DB-->>Task : 返回用户列表
loop 批量处理用户
Task->>Service : 调用refreshAccessToken
Service->>Auth : 执行令牌刷新逻辑
Auth->>电商平台 : 调用刷新接口
电商平台-->>Auth : 返回新令牌
Auth-->>Service : 返回刷新结果
Service-->>Task : 返回新访问令牌
end
Task->>DB : 更新用户令牌信息
Task->>Redis : 更新缓存
```

**图表来源**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java#L29-L290)
- [SellerService.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/service/SellerService.java#L1-L140)
- [SellerServiceImpl.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/service/impl/SellerServiceImpl.java#L1-L644)

## 详细组件分析

### RefreshAccessTokenTask 分析

`RefreshAccessTokenTask` 类实现了多个平台的令牌刷新任务，包括淘宝、抖音、1688、快手、微信小店、有赞和小红书等。每个平台都有独立的定时任务方法，通过 `@Scheduled` 注解配置不同的执行周期。

任务执行流程如下：
1. 检查对应平台的刷新开关是否开启
2. 计算令牌过期时间范围
3. 分页查询需要刷新令牌的用户
4. 批量处理用户，调用 `SellerService.refreshAccessToken` 方法刷新令牌
5. 记录执行日志和耗时

```mermaid
flowchart TD
Start([开始]) --> CheckSwitch["检查平台刷新开关"]
CheckSwitch --> |关闭| End1([忽略任务])
CheckSwitch --> |开启| CalculateTime["计算过期时间范围"]
CalculateTime --> QueryUsers["分页查询用户"]
QueryUsers --> HasUsers{"有用户?"}
HasUsers --> |否| End2([处理完毕])
HasUsers --> |是| ProcessBatch["批量处理用户"]
ProcessBatch --> RefreshToken["调用refreshAccessToken"]
RefreshToken --> UpdateDB["更新数据库"]
UpdateDB --> UpdateCache["更新Redis缓存"]
UpdateCache --> QueryUsers
```

**图表来源**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java#L29-L290)

**章节来源**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java#L29-L290)

### RefreshAccessTokenTaskConfig 分析

`RefreshAccessTokenTaskConfig` 配置类使用 `@Data` 注解自动生成 getter/setter 方法，包含了以下关键配置项：

- `poolSize`: 线程池大小
- `coreSize`: 核心线程数
- 各平台刷新开关（如 `refreshAccessTokenSwitch`、`doudianRefreshAccessTokenSwitch` 等）
- 小红书平台的刷新时间间隔配置

```mermaid
classDiagram
class RefreshAccessTokenTaskConfig {
+Integer poolSize
+Integer coreSize
+Boolean refreshAccessTokenSwitch
+Boolean taobaoSupplierRefreshAccessTokenSwitch
+Boolean doudianRefreshAccessTokenSwitch
+Boolean ali1688RefreshAccessTokenSwitch
+Boolean kwaishopRefreshAccessTokenSwitch
+Boolean wxshopRefreshAccessTokenSwitch
+Boolean wxvideoshopRefreshAccessTokenSwitch
+Boolean youzanRefreshAccessTokenSwitch
+Boolean xhsRefreshAccessTokenSwitch
+long xhsRefreshAccessTokenIntervalStart
+long xhsRefreshAccessTokenIntervalEnd
}
```

**图表来源**
- [RefreshAccessTokenTaskConfig.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/config/RefreshAccessTokenTaskConfig.java#L12-L63)

**章节来源**
- [RefreshAccessTokenTaskConfig.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/config/RefreshAccessTokenTaskConfig.java#L12-L63)

## 依赖分析

```mermaid
graph TD
RefreshAccessTokenTask --> SellerService
RefreshAccessTokenTask --> UserRepository
RefreshAccessTokenTask --> PlatformProperties
SellerService --> AuthService
SellerService --> UserRepository
SellerService --> RedisRepository
AuthService --> HttpUtil
AuthService --> AesUtil
```

**图表来源**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java#L29-L290)
- [SellerServiceImpl.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/service/impl/SellerServiceImpl.java#L1-L644)
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java#L1-L290)

**章节来源**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java#L29-L290)
- [SellerServiceImpl.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/service/impl/SellerServiceImpl.java#L1-L644)

## 性能考虑

`RefreshAccessTokenTask` 任务在设计时考虑了以下性能优化：
1. 分页查询：使用 `offset` 和 `limit` 参数分页查询用户，避免一次性加载过多数据
2. 批量处理：对查询到的用户进行批量处理，减少数据库和缓存的访问次数
3. 异常处理：在 `refreshUserToken` 方法中使用 try-catch 包裹，确保单个用户处理失败不会影响整体任务执行
4. 日志记录：使用 MDC（Mapped Diagnostic Context）为每个任务添加独立的 topic，便于日志追踪和分析

## 故障排除指南

当 `RefreshAccessTokenTask` 任务执行出现问题时，可以按照以下步骤进行排查：

1. 检查对应平台的刷新开关是否已开启
2. 查看任务执行日志，确认是否有异常堆栈
3. 检查数据库连接是否正常
4. 验证 Redis 缓存服务是否可用
5. 确认电商平台的刷新接口是否正常

**章节来源**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java#L29-L290)
- [SellerServiceImpl.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/service/impl/SellerServiceImpl.java#L1-L644)

## 结论

`RefreshAccessTokenTask` 任务在保障用户授权状态持续有效中起到了关键作用，通过周期性地扫描和刷新即将过期的访问令牌，有效避免了因令牌过期导致的服务中断。任务设计考虑了幂等性、失败重试和监控告警等容错机制，确保了系统的稳定性和用户体验。