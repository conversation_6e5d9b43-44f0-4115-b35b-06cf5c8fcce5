# 用户信息变更事件处理

<cite>
**本文档引用文件**  
- [UserChangedEvent.java](file://uac-api/src/main/java/cn/loveapp/uac/proto/event/UserChangedEvent.java)
- [CallbackRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/CallbackRequest.java)
- [CallbackController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/CallbackController.java)
- [SubscribeUserMessageEventHandlerImpl.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/event/SubscribeUserMessageEventHandlerImpl.java)
- [UserInfoChangedConfig.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/config/UserInfoChangedConfig.java)
- [RocketMqQueueHelper.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/RocketMqQueueHelper.java)
- [UserChangedRequestProto.java](file://uac-api/src/main/java/cn/loveapp/uac/proto/UserChangedRequestProto.java)
</cite>

## 目录
1. [简介](#简介)
2. [事件结构定义](#事件结构定义)
3. [事件发布机制](#事件发布机制)
4. [事件消费流程](#事件消费流程)
5. [消息序列化与传输](#消息序列化与传输)
6. [消费确认与重试策略](#消费确认与重试策略)
7. [事件驱动架构流程图](#事件驱动架构流程图)
8. [扩展事件处理器](#扩展事件处理器)

## 简介
本文档详细说明用户信息变更事件（UserChangedEvent）的发布与消费机制。描述当用户信息发生变更时，系统如何通过CallbackRequest触发CallbackController接收回调，进而生成UserChangedEvent事件。解释事件如何通过RocketMQ进行异步广播，以及SubscribeUserMessageEventHandlerImpl如何监听并处理这些事件。涵盖事件结构定义、消息序列化、消费确认机制、错误重试策略等内容，并提供事件驱动架构的流程图，指导开发者如何扩展新的事件处理器。

## 事件结构定义

用户信息变更事件（UserChangedEvent）用于封装用户信息变更的核心数据，其结构定义如下：

```java
@Data
public class UserChangedEvent {
    private EventTypeEnum type;           // 事件类型
    private Integer oldVipFlag;           // 旧VIP等级
    private Integer newVipFlag;           // 新VIP等级
}
```

该事件通过UserChangedRequestProto进行封装传输，包含用户身份信息及变更事件内容。

**本节来源**  
- [UserChangedEvent.java](file://uac-api/src/main/java/cn/loveapp/uac/proto/event/UserChangedEvent.java#L7-L25)
- [UserChangedRequestProto.java](file://uac-api/src/main/java/cn/loveapp/uac/proto/UserChangedRequestProto.java#L14)

## 事件发布机制

用户信息变更事件的发布始于外部系统的授权回调请求。系统通过以下流程触发事件：

1. 外部系统发起`CallbackRequest`，携带`code`、`platformId`和`app`等参数。
2. `CallbackController`接收请求并调用`CallbackPlatformHandleService`处理授权逻辑。
3. 授权成功后，`OAuthDecorationServiceImpl`调用`SellerServiceImpl`进行用户信息更新。
4. 在用户信息更新过程中，`OperationService`计算用户等级与周期变更，并生成`UserChangedEvent`。
5. `SellerServiceImpl`最终调用`operationService.eventHandle(userInfoBo, userChangedEvent)`触发事件处理。

事件发布由`SubscribeUserMessageEventHandlerImpl`的`userChangedEventHandle`方法完成，该方法将事件封装为`UserChangedRequestProto`并通过RocketMQ异步发送。

**本节来源**  
- [CallbackRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/CallbackRequest.java#L13-L22)
- [CallbackController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/CallbackController.java#L21-L43)
- [OAuthDecorationServiceImpl.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/service/impl/OAuthDecorationServiceImpl.java#L100-L131)
- [SellerServiceImpl.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/service/impl/SellerServiceImpl.java#L100-L150)

## 事件消费流程

事件消费由`SubscribeUserMessageEventHandlerImpl`实现，具体流程如下：

1. `userChangedEventHandle`方法接收`UserInfoBo`和`UserChangedEvent`对象。
2. 校验输入参数，确保事件类型不为空。
3. 构建`UserChangedRequestProto`对象，填充用户身份信息和事件内容。
4. 从`UserInfoChangedConfig`中获取对应事件类型的MQ转发配置（topic和tag）。
5. 使用线程池异步执行消息推送，调用`RocketMqQueueHelper.push`方法发送消息。

消费端通过RocketMQ的消费者组订阅指定topic，自动接收并处理事件消息，实现业务解耦与异步处理。

**本节来源**  
- [SubscribeUserMessageEventHandlerImpl.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/event/SubscribeUserMessageEventHandlerImpl.java#L25-L91)
- [UserInfoChangedConfig.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/config/UserInfoChangedConfig.java#L0-L47)

## 消息序列化与传输

系统使用RocketMQ作为消息中间件，消息序列化与传输流程如下：

1. 消息体对象（如`UserChangedRequestProto`）通过JSON序列化为UTF-8字节数组。
2. `RocketMqQueueHelper`创建`Message`对象，设置topic、tag和消息体。
3. 通过`DefaultMQProducer`发送消息到RocketMQ Broker。
4. 消息在Broker中持久化并分发给订阅该topic的消费者。

消息支持自定义属性（userProperties），可用于传递上下文信息或控制消费行为。

**本节来源**  
- [RocketMqQueueHelper.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/RocketMqQueueHelper.java#L27-L59)
- [UserChangedRequestProto.java](file://uac-api/src/main/java/cn/loveapp/uac/proto/UserChangedRequestProto.java)

## 消费确认与重试策略

系统实现了可靠的消费确认与重试机制：

- **发送确认**：`producer.send()`方法同步等待Broker返回`SendResult`，获取`msgId`作为消息唯一标识。
- **重试机制**：若发送失败，系统最多重试`retryCount`次（默认1次），每次重试间隔随次数递增（200ms × retry）。
- **异常处理**：发送失败日志记录错误信息，最终失败时抛出异常并记录错误日志。
- **幂等性保障**：消费者需自行实现消息幂等处理，防止重复消费导致数据异常。

重试次数通过`@Value("${uac.event.subscribe.retry:1}")`配置，支持动态调整。

**本节来源**  
- [RocketMqQueueHelper.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/RocketMqQueueHelper.java#L105-L138)
- [SubscribeUserMessageEventHandlerImpl.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/event/SubscribeUserMessageEventHandlerImpl.java#L30-L31)

## 事件驱动架构流程图

```mermaid
flowchart TD
A[外部系统] --> |CallbackRequest| B(CallbackController)
B --> C[CallbackPlatformHandleService]
C --> D[OAuthDecorationServiceImpl]
D --> E[SellerServiceImpl]
E --> F[OperationService<br/>计算变更]
F --> G{生成<br/>UserChangedEvent}
G --> H[SubscribeUserMessageEventHandlerImpl]
H --> I[RocketMqQueueHelper<br/>序列化并发送]
I --> J[RocketMQ Broker]
J --> K[消费者服务1]
J --> L[消费者服务2]
J --> M[...]
style A fill:#f9f,stroke:#333
style K fill:#bbf,stroke:#333
style L fill:#bbf,stroke:#333
style M fill:#bbf,stroke:#333
```

**图示来源**  
- [CallbackController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/CallbackController.java)
- [SubscribeUserMessageEventHandlerImpl.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/event/SubscribeUserMessageEventHandlerImpl.java)
- [RocketMqQueueHelper.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/RocketMqQueueHelper.java)

## 扩展事件处理器

开发者可通过以下方式扩展新的事件处理器：

1. 实现`SubscribeUserMessageEventHandler`接口或继承现有实现类。
2. 注入`UserInfoChangedConfig`以获取事件路由配置。
3. 在`userChangedEventHandle`方法中添加自定义业务逻辑。
4. 通过Apollo配置中心动态添加新事件类型的topic和tag映射。

示例配置：
```yaml
uac:
  event:
    subscribe:
      configs:
        VIP_LEVEL_CHANGED: 
          - topic: user-change-topic
            tag: vip-change
```

**本节来源**  
- [SubscribeUserMessageEventHandlerImpl.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/event/SubscribeUserMessageEventHandlerImpl.java)
- [UserInfoChangedConfig.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/config/UserInfoChangedConfig.java)