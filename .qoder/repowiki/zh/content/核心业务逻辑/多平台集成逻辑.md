# 多平台集成逻辑

<cite>
**本文档引用文件**  
- [AuthService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AuthService.java)
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java)
- [PddAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java)
- [UserPlatformHandleService.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/platform/biz/UserPlatformHandleService.java)
- [TaoBaoTradeAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/taobao/TaoBaoTradeAppConfig.java)
- [PDDTradeAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/pdd/PDDTradeAppConfig.java)
- [AppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/AppConfig.java)
</cite>

## 目录
1. [引言](#引言)
2. [核心组件](#核心组件)
3. [适配器模式架构](#适配器模式架构)
4. [基类通用能力](#基类通用能力)
5. [平台特有配置注入](#平台特有配置注入)
6. [请求路由机制](#请求路由机制)
7. [新增平台扩展步骤](#新增平台扩展步骤)
8. [最佳实践](#最佳实践)

## 引言
本文档深入剖析多平台集成的适配器模式实现，重点解释AuthService和UserPlatformHandleService如何作为抽象层，统一处理不同电商平台（如淘宝、拼多多、京东等）的差异化逻辑。通过分析基类与具体实现类的设计，阐述系统如何实现灵活扩展与高内聚低耦合。

## 核心组件

本系统通过适配器模式实现多平台集成，核心组件包括：
- **AuthService接口**：定义统一的认证操作契约
- **BaseAuthServiceImpl基类**：提供通用能力实现
- **平台具体实现类**：如TaoAuthServiceImpl、PddAuthServiceImpl等
- **平台配置类**：如TaoBaoTradeAppConfig、PDDTradeAppConfig等
- **UserPlatformHandleService**：用户平台处理服务接口

**核心组件关系图**

```mermaid
classDiagram
class AuthService {
<<interface>>
+decryptToken(String, String, String) String
+encryptToken(String, String, String) String
+getCallbackResultByCode(String, String, String) T
+refreshToken(UserInfoBo, String, String, String) T
+convertUserRedisEntity2UserInfoBo(UserInfoBo, UserRedisEntity, String, String) void
}
class BaseAuthServiceImpl {
-Map~String, AppConfig~ appConfigMap
-DistributeConfig distributeConfig
+getRetryCount() Integer
+reGetAccessTokenWithRefreshToken(String, String, Map, Class, String) T
+decryptToken(String, String, String) String
+encryptToken(String, String, String) String
+getCallbackResultByCode(String, String, Map, Class, String) T
+executeRefreshTokenNetwork(String, String, Map, String) String
+getActualConfig(String) AppConfig
+convertUserRedisEntity2UserInfoBo(UserInfoBo, UserRedisEntity, String, String) void
}
class TaoAuthServiceImpl {
-Integer retryCount
+getRetryCount() Integer
+getCallbackResultByCode(String, String, String) RefreshTokenCallbackResult
+refreshToken(UserInfoBo, String, String, String) RefreshTokenCallbackResult
+decryptToken(String, String, String) String
+encryptToken(String, String, String) String
+convertUserRedisEntity2UserInfoBo(UserInfoBo, UserRedisEntity, String, String) void
+getPlatformId() String
}
class PddAuthServiceImpl {
-Integer retryCount
+getRetryCount() Integer
+getCallbackResultByCode(String, String, String) RefreshTokenCallbackResult
+refreshToken(UserInfoBo, String, String, String) RefreshTokenCallbackResult
+getPlatformId() String
}
class UserPlatformHandleService {
<<interface>>
+login(UserInfoBo, String, String) UserInfoResponse
+quickLogin(LoginUserInfoBo, String, String) UserInfoResponse
+getUserInfo(UserInfoBo, String, String) UserInfoResponse
+getUserFullInfo(UserInfoBo, boolean, String, String) UserProductInfo
+refreshUserInfo(UserInfoBo, String, String) Boolean
+getAccessToken(UserInfoBo, String, String) UserInfoResponse
+refreshAccessToken(UserInfoBo, String, refreshToken, String, String) String
+rebuildUserInfo(UserInfoBo, String, String) UserInfoResponse
}
AuthService <|-- BaseAuthServiceImpl
BaseAuthServiceImpl <|-- TaoAuthServiceImpl
BaseAuthServiceImpl <|-- PddAuthServiceImpl
UserPlatformHandleService <|.. UserPlatformHandleServiceImpl
```

**图示来源**  
- [AuthService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AuthService.java#L14-L59)
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java#L34-L288)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java#L31-L249)
- [PddAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java#L26-L129)
- [UserPlatformHandleService.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/platform/biz/UserPlatformHandleService.java#L18-L100)

**本节来源**  
- [AuthService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AuthService.java)
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java)
- [PddAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java)
- [UserPlatformHandleService.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/platform/biz/UserPlatformHandleService.java)

## 适配器模式架构

系统采用适配器模式实现多平台集成，通过定义统一接口AuthService，为不同电商平台提供一致的认证服务访问方式。各平台的具体实现类继承自BaseAuthServiceImpl，重写特定方法以适应平台差异。

**架构优势**：
- **解耦性**：平台具体实现与调用方解耦
- **可扩展性**：新增平台只需实现对应适配器
- **一致性**：对外提供统一的API接口
- **可维护性**：平台特有逻辑集中管理

```mermaid
graph TD
A[客户端] --> B[UserPlatformHandleService]
B --> C[AuthService]
C --> D[BaseAuthServiceImpl]
D --> E[TaoAuthServiceImpl]
D --> F[PddAuthServiceImpl]
D --> G[JdAuthServiceImpl]
D --> H[KwaishopAuthServiceImpl]
D --> I[DoudianAuthServiceImpl]
D --> J[TikTokAuthServiceImpl]
D --> K[XhsAuthServiceImpl]
D --> L[YouzanAuthServiceImpl]
style A fill:#f9f,stroke:#333
style B fill:#bbf,stroke:#333
style C fill:#f96,stroke:#333
style D fill:#6f9,stroke:#333
style E fill:#6cc,stroke:#333
style F fill:#6cc,stroke:#333
style G fill:#6cc,stroke:#333
style H fill:#6cc,stroke:#333
style I fill:#6cc,stroke:#333
style J fill:#6cc,stroke:#333
style K fill:#6cc,stroke:#333
style L fill:#6cc,stroke:#333
classDef client fill:#f9f,stroke:#333;
classDef service fill:#bbf,stroke:#333;
classDef interface fill:#f96,stroke:#333;
classDef base fill:#6f9,stroke:#333;
classDef impl fill:#6cc,stroke:#333;
class A client
class B service
class C interface
class D base
class E,F,G,H,I,J,K,L impl
```

**图示来源**  
- [AuthService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AuthService.java)
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java)
- [PddAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java)

**本节来源**  
- [AuthService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AuthService.java)
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java)

## 基类通用能力

BaseAuthServiceImpl作为所有平台认证服务的基类，提供了通用的能力实现，包括：

### 通用功能实现
- **令牌加解密**：统一的AES加解密逻辑
- **网络请求**：标准化的HTTP请求处理
- **重试机制**：可配置的网络请求重试
- **配置管理**：平台配置的统一获取
- **数据转换**：用户信息实体转换

### 关键方法说明
- `decryptToken`：实现令牌解密，处理特殊字符过滤
- `encryptToken`：实现令牌加密
- `getCallbackResultByCode`：通过授权码获取回调结果，包含重试逻辑
- `executeRefreshTokenNetwork`：执行刷新令牌的网络请求
- `getActualConfig`：获取实际配置，支持全局配置 fallback
- `convertUserRedisEntity2UserInfoBo`：将Redis实体转换为业务对象

```mermaid
flowchart TD
A[开始] --> B{令牌为空?}
B --> |是| C[返回原值]
B --> |否| D{长度<81?}
D --> |是| E[直接返回]
D --> |否| F[执行AES解密]
F --> G{解密异常?}
G --> |是| H[过滤特殊字符]
H --> I[重新解密]
I --> J[返回解密结果]
G --> |否| J
J --> K[结束]
style A fill:#f9f,stroke:#333
style K fill:#f9f,stroke:#333
style B fill:#ff6,stroke:#333
style D fill:#ff6,stroke:#333
style G fill:#ff6,stroke:#333
style C,E,J fill:#6f9,stroke:#333
style F,H,I fill:#6cc,stroke:#333
classDef startend fill:#f9f,stroke:#333;
classDef condition fill:#ff6,stroke:#333;
classDef process fill:#6f9,stroke:#333;
classDef operation fill:#6cc,stroke:#333;
class A,K startend
class B,D,G condition
class C,E,J process
class F,H,I operation
```

**图示来源**  
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java#L100-L150)

**本节来源**  
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java)

## 平台特有配置注入

系统通过Spring的依赖注入机制，将平台特有参数注入到具体实现类中，实现配置与代码的分离。

### 配置类结构
- **AppConfig**：基础配置类，定义通用配置项
- **平台特有配置类**：继承AppConfig，可扩展特有属性

### 配置项说明
- `appkey`：应用标识
- `appSecret`：应用密钥
- `sessionkey`：会话密钥
- `refreshTokenUrl`：刷新令牌URL
- `authCodeTokenUrl`：授权码获取令牌URL
- `redirectUrl`：重定向URL

### 注入机制
通过构造函数注入，将多个平台配置以Map形式传递给基类，实现灵活的配置管理。

```mermaid
classDiagram
class AppConfig {
+String appkey
+String appSecret
+String sessionkey
+String refreshTokenUrl
+String authCodeTokenUrl
+String redirectUrl
}
class TaoBaoTradeAppConfig {
+String rebuildUserUrl
+String aesDecryptUrl
}
class PDDTradeAppConfig {
}
AppConfig <|-- TaoBaoTradeAppConfig
AppConfig <|-- PDDTradeAppConfig
TaoAuthServiceImpl --> TaoBaoTradeAppConfig : "注入"
PddAuthServiceImpl --> PDDTradeAppConfig : "注入"
```

**图示来源**  
- [AppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/AppConfig.java#L1-L20)
- [TaoBaoTradeAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/taobao/TaoBaoTradeAppConfig.java#L1-L20)
- [PDDTradeAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/pdd/PDDTradeAppConfig.java#L1-L19)

**本节来源**  
- [AppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/AppConfig.java)
- [TaoBaoTradeAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/taobao/TaoBaoTradeAppConfig.java)
- [PDDTradeAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/pdd/PDDTradeAppConfig.java)

## 请求路由机制

系统通过平台ID和应用类型实现请求的自动路由，确保请求被正确分发到对应的平台处理器。

### 路由流程
1. 客户端发起请求，携带平台ID和应用类型
2. UserPlatformHandleService接收请求
3. 根据平台ID查找对应的AuthService实现
4. 调用具体实现类的方法处理请求
5. 返回处理结果

### 具体实现
- **TaoAuthServiceImpl**：处理淘宝平台请求，重写`getCallbackResultByCode`和`refreshToken`方法
- **PddAuthServiceImpl**：处理拼多多平台请求，设置特定请求头
- **其他平台**：类似实现，适应各自平台特性

```mermaid
sequenceDiagram
participant Client as "客户端"
participant UPS as "UserPlatformHandleService"
participant AS as "AuthService"
participant TAS as "TaoAuthServiceImpl"
participant PAS as "PddAuthServiceImpl"
Client->>UPS : login(userInfoBo, platformId, appName)
UPS->>AS : 根据platformId获取实现
alt 淘宝平台
AS-->>TAS : 返回TaoAuthServiceImpl
TAS->>TAS : 执行淘宝特有逻辑
TAS-->>UPS : 返回结果
else 拼多多平台
AS-->>PAS : 返回PddAuthServiceImpl
PAS->>PAS : 执行拼多多特有逻辑
PAS-->>UPS : 返回结果
else 其他平台
AS-->>其他实现 : 返回对应实现
其他实现->>其他实现 : 执行特有逻辑
其他实现-->>UPS : 返回结果
end
UPS-->>Client : 返回用户信息
```

**图示来源**  
- [UserPlatformHandleService.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/platform/biz/UserPlatformHandleService.java#L18-L100)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java#L31-L249)
- [PddAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java#L26-L129)

**本节来源**  
- [UserPlatformHandleService.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/platform/biz/UserPlatformHandleService.java)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java)
- [PddAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java)

## 新增平台扩展步骤

新增平台的扩展遵循标准化流程，确保系统的一致性和可维护性。

### 扩展步骤
1. **创建配置类**：继承AppConfig，定义平台特有配置
2. **实现AuthService**：继承BaseAuthServiceImpl，实现平台特有逻辑
3. **重写必要方法**：根据平台特性重写`getCallbackResultByCode`、`refreshToken`等方法
4. **注入配置**：在构造函数中注入平台配置
5. **注册Bean**：使用@Service注解注册为Spring Bean
6. **实现getPlatformId**：返回平台标识

### 示例：新增京东平台
```java
@Service
public class JdAuthServiceImpl extends BaseAuthServiceImpl implements AuthService {
    
    @Value("${uac.network.retry.count:5}")
    private Integer retryCount;
    
    public JdAuthServiceImpl(JdTradeERPAppConfig jdTradeERPAppConfig, DistributeConfig distributeConfig) {
        super(ImmutableMap.of(
            CommonAppConstants.APP_TRADE_ERP, jdTradeERPAppConfig
        ), distributeConfig);
    }
    
    @Override
    public Integer getRetryCount() {
        return retryCount;
    }
    
    @Override
    public RefreshTokenCallbackResult getCallbackResultByCode(String code, String platformId, String appName) throws Exception {
        // 京东特有实现
    }
    
    @Override
    public RefreshTokenCallbackResult refreshToken(UserInfoBo userInfoBo, String refreshToken, String platformId, String appName) throws Exception {
        // 京东特有实现
    }
    
    @Override
    public String getPlatformId() {
        return CommonPlatformConstants.PLATFORM_JD;
    }
}
```

**本节来源**  
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java)
- [PddAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java)

## 最佳实践

### 设计原则
- **单一职责**：每个实现类只负责一个平台的认证逻辑
- **开闭原则**：对扩展开放，对修改关闭
- **依赖倒置**：依赖抽象而非具体实现
- **配置分离**：将配置与代码分离，便于维护

### 代码规范
- **命名规范**：平台实现类以平台名开头，如TaoAuthServiceImpl
- **注释要求**：关键方法和逻辑必须有详细注释
- **异常处理**：统一异常处理机制，记录详细日志
- **测试覆盖**：每个实现类必须有对应的单元测试

### 性能优化
- **缓存机制**：合理使用Redis缓存频繁访问的数据
- **连接池**：使用HTTP连接池提高网络请求效率
- **异步处理**：非关键路径操作采用异步方式
- **批量操作**：支持批量处理，减少网络往返

### 安全考虑
- **敏感信息加密**：令牌等敏感信息必须加密存储
- **配置安全**：配置文件中的密钥等信息应加密或通过环境变量注入
- **输入验证**：严格验证所有输入参数
- **日志脱敏**：日志中敏感信息需脱敏处理

**本节来源**  
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java)
- [PddAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java)
- [UserPlatformHandleService.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/platform/biz/UserPlatformHandleService.java)