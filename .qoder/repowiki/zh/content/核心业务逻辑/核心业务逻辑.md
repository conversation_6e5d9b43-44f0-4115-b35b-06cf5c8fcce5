# 核心业务逻辑

<cite>
**本文档引用的文件**  
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java)
- [OpenUserConsumer.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/consumer/OpenUserConsumer.java)
- [UserChangedEvent.java](file://uac-api/src/main/java/cn/loveapp/uac/proto/event/UserChangedEvent.java)
- [UserChangedRequestProto.java](file://uac-api/src/main/java/cn/loveapp/uac/proto/UserChangedRequestProto.java)
- [NewUserPlatformHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/NewUserPlatformHandleService.java)
- [AbstractNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/AbstractNewUserPlatformHandleServiceImpl.java)
- [PddNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/PddNewUserPlatformHandleServiceImpl.java)
- [TaoNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/TaoNewUserPlatformHandleServiceImpl.java)
- [UserServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/service/impl/UserServiceImpl.java)
- [AbstractUserSaveDataBusinessHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/business/impl/AbstractUserSaveDataBusinessHandleService.java)
</cite>

## 目录
1. [用户认证与授权流程](#用户认证与授权流程)
2. [新用户注册与开通生命周期](#新用户注册与开通生命周期)
3. [用户信息变更事件处理](#用户信息变更事件处理)
4. [多平台适配器模式实现](#多平台适配器模式实现)

## 用户认证与授权流程

本系统基于OAuth2.0协议实现用户认证与授权机制，通过定期刷新访问令牌（Access Token）保障长期服务可用性。核心任务由`RefreshAccessTokenTask`驱动，依据各电商平台的授权过期策略，定时扫描需刷新Token的用户并执行更新操作。

Token刷新机制采用分平台、分应用的精细化调度策略，通过Spring的`@Scheduled`注解配置Cron表达式实现定时任务。任务首先判断全局开关是否启用，随后调用`userRepository`查询满足条件的用户（如一级VIP、Token即将过期等），再通过`sellerService.refreshAccessToken`方法完成Token刷新。

对于不同平台，系统配置了独立的刷新周期与时间窗口。例如淘宝平台每小时执行一次，而小红书（XHS）因API限制设置为每15分钟一次。系统通过`doRefreshTokenByApps`统一处理多应用场景，确保同一平台下多个应用（如商品、交易、代发）的Token均被正确刷新。

```mermaid
sequenceDiagram
participant Scheduler as 定时调度器
participant Task as RefreshAccessTokenTask
participant DB as 数据库
participant SellerService as SellerService
participant Platform as 电商平台
Scheduler->>Task : 触发定时任务
Task->>Task : 检查任务开关
alt 开关关闭
Task-->>Scheduler : 忽略执行
else 开关开启
Task->>DB : 查询需刷新Token的用户
DB-->>Task : 返回用户列表
loop 遍历用户
Task->>SellerService : 调用refreshAccessToken
SellerService->>Platform : 调用平台API刷新Token
Platform-->>SellerService : 返回新Token
SellerService-->>Task : 返回结果
end
end
```

**图示来源**  
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java)

**本节来源**  
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java)

## 新用户注册与开通生命周期

新用户注册与开通流程采用异步消息驱动架构，确保高并发下的系统稳定性与可扩展性。整个生命周期从请求接收开始，经由消息队列异步调度，最终由各平台处理器完成具体开通逻辑。

流程起始于外部系统发送用户开通请求，该请求被封装为`OpenUserRequest`对象并发布至RocketMQ消息队列。`OpenUserConsumer`作为消息消费者监听指定Topic，接收到消息后进行结构校验与状态检查，确保仅处理处于“开通中”状态的用户。

消费者调用`UserSaveDataBusinessHandleService.openSaveData`方法启动核心业务逻辑。该方法首先获取用户完整信息，验证授权有效性，并根据VIP等级判断是否允许开通。随后执行数据库分配、RDS推送库绑定、消息通道（TMC）订阅等操作。若所有步骤成功，则更新用户状态为“开通成功”，并触发拉取历史数据任务。

```mermaid
flowchart TD
A[接收开通请求] --> B[发布至RocketMQ]
B --> C[OpenUserConsumer消费消息]
C --> D[校验用户状态]
D --> E{用户状态正常?}
E --> |是| F[调用UserSaveDataBusinessHandleService]
E --> |否| G[记录失败日志]
F --> H[获取用户信息]
H --> I[验证授权与VIP等级]
I --> J[分配数据库与RDS]
J --> K[开通TMC/RDS推送]
K --> L[更新用户状态为成功]
L --> M[发送拉数据消息]
M --> N[完成开通]
```

**图示来源**  
- [OpenUserConsumer.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/consumer/OpenUserConsumer.java)
- [AbstractUserSaveDataBusinessHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/business/impl/AbstractUserSaveDataBusinessHandleService.java)

**本节来源**  
- [OpenUserConsumer.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/consumer/OpenUserConsumer.java)
- [AbstractUserSaveDataBusinessHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/business/impl/AbstractUserSaveDataBusinessHandleService.java)

## 用户信息变更事件处理

用户信息变更事件通过领域驱动设计（DDD）中的事件机制实现，确保系统各模块间松耦合与数据一致性。当用户等级、昵称等关键信息发生变更时，系统会发布`UserChangedEvent`事件，供下游服务订阅与处理。

事件定义位于`UserChangedEvent.java`，包含变更类型（`EventTypeEnum`）、旧VIP等级与新VIP等级等字段。该事件被封装在`UserChangedRequestProto`中，作为RPC调用的载荷进行跨服务传输。

事件发布通常发生在用户信息更新事务提交后，由业务服务触发。订阅方通过实现事件处理器（如`SubscribeUserMessageEventHandler`）监听特定事件类型，并执行相应逻辑，例如更新缓存、同步数据仓库或触发营销活动。

该机制支持灵活的扩展性，新增订阅者无需修改发布者代码，只需注册事件监听器即可。同时，结合RocketMQ等消息中间件，可实现事件的可靠投递与异步处理，提升系统整体性能。

```mermaid
sequenceDiagram
participant UserService as 用户服务
participant EventPublisher as 事件发布器
participant MQ as 消息队列
participant EventHandler as 事件处理器
UserService->>UserService : 更新用户信息
UserService->>EventPublisher : 创建UserChangedEvent
EventPublisher->>MQ : 发布事件消息
MQ-->>EventHandler : 投递消息
EventHandler->>EventHandler : 处理事件如更新缓存
EventHandler-->>MQ : 确认消费
```

**图示来源**  
- [UserChangedEvent.java](file://uac-api/src/main/java/cn/loveapp/uac/proto/event/UserChangedEvent.java)
- [UserChangedRequestProto.java](file://uac-api/src/main/java/cn/loveapp/uac/proto/UserChangedRequestProto.java)

**本节来源**  
- [UserChangedEvent.java](file://uac-api/src/main/java/cn/loveapp/uac/proto/event/UserChangedEvent.java)
- [UserChangedRequestProto.java](file://uac-api/src/main/java/cn/loveapp/uac/proto/UserChangedRequestProto.java)

## 多平台适配器模式实现

系统采用适配器模式（Adapter Pattern）实现对多电商平台（如淘宝、拼多多、抖音等）的统一接入与差异化处理。核心接口`NewUserPlatformHandleService`定义了各平台共有的行为契约，如是否需要拉取数据、是否订阅消息推送、RDS规则获取等。

具体平台实现类（如`PddNewUserPlatformHandleServiceImpl`、`TaoNewUserPlatformHandleServiceImpl`）继承自`AbstractNewUserPlatformHandleServiceImpl`，后者提供了默认实现与通用工具方法。子类通过重写特定方法来提供平台专属逻辑。例如，拼多多实现类重写了`isNeedPullData`和`isNeedSubscribeMc`方法，返回`true`表示需要拉取数据和开通消息推送。

系统通过`getDispatcherId`方法返回平台标识（如`PLATFORM_PDD`），结合Spring的依赖注入机制，实现运行时的策略选择。当需要处理某平台业务时，框架根据平台ID自动注入对应的适配器实例，从而执行正确的业务逻辑。

该设计实现了“开闭原则”，新增平台只需添加新的适配器实现类，无需修改现有代码，极大提升了系统的可维护性与扩展性。

```mermaid
classDiagram
class NewUserPlatformHandleService {
<<interface>>
+isNeedPullData()
+isNeedSubscribeMc()
+getRdsRule()
+getDispatcherId()
}
class AbstractNewUserPlatformHandleServiceImpl {
+isNeedPullData() false
+isNeedSubscribeMc() false
+getRdsRule() EMPTY_RDS
+doSend2PullDataQueue()
}
class PddNewUserPlatformHandleServiceImpl {
+isNeedPullData() true
+isNeedSubscribeMc() true
+getDispatcherId() PLATFORM_PDD
}
class TaoNewUserPlatformHandleServiceImpl {
+isNeedPullData() true
+isNeedSubscribeMc() true
+getDispatcherId() PLATFORM_TAO
}
NewUserPlatformHandleService <|-- AbstractNewUserPlatformHandleServiceImpl
AbstractNewUserPlatformHandleServiceImpl <|-- PddNewUserPlatformHandleServiceImpl
AbstractNewUserPlatformHandleServiceImpl <|-- TaoNewUserPlatformHandleServiceImpl
```

**图示来源**  
- [NewUserPlatformHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/NewUserPlatformHandleService.java)
- [AbstractNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/AbstractNewUserPlatformHandleServiceImpl.java)
- [PddNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/PddNewUserPlatformHandleServiceImpl.java)
- [TaoNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/TaoNewUserPlatformHandleServiceImpl.java)

**本节来源**  
- [NewUserPlatformHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/NewUserPlatformHandleService.java)
- [AbstractNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/AbstractNewUserPlatformHandleServiceImpl.java)
- [PddNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/PddNewUserPlatformHandleServiceImpl.java)
- [TaoNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/TaoNewUserPlatformHandleServiceImpl.java)