# 新用户开通流程

<cite>
**本文档引用文件**  
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java)
- [OpenUserConsumer.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/consumer/OpenUserConsumer.java)
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java)
- [TaoNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/TaoNewUserPlatformHandleServiceImpl.java)
- [PddNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/PddNewUserPlatformHandleServiceImpl.java)
- [OpenResult.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/constant/OpenResult.java)
- [AyBusinessOpenUser.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/AyBusinessOpenUser.java)
- [UserSaveDataBusinessHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/business/UserSaveDataBusinessHandleService.java)
- [NewUserTaskService.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/NewUserTaskService.java)
- [AbstractNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/AbstractNewUserPlatformHandleServiceImpl.java)
</cite>

## 目录
1. [简介](#简介)
2. [核心流程概述](#核心流程概述)
3. [新用户开通状态机](#新用户开通状态机)
4. [关键组件分析](#关键组件分析)
5. [平台专用开通逻辑](#平台专用开通逻辑)
6. [重试与异常处理机制](#重试与异常处理机制)
7. [回调与结果处理](#回调与结果处理)
8. [性能与调度优化](#性能与调度优化)
9. [总结](#总结)

## 简介
本文档全面解析新用户注册与开通的完整生命周期。从 `NewuserController` 接收开通请求开始，描述数据如何进入消息队列，由 `OpenUserConsumer` 进行消费处理。详细说明调度任务 `ScanWaitOpenUser` 如何扫描待处理用户，并触发各平台专用的开通逻辑（如 `TaoNewUserPlatformHandleServiceImpl`、`PddNewUserPlatformHandleServiceImpl`）。解释开通过程中的状态机管理、重试机制、异常处理及结果回调。提供新用户开通流程的状态转换图和关键代码路径，帮助开发者理解异步处理模式和分布式任务协调机制。

## 核心流程概述

新用户开通流程采用异步消息驱动架构，主要分为以下阶段：

1. **请求接收**：通过 `NewuserController` 的 `/prepare` 接口接收用户开通请求。
2. **状态预置**：将用户状态设置为“待开通”（WAIT_OPEN），并记录日志。
3. **定时扫描**：调度任务 `ScanWaitOpenUser` 定期扫描处于“待开通”状态的用户。
4. **消息入队**：将待开通用户信息发送至 RocketMQ 消息队列。
5. **异步消费**：`OpenUserConsumer` 消费消息，调用具体平台的开通逻辑。
6. **结果更新**：根据开通结果更新用户状态和日志，支持重试或失败归档。

```mermaid
flowchart TD
A[NewuserController接收请求] --> B{参数校验}
B --> |失败| C[返回错误]
B --> |成功| D[设置用户状态为待开通]
D --> E[存入数据库]
E --> F[等待调度扫描]
F --> G[ScanWaitOpenUser定时扫描]
G --> H{存在待开通用户?}
H --> |否| I[等待下次扫描]
H --> |是| J[发送消息至RocketMQ]
J --> K[OpenUserConsumer消费消息]
K --> L[调用平台专用开通逻辑]
L --> M{开通成功?}
M --> |是| N[更新状态为已开通]
M --> |否| O{可重试?}
O --> |是| P[更新状态为待重试]
O --> |否| Q[更新状态为失败]
N --> R[流程结束]
P --> R
Q --> R
```

**图示来源**  
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java#L22-L101)
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java#L25-L92)
- [OpenUserConsumer.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/consumer/OpenUserConsumer.java#L33-L144)

**本节来源**  
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java#L22-L101)
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java#L25-L92)
- [OpenUserConsumer.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/consumer/OpenUserConsumer.java#L33-L144)

## 新用户开通状态机

系统通过 `AyBusinessOpenUser` 实体的状态字段（status）管理用户开通生命周期，状态转换如下：

```mermaid
stateDiagram-v2
[*] --> WAIT_OPEN
WAIT_OPEN --> OPENING : 开始处理
OPENING --> DONE : 开通成功
OPENING --> WAIT_RETRY : 可重试失败
OPENING --> FAILED : 不可重试失败
WAIT_RETRY --> OPENING : 重试任务触发
DONE --> [*]
FAILED --> [*]
WAIT_RETRY --> [*]
note right of WAIT_OPEN
状态码 : 101
含义 : 待开通
end note
note right of OPENING
状态码 : 102
含义 : 正在开通
end note
note right of DONE
状态码 : 10
含义 : 已开通
end note
note right of WAIT_RETRY
状态码 : -102
含义 : 等待重试
end note
note right of FAILED
状态码 : -101
含义 : 开通失败
end note
```

**图示来源**  
- [AyBusinessOpenUser.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/AyBusinessOpenUser.java#L12-L128)
- [OpenResult.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/constant/OpenResult.java#L6-L66)

**本节来源**  
- [AyBusinessOpenUser.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/AyBusinessOpenUser.java#L12-L128)
- [OpenResult.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/constant/OpenResult.java#L6-L66)

## 关键组件分析

### NewuserController
作为新用户开通的入口控制器，负责接收 HTTP 请求并进行初步校验。其核心方法 `prepare` 调用 `UserSaveDataBusinessHandleService.prepareSaveData` 将用户置为待开通状态。

### OpenUserConsumer
继承自 `BaseOnsConsumer`，负责消费 RocketMQ 中的开通消息。其 `execute` 方法解析 `OpenUserRequest`，查询用户状态，调用 `userSaveOrderService.openSaveData` 执行开通逻辑，并根据结果更新状态。

### ScanWaitOpenUser
定时任务组件，通过 `@Scheduled` 注解配置固定延迟执行。其 `scanWaitOpenUser` 方法遍历所有业务线（businessId），调用 `newUserTaskService.scanWaitOpenUser` 扫描待开通用户并发送消息。

### UserSaveDataBusinessHandleService
核心业务接口，定义了 `openSaveData` 方法用于执行实际的开通逻辑，返回 `OpenResult` 枚举表示结果。

```mermaid
classDiagram
class NewuserController {
+prepare(SaveDataDTO)
+saveDataCourse(HttpServletRequest)
}
class OpenUserConsumer {
+execute(AiyongMessageExt)
+updateOpenuser(AyBusinessOpenUser, AyBusinessOpenUserLog, OpenResult, String)
}
class ScanWaitOpenUser {
+scanWaitOpenUser()
}
class UserSaveDataBusinessHandleService {
<<interface>>
+openSaveData(AyBusinessOpenUserLog, String, String, String, String, String) OpenResult
+prepareSaveData(String, SaveDataDTO) boolean
}
class AyBusinessOpenUser {
+status Integer
+sellerId String
+platId String
+appName String
+isOpening() Boolean
}
NewuserController --> UserSaveDataBusinessHandleService : "依赖"
OpenUserConsumer --> UserSaveDataBusinessHandleService : "依赖"
ScanWaitOpenUser --> NewUserTaskService : "依赖"
OpenUserConsumer --> AyBusinessOpenUser : "查询状态"
```

**图示来源**  
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java#L22-L101)
- [OpenUserConsumer.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/consumer/OpenUserConsumer.java#L33-L144)
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java#L25-L92)
- [UserSaveDataBusinessHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/business/UserSaveDataBusinessHandleService.java#L16-L56)
- [AyBusinessOpenUser.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/AyBusinessOpenUser.java#L12-L128)

**本节来源**  
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java#L22-L101)
- [OpenUserConsumer.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/consumer/OpenUserConsumer.java#L33-L144)
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java#L25-L92)
- [UserSaveDataBusinessHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/business/UserSaveDataBusinessHandleService.java#L16-L56)
- [AyBusinessOpenUser.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/AyBusinessOpenUser.java#L12-L128)

## 平台专用开通逻辑

系统通过策略模式实现多平台支持，所有平台专用逻辑均继承自 `AbstractNewUserPlatformHandleServiceImpl`，并实现 `NewUserPlatformHandleService` 接口。

### 淘宝平台 (TaoNewUserPlatformHandleServiceImpl)
- 实现 `isNeedPullData` 和 `isNeedSubscribeMc` 返回 `true`，表示需要拉取数据和订阅消息。
- 在 `handlePrepareOpenOnNormalCondition` 中判断活跃标识，若超时则触发拉数据。
- 支持代发用户特殊逻辑处理。

### 拼多多平台 (PddNewUserPlatformHandleServiceImpl)
- 同样实现 `isNeedPullData` 和 `isNeedSubscribeMc` 返回 `true`。
- 通过 `getDispatcherId` 返回 `PLATFORM_PDD` 作为分发标识。

### 抽象基类 (AbstractNewUserPlatformHandleServiceImpl)
提供通用方法：
- `doCheckTokenAndSend2PullDataQueue`：校验 token 后发送拉数据消息。
- `doSend2PullDataQueue`：构造 `PullDataRequestProto` 并发送至 RocketMQ。

```mermaid
classDiagram
class NewUserPlatformHandleService {
<<interface>>
+isNeedPullData(String, String, String) Boolean
+isNeedSubscribeMc(String, String, String) Boolean
+getDispatcherId() String
}
class AbstractNewUserPlatformHandleServiceImpl {
<<abstract>>
+doCheckTokenAndSend2PullDataQueue(SaveDataDTO, UserProductInfoBusinessExt, String, String) Boolean
+doSend2PullDataQueue(String, String, Integer, String, String, String) Boolean
}
class TaoNewUserPlatformHandleServiceImpl {
+isNeedPullData() Boolean
+handlePrepareOpenOnNormalCondition(SaveDataDTO, UserProductInfoBusinessExt, NewUserPlatformHandleDTO, String, String)
}
class PddNewUserPlatformHandleServiceImpl {
+isNeedPullData() Boolean
}
NewUserPlatformHandleService <|-- AbstractNewUserPlatformHandleServiceImpl
AbstractNewUserPlatformHandleServiceImpl <|-- TaoNewUserPlatformHandleServiceImpl
AbstractNewUserPlatformHandleServiceImpl <|-- PddNewUserPlatformHandleServiceImpl
```

**图示来源**  
- [TaoNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/TaoNewUserPlatformHandleServiceImpl.java#L26-L83)
- [PddNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/PddNewUserPlatformHandleServiceImpl.java#L11-L28)
- [AbstractNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/AbstractNewUserPlatformHandleServiceImpl.java#L0-L192)

**本节来源**  
- [TaoNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/TaoNewUserPlatformHandleServiceImpl.java#L26-L83)
- [PddNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/PddNewUserPlatformHandleServiceImpl.java#L11-L28)
- [AbstractNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/AbstractNewUserPlatformHandleServiceImpl.java#L0-L192)

## 重试与异常处理机制

系统通过状态码和定时任务实现健壮的重试机制：

- **可重试失败**：当 `OpenResult` 的 `code` 在 -100 至 -200 之间时，状态更新为 `WAIT_RETRY`（-102）。
- **不可重试失败**：其他负数状态码（除 `REPEATED_REQUESTS`）更新为 `FAILED`（-101）。
- **重复请求**：`REPEATED_REQUESTS`（-1000）直接忽略，不记录日志。
- **调度重试**：`ScanRetryUser` 任务定期扫描 `WAIT_RETRY` 用户，重新触发开通流程。

```mermaid
flowchart TD
A[OpenUserConsumer处理消息] --> B{开通成功?}
B --> |是| C[状态: DONE]
B --> |否| D[检查OpenResult.code]
D --> E{code <= -100 && code > -200?}
E --> |是| F[状态: WAIT_RETRY]
E --> |否| G{code <= REPEATED_REQUESTS.code?}
G --> |是| H[忽略请求]
G --> |否| I[状态: FAILED]
F --> J[ScanRetryUser任务扫描]
J --> K[重新发送消息]
K --> A
```

**图示来源**  
- [OpenUserConsumer.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/consumer/OpenUserConsumer.java#L33-L144)
- [OpenResult.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/constant/OpenResult.java#L6-L66)
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java#L25-L92)

**本节来源**  
- [OpenUserConsumer.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/consumer/OpenUserConsumer.java#L33-L144)
- [OpenResult.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/constant/OpenResult.java#L6-L66)
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java#L25-L92)

## 回调与结果处理

开通结果通过以下方式处理：
- **状态更新**：调用 `ayBusinessOpenUserRepository.updateByStatus` 更新主表状态。
- **日志记录**：调用 `ayTradeOpenUserLogDao.update` 记录详细原因。
- **外部状态同步**：对于淘宝平台，根据结果调用 `userProductionInfoExtRepository.updateApiStatus` 更新 API 状态。
- **MDC上下文**：使用 `MDC.put` 记录 `sellerNick`、`platformId` 等上下文信息，便于日志追踪。

**本节来源**  
- [OpenUserConsumer.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/consumer/OpenUserConsumer.java#L33-L144)

## 性能与调度优化

系统通过以下方式优化性能：
- **线程池隔离**：`ScanWaitOpenUser` 使用独立的 `ThreadPoolExecutor` 处理不同业务线的扫描任务，避免相互阻塞。
- **异步处理**：开通逻辑完全异步化，避免阻塞主线程。
- **限流控制**：通过 `RateLimitHelper` 和 `RocketMQ` 生产者配置控制消息发送速率。
- **配置化调度**：扫描间隔通过 `${uac.newuser.task.scanWaitOpenUser.delay}` 配置，支持动态调整。

**本节来源**  
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java#L25-L92)
- [AbstractNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/AbstractNewUserPlatformHandleServiceImpl.java#L0-L192)

## 总结

新用户开通流程采用“请求-扫描-消费”的异步架构，通过状态机精确控制用户生命周期，结合策略模式支持多平台扩展。系统具备完善的重试、异常处理和日志追踪能力，确保高可用性和可维护性。开发者在扩展新平台时，只需实现 `NewUserPlatformHandleService` 接口并注册为 Spring Bean 即可。