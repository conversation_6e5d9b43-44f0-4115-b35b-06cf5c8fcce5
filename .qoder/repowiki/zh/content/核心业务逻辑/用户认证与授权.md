# 用户认证与授权

<cite>
**本文档引用文件**  
- [UserController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java)
- [PddAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java)
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java)
- [UserRequestInterceptor.java](file://uac-service/src/main/java/cn/loveapp/uac/service/interceptor/UserRequestInterceptor.java)
- [OAuthDecorationServiceImpl.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/service/impl/OAuthDecorationServiceImpl.java)
</cite>

## 目录
1. [用户登录请求处理](#用户登录请求处理)  
2. [平台认证服务实现](#平台认证服务实现)  
3. [Token生成与存储策略](#token生成与存储策略)  
4. [自动刷新访问令牌机制](#自动刷新访问令牌机制)  
5. [认证拦截器与Token校验](#认证拦截器与token校验)  
6. [异常处理流程](#异常处理流程)  
7. [认证时序图](#认证时序图)  
8. [常见认证失败场景与排查](#常见认证失败场景与排查)

## 用户登录请求处理

用户登录请求由 `UserController` 统一处理，提供多个登录接口以支持不同业务场景。核心方法包括 `login` 和 `quickLogin`，分别对应常规登录和快速登录流程。

`login` 方法接收 `UserInfoRequest` 请求对象，通过 `UserPlatformHandleService` 的 `login` 方法完成用户登录逻辑。该方法会校验用户身份信息，并返回包含用户基本信息的 `UserInfoResponse`。若用户不存在，则捕获 `UserException` 并返回预定义的错误码和消息。

`quickLogin` 方法用于快速登录场景，接收 `LoginInfoRequest` 请求对象，调用 `UserPlatformHandleService` 的 `quickLogin` 方法完成登录。该接口同样具备异常捕获机制，确保在用户不存在时返回标准化响应。

所有登录接口均通过 `@Validated` 注解进行参数校验，确保输入数据的合法性。控制器返回统一的 `CommonApiResponse` 结构，保证接口响应格式的一致性。

**用户登录请求处理**
- [UserController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java#L29-L107)

## 平台认证服务实现

系统通过 `AuthService` 接口及其实现类支持多平台认证，包括淘宝（TaoAuthServiceImpl）、拼多多（PddAuthServiceImpl）等。各平台实现类继承 `BaseAuthServiceImpl`，遵循统一的认证流程。

`TaoAuthServiceImpl` 负责淘宝平台的认证逻辑。其 `getCallbackResultByCode` 方法通过授权码获取访问令牌，调用父类 `getCallbackResultByCode` 方法发起网络请求，解析返回的 `TaobaoRefreshTokenCallbackResult` 对象。关键信息如 `accessToken`、`refreshToken`、`sellerId`、`sellerNick` 等均从回调结果中提取，并进行加密处理后封装到 `RefreshTokenCallbackResult` 中返回。

`PddAuthServiceImpl` 实现拼多多平台认证。与淘宝类似，`getCallbackResultByCode` 方法构建特定的HTTP请求头（如 `Content-type`、`Accept`、`User-Agent`），调用父类方法获取 `PddRefreshTokenCallbackResult`。除基础令牌信息外，拼多多还返回 `scope` 权限范围，该信息也被保留。

两个实现类均重写了 `refreshToken` 方法，使用 `refreshToken` 获取新的 `accessToken`。同时，`createdAuthDeadLine` 方法根据 `expiresIn` 有效期计算授权到期时间，确保系统能准确判断令牌状态。

**平台认证服务实现**
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java#L31-L249)
- [PddAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java#L26-L129)

## Token生成与存储策略

系统采用加密存储策略保护用户敏感的访问令牌（`accessToken`）和刷新令牌（`refreshToken`）。核心加密逻辑由 `AesUtil` 工具类实现，通过 `aesEncryptForSession` 和 `aesDecryptForSession` 方法进行加解密。

在 `TaoAuthServiceImpl` 中，`encryptToken` 方法负责加密令牌。对于非商品应用（`APP_ITEM`）的令牌，若长度小于81字符，则直接返回（通常为6或7开头的短令牌）；否则使用AES算法加密。加密密钥来源于对应应用的配置（`getActualConfig(appName).getSessionkey()`）。`decryptToken` 方法则执行逆向操作，对加密令牌进行解密。

令牌信息最终存储在 `UserRedisEntity` 中。`convertUserRedisEntity2UserInfoBo` 方法负责将Redis实体转换为业务对象 `UserInfoBo`。不同应用类型（`APP_TRADE`、`APP_ITEM`、`APP_SHOP_HELPER`）对应不同的Redis字段，如 `trade_access_token_m`、`item_access_token_mp` 等，实现了多应用令牌的隔离存储。

**Token生成与存储策略**
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java#L200-L249)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java#L220-L249)

## 自动刷新访问令牌机制

为保障服务连续性，系统通过 `RefreshAccessTokenTask` 定时任务自动刷新即将过期的访问令牌。该任务由Spring的 `@Scheduled` 注解驱动，根据配置的Cron表达式定期执行。

核心方法 `scanTaoBaoAuthorizationExpiredAction` 负责刷新淘宝平台的令牌。它首先检查全局开关 `refreshAccessTokenSwitch`，若关闭则直接返回。随后，构建查询条件：`expireDeadLine` 为当前时间，查询所有一级（`LEVEL_ONE`）且需要认证（`NEED_AUTH`）的用户。通过 `userRepository.queryByLevelAndW1DeadLine` 分页查询用户列表。

`refreshUserToken` 方法实现批量处理逻辑。它以 `maxId` 为分页游标，循环查询用户，直至无更多数据。对于每个用户，检查其 `toprefreshkey`（即 `refreshToken`）是否为空。若存在，则创建 `UserInfoBo` 对象，调用 `sellerService.refreshAccessToken` 方法刷新令牌。整个过程包含完善的日志记录和异常捕获，确保任务的健壮性。

系统还为其他平台（如抖音、快手、微信小店等）提供了类似的刷新任务，如 `scanDoudianAuthorizationExpiredAction`、`scanKwaishopAuthorizationExpiredAction` 等，均通过 `doRefreshTokenByApps` 模板方法实现，提高了代码复用性。

```mermaid
flowchart TD
A[定时任务触发] --> B{全局开关开启?}
B --> |否| C[忽略任务]
B --> |是| D[构建查询条件]
D --> E[分页查询待刷新用户]
E --> F{用户列表为空?}
F --> |是| G[任务结束]
F --> |否| H[遍历用户列表]
H --> I{refreshToken存在?}
I --> |否| J[跳过]
I --> |是| K[调用refreshAccessToken]
K --> L[记录日志]
L --> M[处理下一个用户]
M --> E
```

**图示来源**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java#L29-L290)

**用户登录请求处理**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java#L29-L290)

## 认证拦截器与Token校验

系统通过 `UserRequestInterceptor` 实现请求拦截，将关键用户信息注入MDC（Mapped Diagnostic Context），便于日志追踪。`preHandle` 方法在请求处理前执行，从HTTP请求参数中提取 `sellerNick`、`subSellerNick`、`sellerId`、`subSellerId`、`platformId`、`app` 等信息，并存入MDC上下文。

这些信息在日志输出时自动附加，使得每条日志都能关联到具体的用户和平台，极大提升了问题排查效率。例如，在 `RefreshAccessTokenTask` 的日志中，可以看到 `sellerNick` 和 `jobName`，能快速定位到具体用户的刷新任务。

Token校验逻辑主要在业务服务层完成。`OAuthDecorationServiceImpl` 的 `refreshAccessToken` 方法是核心入口。它接收 `UserInfoBo` 和 `refreshToken`，根据 `platformId` 获取对应的 `AuthService` 实现（如 `TaoAuthServiceImpl`），调用其 `refreshToken` 方法。校验过程隐含在平台SDK的调用中，若 `refreshToken` 无效，平台会返回错误，该异常会被捕获并记录。

```mermaid
sequenceDiagram
participant Client as 客户端
participant Interceptor as UserRequestInterceptor
participant Service as OAuthDecorationServiceImpl
participant AuthService as TaoAuthServiceImpl
participant Platform as 淘宝平台
Client->>Interceptor : 发起请求(sellerNick, platformId, app)
Interceptor->>Interceptor : 提取参数并存入MDC
Interceptor->>Service : 调用refreshAccessToken
Service->>AuthService : 根据platformId获取实现
AuthService->>Platform : 使用refreshToken请求新accessToken
Platform-->>AuthService : 返回新accessToken和refreshToken
AuthService-->>Service : 返回结果
Service-->>Client : 返回成功响应
```

**图示来源**
- [UserRequestInterceptor.java](file://uac-service/src/main/java/cn/loveapp/uac/service/interceptor/UserRequestInterceptor.java#L13-L37)
- [OAuthDecorationServiceImpl.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/service/impl/OAuthDecorationServiceImpl.java#L0-L40)

**用户登录请求处理**
- [UserRequestInterceptor.java](file://uac-service/src/main/java/cn/loveapp/uac/service/interceptor/UserRequestInterceptor.java#L13-L37)
- [OAuthDecorationServiceImpl.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/service/impl/OAuthDecorationServiceImpl.java#L0-L40)

## 异常处理流程

系统建立了分层的异常处理机制。在控制器层，`UserController` 通过 `try-catch` 捕获 `UserException`，将其转换为标准化的 `CommonApiResponse`，避免将内部异常暴露给前端。

在服务层，`RefreshAccessTokenTask` 的 `batchProcess` 方法对每个用户的刷新操作进行独立的异常捕获。即使单个用户的刷新失败，也不会中断整个任务的执行。错误信息通过 `LOGGER.logError` 记录，包含 `sellerNick` 和 `sellerId`，便于定位问题。

平台认证服务（如 `TaoAuthServiceImpl`）在 `getCallbackResultByCode` 和 `refreshToken` 方法中，会检查回调结果的 `isSuccess` 状态。若请求失败，直接返回 `null`，由上层服务决定如何处理。对于解密异常（`IllegalBlockSizeException`），代码尝试过滤特殊字符（如 `\r`, `\n`）后重试，体现了对网络传输中可能存在的数据污染的容错能力。

**异常处理流程**
- [UserController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java#L29-L107)
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java#L29-L290)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java#L31-L249)

## 认证时序图

```mermaid
sequenceDiagram
participant Browser as 浏览器
participant UserController as UserController
participant UserPlatformHandleService as UserPlatformHandleService
participant OAuthDecorationService as OAuthDecorationService
participant TaoAuthServiceImpl as TaoAuthServiceImpl
participant Taobao as 淘宝平台
Browser->>UserController : POST /uac/user/login(sellerNick, platformId=tao, app=trade)
UserController->>UserPlatformHandleService : login(userInfoBo)
UserPlatformHandleService->>OAuthDecorationService : authCodeAndRefreshUser(authBo)
OAuthDecorationService->>TaoAuthServiceImpl : getAuthCodeResult(authBo)
TaoAuthServiceImpl->>Taobao : 使用code请求accessToken
Taobao-->>TaoAuthServiceImpl : 返回accessToken, refreshToken, sellerId等
TaoAuthServiceImpl->>TaoAuthServiceImpl : encryptToken(加密)
TaoAuthServiceImpl-->>OAuthDecorationService : 返回Auth结果
OAuthDecorationService->>UserPlatformHandleService : 处理用户信息
UserPlatformHandleService-->>UserController : 返回UserInfoResponse
UserController-->>Browser : 返回CommonApiResponse
```

**图示来源**
- [UserController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java#L29-L107)
- [OAuthDecorationServiceImpl.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/service/impl/OAuthDecorationServiceImpl.java#L0-L40)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java#L31-L249)

## 常见认证失败场景与排查

1.  **`refreshToken` 为空或无效**：在 `RefreshAccessTokenTask` 中，若 `userProductInfo.getToprefreshkey()` 为空，会直接跳过该用户。排查时需检查数据库中该字段是否为空，或用户是否已取消授权。
2.  **平台返回认证失败**：`TaoAuthServiceImpl` 或 `PddAuthServiceImpl` 的 `getCallbackResultByCode` 方法返回 `null`，通常因为授权码（code）已过期或被使用。需确保前端在获取code后立即发起登录请求。
3.  **解密失败**：`decryptToken` 方法可能因非法块大小（`IllegalBlockSizeException`）而失败。系统已尝试过滤 `\r\n` 等特殊字符，若仍失败，需检查加密密钥（`sessionkey`）是否正确，或令牌本身是否已损坏。
4.  **网络请求异常**：调用平台API时可能发生网络超时或连接失败。系统通过 `retryCount` 配置进行重试，但若持续失败，需检查网络连通性或平台服务状态。
5.  **用户不存在**：`UserController` 捕获 `UserException` 并返回 `NO_EXIST_USER` 错误码。这通常发生在用户首次登录且系统未创建记录时，属于正常业务流程。

**常见认证失败场景与排查**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java#L29-L290)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java#L31-L249)
- [UserController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java#L29-L107)