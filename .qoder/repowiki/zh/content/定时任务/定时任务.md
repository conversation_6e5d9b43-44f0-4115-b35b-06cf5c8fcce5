# 定时任务

<cite>
**本文档引用的文件**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java)
- [RefreshAccessTokenTaskConfig.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/config/RefreshAccessTokenTaskConfig.java)
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java)
- [ScanRetryUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanRetryUser.java)
- [RedisCleanTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/maintain/RedisCleanTask.java)
- [NewUserTaskConfig.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/config/NewUserTaskConfig.java)
</cite>

## 目录
1. [简介](#简介)
2. [核心定时任务](#核心定时任务)
3. [uac-authorization-job 模块任务](#uac-authorization-job-模块任务)
4. [uac-newuser-scheduler 模块任务](#uac-newuser-scheduler-模块任务)
5. [调度配置与执行机制](#调度配置与执行机制)
6. [异常处理与监控](#异常处理与监控)
7. [总结](#总结)

## 简介
本文档旨在全面描述用户中心服务系统中的核心定时任务，重点涵盖 `uac-authorization-job` 和 `uac-newuser-scheduler` 两个模块。这些任务是保障系统稳定运行、数据一致性以及新用户自动化开通流程的关键。文档将详细说明各任务的职责、调度策略、执行逻辑、配置项及异常处理机制。

## 核心定时任务
系统中的定时任务主要分为两大类：**平台授权Token刷新任务** 和 **新用户开通流程驱动任务**。前者确保与各电商平台的API连接持续有效，后者则自动化处理新用户的开通、重试及状态维护，是业务流转的核心引擎。

## uac-authorization-job 模块任务

该模块的核心是 `RefreshAccessTokenTask`，负责定期刷新各电商平台的用户授权Token，防止因Token过期导致服务中断。

### RefreshAccessTokenTask 任务详解

`RefreshAccessTokenTask` 是一个Spring组件，通过 `@Scheduled` 注解配置了多个Cron任务，针对不同平台和应用场景进行Token刷新。

**任务职责**：
- 扫描即将过期或已过期的用户授权记录。
- 调用 `SellerService` 的 `refreshAccessToken` 方法，使用 `refresh_token` 获取新的 `access_token`。
- 将刷新结果记录到日志中，便于监控和排查问题。

**Section sources**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java#L29-L290)

### 任务调度配置

该任务的调度行为由 `RefreshAccessTokenTaskConfig` 类通过Spring的 `@Value` 注解从配置文件中读取，实现了灵活的开关控制和参数调整。

```mermaid
classDiagram
class RefreshAccessTokenTask {
+LOGGER LoggerHelper
-userRepository UserRepository
-sellerService SellerService
-refreshAccessTokenTaskConfig RefreshAccessTokenTaskConfig
+scanTaoBaoNeedAuthAction() void
+scanTaoBaoAuthorizationExpiredAction() void
+scanTaoBaoSupplierAuthorizationExpiredAction() void
+scanDoudianAuthorizationExpiredAction() void
+scan1688AuthorizationExpiredAction() void
+scanKwaishopAuthorizationExpiredAction() void
+scanWxshopAuthorizationExpiredAction() void
+scanWxvideoshopAuthorizationExpiredAction() void
+scanYouzanAuthorizationExpiredAction() void
+scanXhsAuthorizationExpiredAction() void
-doRefreshTokenByApps(...) void
-refreshUserToken(...) void
-batchProcess(...) void
}
class RefreshAccessTokenTaskConfig {
+poolSize Integer
+coreSize Integer
+refreshAccessTokenSwitch Boolean
+taobaoSupplierRefreshAccessTokenSwitch Boolean
+doudianRefreshAccessTokenSwitch Boolean
+ali1688RefreshAccessTokenSwitch Boolean
+kwaishopRefreshAccessTokenSwitch Boolean
+wxshopRefreshAccessTokenSwitch Boolean
+wxvideoshopRefreshAccessTokenSwitch Boolean
+youzanRefreshAccessTokenSwitch Boolean
+xhsRefreshAccessTokenSwitch Boolean
+xhsRefreshAccessTokenIntervalStart long
+xhsRefreshAccessTokenIntervalEnd long
}
RefreshAccessTokenTask --> RefreshAccessTokenTaskConfig : "依赖"
```

**Diagram sources**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java#L29-L290)
- [RefreshAccessTokenTaskConfig.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/config/RefreshAccessTokenTaskConfig.java#L0-L64)

## uac-newuser-scheduler 模块任务

该模块包含一系列驱动新用户开通流程的定时任务，通过扫描数据库中的特定状态用户，触发后续的业务处理。

### ScanWaitOpenUser 任务

`ScanWaitOpenUser` 任务负责扫描所有处于“待开通”状态的用户，并将它们提交到开户队列中，启动开户流程。

**执行逻辑**：
1.  任务启动时，检查配置开关 `scanWaitOpenUserEnable`。
2.  若开关开启，则遍历所有配置的业务ID (`businessIds`)。
3.  为每个业务ID，通过线程池异步调用 `NewUserTaskService` 的 `scanWaitOpenUser` 方法，进行分页查询和处理。

**Section sources**
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java#L25-L92)

### ScanRetryUser 任务

`ScanRetryUser` 任务用于扫描那些在开户过程中失败、需要重试的用户。

**执行逻辑**：
1.  任务启动时，检查配置开关 `scanRetryUserEnable`。
2.  若开关开启，则遍历所有配置的业务ID。
3.  为每个业务ID，通过线程池异步调用 `NewUserTaskService` 的 `scanRetryUser` 方法，重新发起开户请求。

**Section sources**
- [ScanRetryUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanRetryUser.java#L24-L89)

### RedisClean 任务

`RedisCleanTask` 是一个维护性任务，负责清理长时间未更新的用户缓存数据，以释放Redis内存，避免缓存膨胀。

**执行逻辑**：
1.  任务按Cron表达式每日凌晨1点执行。
2.  计算出两个月前的时间点作为清理界限。
3.  遍历所有支持的平台和应用（排除特定平台）。
4.  分页查询 `w1_deadline` 在清理界限之前的用户数据。
5.  对于每个用户，检查其 `order_cycle_end`、`lastupdatetime` 和 `w1_deadline` 三个时间戳，如果它们均不为空且都早于清理界限，则判定为可清理。
6.  删除该用户在对应Redis实例中的缓存。

**Section sources**
- [RedisCleanTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/maintain/RedisCleanTask.java#L30-L250)

## 调度配置与执行机制

系统的定时任务调度主要依赖Spring的 `@Scheduled` 注解，并通过外部化配置实现灵活管理。

### Cron表达式与固定延迟

- **Cron表达式**：用于在特定时间点执行任务，如 `0 0 1 * * ?` 表示每天凌晨1点执行。`RefreshAccessTokenTask` 中的大部分任务使用此方式。
- **固定延迟 (fixedDelayString)**：用于在上一次任务执行完成后，等待指定延迟再执行下一次。`ScanWaitOpenUser` 和 `ScanRetryUser` 使用此方式，配置为每分钟执行一次。

### 配置化开关

所有关键任务都配备了配置开关，通过 `RefreshAccessTokenTaskConfig` 和 `NewUserTaskConfig` 中的布尔值控制，可以在不重启服务的情况下动态启用或禁用任务，极大地提升了运维的灵活性和安全性。

```mermaid
flowchart TD
Start([任务调度启动]) --> CheckConfig["检查配置开关"]
CheckConfig --> |开关关闭| End([任务忽略])
CheckConfig --> |开关开启| ExecuteLogic["执行核心业务逻辑"]
ExecuteLogic --> QueryData["分页查询目标数据"]
QueryData --> ProcessData["处理数据 (刷新Token/发送队列/清理缓存)"]
ProcessData --> LogResult["记录执行日志"]
LogResult --> End
```

**Diagram sources**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java#L29-L290)
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java#L25-L92)
- [RedisCleanTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/maintain/RedisCleanTask.java#L30-L250)

## 异常处理与监控

### 异常处理

所有任务都实现了完善的异常处理机制：
- 使用 `try-catch` 块包裹核心执行逻辑，捕获并记录所有异常，防止单个用户的处理失败导致整个任务中断。
- 在 `batchProcess` 等方法中，对每个用户的处理都进行独立的异常捕获，确保一个用户的错误不会影响其他用户。
- 利用 `MDC` (Mapped Diagnostic Context) 在日志中添加任务名称等上下文信息，便于在海量日志中追踪特定任务的执行情况。

### 监控告警

- **日志监控**：所有任务的开始、结束、耗时、成功/失败信息均被详细记录。通过日志系统（如ELK）可以监控任务的执行频率、成功率和耗时趋势。
- **告警机制**：当任务执行出现异常（如抛出未捕获的异常）或执行耗时过长时，日志中的错误信息会触发监控告警，通知运维人员及时介入。

**Section sources**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java#L29-L290)
- [RedisCleanTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/maintain/RedisCleanTask.java#L30-L250)

## 总结
本文档详细阐述了用户中心服务系统中的核心定时任务。`uac-authorization-job` 模块通过 `RefreshAccessTokenTask` 确保了与各电商平台的授权连接稳定，是服务可用性的基石。`uac-newuser-scheduler` 模块则通过 `ScanWaitOpenUser`、`ScanRetryUser` 和 `RedisCleanTask` 等任务，实现了新用户开通流程的自动化和系统资源的维护。这些任务均采用配置化、异步化的设计，并具备完善的异常处理和日志监控能力，共同保障了系统的高效、稳定运行。