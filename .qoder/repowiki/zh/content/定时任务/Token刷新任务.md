# Token刷新任务

<cite>
**本文档引用的文件**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java)
- [RefreshAccessTokenTaskConfig.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/config/RefreshAccessTokenTaskConfig.java)
- [AuthService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AuthService.java)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java)
- [PddAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java)
</cite>

## 目录
1. [简介](#简介)
2. [任务调度配置](#任务调度配置)
3. [核心功能与执行流程](#核心功能与执行流程)
4. [异常处理机制](#异常处理机制)
5. [性能优化建议](#性能优化建议)
6. [扩展新平台支持](#扩展新平台支持)
7. [结论](#结论)

## 简介
`RefreshAccessTokenTask` 是用户中心服务中的一个关键定时任务，负责定期检查并刷新第三方电商平台（如淘宝、京东、拼多多、抖音、快手、小红书等）用户的授权Token。该任务通过调用各平台的授权服务接口，使用有效的 `refresh_token` 获取新的 `access_token`，从而防止因Token过期导致的服务中断，保障系统与各电商平台的持续稳定对接。

**Section sources**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java)

## 任务调度配置

### Cron表达式与执行周期
该任务为多个平台配置了独立的Cron表达式，以实现精细化的调度控制。核心调度策略如下：

- **淘宝主站 (TaoBao)**:
  - `0 0 0 * * *`: 每日凌晨0点执行，扫描需要授权的用户。
  - `0 0 2 * * *`: 每日凌晨2点执行，扫描授权即将过期的用户。
- **淘宝供货商 (TaoBao Supplier)**:
  - `0 0 0/1 * * *`: 每小时执行一次，确保供货商业务的Token及时刷新。
- **其他主流平台 (如抖音、1688、快手、微信小店等)**:
  - `0 0 0/1 * * *`: 每小时执行一次，保证高频率的Token健康检查。
- **小红书 (XHS)**:
  - `0 */15 * * * *`: 每15分钟执行一次，因其Token有效期较短，需要更频繁的刷新。

### 线程池配置
任务的线程池大小由 `RefreshAccessTokenTaskConfig` 类中的配置项控制：
- **核心线程数 (`coreSize`)**: 由 `uac.refresh.access.token.task.core.size.config` 配置，默认为1。
- **最大线程池大小 (`poolSize`)**: 由 `uac.refresh.access.token.task.pool.size.config` 配置，默认为1。

该配置通过 `RefreshAccessTokenTaskConfig` 类注入，允许在不同环境（开发、生产）中动态调整，以平衡资源消耗和任务执行效率。

```mermaid
flowchart TD
A["任务调度配置"] --> B["Cron表达式"]
A --> C["线程池配置"]
B --> D["淘宝: 每日0点/2点"]
B --> E["供货商: 每小时"]
B --> F["小红书: 每15分钟"]
C --> G["核心线程数: 1"]
C --> H["最大线程数: 1"]
```

**Diagram sources**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java)
- [RefreshAccessTokenTaskConfig.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/config/RefreshAccessTokenTaskConfig.java)

**Section sources**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java#L1-L292)
- [RefreshAccessTokenTaskConfig.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/config/RefreshAccessTokenTaskConfig.java#L1-L65)

## 核心功能与执行流程

### 功能概述
`RefreshAccessTokenTask` 的核心功能是作为调度中心，协调数据查询、Token刷新和结果处理。它不直接实现刷新逻辑，而是依赖 `SellerService` 和各平台的 `AuthService` 实现。

### 执行流程
任务的执行流程遵循典型的批处理模式：

1.  **任务触发**: 由 `@Scheduled` 注解根据Cron表达式触发。
2.  **开关检查**: 首先检查 `RefreshAccessTokenTaskConfig` 中对应的平台刷新开关，若关闭则直接返回。
3.  **分页查询**: 调用 `UserRepository` 的 `queryByW1DeadLineAndOrderCycleEnd` 等方法，分页查询出授权即将过期的用户信息（`UserProductInfo`）。查询条件基于当前时间、过期截止时间（`w1DeadLine`）和订单周期结束时间（`orderCycleEnd`），每次查询最多1000条记录。
4.  **批量处理**: 对每一批查询到的用户，执行 `batchProcess` 方法。
5.  **构建用户信息**: 为每个用户创建 `UserInfoBo` 对象，封装平台ID、应用名、用户昵称等必要信息。
6.  **调用刷新服务**: 通过 `SellerService.refreshAccessToken` 方法，传入 `UserInfoBo` 和 `refresh_token`，触发具体的Token刷新逻辑。
7.  **结果处理**: 记录刷新成功或失败的日志。成功则记录新Token，失败则捕获异常并记录错误信息。

```mermaid
sequenceDiagram
participant Scheduler as 调度器
participant Task as RefreshAccessTokenTask
participant Repo as UserRepository
participant Service as SellerService
participant Auth as AuthService
Scheduler->>Task : 触发定时任务
Task->>Task : 检查刷新开关
alt 开关关闭
Task-->>Scheduler : 忽略任务
else 开关开启
loop 分页查询
Task->>Repo : queryByW1DeadLineAndOrderCycleEnd(maxId)
Repo-->>Task : 返回用户列表
alt 用户列表为空
break
end
loop 批量处理每个用户
Task->>Task : 构建 UserInfoBo
Task->>Service : refreshAccessToken(userInfoBo, refreshToken)
Service->>Auth : refreshToken(userInfoBo, refreshToken)
Auth-->>Service : 返回新Token
Service-->>Task : 返回新Token
Task->>Task : 记录日志
end
end
Task-->>Scheduler : 任务完成
end
```

**Diagram sources**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java#L1-L292)

**Section sources**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java#L1-L292)

## 异常处理机制

### 网络异常与平台限流
当调用第三方平台API时，可能会遇到网络超时、连接失败或平台接口限流等异常。系统通过以下方式应对：
- **重试机制**: 在 `AuthService` 的基类 `BaseAuthServiceImpl` 中，通过 `getRetryCount()` 方法获取重试次数（默认5次），对网络请求进行自动重试，以应对瞬时故障。
- **异常捕获**: 在 `RefreshAccessTokenTask` 的 `batchProcess` 方法中，使用 `try-catch` 捕获所有异常，确保单个用户的刷新失败不会导致整个任务中断。

### 刷新失败后的重试策略
- **任务级重试**: 由于任务是周期性执行的，即使某次执行失败，下一次调度周期会重新尝试刷新该用户的Token。
- **日志告警**: 所有刷新失败的异常都会被记录到日志中，并通过 `LoggerHelper` 输出详细的错误信息（包括用户昵称、ID和错误消息），便于运维人员排查问题。
- **配置化开关**: 每个平台的刷新任务都有独立的开关（如 `doudianRefreshAccessTokenSwitch`），在平台出现大规模故障时，可以手动关闭该任务，避免无效请求和日志刷屏。

**Section sources**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java#L1-L292)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java#L1-L251)
- [PddAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java#L1-L131)

## 性能优化建议

### 批量处理与并发控制
- **批量处理**: 任务采用分页查询（`limit=1000`）和批量处理的方式，避免一次性加载过多数据导致内存溢出。
- **并发控制**: 当前线程池配置为单线程（`coreSize=1`），确保了任务执行的顺序性和数据一致性。对于需要更高吞吐量的场景，可适当增加 `coreSize` 和 `poolSize`，但需评估对数据库和第三方API的压力。

### 监控指标
建议增加以下监控指标以提升可观测性：
- **刷新成功率**: 统计成功刷新的Token数量与总尝试数量的比率。
- **平均耗时**: 记录每次任务执行的总耗时，以及单个用户Token刷新的平均耗时。
- **失败率告警**: 对刷新失败的异常进行统计，并设置阈值告警，及时发现平台接口异常或配置问题。

**Section sources**
- [RefreshAccessTokenTask.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/task/RefreshAccessTokenTask.java#L1-L292)
- [RefreshAccessTokenTaskConfig.java](file://uac-job/uac-authorization-job/src/main/java/cn/loveapp/uac/authorization/config/RefreshAccessTokenTaskConfig.java#L1-L65)

## 扩展新平台支持

要为新的电商平台（例如“新平台X”）添加Token刷新支持，需遵循以下步骤：

1.  **创建配置类**: 在 `uac-common` 模块的 `config` 包下，为新平台创建一个配置类（如 `XPlatformAppConfig`），用于存储AppKey、AppSecret等密钥信息。
2.  **实现AuthService**: 在 `uac-common` 模块的 `platform/api/impl` 包下，创建一个实现类（如 `XAuthServiceImpl`），继承 `BaseAuthServiceImpl` 并实现 `AuthService` 接口。重点实现 `refreshToken` 方法，定义调用新平台API的具体逻辑。
3.  **注入配置**: 在 `XAuthServiceImpl` 的构造函数中，将第1步创建的配置类作为参数注入，并通过 `super()` 传递给基类。
4.  **在任务中集成**: 在 `RefreshAccessTokenTask` 中，注入新平台的配置属性（如 `PlatformXProperties`），然后仿照 `scanDoudianAuthorizationExpiredAction` 的模式，添加一个新的调度方法 `scanXAuthorizationExpiredAction`，调用 `doRefreshTokenByApps` 方法完成集成。

```mermaid
classDiagram
class AuthService {
<<interface>>
+refreshToken(UserInfoBo, String, String, String) RefreshTokenCallbackResult
}
class BaseAuthServiceImpl {
+reGetAccessTokenWithRefreshToken(String, String, Map~String, String~, Class~T~, String) T
}
class TaoAuthServiceImpl
class PddAuthServiceImpl
class XAuthServiceImpl
AuthService <|-- BaseAuthServiceImpl
BaseAuthServiceImpl <|-- TaoAuthServiceImpl
BaseAuthServiceImpl <|-- PddAuthServiceImpl
BaseAuthServiceImpl <|-- XAuthServiceImpl
```

**Diagram sources**
- [AuthService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AuthService.java#L14-L59)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java#L1-L251)
- [PddAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java#L1-L131)

**Section sources**
- [AuthService.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/AuthService.java#L14-L59)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java#L1-L251)
- [PddAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java#L1-L131)

## 结论
`RefreshAccessTokenTask` 是一个设计良好、可扩展的定时任务，通过配置化的调度、分页批处理和统一的异常处理，有效地保障了多平台授权Token的持续有效性。其核心在于解耦，将调度逻辑与具体的刷新实现分离，使得系统能够轻松地支持新的电商平台。通过合理的性能监控和优化，该任务能够稳定、高效地运行，为上层业务提供可靠的授权保障。