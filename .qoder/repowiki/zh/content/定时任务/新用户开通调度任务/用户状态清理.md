# 用户状态清理

<cite>
**本文档引用的文件**
- [ScanAuthCancelledUserTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/close/ScanAuthCancelledUserTask.java)
- [NewUserTaskConfig.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/config/NewUserTaskConfig.java)
- [UserProductionInfoExtRepository.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/UserProductionInfoExtRepository.java)
- [UserProductInfoBusinessExt.java](file://uac-api/src/main/java/cn/loveapp/uac/entity/UserProductInfoBusinessExt.java)
- [RedisConfiguration.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/RedisConfiguration.java)
</cite>

## 目录
1. [简介](#简介)
2. [任务执行机制](#任务执行机制)
3. [授权状态检测逻辑](#授权状态检测逻辑)
4. [状态同步与数据清理策略](#状态同步与数据清理策略)
5. [执行频率与性能影响](#执行频率与性能影响)
6. [大规模用户变更处理](#大规模用户变更处理)
7. [数据一致性保障](#数据一致性保障)
8. [结论](#结论)

## 简介
本系统中的用户状态清理任务旨在自动检测并处理已取消平台授权的用户，确保本地系统状态与各电商平台（如淘宝、拼多多、抖店等）的授权状态保持一致。当前实现主要针对抖店平台，通过定时调用平台API验证用户授权有效性，一旦发现用户取消授权，则立即更新本地数据库状态、清理相关缓存，并触发后续资源回收流程。该机制对于维护系统数据准确性、释放无效资源、提升整体系统稳定性具有重要意义。

## 任务执行机制

```mermaid
flowchart TD
Start([开始执行任务]) --> CheckConfig["检查任务开关配置"]
CheckConfig --> |开启| QueryUsers["分页查询待检测用户"]
CheckConfig --> |关闭| End([任务跳过])
QueryUsers --> HasUsers{"查询到用户?"}
HasUsers --> |否| End
HasUsers --> |是| ProcessUser["处理单个用户"]
ProcessUser --> GetSession["获取用户TopSession"]
GetSession --> CallAPI["调用平台API验证授权"]
CallAPI --> CheckResult{"授权失败?"}
CheckResult --> |是| UpdateStatus["更新本地pullStatus为-101"]
CheckResult --> |否| NextUser["处理下一个用户"]
UpdateStatus --> LogUpdate["记录更新日志"]
LogUpdate --> NextUser
NextUser --> MoreUsers{"还有更多用户?"}
MoreUsers --> |是| ProcessUser
MoreUsers --> |否| End
```

**图示来源**
- [ScanAuthCancelledUserTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/close/ScanAuthCancelledUserTask.java#L32-L110)

**本节来源**
- [ScanAuthCancelledUserTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/close/ScanAuthCancelledUserTask.java#L32-L110)
- [NewUserTaskConfig.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/config/NewUserTaskConfig.java#L13-L76)

## 授权状态检测逻辑

```mermaid
sequenceDiagram
participant Task as ScanAuthCancelledUserTask
participant UAC as UserCenterInnerApiService
participant SDK as DoudianSDKService
participant Platform as 抖店平台
Task->>Task : 启动定时任务
Task->>Task : 检查scanAuthCancelledUserEnable开关
alt 开关开启
loop 分页查询用户
Task->>UserProductionInfoExtRepository : queryByPullStatusAndStoreIdAndTopStatusLimit()
UserProductionInfoExtRepository-->>Task : 返回用户列表
loop 遍历每个用户
Task->>UAC : getTopSession(sellerNick, PLATFORM_DOUDIAN)
UAC-->>Task : 返回sessionKey
Task->>SDK : execute(OpenCloudDdpGetShopListRequest, sessionKey)
SDK->>Platform : HTTP请求
Platform-->>SDK : 返回响应
SDK-->>Task : OpenCloudDdpGetShopListResponse
Task->>Task : 检查response.code == "30001"
alt 授权失败
Task->>UserProductInfoBusinessExt : setPullStatus(DB_FAILED)
Task->>UserProductionInfoExtRepository : update()
end
end
end
end
Task->>Task : 记录任务耗时日志
```

**图示来源**
- [ScanAuthCancelledUserTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/close/ScanAuthCancelledUserTask.java#L32-L110)

**本节来源**
- [ScanAuthCancelledUserTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/close/ScanAuthCancelledUserTask.java#L32-L110)
- [UserCenterInnerApiService.java](file://uac-api/src/main/java/cn/loveapp/uac/service/UserCenterInnerApiService.java)
- [DoudianSDKService.java](file://uac-common/src/main/java/cn/loveapp/common/platformsdk/doudian/DoudianSDKService.java)

## 状态同步与数据清理策略

```mermaid
classDiagram
class UserProductInfoBusinessExt {
+sellerId : String
+sellerNick : String
+storeId : String
+dbStatus : Integer
+topStatus : String
+pullStatus : Integer
+apiStatus : Integer
+appName : String
+products : String
+DB_WAIT = 101
+DB_DOING = 102
+DB_FAILED = -101
+DB_DONE = 10
+API_STATUS_FAILED_BY_SESSION = -201
+API_STATUS_SUCCESS = 10
+checkApiStatus(apiStatus) : Boolean
+isSuccess() : Boolean
}
class UserProductionInfoExtRepository {
+queryByPullStatusAndStoreIdAndTopStatusLimit(pullStatus, storeId, topStatus, offset, limit, businessId) : UserProductInfoBusinessExt[]
+update(userProductInfoBusinessExt, businessId) : int
}
class NewUserTaskConfig {
+scanAuthCancelledUserLimit : Integer
+scanAuthCancelledUserEnable : Boolean
+poolSize : Integer
+businessPoolSize : Integer
}
class RedisConfiguration {
+redisItemProperties
+redisTradeProperties
+redisDistributeProperties
+redisShophelperProperties
}
UserProductionInfoExtRepository --> UserProductInfoBusinessExt : "返回"
ScanAuthCancelledUserTask --> UserProductionInfoExtRepository : "依赖"
ScanAuthCancelledUserTask --> UserProductInfoBusinessExt : "操作"
ScanAuthCancelledUserTask --> NewUserTaskConfig : "依赖"
ScanAuthCancelledUserTask --> RedisConfiguration : "间接影响"
```

**图示来源**
- [UserProductInfoBusinessExt.java](file://uac-api/src/main/java/cn/loveapp/uac/entity/UserProductInfoBusinessExt.java#L14-L134)
- [UserProductionInfoExtRepository.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/UserProductionInfoExtRepository.java#L12-L140)
- [NewUserTaskConfig.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/config/NewUserTaskConfig.java#L13-L76)
- [RedisConfiguration.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/RedisConfiguration.java)

**本节来源**
- [UserProductInfoBusinessExt.java](file://uac-api/src/main/java/cn/loveapp/uac/entity/UserProductInfoBusinessExt.java#L14-L134)
- [UserProductionInfoExtRepository.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/UserProductionInfoExtRepository.java#L12-L140)
- [RedisConfiguration.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/RedisConfiguration.java)

## 执行频率与性能影响

```mermaid
flowchart LR
subgraph 配置参数
A[uac.newuser.task.scanAuthCancelledUser.delay: 5分钟]
B[uac.newuser.task.scanAuthCancelledUser.limit: 50条/页]
C[uac.newuser.task.scanAuthCancelledUser.enable: true]
D[uac.newuser.task.pool.size: 50线程]
end
subgraph 性能影响
E[执行频率] --> F[每5分钟一次]
G[分页大小] --> H[单次处理50用户]
I[线程池] --> J[并发处理能力]
K[API调用] --> L[抖店API限流]
M[数据库查询] --> N[分页查询性能]
end
A --> E
B --> G
C --> F
D --> J
```

**图示来源**
- [NewUserTaskConfig.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/config/NewUserTaskConfig.java#L13-L76)

**本节来源**
- [NewUserTaskConfig.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/config/NewUserTaskConfig.java#L13-L76)
- [ScanAuthCancelledUserTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/close/ScanAuthCancelledUserTask.java#L32-L110)

## 大规模用户变更处理

```mermaid
flowchart TD
Start[开始处理] --> Config["读取配置: scanAuthCancelledUserLimit=50"]
Config --> Loop["循环分页处理"]
Loop --> Query["查询50条用户记录"]
Query --> Check{"查询结果为空?"}
Check --> |是| End[结束]
Check --> |否| Process["处理当前页用户"]
Process --> API["调用平台API验证"]
API --> Update["更新数据库状态"]
Update --> Next["检查是否需继续"]
Next --> |记录数<50| End
Next --> |记录数=50| Loop
style Loop fill:#f9f,stroke:#333,stroke-width:2px
```

**图示来源**
- [ScanAuthCancelledUserTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/close/ScanAuthCancelledUserTask.java#L32-L110)

**本节来源**
- [ScanAuthCancelledUserTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/close/ScanAuthCancelledUserTask.java#L32-L110)
- [UserProductionInfoExtRepository.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/UserProductionInfoExtRepository.java#L12-L140)

## 数据一致性保障

```mermaid
stateDiagram-v2
[*] --> Idle
Idle --> Querying : 任务启动
Querying --> Validating : 获取用户列表
Validating --> API_Call : 获取TopSession
API_Call --> Check_Response : 调用平台API
Check_Response --> |code=30001| Update_DB : 更新pullStatus=-101
Check_Response --> |code!=30001| Next_User : 处理下一个
Update_DB --> Log_Success : 记录成功日志
Next_User --> More_Users?
More_Users? --> |是| Validating
More_Users? --> |否| Complete
Complete --> Idle : 任务结束
Log_Success --> Next_User
state Update_DB {
[*] --> DB_Transaction
DB_Transaction --> |成功| Updated
DB_Transaction --> |失败| Log_Error
Log_Error --> Updated
}
```

**图示来源**
- [ScanAuthCancelledUserTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/close/ScanAuthCancelledUserTask.java#L32-L110)

**本节来源**
- [ScanAuthCancelledUserTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/close/ScanAuthCancelledUserTask.java#L32-L110)
- [UserProductionInfoExtRepository.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/repository/UserProductionInfoExtRepository.java#L12-L140)

## 结论
`ScanAuthCancelledUserTask` 是一个关键的后台定时任务，负责维护系统与抖店平台之间的用户授权状态一致性。该任务通过配置化的开关和参数控制执行，采用分页查询和逐个验证的方式，调用平台API检测用户授权状态。一旦发现用户取消授权（返回特定错误码30001），任务会立即将本地数据库中的`pullStatus`更新为`-101`（DB_FAILED），并记录操作日志。此机制确保了系统能够及时响应外部平台的授权变更，避免对已失效用户进行不必要的数据拉取和处理，从而优化了系统资源使用并保障了数据的准确性。未来可考虑扩展支持更多电商平台的授权状态检测。