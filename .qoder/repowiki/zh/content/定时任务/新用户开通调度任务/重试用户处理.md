# 重试用户处理

<cite>
**本文档引用文件**   
- [ScanRetryUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanRetryUser.java)
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java)
- [NewUserTaskService.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/NewUserTaskService.java)
- [AyBusinessOpenUser.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/AyBusinessOpenUser.java)
- [LevelConstant.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/constant/LevelConstant.java)
</cite>

## 目录
1. [简介](#简介)
2. [任务执行流程](#任务执行流程)
3. [核心组件分析](#核心组件分析)
4. [重试逻辑实现](#重试逻辑实现)
5. [用户状态管理](#用户状态管理)
6. [重试次数限制策略](#重试次数限制策略)
7. [日志记录与监控](#日志记录与监控)
8. [告警机制](#告警机制)
9. [监控指标建议](#监控指标建议)
10. [与用户生命周期管理的集成](#与用户生命周期管理的集成)

## 简介
`ScanRetryUser`任务是用户中心服务中用于处理开通失败用户的核心定时任务。该任务周期性扫描处于重试状态的用户，根据预设的重试策略进行重新开通尝试。当用户开通失败时，系统会将其状态标记为可重试状态（WAIT_RETRY），并记录重试次数和时间。本任务负责识别这些用户，执行重试逻辑，并在达到最大重试次数后将用户标记为永久失败。

**Section sources**
- [ScanRetryUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanRetryUser.java#L1-L90)

## 任务执行流程
`ScanRetryUser`任务通过Spring的`@Scheduled`注解配置为固定延迟执行，默认每分钟执行一次。任务启动时会创建一个线程池来并行处理不同业务线的重试请求。任务首先检查配置是否启用，然后遍历所有配置的业务ID，为每个业务ID提交一个异步任务到线程池中执行。

```mermaid
flowchart TD
Start([任务开始]) --> CheckConfig["检查任务是否启用"]
CheckConfig --> |禁用| End([任务结束])
CheckConfig --> |启用| GetBusinessIds["获取业务ID列表"]
GetBusinessIds --> ForEachBusiness["遍历每个业务ID"]
ForEachBusiness --> SubmitTask["提交异步任务到线程池"]
SubmitTask --> ExecuteService["执行NewUserTaskService.scanRetryUser"]
ExecuteService --> End
```

**Diagram sources **
- [ScanRetryUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanRetryUser.java#L1-L90)

**Section sources**
- [ScanRetryUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanRetryUser.java#L1-L90)

## 核心组件分析
### ScanRetryUser 任务类
`ScanRetryUser`是Spring管理的组件，实现了`ApplicationListener<ContextClosedEvent>`接口，能够在应用关闭时优雅地处理线程池的关闭。该类主要职责是调度重试任务的执行。

#### 类结构
```mermaid
classDiagram
class ScanRetryUser {
-LOGGER : LoggerHelper
-newUserTaskService : NewUserTaskService
-newUserTaskConfig : NewUserTaskConfig
-businessExecutorService : ThreadPoolExecutor
+ScanRetryUserConstruct()
+onApplicationEvent(event : ContextClosedEvent)
+scanRetryUser()
}
ScanRetryUser --> NewUserTaskService : "依赖"
ScanRetryUser --> NewUserTaskConfig : "依赖"
ScanRetryUser --> ThreadPoolExecutor : "使用"
```

**Diagram sources **
- [ScanRetryUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanRetryUser.java#L1-L90)

### NewUserTaskService 接口
`NewUserTaskService`定义了新用户处理相关的服务接口，其中`scanRetryUser`方法专门用于处理重试用户的逻辑。

```mermaid
classDiagram
class NewUserTaskService {
<<interface>>
+scanWaitOpenUser(platformId : String, businessId : String, appName : String)
+scanRetryUser(platformId : String, businessId : String, appName : String)
+scanOpenExceptionUser(platformId : String, businessId : String, appName : String)
+scanOfflineUser(platformId : String, businessId : String, appName : String)
}
```

**Diagram sources **
- [NewUserTaskService.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/NewUserTaskService.java#L1-L76)

### NewUserTaskServiceImpl 实现类
`NewUserTaskServiceImpl`是`NewUserTaskService`接口的具体实现，包含了重试用户处理的核心逻辑。

#### 依赖关系
```mermaid
graph TD
ScanRetryUser --> NewUserTaskService
NewUserTaskService --> NewUserTaskServiceImpl
NewUserTaskServiceImpl --> AyBusinessOpenUserRepository
NewUserTaskServiceImpl --> UserProductionInfoExtRepository
NewUserTaskServiceImpl --> RocketMqQueueHelper
NewUserTaskServiceImpl --> NewUserQueueConfigMap
NewUserTaskServiceImpl --> UserService
```

**Diagram sources **
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L1-L502)

**Section sources**
- [ScanRetryUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanRetryUser.java#L1-L90)
- [NewUserTaskService.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/NewUserTaskService.java#L1-L76)
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L1-L502)

## 重试逻辑实现
### 查询逻辑
任务通过`AyBusinessOpenUserRepository`查询状态为`WAIT_RETRY`（-102）的用户。查询条件包括：
- 状态：WAIT_RETRY
- 最大ID（用于分页）
- 偏移量和限制数量
- 业务ID

查询结果按ID分页处理，避免一次性加载过多数据影响系统性能。

### 重试次数判断
系统通过`retry_count`字段跟踪用户的重试次数。每次重试前会检查当前重试次数是否超过默认最大重试次数（由`uac.open.user.retry.count`配置，默认480次）。如果超过，则将用户标记为永久失败。

### 指数退避算法
虽然当前实现中没有显式的指数退避算法，但通过配置化的延迟执行（`fixedDelayString = "${uac.newuser.task.scanRetryUser.delay:#{1 * 60 * 1000}}"`）实现了基本的重试间隔控制。实际的重试间隔由任务调度频率决定，默认为1分钟。

```mermaid
flowchart TD
Start([开始重试]) --> QueryUsers["查询WAIT_RETRY状态用户"]
QueryUsers --> CheckEmpty{"用户为空?"}
CheckEmpty --> |是| End([结束])
CheckEmpty --> |否| ForEachUser["遍历每个用户"]
ForEachUser --> GetRetryCount["获取retry_count"]
GetRetryCount --> CheckLimit{"retry_count > defaultRetryCount?"}
CheckLimit --> |是| MarkFailed["标记为FAILED状态"]
CheckLimit --> |否| IncrementRetry["retry_count + 1"]
IncrementRetry --> UpdateStatus["更新为OPENING状态"]
UpdateStatus --> SendToQueue["发送到newuser队列"]
SendToQueue --> LogResult["记录执行结果"]
LogResult --> NextUser
MarkFailed --> UpdateDB["更新数据库"]
UpdateDB --> NextUser
NextUser --> End
```

**Diagram sources **
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L1-L502)

**Section sources**
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L1-L502)

## 用户状态管理
### 状态转换
系统定义了完整的用户状态机，重试过程中涉及的状态转换如下：

```mermaid
stateDiagram-v2
[*] --> WAIT_RETRY
WAIT_RETRY --> OPENING : "重试开始"
OPENING --> DONE : "开通成功"
OPENING --> WAIT_RETRY : "开通失败"
OPENING --> FAILED : "超过最大重试次数"
WAIT_RETRY --> FAILED : "超过最大重试次数"
```

### 状态定义
在`AyBusinessOpenUser`实体类中定义了所有可能的用户状态：

```mermaid
classDiagram
class AyBusinessOpenUser {
+WAIT_OPEN = 101
+OPENING = 102
+DONE = 10
+FAILED_INVALID_TOKEN = -103
+FAILED = -101
+WAIT_RETRY = -102
+RULE_DEFAULT = 1
+OPEN_LOG_TYPE_NEW_OPEN = 1
+OPEN_LOG_TYPE_RE_OPEN = 2
+OPEN_LOG_TYPE_NEW_APP = 3
}
```

**Diagram sources **
- [AyBusinessOpenUser.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/AyBusinessOpenUser.java#L1-L130)

**Section sources**
- [AyBusinessOpenUser.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/AyBusinessOpenUser.java#L1-L130)

## 重试次数限制策略
### 基于LevelConstant的等级规则
虽然当前重试逻辑主要基于固定的重试次数限制，但系统中存在`LevelConstant`类，定义了不同用户等级对应的权限：

```mermaid
classDiagram
class LevelConstant {
+LEVEL_BASIC_VERSION = 0
+LEVEL_ADVANCED_VERSION = 1
+LEVEL_UNUSED_VERSION = 2
+LEVEL_ADVANCED_AUTOMATIC_RENEWAL_VERSION = 3
+LEVEL_PROBATION_VERSION_NOT_SAVE_ORDER = 4
+LEVEL_PROBATION_VERSION_SAVE_ORDER = 5
}
```

这些等级可能影响重试策略，例如高级版本用户可能享有更多的重试机会或更短的重试间隔。

### 配置化重试限制
系统通过配置项`uac.open.user.retry.count`（默认480）来控制最大重试次数。这个值可以在不同环境（开发、预发、生产）中进行调整，以适应不同的业务需求。

**Diagram sources **
- [LevelConstant.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/constant/LevelConstant.java#L1-L41)

**Section sources**
- [LevelConstant.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/constant/LevelConstant.java#L1-L41)
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L1-L502)

## 日志记录与监控
### 日志记录
系统使用`LoggerHelper`进行详细的日志记录，包括：
- 任务开始和结束
- 处理的用户数量
- 每个用户的重试过程
- 成功和失败的详细信息

日志中包含MDC（Mapped Diagnostic Context）信息，便于追踪特定任务的执行过程。

### 日志级别
- `logInfo`: 记录正常执行流程
- `logError`: 记录异常情况和错误信息

**Section sources**
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L1-L502)

## 告警机制
虽然当前代码中没有直接的告警发送逻辑，但系统通过以下方式支持告警机制：
1. **错误日志记录**: 所有异常都会被记录为错误日志，可以被监控系统捕获
2. **状态标记**: 持续失败的用户会被标记为`FAILED`状态，便于后续分析
3. **RocketMQ消息**: 重试请求通过消息队列发送，消息发送失败会被记录

开发团队可以通过监控错误日志、FAILED状态用户数量等指标来设置告警规则。

**Section sources**
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L1-L502)

## 监控指标建议
为了有效监控重试用户处理任务，建议关注以下指标：

| 指标名称 | 描述 | 收集方式 |
|---------|------|---------|
| 重试任务执行频率 | 任务实际执行的间隔时间 | 日志时间戳分析 |
| 待重试用户数量 | WAIT_RETRY状态的用户总数 | 数据库查询统计 |
| 失败率 | 重试失败用户占总重试用户的比率 | (失败用户数/总重试用户数)×100% |
| 平均重试次数 | 所有重试用户的平均重试次数 | 数据库retry_count字段统计 |
| 任务执行时间 | 单次任务执行的耗时 | 任务开始和结束时间差 |
| 消息发送成功率 | 发送到newuser队列的消息成功率 | 消息系统返回结果统计 |
| 永久失败用户增长 | 每日被标记为FAILED状态的用户数量 | 数据库状态变更监控 |

这些指标可以帮助团队及时发现重试系统的问题，优化重试策略。

## 与用户生命周期管理的集成
`ScanRetryUser`任务是用户生命周期管理的重要组成部分，与其他任务协同工作：

```mermaid
graph TB
subgraph "用户生命周期"
A[新用户注册] --> B[ScanWaitOpenUser]
B --> C[开通处理]
C --> D{成功?}
D --> |是| E[标记为DONE]
D --> |否| F[标记为WAIT_RETRY]
F --> G[ScanRetryUser]
G --> C
G --> H{超过最大重试次数?}
H --> |是| I[标记为FAILED]
H --> |否| G
J[ScanOpenExceptionUser] --> K{长时间OPENING?}
K --> |是| L[重置为WAIT_OPEN]
L --> B
end
```

该任务与`ScanWaitOpenUser`（处理待开通用户）和`ScanOpenExceptionUser`（处理异常开通用户）等任务共同构成了完整的用户开通和重试机制。

**Diagram sources **
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L1-L502)

**Section sources**
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L1-L502)