# 待开通用户扫描

<cite>
**本文档引用文件**   
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java)
- [NewUserTaskConfig.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/config/NewUserTaskConfig.java)
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java)
- [TaoNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/TaoNewUserPlatformHandleServiceImpl.java)
- [AbstractNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/AbstractNewUserPlatformHandleServiceImpl.java)
- [NewUserPlatformHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/NewUserPlatformHandleService.java)
- [application.properties](file://uac-newusers/uac-newuser-scheduler/src/main/resources/application.properties)
</cite>

## 目录
1. [简介](#简介)
2. [任务调度配置](#任务调度配置)
3. [数据库查询与分页处理](#数据库查询与分页处理)
4. [任务执行流程](#任务执行流程)
5. [平台适配器模式实现](#平台适配器模式实现)
6. [异常处理与重试机制](#异常处理与重试机制)
7. [性能优化建议](#性能优化建议)

## 简介
待开通用户扫描任务（ScanWaitOpenUser）是用户中心服务中的核心定时任务之一，负责扫描待开通的新用户并触发开户流程。该任务通过Spring的@Scheduled注解实现定时执行，采用分页查询方式从数据库中筛选状态为"待开通"的用户记录，并通过消息队列异步处理用户开通流程。任务设计遵循平台适配器模式，支持多种电商平台的差异化处理逻辑，确保系统具有良好的扩展性和可维护性。

## 任务调度配置
待开通用户扫描任务的调度配置采用Spring框架的定时任务机制，通过Cron表达式或固定延迟方式控制任务执行频率。

```mermaid
flowchart TD
A[定时任务触发] --> B{任务开关是否启用}
B --> |否| C[跳过执行]
B --> |是| D[获取业务ID列表]
D --> E[为每个业务ID创建执行线程]
E --> F[执行用户扫描逻辑]
F --> G[修改用户状态为开通中]
G --> H[发送消息到开户队列]
H --> I[任务执行完成]
```

**图源**
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java#L75-L92)
- [NewUserTaskConfig.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/config/NewUserTaskConfig.java#L45-L50)

任务的执行延迟通过配置项`uac.newuser.task.scanWaitOpenUser.delay`控制，默认值为1分钟（60000毫秒）。该配置支持SpEL表达式，允许在配置文件中动态设置。任务开关`scanWaitOpenUserEnable`默认为关闭状态，需要在配置文件中显式启用。

**本节来源**
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java#L75-L92)
- [NewUserTaskConfig.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/config/NewUserTaskConfig.java#L45-L50)

## 数据库查询与分页处理
待开通用户扫描任务采用分页查询策略处理大规模用户数据，避免一次性加载过多数据导致内存溢出。

```mermaid
flowchart TD
A[初始化maxId=0] --> B[执行分页查询]
B --> C{查询结果为空?}
C --> |是| D[结束扫描]
C --> |否| E[更新maxId为结果集中最大ID]
E --> F[处理当前页用户]
F --> G[修改用户状态]
G --> H[发送消息到队列]
H --> B
```

**图源**
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L45-L85)

任务通过`AyBusinessOpenUserRepository.queryByStatus`方法查询状态为`WAIT_OPEN`（值为1）的用户记录。查询采用基于ID的分页策略，每次查询后记录结果集中最大的ID值作为下一次查询的起始点，确保不会遗漏数据。分页大小由配置项`uac.open.user.scan.limit`控制，默认值为200条记录。

在预发环境处理方面，任务会根据环境配置和灰度用户列表进行特殊处理。生产环境中会排除灰度用户，而预发环境中则只处理灰度用户，确保测试数据不会影响生产环境。

**本节来源**
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L45-L85)

## 任务执行流程
待开通用户扫描任务的执行流程包含多个关键步骤，确保用户开通过程的可靠性和一致性。

```mermaid
sequenceDiagram
participant Task as ScanWaitOpenUser任务
participant Service as NewUserTaskService
participant Repo as AyBusinessOpenUserRepository
participant MQ as 消息队列
Task->>Service : scanWaitOpenUser()
Service->>Repo : queryByStatus(WAIT_OPEN)
Repo-->>Service : 返回用户列表
loop 每个用户
Service->>Service : 修改状态为OPENING
Service->>Repo : updateByStatus()
Service->>MQ : 发送OpenUserRequest
MQ-->>Service : 返回消息ID
alt 发送失败
Service->>Service : 重置状态为WAIT_OPEN
Service->>日志 : 记录错误
end
end
```

**图源**
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java#L75-L92)
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L45-L85)

任务执行流程如下：
1. 检查任务开关是否启用，若未启用则直接返回
2. 获取需要处理的业务ID列表
3. 为每个业务ID创建独立的执行线程
4. 执行分页查询，获取待开通用户列表
5. 对每个用户执行以下操作：
   - 修改用户状态为"开通中"（OPENING）
   - 构造OpenUserRequest消息
   - 发送消息到newuser开户队列
   - 若发送失败，重置用户状态为"待开通"

任务采用线程池并发处理不同业务ID的用户数据，线程池大小由`businessPoolSize`配置项控制，默认为10个线程。

**本节来源**
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java#L75-L92)
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L45-L85)

## 平台适配器模式实现
待开通用户扫描任务通过平台适配器模式实现对不同电商平台的差异化处理，确保系统具有良好的扩展性。

```mermaid
classDiagram
class NewUserPlatformHandleService {
<<interface>>
+isNeedPullData()
+isNeedSubscribeMc()
+handlePrepareOpenOnNormalCondition()
+send2PullDataQueue()
}
class AbstractNewUserPlatformHandleServiceImpl {
-openUserDispatcherConfig
-userCenterService
-rocketMqQueueHelper
+isNeedPullData()
+isNeedSubscribeMc()
+doSend2PullDataQueue()
}
class TaoNewUserPlatformHandleServiceImpl {
-searchActiveTimeout
-searchActiveRedisRepository
+isNeedPullData()
+isNeedSubscribeMc()
+handlePrepareOpenOnNormalCondition()
+getDispatcherId()
}
NewUserPlatformHandleService <|-- AbstractNewUserPlatformHandleServiceImpl
AbstractNewUserPlatformHandleServiceImpl <|-- TaoNewUserPlatformHandleServiceImpl
```

**图源**
- [NewUserPlatformHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/NewUserPlatformHandleService.java#L12-L109)
- [AbstractNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/AbstractNewUserPlatformHandleServiceImpl.java#L25-L192)
- [TaoNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/TaoNewUserPlatformHandleServiceImpl.java#L26-L83)

平台适配器模式的核心组件包括：
- **NewUserPlatformHandleService接口**：定义了平台处理的通用方法契约
- **AbstractNewUserPlatformHandleServiceImpl抽象类**：提供通用实现和默认行为
- **具体平台实现类**：如TaoNewUserPlatformHandleServiceImpl，实现特定平台的业务逻辑

以淘宝平台为例，`TaoNewUserPlatformHandleServiceImpl`实现了特定的处理逻辑：
- 需要拉取数据（isNeedPullData返回true）
- 需要订阅消息中心（isNeedSubscribeMc返回true）
- 特殊的预开户处理逻辑，包括活跃标识检查和代发业务处理

当用户开通时，系统通过`getDispatcherId()`方法识别平台类型，并调用相应的适配器进行处理，实现了开闭原则。

**本节来源**
- [NewUserPlatformHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/NewUserPlatformHandleService.java#L12-L109)
- [AbstractNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/AbstractNewUserPlatformHandleServiceImpl.java#L25-L192)
- [TaoNewUserPlatformHandleServiceImpl.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/impl/TaoNewUserPlatformHandleServiceImpl.java#L26-L83)

## 异常处理与重试机制
待开通用户扫描任务实现了完善的异常处理和重试机制，确保系统的可靠性和数据一致性。

任务在执行过程中可能遇到多种异常情况，包括：
- 数据库连接异常
- 消息队列发送失败
- 网络超时
- 业务逻辑异常

针对这些异常，任务采用了以下处理策略：
1. **状态回滚机制**：当消息发送失败时，立即将用户状态从"开通中"重置为"待开通"，确保用户不会被遗漏
2. **异常捕获与日志记录**：所有异常都被捕获并记录详细的错误日志，便于问题排查
3. **线程安全处理**：使用线程池的拒绝策略确保在高负载情况下任务的稳定性

在消息发送层面，系统通过RocketMQ的消息队列提供了可靠的异步通信机制。消息发送失败时，RocketMQ的生产者会自动进行重试，确保消息最终能够送达。

对于长时间处于"开通中"状态的异常用户，系统还提供了专门的修复任务`ScanOpenExceptionUser`，定期扫描超过24小时仍处于开通中的用户，并将其状态重置为"待开通"，防止用户被永久卡住。

**本节来源**
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L65-L85)
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java#L75-L92)

## 性能优化建议
为应对大规模用户数据的处理需求，提出以下性能优化建议：

### 分页大小优化
当前分页大小默认为200条记录，建议根据实际数据量和系统负载进行调整：
- 对于用户量较小的业务，可适当增大分页大小（如500-1000）以减少查询次数
- 对于用户量较大的业务，应保持较小的分页大小（如100-200）以控制内存使用

### 数据库索引优化
确保`AyBusinessOpenUser`表在以下字段上建立合适的索引：
- `status`字段：用于快速筛选待开通用户
- `id`字段：用于分页查询的排序和过滤
- `business_id`字段：用于按业务ID过滤

建议创建复合索引`(status, id, business_id)`，可以显著提升查询性能。

### 线程池配置优化
当前业务线程池大小为10，可根据服务器CPU核心数和业务负载进行调整：
- 一般建议设置为CPU核心数的1-2倍
- 需要监控线程池的队列长度和拒绝率，避免任务积压

### 缓存优化
对于频繁访问但不经常变更的数据，可考虑引入缓存机制：
- 使用Redis缓存用户基本信息
- 缓存平台配置信息，减少数据库查询
- 实现热点数据的本地缓存

### 监控与告警
建立完善的监控体系：
- 监控任务执行时间，及时发现性能瓶颈
- 监控消息队列积压情况
- 设置异常告警，及时发现和处理问题

**本节来源**
- [NewUserTaskConfig.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/config/NewUserTaskConfig.java#L55-L60)
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L25-L30)