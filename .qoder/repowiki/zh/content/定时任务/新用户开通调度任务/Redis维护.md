# Redis维护

<cite>
**本文档引用文件**  
- [RedisCleanTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/maintain/RedisCleanTask.java)
- [CacheTimeoutConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/CacheTimeoutConfig.java)
</cite>

## 目录
1. [简介](#简介)
2. [核心组件](#核心组件)
3. [任务执行流程](#任务执行流程)
4. [扫描与清理策略](#扫描与清理策略)
5. [批量删除实现](#批量删除实现)
6. [与缓存超时配置的协同](#与缓存超时配置的协同)
7. [大Key处理与阻塞规避](#大key处理与阻塞规避)
8. [监控指标](#监控指标)
9. [配置建议](#配置建议)
10. [依赖分析](#依赖分析)

## 简介
RedisClean任务是用户中心服务中的一个关键维护任务，负责定期清理过期或无效的缓存数据。该任务通过扫描数据库中的用户产品信息，识别出长期未更新或已过期的用户，并删除其在Redis中的缓存数据，从而释放内存资源，提升缓存命中率，确保缓存数据的时效性和系统稳定性。

## 核心组件

该任务的核心实现位于`RedisCleanTask`类中，主要依赖以下组件：
- **UserRepository**：用于分页查询满足清理条件的用户数据。
- **StringRedisTemplate**：用于操作Redis，支持多个Redis实例（trade、item、distribute）。
- **UserManageRedisRepositoryHashRedisRepository**：用于生成用户缓存的Redis Key。
- **CacheTimeoutConfig**：提供缓存超时时间的配置。

**本节涉及源文件**  
- [RedisCleanTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/maintain/RedisCleanTask.java)
- [CacheTimeoutConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/CacheTimeoutConfig.java)

## 任务执行流程

RedisClean任务通过Spring的`@Scheduled`注解定时触发，执行流程如下：

```mermaid
flowchart TD
A[任务触发] --> B[计算清理时间边界]
B --> C[并行提交各平台清理子任务]
C --> D{等待所有子任务完成}
D --> E[汇总清理结果]
E --> F[记录总清理数量与成功率]
```

**流程图来源**  
- [RedisCleanTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/maintain/RedisCleanTask.java#L309-L371)

## 扫描与清理策略

任务采用基于时间窗口的扫描策略，确保数据清理的连续性和完整性。

### 扫描边界确定
- **首次执行**：以当前时间前推2个月作为默认扫描边界。
- **后续执行**：以`getLastScanTime()`获取的上次扫描时间为起点，仅扫描新产生的过期数据，避免重复扫描。

### 清理条件判断
清理条件由`shouldCleanUserCache`方法定义，需同时满足以下条件：
- 用户的`order_cycle_end`、`lastupdatetime`、`w1_deadline`三个时间字段中至少有一个不为null。
- 所有非null的时间字段均早于扫描边界时间（即2个月前）。

```mermaid
flowchart TD
Start([开始]) --> CheckNull{"三个时间字段<br/>均为空？"}
CheckNull --> |是| Skip[跳过]
CheckNull --> |否| CheckOrder{"order_cycle_end<br/>在边界后？"}
CheckOrder --> |是| Skip
CheckOrder --> |否| CheckLast{"lastupdatetime<br/>在边界后？"}
CheckLast --> |是| Skip
CheckLast --> |否| CheckW1{"w1_deadline<br/>在边界后？"}
CheckW1 --> |是| Skip
CheckW1 --> |否| Clean[清理缓存]
Clean --> End([结束])
Skip --> End
```

**流程图来源**  
- [RedisCleanTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/maintain/RedisCleanTask.java#L537-L571)

## 批量删除实现

任务采用分页扫描与逐个删除相结合的方式实现批量清理，兼顾效率与稳定性。

### 分页查询
- 使用`userRepository.queryByW1DeadlineBeforeWithPage`方法，按`w1_deadline`字段分页查询过期用户。
- 每页大小为1000条记录，通过`lastW1Deadline`作为分页游标，确保数据一致性。

### 并行处理
- 任务初始化一个固定大小的线程池（默认5个线程），用于并行处理不同平台的清理任务。
- 每个平台作为一个独立的`PlatformCleanTask`提交到线程池，实现跨平台的并发清理。

```mermaid
classDiagram
class RedisCleanTask {
+ExecutorService executorService
+clean()
+initializeThreadPool()
+destroy()
}
class PlatformCleanTask {
+String platformId
+LocalDateTime defaultBoundary
+call()
}
RedisCleanTask --> PlatformCleanTask : "创建并提交"
RedisCleanTask --> ExecutorService : "使用"
```

**类图来源**  
- [RedisCleanTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/maintain/RedisCleanTask.java#L46-L83)

## 与缓存超时配置的协同

虽然`RedisCleanTask`本身不直接使用`CacheTimeoutConfig`中的`userSettingsCacheTimeout`配置，但两者在缓存管理策略上形成互补：

- **CacheTimeoutConfig**：定义了缓存的**主动过期时间**，依赖Redis自身的TTL机制，适用于有明确生命周期的缓存。
- **RedisCleanTask**：执行**被动清理**，处理因各种原因（如配置变更、数据异常）导致未被TTL机制清理的“僵尸”缓存，确保数据的最终一致性。

两者共同作用，构建了多层次的缓存生命周期管理机制。

**本节涉及源文件**  
- [CacheTimeoutConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/CacheTimeoutConfig.java#L0-L21)
- [RedisCleanTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/maintain/RedisCleanTask.java)

## 大Key处理与阻塞规避

`RedisCleanTask`通过以下设计规避因删除大Key导致的Redis阻塞问题：

1. **避免单次大规模删除**：任务不使用`KEYS`或`SCAN`命令进行全量Key匹配和批量删除（如`DEL`命令），而是通过数据库查询定位到具体的用户实体，再逐个删除其对应的缓存Key。
2. **细粒度删除**：每次`cleanUserRedisCache`方法只执行一次`redisTemplate.delete(redisKey)`操作，删除单个用户的缓存。即使该用户的缓存是一个大Hash或大List，其删除操作的复杂度也相对可控。
3. **分页与限流**：通过分页查询（每页1000条）和线程池并发度控制（默认5个线程），天然地限制了单位时间内对Redis的删除操作频率，起到了限流作用。

这种“查库定位，逐个删除”的模式，虽然执行时间较长，但能有效避免对Redis服务器造成瞬时高负载。

## 监控指标

任务通过日志和Redis状态记录提供了丰富的监控信息：

- **任务状态**：通过`TaskStatus`对象记录任务的运行状态（RUNNING, COMPLETED, FAILED），存储在Redis中，Key为`redis_clean_task_status:{platformId}:{appName}`。
- **任务进度**：通过`TaskProgress`对象记录清理进度，包括已处理数量、已清理数量和最后处理的`w1_deadline`，Key为`redis_clean_task_progress:{platformId}:{appName}`。
- **执行日志**：详细记录任务启动、每批次处理数量、清理总数、成功/失败平台数等信息，便于追踪和审计。

## 配置建议

为平衡数据新鲜度和系统稳定性，建议对以下参数进行合理配置：

| 配置项 | 配置键 | 建议值 | 说明 |
| :--- | :--- | :--- | :--- |
| 任务执行周期 | `uac.newuser.task.redisClean.cron` | `0 0 1 * * ?` (每日凌晨1点) | 避开业务高峰期，选择低峰时段执行。 |
| 线程池大小 | `uac.newuser.task.redisClean.threadPoolSize` | 3-8 | 根据平台数量和Redis性能调整，过高可能导致Redis连接数过多。 |
| 分页大小 | (代码内常量) | 1000 | 控制单次数据库查询和内存占用，不宜过大。 |
| 任务超时时间 | (代码内常量) | 30分钟 | 为单个平台的清理任务设置超时，防止长时间卡死。 |

## 依赖分析

```mermaid
graph TD
RedisCleanTask --> UserRepository : "查询过期用户"
RedisCleanTask --> StringRedisTemplate : "删除缓存"
RedisCleanTask --> UserManageRedisRepositoryHashRedisRepository : "生成缓存Key"
RedisCleanTask --> CacheTimeoutConfig : "间接协同"
RedisCleanTask --> ExecutorService : "并行处理"
```

**依赖图来源**  
- [RedisCleanTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/maintain/RedisCleanTask.java)
- [CacheTimeoutConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/CacheTimeoutConfig.java)