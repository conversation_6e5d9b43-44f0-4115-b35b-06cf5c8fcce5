# 新用户开通调度任务

<cite>
**本文档引用文件**  
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java)
- [ScanRetryUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanRetryUser.java)
- [ScanAuthCancelledUserTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/close/ScanAuthCancelledUserTask.java)
- [RedisCleanTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/maintain/RedisCleanTask.java)
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java)
- [NewUserTaskConfig.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/config/NewUserTaskConfig.java)
- [NewUserPlatformHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/NewUserPlatformHandleService.java)
</cite>

## 目录
1. [简介](#简介)
2. [核心调度任务](#核心调度任务)
3. [任务实现逻辑](#任务实现逻辑)
4. [任务调度与依赖关系](#任务调度与依赖关系)
5. [状态机与数据库交互](#状态机与数据库交互)
6. [错误处理与日志记录](#错误处理与日志记录)
7. [监控与告警建议](#监控与告警建议)
8. [总结](#总结)

## 简介
本文档详细阐述了新用户开通流程中的核心调度任务，包括`ScanWaitOpenUser`、`ScanRetryUser`、`ScanAuthCancelledUserTask`和`RedisCleanTask`。这些任务协同工作，实现了新用户注册的自动化处理、失败重试、授权清理和缓存维护。文档重点分析了各任务的执行逻辑、调度策略、状态转换机制以及与`NewUserPlatformHandleService`的交互方式，为系统的稳定运行提供技术指导。

## 核心调度任务

### ScanWaitOpenUser任务
该任务负责扫描处于“待开通”状态的新用户，并触发开通流程。它通过查询`ay_business_open_user`表中状态为`WAIT_OPEN`的记录，将用户状态更新为`OPENING`，并将其发送至RocketMQ消息队列，由`OpenUserConsumer`进行后续处理。

**任务来源**
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java#L25-L92)
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L100-L140)

### ScanRetryUser任务
该任务负责处理之前开通失败的用户。它扫描状态为`WAIT_RETRY`的用户，根据配置的重试策略进行再次尝试。当重试次数超过阈值时，用户状态将被标记为`FAILED`。

**任务来源**
- [ScanRetryUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanRetryUser.java#L24-L89)
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L142-L180)

### ScanAuthCancelledUserTask任务
该任务用于清理已取消授权的用户数据。目前主要针对抖店（Doudian）平台，通过调用平台API检查授权状态，若发现用户已取消授权，则将其`pullStatus`置为`-101`，以停止后续的数据拉取。

**任务来源**
- [ScanAuthCancelledUserTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/close/ScanAuthCancelledUserTask.java#L32-L110)

### RedisClean任务
该任务负责维护Redis缓存的健康状态。它定期清理超过两个月未活跃的用户缓存，以释放内存资源。任务会并行处理多个平台和应用的缓存数据。

**任务来源**
- [RedisCleanTask.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/maintain/RedisCleanTask.java#L33-L398)

## 任务实现逻辑

### 数据库查询条件
各任务通过`UserProductionInfoExtRepository`和`AyBusinessOpenUserRepository`进行数据库查询，主要条件如下：
- `ScanWaitOpenUser`: 查询`ay_business_open_user`表中`status = WAIT_OPEN`的记录。
- `ScanRetryUser`: 查询`ay_business_open_user`表中`status = WAIT_RETRY`且`retry_count < 阈值`的记录。
- `ScanAuthCancelledUserTask`: 查询`user_product_info_business_ext`表中`pull_status = DONE`且`platform_id = DOUDIAN`的记录。
- `RedisCleanTask`: 查询`user_product_info`表中`order_cycle_end`、`lastupdatetime`或`w1_deadline`任一字段存在且早于两个月前的记录。

### 状态机转换
系统通过状态码管理用户生命周期：
- `WAIT_OPEN` (待开通) → `OPENING` (开通中) → `SUCCESS` (成功) / `WAIT_RETRY` (待重试)
- `WAIT_RETRY` (待重试) → `OPENING` (开通中) → `FAILED` (失败，超过重试次数)
- `pullStatus = DONE` (正常) → `pullStatus = -101` (已取消授权)

### 与NewUserPlatformHandleService的交互
`NewUserPlatformHandleService`是一个接口，定义了各平台在开通流程中的特殊处理逻辑。具体实现类（如`TaoNewUserPlatformHandleServiceImpl`）会根据业务ID、平台ID和应用名，决定是否需要拉取数据、订阅消息、校验W1截止日期等。`ScanWaitOpenUser`和`ScanRetryUser`在发送用户到消息队列前，会通过此服务的`send2PullDataQueue`方法进行最终的处理和路由。

**交互来源**
- [NewUserPlatformHandleService.java](file://uac-newusers/uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/platform/NewUserPlatformHandleService.java#L12-L109)

## 任务调度与依赖关系

### 调度频率
各任务的调度频率由配置文件`NewUserTaskConfig`中的`@Scheduled`注解控制：
- `ScanWaitOpenUser`: 每分钟执行一次 (`fixedDelayString = "${uac.newuser.task.scanWaitOpenUser.delay:#{1 * 60 * 1000}}"`)。
- `ScanRetryUser`: 每分钟执行一次 (`fixedDelayString = "${uac.newuser.task.scanRetryUser.delay:#{1 * 60 * 1000}}"`)。
- `ScanAuthCancelledUserTask`: 每五分钟执行一次 (`fixedDelayString = "${uac.newuser.task.scanAuthCancelledUser.delay:#{5 * 60 * 1000}}"`)。
- `RedisCleanTask`: 每天凌晨1点执行一次 (`cron = "${uac.newuser.task.redisClean.cron:0 0 1 * * ?}"`)。

### 执行顺序与依赖
这些任务在逻辑上是独立的，但存在隐含的执行顺序：
1.  `ScanWaitOpenUser`是新用户流程的起点。
2.  若开通失败，用户进入`WAIT_RETRY`状态，由`ScanRetryUser`处理。
3.  `ScanAuthCancelledUserTask`和`RedisCleanTask`是后台维护任务，不直接参与开通流程，但会影响用户状态和系统性能。

所有任务的开关和参数均通过`NewUserTaskConfig`类进行集中管理。

**配置来源**
- [NewUserTaskConfig.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/config/NewUserTaskConfig.java#L1-L77)

## 状态机与数据库交互
任务通过`AyBusinessOpenUserRepository`和`UserProductionInfoExtRepository`与数据库交互，实现状态持久化。关键操作包括：
- `queryByStatus`: 分页查询指定状态的用户。
- `updateByStatus`: 更新用户状态。
- `batchUpdatePullStatusForSellerId`: 批量更新用户的`pullStatus`。
- `queryByW1DeadlineBeforeWithPage`: 分页查询过期的用户数据用于清理。

这些DAO操作确保了状态变更的原子性和数据一致性。

**数据库交互来源**
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java)

## 错误处理与日志记录
系统采用完善的错误处理机制：
- **异常捕获**: 所有任务的核心逻辑都包裹在`try-catch`块中，防止单个用户处理失败导致整个任务中断。
- **状态回滚**: 在发送消息队列失败时，`ScanWaitOpenUser`会将用户状态从`OPENING`回滚到`WAIT_OPEN`，确保任务可重试。
- **日志记录**: 使用`LoggerHelper`记录详细的日志信息，包括任务开始/结束、处理的用户数量、错误堆栈等，便于问题追踪和审计。

## 监控与告警建议
为确保任务的稳定运行，建议配置以下监控和告警：
1.  **任务执行频率监控**: 监控各任务的`@Scheduled`执行日志，确保其按预期频率运行。若长时间无日志输出，应触发告警。
2.  **消息队列积压监控**: 监控`newuser`和`pull_data`等核心消息队列的积压情况。若消息积压超过阈值，说明下游处理能力不足，需告警。
3.  **错误日志监控**: 对`LOGGER.logError`的日志进行监控，特别是`发送消息失败`、`数据库更新失败`等关键词，出现频率过高时应立即告警。
4.  **线程池监控**: 监控`businessExecutorService`等线程池的状态，关注队列长度和拒绝任务数，防止因线程池满导致任务丢失。
5.  **业务指标监控**: 统计每日成功开通、失败、重试的用户数量，形成趋势图，异常波动时发出告警。

## 总结
本文档全面解析了新用户开通调度任务的自动化流程。`ScanWaitOpenUser`和`ScanRetryUser`构成了开通流程的核心，而`ScanAuthCancelledUserTask`和`RedisCleanTask`则保障了系统的健壮性和资源效率。通过清晰的状态机、合理的调度策略和完善的错误处理，该系统能够高效、稳定地处理新用户注册，为业务发展提供坚实的技术支撑。