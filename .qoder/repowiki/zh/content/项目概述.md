# 项目概述

<cite>
**本文档引用文件**  
- [README.md](file://README.md)
- [pom.xml](file://pom.xml)
- [uac-api/pom.xml](file://uac-api/pom.xml)
- [uac-common/pom.xml](file://uac-common/pom.xml)
- [uac-db-common/pom.xml](file://uac-db-common/pom.xml)
- [uac-service/pom.xml](file://uac-service/pom.xml)
- [uac-service-common/pom.xml](file://uac-service-common/pom.xml)
- [uac-newusers/pom.xml](file://uac-newusers/pom.xml)
- [uac-newusers/uac-newuser-common/pom.xml](file://uac-newusers/uac-newuser-common/pom.xml)
- [uac-newusers/uac-newuser-scheduler/pom.xml](file://uac-newusers/uac-newuser-scheduler/pom.xml)
</cite>

## 目录
1. [项目简介](#项目简介)
2. [项目结构](#项目结构)
3. [核心模块职责](#核心模块职责)
4. [系统架构层次](#系统架构层次)
5. [服务通信机制](#服务通信机制)
6. [缓存与配置管理](#缓存与配置管理)
7. [电商平台集成能力](#电商平台集成能力)
8. [总结](#总结)

## 项目简介

爱用UAC（User Access Control）用户中心服务组是爱用科技的核心用户管理系统，承担着多平台用户统一管理、认证授权、账户生命周期管理等关键职能。该项目作为企业级微服务架构的代表，基于Spring Boot框架构建，采用Maven多模块组织方式，支持高并发、高可用的分布式场景。

系统通过标准化接口定义、分层架构设计和模块化组件复用，实现了对淘宝、京东、拼多多、抖音等多个电商平台的无缝集成，为上层业务系统提供稳定可靠的用户数据服务与身份认证能力。

**Section sources**
- [README.md](file://README.md#L1-L85)

## 项目结构

本项目采用Maven多模块架构，整体结构清晰，职责分明。各模块按功能划分，形成高内聚、低耦合的服务体系。

```mermaid
graph TD
A[uac-service-group] --> B[uac-api]
A --> C[uac-newuser-api]
A --> D[uac-common]
A --> E[uac-service]
A --> F[uac-job]
A --> G[uac-db-common]
A --> H[uac-service-common]
A --> I[uac-newusers]
A --> J[uac-domain]
I --> K[uac-newuser-common]
I --> L[uac-newuser-scheduler]
I --> M[uac-newuser-service]
F --> N[uac-authorization-job]
style A fill:#f9f,stroke:#333
style B fill:#bbf,stroke:#333
style C fill:#bbf,stroke:#333
style D fill:#bbf,stroke:#333
style E fill:#bbf,stroke:#333
style F fill:#bbf,stroke:#333
style G fill:#bbf,stroke:#333
style H fill:#bbf,stroke:#333
style I fill:#bbf,stroke:#333
style J fill:#bbf,stroke:#333
```

**Diagram sources**
- [README.md](file://README.md#L10-L35)
- [pom.xml](file://pom.xml#L15-L32)

**Section sources**
- [README.md](file://README.md#L10-L35)
- [pom.xml](file://pom.xml#L1-L55)

## 核心模块职责

各核心模块在系统中承担不同的职责，形成完整的用户中心服务体系。

### uac-api（接口定义模块）
定义了对外暴露的二方API接口，包含请求、响应、实体、异常等基础数据结构，供其他服务依赖调用。该模块不包含业务逻辑，仅作为契约层存在。

### uac-common（公共组件模块）
提供跨模块复用的通用功能组件，包括平台配置、消息处理、工具类、Redis操作封装、RocketMQ生产者配置等，是整个服务组的基础支撑层。

### uac-db-common（数据访问模块）
封装数据库访问逻辑，包含MyBatis DAO接口、XML映射文件、实体类、Repository实现及平台相关的用户产品信息处理服务，负责与MySQL数据库交互。

### uac-service（主服务模块）
核心Web服务模块，提供HTTP接口入口，处理用户认证、账户管理、回调通知等核心业务逻辑，集成各底层模块完成实际业务流程。

### uac-service-common（服务通用模块）
提供服务间共享的业务服务实现，如OAuth处理、运营服务、卖家服务、活动服务等，被uac-service及其他模块引用。

### uac-newusers（新用户管理模块）
专门处理新用户注册、开通、迁移等全生命周期管理任务，包含通用逻辑、定时调度和服务接口三部分。

### uac-job（定时任务模块）
执行周期性后台任务，如用户授权Token刷新、异常用户扫描、Redis清理等，保障系统长期稳定运行。

**Section sources**
- [README.md](file://README.md#L37-L85)
- [pom.xml](file://pom.xml#L15-L32)

## 系统架构层次

系统采用典型的分层架构设计，各层之间通过明确的依赖关系进行解耦。

```mermaid
graph BT
subgraph "表现层"
A[uac-service]
end
subgraph "服务通用层"
B[uac-service-common]
end
subgraph "接口定义层"
C[uac-api]
end
subgraph "公共组件层"
D[uac-common]
end
subgraph "数据访问层"
E[uac-db-common]
end
subgraph "领域模型层"
F[uac-domain]
end
A --> B
A --> D
A --> E
B --> C
B --> D
B --> E
D --> C
D --> F
E --> D
E --> F
style A fill:#f96,stroke:#333
style B fill:#69f,stroke:#333
style C fill:#6f9,stroke:#333
style D fill:#9f6,stroke:#333
style E fill:#ff6,stroke:#333
style F fill:#6ff,stroke:#333
```

**Diagram sources**
- [uac-api/pom.xml](file://uac-api/pom.xml#L1-L74)
- [uac-common/pom.xml](file://uac-common/pom.xml#L1-L39)
- [uac-db-common/pom.xml](file://uac-db-common/pom.xml#L1-L27)
- [uac-service/pom.xml](file://uac-service/pom.xml#L1-L51)
- [uac-service-common/pom.xml](file://uac-service-common/pom.xml#L1-L33)

**Section sources**
- [uac-api/pom.xml](file://uac-api/pom.xml#L1-L74)
- [uac-common/pom.xml](file://uac-common/pom.xml#L1-L39)
- [uac-db-common/pom.xml](file://uac-db-common/pom.xml#L1-L27)
- [uac-service/pom.xml](file://uac-service/pom.xml#L1-L51)
- [uac-service-common/pom.xml](file://uac-service-common/pom.xml#L1-L33)

## 服务通信机制

系统内部通过多种方式实现服务间通信，确保松耦合与高性能。

### RPC调用
通过`uac-api`模块定义的接口，使用Spring Cloud OpenFeign或内部RPC机制实现服务间远程调用。例如`UserCenterInnerApiService`用于内部服务间用户信息查询。

### 消息驱动（RocketMQ）
采用事件驱动架构，通过RocketMQ实现异步解耦。关键业务事件如用户变更、授权回调等通过消息队列通知下游系统，提升系统响应速度与可靠性。

### 定时任务调度
通过`uac-job`和`uac-newuser-scheduler`模块执行定时任务，如Token自动刷新、异常用户重试开通等，保证后台任务有序执行。

**Section sources**
- [uac-common/pom.xml](file://uac-common/pom.xml#L20-L22)
- [uac-newusers/uac-newuser-common/pom.xml](file://uac-newusers/uac-newuser-common/pom.xml#L30-L31)
- [README.md](file://README.md#L70-L85)

## 缓存与配置管理

### 缓存策略（Redis）
系统广泛使用Redis作为缓存层，提升访问性能：
- 用户基本信息缓存
- 授权Token缓存
- 配置信息缓存
- 会话状态缓存
通过`uac-common`中的`RedisRepository`抽象封装，统一管理Redis读写操作，并结合Caffeine本地缓存实现多级缓存架构。

### 配置管理（Apollo）
所有服务均接入Apollo配置中心，实现配置动态化：
- 数据库连接信息
- 第三方平台AppKey/AppSecret
- 业务开关控制
- 缓存超时时间
- 消息队列Topic配置
支持不同环境（dev/prod）差异化配置，无需重启服务即可生效。

**Section sources**
- [uac-common/pom.xml](file://uac-common/pom.xml#L18-L19)
- [uac-common/src/main/java/cn/loveapp/uac/common/config/redis](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis)
- [README.md](file://README.md#L40-L45)

## 电商平台集成能力

系统具备强大的多平台集成能力，支持主流电商平台的用户接入与管理。

### 支持平台
- **淘宝/天猫**：通过`TaoAuthServiceImpl`处理OAuth授权
- **京东**：`JdAuthServiceImpl`实现京东ERP对接
- **拼多多**：`PddAuthServiceImpl`支持PDD多店铺授权
- **抖音/抖店**：`DoudianAuthServiceImpl`处理抖音开放平台认证
- **快手小店**：`KwaishopAuthServiceImpl`集成快手生态
- **微信视频号**：`WxvideoshopAuthServiceImpl`支持视频号电商
- **有赞**、**1688**、**爱用科技自有平台**等

### 集成方式
各平台通过统一的`AuthService`接口实现标准化接入，遵循OAuth 2.0协议完成用户授权流程，并将授权信息持久化存储于数据库，后续通过Token进行API调用。

平台特有逻辑通过策略模式分治，新增平台只需实现对应`AuthServiceImpl`即可快速接入。

**Section sources**
- [uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl)
- [README.md](file://README.md#L1-L85)

## 总结

爱用UAC用户中心服务组是一个高度模块化、可扩展的企业级用户管理系统。其采用Spring Boot微服务架构，通过Maven多模块组织代码，实现了清晰的职责划分与良好的可维护性。

系统以`uac-api`为契约，`uac-common`为公共基础，`uac-db-common`为数据支撑，`uac-service`为核心服务，构建了完整的用户管理体系。同时通过RocketMQ事件驱动、Redis多级缓存、Apollo动态配置等技术手段，保障了系统的高性能与高可用。

对于初学者而言，该项目提供了清晰的模块边界与分层结构；对于高级开发者，则展现了复杂业务场景下的架构设计智慧与工程实践能力。