# 消息队列

<cite>
**本文档引用的文件**
- [RocketMqQueueHelper.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/RocketMqQueueHelper.java)
- [BaseOnsConsumer.java](file://uac-common/src/main/java/cn/loveapp/uac/common/consumer/BaseOnsConsumer.java)
- [AiyongMessageExt.java](file://uac-common/src/main/java/cn/loveapp/uac/common/consumer/AiyongMessageExt.java)
- [RocketMQAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/RocketMQAppConfig.java)
- [RocketMQDefaultProducerConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/RocketMQDefaultProducerConfig.java)
- [BaseRocketMQDefaultConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/BaseRocketMQDefaultConfig.java)
- [SubscribeUserMessageEventHandlerImpl.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/event/SubscribeUserMessageEventHandlerImpl.java)
- [UserInfoChangedConfig.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/config/UserInfoChangedConfig.java)
- [UserChangedRequestProto.java](file://uac-api/src/main/java/cn/loveapp/uac/proto/UserChangedRequestProto.java)
- [SubscribeUserMessageRequestProto.java](file://uac-api/src/main/java/cn/loveapp/uac/proto/SubscribeUserMessageRequestProto.java)
- [MqUserPropertyConstant.java](file://uac-common/src/main/java/cn/loveapp/uac/common/constant/MqUserPropertyConstant.java)
- [NewuserConsumerConfig.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/config/NewuserConsumerConfig.java)
</cite>

## 目录
1. [简介](#简介)
2. [核心消息主题与标签](#核心消息主题与标签)
3. [消息生产者配置与使用](#消息生产者配置与使用)
4. [消息消费者实现模式](#消息消费者实现模式)
5. [消息序列化格式](#消息序列化格式)
6. [消息重试机制与死信队列处理](#消息重试机制与死信队列处理)
7. [消费幂等性保证](#消费幂等性保证)
8. [监控消息积压与消费延迟的最佳实践](#监控消息积压与消费延迟的最佳实践)

## 简介
本系统通过集成RocketMQ消息队列，实现了关键业务场景的异步解耦与高效处理。消息队列的核心作用在于解耦用户信息变更通知和异步处理耗时操作。当用户信息发生变更时，系统会将事件发布到消息队列，由下游服务异步消费并执行相应的业务逻辑，从而避免了同步调用带来的性能瓶颈和系统耦合。此外，对于数据拉取、用户开通等耗时操作，系统也通过消息队列进行异步化处理，显著提升了用户体验和系统吞吐量。

## 核心消息主题与标签
系统定义了多个核心消息主题（Topic）和标签（Tag）来分类和路由不同的业务事件。

- **用户变更事件主题**：用于发布用户信息变更的通知。在`UserInfoChangedConfig`配置类中，通过`uac.event.subscribe`前缀定义了消息转发配置，其中`topic`字段指定了目标消息主题，`tag`字段指定了目标消息标签。
- **通用订阅消息主题**：用于发布OAuth授权、身份变更等通用订阅消息。在`SubscribeUserMessageEventHandlerImpl`中，通过`uac.event.subscribe.topics`配置项获取需要订阅的主题列表。

**Section sources**
- [UserInfoChangedConfig.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/config/UserInfoChangedConfig.java#L1-L46)
- [SubscribeUserMessageEventHandlerImpl.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/event/SubscribeUserMessageEventHandlerImpl.java#L1-L92)

## 消息生产者配置与使用
消息生产者的配置与使用主要通过`RocketMqQueueHelper`和`RocketMQDefaultProducerConfig`类来实现。

`RocketMQDefaultProducerConfig`继承自`BaseRocketMQDefaultConfig`，定义了生产者的核心配置，包括`topic`、`tag`、`producerId`等。`RocketMQAppConfig`则负责配置RocketMQ的`namesrvAddr`，即Name Server的地址。

`RocketMqQueueHelper`是消息发送的核心工具类，提供了多个`push`方法的重载。生产者通过调用`push`方法将消息发送到指定的Topic和Tag。该方法支持配置重试次数（`retryCount`）、延迟等级（`delayTimeLevel`）以及用户自定义属性（`userProperties`）。消息发送过程包含重试逻辑，若发送失败，会根据配置的重试次数进行重试，每次重试间隔会逐渐增加。

```mermaid
sequenceDiagram
participant 业务逻辑 as 业务逻辑
participant Helper as RocketMqQueueHelper
participant Producer as DefaultMQProducer
participant Broker as RocketMQ Broker
业务逻辑->>Helper : push(topic, tag, data, producer, retryCount)
Helper->>Helper : checkPushData(data)
Helper->>Helper : 创建Message对象
loop 重试循环
Helper->>Producer : send(Message)
Producer->>Broker : 发送消息
alt 发送成功
Broker-->>Producer : SendResult
Producer-->>Helper : 返回MessageId
break 成功退出循环
else 发送失败且未达重试上限
Producer-->>Helper : 抛出异常
Helper->>Helper : 等待(200 * retry)毫秒
else 发送失败且已达重试上限
Producer-->>Helper : 抛出异常
Helper->>Helper : 记录错误日志
break 退出循环
end
end
Helper-->>业务逻辑 : 返回MessageId或null
```

**Diagram sources**
- [RocketMqQueueHelper.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/RocketMqQueueHelper.java#L27-L167)
- [RocketMQDefaultProducerConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/RocketMQDefaultProducerConfig.java#L1-L26)
- [RocketMQAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/RocketMQAppConfig.java#L1-L18)

**Section sources**
- [RocketMqQueueHelper.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/RocketMqQueueHelper.java#L1-L167)
- [RocketMQDefaultProducerConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/RocketMQDefaultProducerConfig.java#L1-L26)
- [RocketMQAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/RocketMQAppConfig.java#L1-L18)

## 消息消费者实现模式
消息消费者的实现基于`BaseOnsConsumer`抽象类。所有具体的消费者类都需要继承此类，并实现`execute`抽象方法来定义具体的业务处理逻辑。

`BaseOnsConsumer`实现了`MessageListenerConcurrently`接口，采用并发消费模式。其核心流程如下：首先将接收到的`MessageExt`对象转换为内部的`AiyongMessageExt`对象，然后记录日志并调用`rateLimiterAcquire()`进行限流控制，最后在`execute`方法中执行具体的业务逻辑。该基类还提供了异常处理机制，能够捕获`ResendMessageException`并根据需要将消息重新发送回队列。

```mermaid
classDiagram
class BaseOnsConsumer {
<<abstract>>
+static volatile boolean stop
+RateLimiter rateLimiter
-Environment environment
-RocketMqQueueHelper rocketMqQueueHelper
-DefaultMQProducer defaultMQProducer
+BaseOnsConsumer(registry, environment, timerName)
+configChangeLisenner(changeEvent)
+getRateLimiter()
+rateLimiterAcquire()
+consumeMessage(msgs, context) ConsumeConcurrentlyStatus
<<abstract>> +execute(aiyongMessageExt) void
+postExecute() void
+stop() void
-convertAiyongMessageExtFromRocketMQ(message) AiyongMessageExt
}
class AiyongMessageExt {
+String content
+String topic
+String tag
+String key
+Map~String, String~ userProperties
+String msgId
+String transactionId
+AiyongMessageExt()
+AiyongMessageExt(content, forceHandleFlag)
+getUserProperty(key) String
+setUserProperty(key, value) void
+setForceHandleFlag(forceHandleFlag) void
+getForceHandleFlag() boolean
+getResendTimes() int
}
class NewUserTaskServiceImpl {
+actionUserFixLongWaitingProcess(userProductInfoBusinessExts, businessId) void
+scanUserFixLongWaiting(platformId, businessId, appName) void
+scanUserFailedLongWaiting(platformId, businessId, appName) void
}
BaseOnsConsumer <|-- NewUserTaskServiceImpl : 实现execute方法
BaseOnsConsumer --> AiyongMessageExt : 转换消息
```

**Diagram sources**
- [BaseOnsConsumer.java](file://uac-common/src/main/java/cn/loveapp/uac/common/consumer/BaseOnsConsumer.java#L1-L229)
- [AiyongMessageExt.java](file://uac-common/src/main/java/cn/loveapp/uac/common/consumer/AiyongMessageExt.java#L1-L91)
- [NewUserTaskServiceImpl.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/service/impl/NewUserTaskServiceImpl.java#L1-L454)

**Section sources**
- [BaseOnsConsumer.java](file://uac-common/src/main/java/cn/loveapp/uac/common/consumer/BaseOnsConsumer.java#L1-L229)
- [AiyongMessageExt.java](file://uac-common/src/main/java/cn/loveapp/uac/common/consumer/AiyongMessageExt.java#L1-L91)

## 消息序列化格式
系统中的消息体（Message Body）以二进制形式存储，其序列化和反序列化方式由生产者和消费者协商确定。根据代码分析，消息内容主要采用JSON格式进行序列化。

在`RocketMqQueueHelper`的`checkPushData`方法中，如果传入的`pushData`不是字符串类型，则会使用`JSON.toJSONString(pushData)`将其转换为JSON字符串。这意味着大多数Java对象（如`UserChangedRequestProto`、`SubscribeUserMessageRequestProto`）在发送前都会被序列化为JSON字符串，然后编码为UTF-8字节数组存入消息体。消费者在接收到消息后，需要根据消息的`topic`和`tag`来决定如何反序列化`content`字段。

**Section sources**
- [RocketMqQueueHelper.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/RocketMqQueueHelper.java#L80-L85)
- [UserChangedRequestProto.java](file://uac-api/src/main/java/cn/loveapp/uac/proto/UserChangedRequestProto.java#L1-L15)
- [SubscribeUserMessageRequestProto.java](file://uac-api/src/main/java/cn/loveapp/uac/proto/SubscribeUserMessageRequestProto.java#L1-L18)

## 消息重试机制与死信队列处理
系统实现了两层重试机制：消息队列本身的重试和业务逻辑的重试。

1.  **生产者重试**：`RocketMqQueueHelper.push`方法内置了发送重试逻辑。当网络抖动或Broker暂时不可用导致发送失败时，方法会根据`retryCount`参数进行重试，每次重试间隔为`200 * retry`毫秒。

2.  **消费者重试**：在`BaseOnsConsumer.consumeMessage`方法中，如果`execute`方法抛出`ResendMessageException`，则会捕获该异常，并将消息重新发送回原队列（或指定的延迟队列），同时在`userProperties`中增加`resendTimes`计数。这允许业务逻辑根据重试次数采取不同的处理策略（例如，前几次重试等待资源恢复，超过一定次数后告警或丢弃）。如果抛出的是其他`Exception`，则返回`ConsumeConcurrentlyStatus.RECONSUME_LATER`，由RocketMQ框架进行重试。

3.  **死信队列处理**：虽然代码中未直接体现死信队列（DLQ）的创建和消费，但RocketMQ本身支持死信队列。当一条消息被消费超过最大重试次数（默认16次）后，RocketMQ会自动将其转移到死信队列（%DLQ%ConsumerGroup）。系统需要配置专门的消费者来监听死信队列，对这些“死信”进行人工干预、分析原因或持久化存储，防止消息丢失。

```mermaid
flowchart TD
A[消息消费] --> B{发生异常?}
B --> |否| C[返回CONSUME_SUCCESS]
B --> |是| D{异常类型}
D --> |ResendMessageException| E[增加resendTimes<br>重新发送回原队列]
D --> |其他Exception| F[返回RECONSUME_LATER<br>由MQ框架重试]
E --> G[消息被重新消费]
F --> G
G --> H{超过最大重试次数?}
H --> |否| G
H --> |是| I[消息进入死信队列%DLQ%]
I --> J[人工处理或专用消费者处理]
```

**Diagram sources**
- [RocketMqQueueHelper.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/RocketMqQueueHelper.java#L105-L138)
- [BaseOnsConsumer.java](file://uac-common/src/main/java/cn/loveapp/uac/common/consumer/BaseOnsConsumer.java#L116-L149)
- [MqUserPropertyConstant.java](file://uac-common/src/main/java/cn/loveapp/uac/common/constant/MqUserPropertyConstant.java#L1-L17)

**Section sources**
- [RocketMqQueueHelper.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/RocketMqQueueHelper.java#L1-L167)
- [BaseOnsConsumer.java](file://uac-common/src/main/java/cn/loveapp/uac/common/consumer/BaseOnsConsumer.java#L1-L229)
- [MqUserPropertyConstant.java](file://uac-common/src/main/java/cn/loveapp/uac/common/constant/MqUserPropertyConstant.java#L1-L17)

## 消费幂等性保证
由于消息重试机制的存在，同一消息可能会被多次投递给消费者，因此消费端必须实现幂等性，确保多次处理同一条消息不会产生副作用。

本系统通过以下方式支持幂等性：
1.  **消息ID（msgId）**：每条消息都有唯一的`msgId`，消费者可以将`msgId`作为幂等键（Idempotent Key）存储在数据库或缓存中。在处理消息前，先检查该`msgId`是否已处理过，如果已存在，则直接返回成功，避免重复处理。
2.  **业务主键**：对于用户信息变更等场景，可以使用`sellerId`、`platformId`等业务主键作为幂等键。例如，在处理用户开通消息时，检查该用户是否已经处于开通状态，如果是，则无需再次执行开通逻辑。
3.  **状态机**：业务逻辑应设计为状态机模式，只允许从特定状态转换到目标状态。例如，用户状态只能从“未开通”变为“已开通”，如果收到“已开通”用户的开通消息，则直接忽略。

虽然代码中没有直接的幂等性检查逻辑，但`BaseOnsConsumer`将`msgId`通过`MDC.put("messageId", messageId)`放入了日志上下文，这为排查重复消费问题提供了便利。

**Section sources**
- [BaseOnsConsumer.java](file://uac-common/src/main/java/cn/loveapp/uac/common/consumer/BaseOnsConsumer.java#L130-L132)

## 监控消息积压与消费延迟的最佳实践
监控消息积压和消费延迟是保障消息队列稳定运行的关键。

1.  **利用RocketMQ控制台**：RocketMQ提供了Web控制台，可以直观地查看各个Topic的生产/消费速率、消息积压量（Lag）和消费者组的消费延迟。运维人员应定期检查控制台，及时发现积压问题。

2.  **集成Micrometer监控**：`BaseOnsConsumer`的构造函数接收`MeterRegistry`参数，并创建了`Timer`来记录每次消息处理的耗时。这些指标可以被Prometheus抓取，并在Grafana中进行可视化，从而实现对消费延迟的实时监控和告警。

3.  **日志分析**：`BaseOnsConsumer`在处理消息的开始和结束时都记录了日志，并包含了`costTime`（处理耗时）。可以通过日志分析系统（如ELK）聚合这些日志，统计平均处理时间和P99/P999延迟。

4.  **消费者健康检查**：`BaseOnsConsumer`提供了`stop()`方法和`setStop()`静态方法，可以在应用关闭时优雅地停止消费。同时，`stop`标志位可以用于实现健康检查，当`stop`为`true`时，返回`RECONSUME_LATER`，确保消息不会丢失。

5.  **告警机制**：应设置告警规则，当消息积压量超过阈值或消费延迟超过一定时间（如5分钟）时，通过邮件、短信或钉钉机器人通知相关人员。

**Section sources**
- [BaseOnsConsumer.java](file://uac-common/src/main/java/cn/loveapp/uac/common/consumer/BaseOnsConsumer.java#L65-L73)
- [BaseOnsConsumer.java](file://uac-common/src/main/java/cn/loveapp/uac/common/consumer/BaseOnsConsumer.java#L145-L149)