# 快速开始

<cite>
**本文档中引用的文件**  
- [README.md](file://README.md)
- [UacServiceApplication.java](file://uac-service/src/main/java/cn/loveapp/uac/service/UacServiceApplication.java)
- [application.properties](file://uac-service/src/main/resources/application.properties)
- [RedisConfiguration.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/RedisConfiguration.java)
- [RocketMQAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/RocketMQAppConfig.java)
</cite>

## 目录
1. [简介](#简介)
2. [开发环境准备](#开发环境准备)
3. [项目克隆与导入](#项目克隆与导入)
4. [本地依赖服务配置](#本地依赖服务配置)
5. [Apollo 配置中心设置](#apollo-配置中心设置)
6. [Maven 编译与打包](#maven-编译与打包)
7. [IDE 中导入与运行](#ide-中导入与运行)
8. [API 调用示例](#api-调用示例)
9. [常见问题与解决方案](#常见问题与解决方案)

## 简介

爱用UAC（User Access Control）用户中心服务组是爱用科技的核心用户管理系统，提供统一的用户认证、授权、账户管理及多平台集成能力。本快速入门指南旨在帮助新开发者快速搭建本地开发环境，顺利运行主服务模块。

**Section sources**  
- [README.md](file://README.md#L1-L10)

## 开发环境准备

在开始开发前，请确保已安装以下基础开发工具：

- **JDK**: 推荐使用 JDK 24，兼容 JDK 8+ 版本
- **Maven**: 3.8.0 及以上版本（推荐 3.9.0+）
- **Redis**: 用于缓存用户数据和会话信息
- **RocketMQ**: 消息中间件，用于异步事件处理
- **Apollo**: 分布式配置中心，管理应用配置
- **MySQL**: 存储用户核心数据（需根据实际数据库配置连接）

**Section sources**  
- [README.md](file://README.md#L20-L35)

## 项目克隆与导入

1. 打开终端，执行以下命令克隆项目仓库：
   ```bash
   git clone https://your-repo-url/usercenter-service-group.git
   cd usercenter-service-group
   ```

2. 项目采用 Maven 多模块结构，主要模块包括：
   - `uac-service`: 主服务模块，包含核心业务逻辑
   - `uac-newuser-service`: 新用户服务模块
   - `uac-common`: 通用组件模块
   - `uac-db-common`: 数据库访问通用模块

3. 使用支持 Maven 的 IDE（如 IntelliJ IDEA）打开项目根目录下的 `pom.xml` 文件，自动导入所有子模块。

**Section sources**  
- [README.md](file://README.md#L12-L20)

## 本地依赖服务配置

### Redis 配置

项目通过 `RedisConfiguration.java` 配置多个 Redis 实例，分别用于不同业务场景（如 trade、item、distribute）。本地开发时需启动 Redis 服务，并确保以下配置正确：

```properties
# 示例：在 application-dev.properties 中配置 Redis
spring.data.redis.trade.host=localhost
spring.data.redis.trade.port=6379
spring.data.redis.trade.password=
spring.data.redis.trade.database=0
```

**Section sources**  
- [RedisConfiguration.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/RedisConfiguration.java#L1-L30)

### RocketMQ 配置

RocketMQ 用于处理用户变更、授权等异步事件。本地需启动 NameServer 和 Broker 服务，并在配置文件中指定地址：

```properties
# 在 Apollo 或本地配置文件中设置
rocketmq.default.app.config.namesrvAddr=localhost:9876
```

**Section sources**  
- [RocketMQAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/RocketMQAppConfig.java#L1-L10)

## Apollo 配置中心设置

1. 确保本地已部署 Apollo 配置中心服务。
2. 项目通过 `application.properties` 启用 Apollo：
   ```properties
   loveapp.apollo.enabled=true
   apollo.bootstrap.enabled=true
   app.id=cn.loveapp.uac
   apollo.bootstrap.namespaces=uac-service,application,service-registry
   ```
3. 在 Apollo 中创建对应 `app.id` 的项目，并导入 `uac-service` 命名空间的配置。
4. 配置示例：
   - `spring.datasource.url`: 数据库连接地址
   - `spring.redis.host`: Redis 地址
   - `rocketmq.namesrv.addr`: RocketMQ 地址

**Section sources**  
- [application.properties](file://uac-service/src/main/resources/application.properties#L1-L15)

## Maven 编译与打包

在项目根目录执行以下 Maven 命令：

1. **清理并编译所有模块**：
   ```bash
   mvn clean compile
   ```

2. **编译并运行单元测试**：
   ```bash
   mvn clean test
   ```

3. **打包所有模块（生成 jar 文件）**：
   ```bash
   mvn clean install
   ```

4. **跳过测试打包（加快构建）**：
   ```bash
   mvn clean install -DskipTests
   ```

打包完成后，各模块的 `target/` 目录下将生成对应的 JAR 文件。

**Section sources**  
- [README.md](file://README.md#L20-L35)

## IDE 中导入与运行

1. 使用 IntelliJ IDEA 打开项目根目录。
2. 确保 Maven 自动导入已启用，等待依赖下载完成。
3. 找到主启动类：
   - **主服务**: `cn.loveapp.uac.service.UacServiceApplication`
4. 右键运行该类的 `main` 方法。
5. 应用启动成功后，默认端口为 `8080`，可通过 `http://localhost:8080/actuator/health` 检查健康状态。

**Section sources**  
- [UacServiceApplication.java](file://uac-service/src/main/java/cn/loveapp/uac/service/UacServiceApplication.java#L1-L40)
- [README.md](file://README.md#L60-L65)

## API 调用示例

为验证本地环境是否配置成功，可调用一个简单的用户信息查询接口：

1. 启动 `uac-service` 模块。
2. 使用 `curl` 或 Postman 发送请求：
   ```bash
   curl -X GET "http://localhost:8080/api/user/info?userId=12345"
   ```

3. 预期返回示例：
   ```json
   {
     "code": 0,
     "message": "success",
     "data": {
       "userId": "12345",
       "nickName": "testUser",
       "email": "<EMAIL>"
     }
   }
   ```

若返回正常数据，说明本地环境配置成功。

**Section sources**  
- [README.md](file://README.md#L50-L60)

## 常见问题与解决方案

| 问题现象 | 可能原因 | 解决方案 |
|--------|--------|--------|
| 启动时报 `ClassNotFoundException` | JDK 版本不兼容 | 确认使用 JDK 8+，推荐 JDK 24 |
| Apollo 连接失败 | Apollo 服务未启动或配置错误 | 检查 `app.id` 和 `namesrvAddr` 配置 |
| Redis 连接超时 | Redis 服务未启动或地址配置错误 | 启动 Redis 并检查 `spring.data.redis.*` 配置 |
| RocketMQ 消息发送失败 | NameServer 未启动 | 启动 RocketMQ NameServer 和 Broker |
| 数据库连接失败 | MySQL 服务未启动或 JDBC 配置错误 | 检查数据库连接 URL、用户名和密码 |
| 接口返回 404 | 应用未正确启动或端口被占用 | 检查日志确认服务已启动，查看 `logs/info.log` |

**Section sources**  
- [README.md](file://README.md#L30-L85)
- [application.properties](file://uac-service/src/main/resources/application.properties#L1-L15)