# 技术栈与依赖

<cite>
**本文档引用的文件**  
- [pom.xml](file://pom.xml)
- [uac-api/pom.xml](file://uac-api/pom.xml)
- [uac-common/pom.xml](file://uac-common/pom.xml)
- [uac-db-common/pom.xml](file://uac-db-common/pom.xml)
- [uac-service/pom.xml](file://uac-service/pom.xml)
- [uac-service-common/pom.xml](file://uac-service-common/pom.xml)
- [uac-job/uac-authorization-job/pom.xml](file://uac-job/uac-authorization-job/pom.xml)
- [uac-common/src/main/java/cn/loveapp/uac/common/config/redis/RedisConfiguration.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/RedisConfiguration.java)
- [uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/ValueRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/ValueRedisRepository.java)
- [uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/RocketMQDefaultProducerConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/RocketMQDefaultProducerConfig.java)
- [uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/RocketMQAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/RocketMQAppConfig.java)
- [uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/BaseRocketMQDefaultConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/BaseRocketMQDefaultConfig.java)
- [uac-common/src/main/java/cn/loveapp/uac/common/utils/RocketMqQueueHelper.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/RocketMqQueueHelper.java)
</cite>

## 目录
1. [项目结构](#项目结构)
2. [核心技术栈](#核心技术栈)
3. [核心依赖库](#核心依赖库)
4. [模块依赖关系](#模块依赖关系)
5. [配置中心与分布式缓存](#配置中心与分布式缓存)
6. [异步消息处理](#异步消息处理)
7. [数据库持久化](#数据库持久化)

## 项目结构

usercenter-service-group 项目采用多模块 Maven 架构，包含多个子模块，职责清晰，模块化程度高。主要模块包括：

- **uac-api**：提供对外接口定义、DTO、请求响应模型等。
- **uac-common**：通用工具类、配置、消息队列、缓存等共享组件。
- **uac-db-common**：数据库访问层，包含 MyBatis DAO 接口与实体类。
- **uac-service**：Web 接口实现模块，处理 HTTP 请求。
- **uac-service-common**：服务层通用逻辑，如业务处理、平台适配等。
- **uac-job**：定时任务模块，如授权令牌刷新任务。
- **uac-newusers**：新用户相关业务处理模块。
- **uac-domain**：领域模型与协议定义。

**Section sources**
- [pom.xml](file://pom.xml)
- [uac-api/pom.xml](file://uac-api/pom.xml)
- [uac-common/pom.xml](file://uac-common/pom.xml)
- [uac-db-common/pom.xml](file://uac-db-common/pom.xml)
- [uac-service/pom.xml](file://uac-service/pom.xml)
- [uac-service-common/pom.xml](file://uac-service-common/pom.xml)
- [uac-job/uac-authorization-job/pom.xml](file://uac-job/uac-authorization-job/pom.xml)

## 核心技术栈

本项目基于 Spring Boot 构建，采用微服务架构风格，结合多种中间件实现高可用、高性能的用户中心服务。核心技术栈如下：

- **Spring Boot**：作为基础框架，提供自动配置、内嵌容器、健康检查等能力，简化开发与部署。
- **MyBatis**：用于数据库持久化操作，通过 XML 映射文件实现 SQL 与 Java 对象的映射。
- **Redis**：使用 Lettuce 客户端实现分布式缓存，支持多 Redis 实例配置（如 trade、item、distribute 等）。
- **RocketMQ**：用于异步消息处理与事件通知，支持高吞吐、高可靠的消息发布与订阅。
- **Apollo**：作为配置中心（虽未在代码中直接体现，但通过 `@ConfigurationProperties` 支持外部化配置，可与 Apollo 集成实现动态配置管理）。

**Section sources**
- [pom.xml](file://pom.xml)
- [uac-common/pom.xml](file://uac-common/pom.xml)
- [uac-db-common/pom.xml](file://uac-db-common/pom.xml)

## 核心依赖库

项目引入多个核心依赖库以提升开发效率与系统性能：

- **Lombok**：通过注解自动生成 getter、setter、toString 等方法，简化 Java 代码。
- **Fastjson**：用于 JSON 序列化与反序列化，广泛应用于接口请求/响应数据转换。
- **HikariCP**：作为数据库连接池（由 `mybatis-spring-boot-starter` 间接引入），提供高性能、低延迟的数据库连接管理。
- **Jakarta Validation & Hibernate Validator**：用于请求参数校验，确保输入数据合法性。
- **Swagger Annotations**：支持 API 文档生成，便于接口调试与文档维护。

**Section sources**
- [uac-api/pom.xml](file://uac-api/pom.xml)
- [uac-newuser-api/pom.xml](file://uac-newuser-api/pom.xml)

## 模块依赖关系

项目采用清晰的模块依赖结构，确保职责分离与可维护性。关键依赖关系如下：

```mermaid
graph TD
uac_api[uac-api] --> uac_common[uac-common]
uac_common --> uac_domain[uac-domain]
uac_db_common[uac-db-common] --> uac_common
uac_service[uac-service] --> uac_api
uac_service --> uac_common
uac_service --> uac_db_common
uac_service --> uac_service_common[uac-service-common]
uac_job[uac-authorization-job] --> uac_common
uac_job --> uac_db_common
uac_job --> uac_service_common
```

**Diagram sources**
- [pom.xml](file://pom.xml)
- [uac-api/pom.xml](file://uac-api/pom.xml)
- [uac-common/pom.xml](file://uac-common/pom.xml)
- [uac-db-common/pom.xml](file://uac-db-common/pom.xml)
- [uac-service/pom.xml](file://uac-service/pom.xml)
- [uac-service-common/pom.xml](file://uac-service-common/pom.xml)
- [uac-job/uac-authorization-job/pom.xml](file://uac-job/uac-authorization-job/pom.xml)

**Section sources**
- [pom.xml](file://pom.xml)
- [uac-api/pom.xml](file://uac-api/pom.xml)
- [uac-common/pom.xml](file://uac-common/pom.xml)
- [uac-db-common/pom.xml](file://uac-db-common/pom.xml)
- [uac-service/pom.xml](file://uac-service/pom.xml)
- [uac-service-common/pom.xml](file://uac-service-common/pom.xml)
- [uac-job/uac-authorization-job/pom.xml](file://uac-job/uac-authorization-job/pom.xml)

## 配置中心与分布式缓存

### Redis 缓存实现

项目通过 `RedisConfiguration` 类配置多个 Redis 连接工厂，支持不同业务场景的缓存隔离（如 trade、item、distribute）。使用 Lettuce 作为 Redis 客户端，支持连接池配置，提升并发性能。

缓存操作通过 `ValueRedisRepository` 和 `HashCacheRepository` 接口封装，提供统一的增删改查方法，支持设置超时时间与条件写入。

### 配置管理

通过 `@ConfigurationProperties` 注解绑定配置项，支持从外部配置中心（如 Apollo）动态加载 Redis、RocketMQ 等中间件配置，实现配置热更新。

**Section sources**
- [uac-common/src/main/java/cn/loveapp/uac/common/config/redis/RedisConfiguration.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/RedisConfiguration.java)
- [uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/ValueRedisRepository.java](file://uac-common/src/main/java/cn/loveapp/uac/common/dao/redis/ValueRedisRepository.java)

## 异步消息处理

### RocketMQ 集成

项目使用 RocketMQ 实现异步消息处理，核心配置类包括：

- `RocketMQAppConfig`：配置 NameServer 地址。
- `RocketMQDefaultProducerConfig`：定义生产者默认配置，如 Topic、Tag、Producer ID 等。
- `BaseRocketMQDefaultConfig`：抽象基类，统一管理生产者与消费者配置。

消息发送通过 `RocketMqQueueHelper` 工具类封装，支持重试机制、延迟消息、用户自定义属性等功能。

### 用户变更事件通知

通过 `UserChangedEvent` 和 `UserChangedRequestProto` 定义用户变更事件结构，当用户信息更新时，服务通过 RocketMQ 发布事件，下游系统订阅并处理，实现解耦与异步化。

**Section sources**
- [uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/RocketMQDefaultProducerConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/RocketMQDefaultProducerConfig.java)
- [uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/RocketMQAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/RocketMQAppConfig.java)
- [uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/BaseRocketMQDefaultConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/BaseRocketMQDefaultConfig.java)
- [uac-common/src/main/java/cn/loveapp/uac/common/utils/RocketMqQueueHelper.java](file://uac-common/src/main/java/cn/loveapp/uac/common/utils/RocketMqQueueHelper.java)
- [uac-api/src/main/java/cn/loveapp/uac/proto/event/UserChangedEvent.java](file://uac-api/src/main/java/cn/loveapp/uac/proto/event/UserChangedEvent.java)

## 数据库持久化

项目使用 MyBatis 作为 ORM 框架，通过 `mybatis-spring-boot-starter` 快速集成。`uac-db-common` 模块包含所有 DAO 接口与 XML 映射文件，实现对多平台用户数据（如淘宝、拼多多、抖音等）的统一管理。

实体类与数据库表通过 XML 文件映射，支持复杂查询与动态 SQL。Repository 层封装 DAO 调用，提供事务管理与缓存集成能力。

**Section sources**
- [uac-db-common/pom.xml](file://uac-db-common/pom.xml)
- [uac-db-common/src/main/resources/mapper](file://uac-db-common/src/main/resources/mapper)
- [uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/dao)