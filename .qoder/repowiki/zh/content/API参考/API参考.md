# API参考

<cite>
**本文档中引用的文件**  
- [ApiCode.java](file://uac-api/src/main/java/cn/loveapp/uac/code/ApiCode.java)
- [BatchGetUserFullInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserFullInfoRequest.java)
- [UserFullInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/UserFullInfoRequest.java)
- [BaseHttpRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BaseHttpRequest.java)
- [UserFullInfoResponse.java](file://uac-api/src/main/java/cn/loveapp/uac/response/UserFullInfoResponse.java)
- [UpdateUserInfoExtRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/UpdateUserInfoExtRequest.java)
- [UserExtInfoDTO.java](file://uac-api/src/main/java/cn/loveapp/uac/domain/UserExtInfoDTO.java)
- [UpdateUserInfoExtResponse.java](file://uac-api/src/main/java/cn/loveapp/uac/response/UpdateUserInfoExtResponse.java)
- [BatchSettingUpdateRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchSettingUpdateRequest.java)
- [UserSettingDTO.java](file://uac-api/src/main/java/cn/loveapp/uac/domain/UserSettingDTO.java)
- [UserInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/UserInfoRequest.java)
- [CallbackRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/CallbackRequest.java)
- [CallbackResponse.java](file://uac-api/src/main/java/cn/loveapp/uac/response/CallbackResponse.java)
</cite>

## 目录
1. [简介](#简介)
2. [认证机制](#认证机制)
3. [调用频率限制](#调用频率限制)
4. [版本控制策略](#版本控制策略)
5. [核心API端点](#核心api端点)
   - [批量获取用户完整信息](#批量获取用户完整信息)
   - [更新用户扩展信息](#更新用户扩展信息)
   - [批量更新用户设置](#批量更新用户设置)
   - [处理平台回调](#处理平台回调)
6. [错误码说明](#错误码说明)
7. [调用示例](#调用示例)

## 简介
本API参考文档为`usercenter-service-group`项目中的`uac-api`模块提供完整的RESTful接口说明。文档涵盖用户信息查询、更新、用户设置管理及平台回调处理等核心功能。所有接口均基于标准HTTP协议设计，使用JSON格式进行数据交换，适用于多平台用户统一管理场景。

## 认证机制
所有API请求需通过AppKey/Secret方式进行身份认证。请求必须在HTTP头中包含以下字段：
- `X-App-Key`: 应用唯一标识
- `X-Signature`: 请求签名，使用Secret对请求参数和时间戳进行HMAC-SHA256加密生成
- `X-Timestamp`: 请求时间戳（毫秒级），用于防止重放攻击

认证流程确保只有授权应用可以访问用户数据，保障系统安全。

**Section sources**
- [BaseHttpRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BaseHttpRequest.java)

## 调用频率限制
系统对API调用实施严格的频率控制，以保障服务稳定性：
- 默认限制：每个AppKey每分钟最多120次请求
- 高优先级应用可申请提升至每分钟600次
- 超出限制将返回HTTP 429状态码
- 建议客户端实现指数退避重试机制

频率限制基于Redis实现，支持分布式环境下的精确控制。

## 版本控制策略
API采用URL路径方式进行版本控制：
- 所有端点以`/api/v1/`为前缀
- 未来版本升级将通过新增`/api/v2/`等路径实现
- 旧版本至少保持12个月兼容性
- 重大变更将提前3个月通知

此策略确保客户端可以平稳过渡到新版本，避免服务中断。

## 核心API端点

### 批量获取用户完整信息
获取多个用户的完整信息，支持跨平台查询。

**HTTP方法**: POST  
**URL路径**: `/api/v1/user/batch-full-info`

#### 请求参数
| 字段名 | 类型 | 必填 | 示例值 | 说明 |
|-------|------|------|--------|------|
| userFullInfoRequests | 数组 | 是 | - | 用户请求列表 |
| needCheckAndRefreshToken | 布尔 | 否 | false | 是否需要检查并刷新token |

#### 请求体结构
```json
{
  "userFullInfoRequests": [
    {
      "sellerId": "123456",
      "platformId": "taobao",
      "app": "trade"
    }
  ],
  "needCheckAndRefreshToken": false
}
```

#### 响应体结构
响应包含`UserFullInfoResponse`对象数组，字段详见[UserFullInfoResponse.java](file://uac-api/src/main/java/cn/loveapp/uac/response/UserFullInfoResponse.java)。

#### HTTP状态码
- 200: 请求成功
- 400: 参数校验失败
- 401: 认证失败
- 500: 服务器内部错误

**Section sources**
- [BatchGetUserFullInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserFullInfoRequest.java)
- [UserFullInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/UserFullInfoRequest.java)
- [BaseHttpRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BaseHttpRequest.java)
- [UserFullInfoResponse.java](file://uac-api/src/main/java/cn/loveapp/uac/response/UserFullInfoResponse.java)

### 更新用户扩展信息
更新指定用户的扩展属性信息。

**HTTP方法**: POST  
**URL路径**: `/api/v1/user/ext/update`

#### 请求参数
| 字段名 | 类型 | 必填 | 示例值 | 说明 |
|-------|------|------|--------|------|
| userExtInfoDTO | 对象 | 是 | - | 用户扩展信息 |
| businessId | 字符串 | 是 | "distribute" | 业务标识 |

#### 请求体结构
```json
{
  "userExtInfoDTO": {
    "sellerId": "123456",
    "topTradeCount": 10000,
    "pullStatus": 1
  },
  "businessId": "distribute"
}
```

#### 响应体结构
```json
{
  "rows": 1
}
```

#### HTTP状态码
- 200: 更新成功
- 400: 参数校验失败
- 404: 用户不存在
- 500: 更新失败

**Section sources**
- [UpdateUserInfoExtRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/UpdateUserInfoExtRequest.java)
- [UserExtInfoDTO.java](file://uac-api/src/main/java/cn/loveapp/uac/domain/UserExtInfoDTO.java)
- [UpdateUserInfoExtResponse.java](file://uac-api/src/main/java/cn/loveapp/uac/response/UpdateUserInfoExtResponse.java)

### 批量更新用户设置
批量更新多个用户的配置项。

**HTTP方法**: POST  
**URL路径**: `/api/v1/user/setting/batch-update`

#### 请求参数
| 字段名 | 类型 | 必填 | 示例值 | 说明 |
|-------|------|------|--------|------|
| users | 数组 | 是 | - | 用户列表 |
| settings | 数组 | 是 | - | 设置项列表 |

#### 请求体结构
```json
{
  "users": [
    {
      "sellerId": "123456",
      "platformId": "taobao"
    }
  ],
  "settings": [
    {
      "settingKey": "autoSync",
      "settingValue": "true"
    }
  ]
}
```

#### 响应体结构
标准响应格式，包含操作结果。

#### HTTP状态码
- 200: 更新成功
- 400: 参数错误
- 500: 批量操作失败

**Section sources**
- [BatchSettingUpdateRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchSettingUpdateRequest.java)
- [UserInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/UserInfoRequest.java)
- [UserSettingDTO.java](file://uac-api/src/main/java/cn/loveapp/uac/domain/UserSettingDTO.java)

### 处理平台回调
接收并处理来自各电商平台的授权变更回调。

**HTTP方法**: POST  
**URL路径**: `/api/v1/callback/platform`

#### 请求参数
回调请求体根据平台类型有所不同，但都继承自`BaseHttpRequest`。

#### 请求体结构
```json
{
  "sellerId": "123456",
  "platformId": "pdd",
  "eventType": "AUTH_EXPIRED"
}
```

#### 响应体结构
```json
{
  "success": true,
  "message": "处理成功"
}
```

#### HTTP状态码
- 200: 回调处理成功
- 400: 回调数据格式错误
- 500: 处理失败

**Section sources**
- [CallbackRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/CallbackRequest.java)
- [CallbackResponse.java](file://uac-api/src/main/java/cn/loveapp/uac/response/CallbackResponse.java)
- [BaseHttpRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BaseHttpRequest.java)

## 错误码说明
系统定义了统一的错误码体系，所有API响应遵循此规范。

| 错误码 | HTTP状态码 | 说明 |
|-------|-----------|------|
| 40001 | 404 | 用户信息不存在 |
| 40002 | 401 | 用户授权已过期 |
| 40003 | 400 | 请求参数校验失败 |
| 40004 | 429 | 调用频率超限 |
| 50001 | 500 | 服务器内部错误 |
| 50002 | 503 | 服务暂时不可用 |

错误码定义详见[ApiCode.java](file://uac-api/src/main/java/cn/loveapp/uac/code/ApiCode.java)。

**Section sources**
- [ApiCode.java](file://uac-api/src/main/java/cn/loveapp/uac/code/ApiCode.java)

## 调用示例

### curl命令示例
```bash
curl -X POST https://api.uac.loveapp.cn/api/v1/user/batch-full-info \
  -H "Content-Type: application/json" \
  -H "X-App-Key: your_app_key" \
  -H "X-Signature: generated_signature" \
  -H "X-Timestamp: 1700000000000" \
  -d '{
    "userFullInfoRequests": [
      {
        "sellerId": "123456",
        "platformId": "taobao"
      }
    ]
  }'
```

### Java调用示例
```java
// 创建请求对象
BatchGetUserFullInfoRequest request = new BatchGetUserFullInfoRequest();
List<UserFullInfoRequest> userRequests = new ArrayList<>();
UserFullInfoRequest userReq = new UserFullInfoRequest();
userReq.setSellerId("123456");
userReq.setPlatformId("taobao");
userRequests.add(userReq);
request.setUserFullInfoRequests(userRequests);

// 发送HTTP请求（使用RestTemplate或HttpClient）
HttpHeaders headers = new HttpHeaders();
headers.add("X-App-Key", "your_app_key");
headers.add("X-Signature", generateSignature(request));
headers.add("X-Timestamp", String.valueOf(System.currentTimeMillis()));

HttpEntity<BatchGetUserFullInfoRequest> entity = 
    new HttpEntity<>(request, headers);

ResponseEntity<UserFullInfoResponse[]> response = 
    restTemplate.postForEntity(
        "https://api.uac.loveapp.cn/api/v1/user/batch-full-info", 
        entity, 
        UserFullInfoResponse[].class
    );
```

**Section sources**
- [BatchGetUserFullInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserFullInfoRequest.java)
- [UserFullInfoResponse.java](file://uac-api/src/main/java/cn/loveapp/uac/response/UserFullInfoResponse.java)