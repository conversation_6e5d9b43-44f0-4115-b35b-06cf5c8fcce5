# 用户管理API

<cite>
**本文档引用文件**  
- [UserController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java)
- [BatchGetUserFullInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserFullInfoRequest.java)
- [BatchGetUserCacheInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserCacheInfoRequest.java)
- [UserSettingCopyRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/UserSettingCopyRequest.java)
- [BatchSettingUpdateRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchSettingUpdateRequest.java)
- [UserSettingDTO.java](file://uac-api/src/main/java/cn/loveapp/uac/domain/UserSettingDTO.java)
</cite>

## 目录
1. [简介](#简介)
2. [批量查询接口](#批量查询接口)
3. [用户设置管理接口](#用户设置管理接口)
4. [HTTP请求与响应示例](#http请求与响应示例)
5. [分页、过滤与性能优化](#分页过滤与性能优化)
6. [处理流程与异常机制](#处理流程与异常机制)

## 简介
本文档详细说明用户管理API的核心功能，聚焦于用户信息批量查询与用户设置管理。涵盖 `BatchGetUserFullInfoRequest`、`BatchGetUserCacheInfoRequest` 等批量查询接口的请求参数、响应结构和使用场景，以及 `UserSettingCopyRequest`、`BatchSettingUpdateRequest` 等设置管理接口的调用方式和数据格式。同时提供实际的HTTP请求/响应示例，并结合 `UserController` 的实现逻辑，解析接口的处理流程与异常处理机制。

## 批量查询接口

### BatchGetUserFullInfoRequest
该接口用于批量获取用户的完整信息。

**请求参数说明：**
- `userFullInfoRequests`: 用户完整信息请求列表，不能为空。
- `needCheckAndRefreshToken`: 是否需要检查并刷新token，默认为 `false`。

此接口适用于需要同时获取多个用户详细信息的场景，如批量用户数据同步或报表生成。

**Section sources**
- [BatchGetUserFullInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserFullInfoRequest.java#L1-L27)

### BatchGetUserCacheInfoRequest
该接口用于批量获取用户指定缓存中的信息。

**请求参数说明：**
- 继承自 `BatchRequest<UserInfoRequest>`，包含多个用户请求。
- `cacheHkey`: 指定的缓存哈希键，不能为空。

适用于从缓存中高效批量读取用户特定数据，提升响应速度，减少数据库压力。

**Section sources**
- [BatchGetUserCacheInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserCacheInfoRequest.java#L1-L20)

## 用户设置管理接口

### UserSettingCopyRequest
该接口用于将一个用户的设置复制到多个目标用户。

**请求参数说明：**
- `originUser`: 源用户信息，包含 `userId`、`platformId` 和可选的 `app`。
- `targetUsers`: 目标用户列表，每个用户需提供 `userId` 和 `platformId`。
- `keepTargetSettings`: 是否保留目标用户已有设置（不覆盖），默认为 `false`。
- `copyType`: 复制类型，支持 `ALL`（全复制）、`ONLY_SETTINGS`（仅设置项）、`ONLY_TAGS`（仅用户标签）。
- `isExitPlatformTypeSetting`: 是否存在平台类型设置。

适用于多店铺配置同步、新用户初始化等场景。

**Section sources**
- [UserSettingCopyRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/UserSettingCopyRequest.java#L1-L86)

### BatchSettingUpdateRequest
该接口用于批量更新多个用户的设置。

**请求参数说明：**
- `users`: 用户列表，每个用户通过 `UserInfoRequest` 定义。
- `settings`: 设置列表，每个设置项为 `UserSettingDTO` 类型，包含 `key`、`value` 和 `description`。

适用于统一修改多个用户的配置项，如批量启用某项功能。

**Section sources**
- [BatchSettingUpdateRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchSettingUpdateRequest.java#L1-L25)

## HTTP请求与响应示例

### 批量获取用户完整信息 - 请求示例
```http
POST /uac/user/batchGetUserFullInfo HTTP/1.1
Content-Type: application/json

{
  "userFullInfoRequests": [
    {
      "sellerNick": "user1",
      "platformId": "TAOBAO"
    },
    {
      "sellerNick": "user2",
      "platformId": "PDD"
    }
  ],
  "needCheckAndRefreshToken": true
}
```

### 批量获取用户完整信息 - 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "userId": "1001",
      "nick": "user1",
      "vipLevel": "VIP3",
      "settings": [
        {
          "key": "theme",
          "value": "dark",
          "description": "界面主题"
        }
      ]
    },
    {
      "userId": "1002",
      "nick": "user2",
      "vipLevel": "VIP1",
      "settings": []
    }
  ]
}
```

### 批量更新用户设置 - 请求示例
```http
POST /uac/user/batchUpdateSettings HTTP/1.1
Content-Type: application/json

{
  "users": [
    {
      "sellerNick": "user1",
      "platformId": "TAOBAO"
    },
    {
      "sellerNick": "user2",
      "platformId": "PDD"
    }
  ],
  "settings": [
    {
      "key": "autoSync",
      "value": "true",
      "description": "开启自动同步"
    }
  ]
}
```

**Section sources**
- [BatchGetUserFullInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserFullInfoRequest.java#L1-L27)
- [BatchSettingUpdateRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchSettingUpdateRequest.java#L1-L25)

## 分页、过滤与性能优化

当前批量查询接口未内置分页机制，建议调用方控制单次请求的用户数量（建议不超过100个），以避免请求体过大或响应时间过长。

**性能优化建议：**
1. **合理使用缓存**：优先调用 `BatchGetUserCacheInfoRequest` 从缓存获取数据，降低数据库负载。
2. **按需刷新Token**：仅在必要时设置 `needCheckAndRefreshToken=true`，避免频繁调用授权服务。
3. **异步处理**：对于耗时较长的设置复制操作，建议通过消息队列异步执行，提高接口响应速度。
4. **批量操作合并**：将多个小批量请求合并为一次大批量请求，减少网络开销。

**Section sources**
- [BatchGetUserCacheInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserCacheInfoRequest.java#L1-L20)
- [BatchGetUserFullInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserFullInfoRequest.java#L1-L27)

## 处理流程与异常机制

### 接口处理流程
以 `UserController` 中的 `login` 接口为例，其处理流程如下：
1. 接收 `UserInfoRequest` 请求参数。
2. 通过 `@Validated` 注解进行参数校验。
3. 调用 `UserPlatformHandleService.login()` 方法执行登录逻辑。
4. 将 `UserInfoBo` 转换为业务处理对象。
5. 返回 `CommonApiResponse<UserInfoResponse>` 格式的成功响应。

其他接口遵循类似流程：参数校验 → 服务调用 → 结果封装 → 响应返回。

### 异常处理机制
系统采用统一的异常处理策略：
- **业务异常（UserException）**：捕获后返回特定错误码和消息，如用户不存在（`NO_EXIST_USER`）。
- **数据写入异常（DbWriteException）**：在 `quickLogin` 等涉及写操作的接口中抛出，返回服务器错误。
- **系统异常**：捕获 `Exception` 并记录日志，返回通用服务器错误，避免敏感信息泄露。

所有异常最终封装为 `CommonApiResponse` 格式，确保前端处理的一致性。

```mermaid
sequenceDiagram
participant Client as 客户端
participant Controller as UserController
participant Service as UserPlatformHandleService
participant DB as 数据库
Client->>Controller : 发送HTTP请求
Controller->>Controller : 参数校验(@Validated)
alt 校验失败
Controller-->>Client : 返回400错误
else 校验通过
Controller->>Service : 调用业务服务
Service->>DB : 查询/更新数据
DB-->>Service : 返回结果
Service-->>Controller : 返回业务结果
Controller-->>Client : 返回200成功响应
end
alt 发生异常
Service->>Controller : 抛出异常(UserException/DbWriteException)
Controller->>Controller : 捕获并封装错误
Controller-->>Client : 返回错误码和消息
end
```

**Diagram sources**
- [UserController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java#L1-L108)

**Section sources**
- [UserController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java#L1-L108)
- [UserPlatformHandleService.java](file://uac-service/src/main/java/cn/loveapp/uac/service/platform/biz/UserPlatformHandleService.java)
- [UserException.java](file://uac/exception/UserException.java)