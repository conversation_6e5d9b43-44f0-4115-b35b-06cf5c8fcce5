# 用户信息查询API

<cite>
**本文档引用文件**  
- [BatchGetUserFullInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserFullInfoRequest.java)
- [BatchGetUserCacheInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserCacheInfoRequest.java)
- [UserCenterInnerApiService.java](file://uac-api/src/main/java/cn/loveapp/uac/service/UserCenterInnerApiService.java)
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java)
- [UserController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java)
</cite>

## 目录
1. [简介](#简介)
2. [核心接口概述](#核心接口概述)
3. [BatchGetUserFullInfoRequest 接口详情](#batchgetuserfullinforequest-接口详情)
4. [BatchGetUserCacheInfoRequest 接口详情](#batchgetusercacheinforequest-接口详情)
5. [请求与响应示例](#请求与响应示例)
6. [实现逻辑与缓存策略](#实现逻辑与缓存策略)
7. [性能特点与调用限制](#性能特点与调用限制)
8. [最佳实践](#最佳实践)

## 简介
本文档详细说明用户中心服务中两个核心批量查询接口：`BatchGetUserFullInfoRequest` 和 `BatchGetUserCacheInfoRequest`。这两个接口用于高效获取多个用户的信息和缓存数据，支持跨平台、多应用的用户信息管理。文档涵盖接口定义、参数说明、使用示例及底层实现机制。

[无来源，本节为概念性概述]

## 核心接口概述
系统提供两个主要批量查询接口：
- `batchGetUserFullInfo`：批量获取用户的完整信息（包括授权令牌、会员状态等）
- `batchGetUserCacheInfo`：批量获取用户在指定缓存键下的信息

这些接口通过 `UserCenterInnerApiService` 定义，并由 `UserCenterServiceImpl` 实现，支持 POST 方法调用，适用于高并发场景下的用户数据批量拉取。

**本节来源**
- [UserCenterInnerApiService.java](file://uac-api/src/main/java/cn/loveapp/uac/service/UserCenterInnerApiService.java#L69-L102)
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java#L116-L135)

## BatchGetUserFullInfoRequest 接口详情

### 接口定义
- **HTTP 方法**：POST
- **URL 路径**：`/export/uac/user/batchGetUserFullInfo`
- **认证要求**：需携带有效访问令牌（通过请求头或参数传递）

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| userFullInfoRequests | List<UserFullInfoRequest> | 是 | 批量用户请求列表，每个元素包含 sellerId、platformId、app 等信息 |
| needCheckAndRefreshToken | boolean | 否 | 是否需要检查并刷新 token，默认为 false |

### 响应结构
成功响应返回 `CommonApiResponse<List<UserFullInfoResponse>>`，其中包含每个用户的完整信息对象，字段包括：
- `sellerId`：用户ID
- `sellerNick`：用户昵称
- `accessToken`：访问令牌
- `refreshToken`：刷新令牌
- `vipflag`：会员标识
- `orderCycleEnd`：会员周期结束时间

错误响应遵循统一格式，包含错误码和消息。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Service as "UserCenterServiceImpl"
participant Handler as "UserPlatformHandleService"
Client->>Service : POST /batchGetUserFullInfo
Service->>Service : 解析请求参数
loop 每个用户请求
Service->>Handler : getUserFullInfo(userInfoBo)
Handler-->>Service : 返回UserProductInfo
Service->>Service : 转换为UserFullInfoResponse
Service->>Service : 更新vipflag
end
Service-->>Client : 返回批量响应
```

**图示来源**
- [BatchGetUserFullInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserFullInfoRequest.java#L12-L26)
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java#L116-L135)

**本节来源**
- [BatchGetUserFullInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserFullInfoRequest.java#L12-L26)
- [UserCenterInnerApiService.java](file://uac-api/src/main/java/cn/loveapp/uac/service/UserCenterInnerApiService.java#L94-L102)

## BatchGetUserCacheInfoRequest 接口详情

### 接口定义
- **HTTP 方法**：POST
- **URL 路径**：`/export/uac/user/batchGetUserCacheInfo`
- **认证要求**：需携带有效访问令牌

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|-------|------|------|------|
| requestList | List<UserInfoRequest> | 是 | 用户基本信息列表，用于定位用户 |
| cacheHkey | String | 是 | 指定的缓存哈希键（hkey），决定从哪个缓存区域读取数据 |

### 响应结构
成功响应返回 `CommonApiResponse<List<UserCacheInfoResponse>>`，每个响应项包含：
- `userId`：用户ID
- `cacheValue`：缓存值（字符串形式）
- `exists`：该缓存项是否存在

支持快速判断用户特定配置或状态是否已缓存。

```mermaid
flowchart TD
Start([开始]) --> ValidateInput["验证输入参数"]
ValidateInput --> CheckCacheHkey{"cacheHkey非空?"}
CheckCacheHkey --> |否| ReturnError["返回参数错误"]
CheckCacheHkey --> |是| ConvertToBo["将UserInfoRequest转换为UserInfoBo"]
ConvertToBo --> CallService["调用userPlatformHandleService.batchGetUserCacheInfo()"]
CallService --> FormatResponse["格式化响应结果"]
FormatResponse --> ReturnSuccess["返回成功响应"]
ReturnError --> End([结束])
ReturnSuccess --> End
```

**图示来源**
- [BatchGetUserCacheInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserCacheInfoRequest.java#L11-L19)
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java#L655-L661)

**本节来源**
- [BatchGetUserCacheInfoRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchGetUserCacheInfoRequest.java#L11-L19)
- [UserCenterInnerApiService.java](file://uac-api/src/main/java/cn/loveapp/uac/service/UserCenterInnerApiService.java#L45-L48)

## 请求与响应示例

### BatchGetUserFullInfoRequest 示例
**请求体：**
```json
{
  "userFullInfoRequests": [
    {
      "sellerId": "123456",
      "platformId": "TAO",
      "app": "TRADE"
    },
    {
      "sellerId": "789012",
      "platformId": "PDD",
      "app": "ITEM"
    }
  ],
  "needCheckAndRefreshToken": true
}
```

**成功响应：**
```json
{
  "code": 0,
  "message": "success",
  "body": [
    {
      "sellerId": "123456",
      "sellerNick": "淘宝用户A",
      "accessToken": "top_session_xxx",
      "refreshToken": "refresh_token_xxx",
      "vipflag": 1,
      "orderCycleEnd": "2025-12-31T23:59:59"
    }
  ]
}
```

### BatchGetUserCacheInfoRequest 示例
**请求体：**
```json
{
  "requestList": [
    {
      "sellerId": "123456",
      "platformId": "TAO",
      "app": "TRADE"
    }
  ],
  "cacheHkey": "user_config"
}
```

**成功响应：**
```json
{
  "code": 0,
  "message": "success",
  "body": [
    {
      "userId": "123456",
      "cacheValue": "{\"theme\":\"dark\",\"lang\":\"zh\"}",
      "exists": true
    }
  ]
}
```

**本节来源**
- [UserCenterServiceImplTest.java](file://uac-service/src/test/java/cn/loveapp/uac/service/export/UserCenterServiceImplTest.java#L74-L159)

## 实现逻辑与缓存策略

### 批量处理机制
两个接口均采用循环处理模式：
- 遍历 `requestList` 中的每个用户请求
- 调用 `userPlatformHandleService` 进行业务逻辑处理
- 收集结果并统一返回
- 对于不存在的用户，系统会记录警告但不中断整体流程

### 缓存策略
- `batchGetUserCacheInfo` 直接依赖 Redis 缓存系统，通过 `cacheHkey` 定位哈希表
- 缓存读取前会进行参数校验和用户身份映射
- 缓存未命中时返回空值，不影响主流程
- 支持最大200个用户的批量操作，防止内存溢出

### 错误码机制
| 错误码 | 含义 | 处理建议 |
|--------|------|---------|
| NO_EXIST_USER | 用户不存在 | 检查 sellerId 或平台信息是否正确 |
| RequestParamError | 请求参数错误 | 校验必填字段和格式 |
| ServerError | 服务端异常 | 重试或联系技术支持 |

**本节来源**
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java#L116-L135)
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java#L655-L661)
- [ApiCode.java](file://uac-api/src/main/java/cn/loveapp/uac/code/ApiCode.java)

## 性能特点与调用限制

### 性能特点
- **高并发支持**：基于 Spring MVC 构建，支持异步处理
- **低延迟设计**：缓存接口直接访问 Redis，响应时间通常小于 50ms
- **批量优化**：避免逐个查询带来的网络开销，提升吞吐量

### 调用频率限制
- 单次请求最多支持 **200 个用户**
- 建议每秒调用不超过 10 次，避免触发限流
- 生产环境已配置熔断机制，防止雪崩效应

### 超时设置
- 推荐客户端设置超时时间为 3 秒
- 服务端默认处理超时为 2 秒

**本节来源**
- [BatchRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchRequest.java#L15-L18)
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java)

## 最佳实践

### 使用建议
1. **合理分批**：单次请求控制在 50-100 个用户，平衡性能与稳定性
2. **缓存预热**：高频访问数据应提前加载至缓存
3. **错误重试**：对 transient 错误（如网络抖动）实施指数退避重试
4. **监控告警**：对接口调用延迟、失败率进行监控

### 安全注意事项
- 禁止在日志中打印 `accessToken` 和 `refreshToken`
- 请求体传输必须使用 HTTPS 加密
- 定期轮换访问密钥

### 调试技巧
- 开启 DEBUG 日志可查看详细处理流程
- 使用 `mockMvc` 进行单元测试验证逻辑正确性
- 利用 Redis CLI 检查缓存状态

**本节来源**
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java)
- [UserController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java)