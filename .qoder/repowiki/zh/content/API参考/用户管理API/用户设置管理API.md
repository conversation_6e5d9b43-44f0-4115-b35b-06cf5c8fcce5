# 用户设置管理API

<cite>
**本文档引用文件**  
- [UserSettingCopyRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/UserSettingCopyRequest.java)
- [BatchSettingUpdateRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchSettingUpdateRequest.java)
- [BatchSettingGetRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchSettingGetRequest.java)
- [UserController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java)
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java)
- [UserCenterInnerApiService.java](file://uac-api/src/main/java/cn/loveapp/uac/service/UserCenterInnerApiService.java)
- [BatchUsersSettingGetRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchUsersSettingGetRequest.java)
</cite>

## 目录
1. [简介](#简介)
2. [核心接口概览](#核心接口概览)
3. [用户设置复制接口](#用户设置复制接口)
4. [批量更新用户设置接口](#批量更新用户设置接口)
5. [批量获取用户设置接口](#批量获取用户设置接口)
6. [数据存储与一致性机制](#数据存储与一致性机制)
7. [事务处理与输入验证](#事务处理与输入验证)
8. [使用场景与最佳实践](#使用场景与最佳实践)
9. [错误处理与性能优化](#错误处理与性能优化)

## 简介
本文档详细说明用户设置管理系统的三大核心接口：用户设置复制、批量更新和批量获取。这些接口为多平台、多应用环境下的用户配置管理提供了高效、灵活的解决方案。系统通过统一的请求结构和响应机制，支持跨用户、跨平台的设置同步与批量操作，确保数据一致性与操作可靠性。

## 核心接口概览
系统提供三个核心接口用于管理用户设置：
- **用户设置复制接口**：支持将一个用户的设置批量复制到多个目标用户，可选择复制全部、仅设置项或仅用户标签。
- **批量更新用户设置接口**：支持同时为多个用户更新一组配置项，提升批量配置效率。
- **批量获取用户设置接口**：支持从单个或多个用户批量读取指定的配置项，支持加载默认设置。

这些接口通过统一的请求/响应结构和异常处理机制，确保操作的原子性和一致性。

## 用户设置复制接口

### 功能说明
`UserSettingCopyRequest` 接口用于将源用户的设置复制到一个或多个目标用户。支持三种复制类型：全复制、仅复制设置项、仅复制用户标签。可通过 `keepTargetSettings` 参数控制是否保留目标用户已存在的设置。

### 请求参数结构
```json
{
  "originUser": {
    "userId": "源用户ID",
    "platformId": "平台ID",
    "app": "应用名称"
  },
  "targetUsers": [
    {
      "userId": "目标用户ID",
      "platformId": "平台ID",
      "app": "应用名称"
    }
  ],
  "keepTargetSettings": false,
  "copyType": "ALL",
  "isExitPlatformTypeSetting": false
}
```

### 调用流程
```mermaid
sequenceDiagram
participant Client as "客户端"
participant Controller as "UserController"
participant Service as "UserCenterServiceImpl"
participant Repository as "UserSettingsRepository"
Client->>Controller : 发送UserSettingCopyRequest
Controller->>Service : 调用userSettingCopy方法
Service->>Service : 根据copyType分支处理
alt 复制设置项
Service->>Repository : 查询源用户设置
Repository-->>Service : 返回设置列表
Service->>Service : 构建目标用户设置映射
opt 保留目标用户设置
Service->>Repository : 查询目标用户现有设置
Repository-->>Service : 返回现有设置
Service->>Service : 从映射中移除已存在设置
end
Service->>Repository : 批量插入新设置
end
alt 复制用户标签
Service->>Repository : 查询源用户标签
Repository-->>Service : 返回标签列表
Service->>Repository : 批量插入标签到目标用户
end
Service-->>Controller : 返回成功
Controller-->>Client : 返回CommonApiResponse.success()
```

**Diagram sources**
- [UserSettingCopyRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/UserSettingCopyRequest.java#L1-L85)
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java#L310-L348)

**Section sources**
- [UserSettingCopyRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/UserSettingCopyRequest.java#L1-L85)
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java#L310-L464)

## 批量更新用户设置接口

### 功能说明
`BatchSettingUpdateRequest` 接口允许客户端一次性为多个用户更新一组配置项。系统将请求中的用户列表与设置列表进行笛卡尔积组合，生成最终的设置更新列表，并通过批量插入/更新操作持久化到数据库。

### 请求参数结构
```json
{
  "users": [
    {
      "sellerId": "用户ID",
      "platformId": "平台ID",
      "app": "应用名称"
    }
  ],
  "settings": [
    {
      "key": "配置键",
      "value": "配置值",
      "description": "描述"
    }
  ]
}
```

### 调用流程
```mermaid
flowchart TD
Start([开始]) --> ValidateInput["验证请求参数"]
ValidateInput --> InputValid{"参数有效?"}
InputValid --> |否| ReturnError["返回参数错误"]
InputValid --> |是| CreateList["创建UserSettings列表"]
CreateList --> LoopUsers["遍历用户列表"]
LoopUsers --> LoopSettings["遍历设置列表"]
LoopSettings --> BuildEntity["构建UserSettings实体"]
BuildEntity --> AddToList["添加到列表"]
AddToList --> MoreSettings{"还有设置?"}
MoreSettings --> |是| LoopSettings
MoreSettings --> |否| MoreUsers{"还有用户?"}
MoreUsers --> |是| LoopUsers
MoreUsers --> |否| BatchUpsert["批量插入/更新设置"]
BatchUpsert --> Success{"操作成功?"}
Success --> |是| ReturnSuccess["返回成功"]
Success --> |否| LogError["记录错误日志"]
LogError --> ReturnServerError["返回服务器错误"]
ReturnError --> End([结束])
ReturnSuccess --> End
ReturnServerError --> End
```

**Diagram sources**
- [BatchSettingUpdateRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchSettingUpdateRequest.java#L1-L24)
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java#L192-L230)

**Section sources**
- [BatchSettingUpdateRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchSettingUpdateRequest.java#L1-L24)
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java#L192-L230)

## 批量获取用户设置接口

### 功能说明
`BatchSettingGetRequest` 接口支持从指定用户批量读取一组配置项。可通过 `loadDefaultSetting` 参数控制是否同时加载默认设置。系统会验证用户ID、平台ID、应用名称及设置名列表的完整性，并从数据库查询匹配的设置项。

### 请求参数结构
```json
{
  "userId": "用户ID",
  "platformId": "平台ID",
  "app": "应用名称",
  "settings": ["setting1", "setting2"],
  "loadDefaultSetting": true
}
```

### 批量获取多个用户设置
系统还支持通过 `BatchUsersSettingGetRequest` 批量获取多个用户的设置，其结构为 `BatchSettingGetRequest` 的数组。

```json
{
  "batchSettingGetParams": [
    {
      "userId": "用户1",
      "platformId": "平台",
      "app": "应用",
      "settings": ["setting1"]
    },
    {
      "userId": "用户2",
      "platformId": "平台",
      "app": "应用",
      "settings": ["setting2"]
    }
  ]
}
```

### 调用流程
```mermaid
sequenceDiagram
participant Client as "客户端"
participant Controller as "UserController"
participant Service as "UserCenterServiceImpl"
participant Repository as "UserSettingsRepository"
Client->>Controller : 发送BatchSettingGetRequest
Controller->>Service : 调用batchSettingGet方法
Service->>Service : 验证请求参数
Service->>Service : 构建QueryUserSettingsParam
Service->>Repository : 批量查询用户设置
Repository-->>Service : 返回UserSettingDTO列表
Service-->>Controller : 返回成功响应
Controller-->>Client : 返回CommonApiResponse.success(列表)
```

**Diagram sources**
- [BatchSettingGetRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchSettingGetRequest.java#L1-L39)
- [BatchUsersSettingGetRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchUsersSettingGetRequest.java#L1-L19)
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java#L232-L258)

**Section sources**
- [BatchSettingGetRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchSettingGetRequest.java#L1-L39)
- [BatchUsersSettingGetRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchUsersSettingGetRequest.java#L1-L19)
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java#L232-L281)

## 数据存储与一致性机制

### 存储结构
用户设置数据存储在 `UserSettings` 实体中，其核心字段包括：
- `userId`: 用户唯一标识
- `platformId`: 平台标识
- `appName`: 应用名称
- `settingKey`: 设置键名
- `settingValue`: 设置值
- `description`: 描述信息

### 一致性保证
系统通过以下机制保证数据一致性：
1. **批量操作原子性**：使用 `batchUpsertUserSetting` 方法确保批量插入/更新的原子性。
2. **设置覆盖策略**：在复制设置时，通过 `keepTargetSettings` 参数控制是否覆盖已有设置。
3. **平台特定设置处理**：识别平台特定设置（如包含平台关键字的设置），避免跨平台错误复制。
4. **唯一性约束**：通过 `userId+platformId+appName+settingKey` 组合确保设置项的唯一性。

```mermaid
erDiagram
USER_SETTINGS {
string userId PK
string platformId PK
string appName PK
string settingKey PK
string settingValue
string description
datetime createTime
datetime updateTime
}
```

**Diagram sources**
- [UserSettings.java](file://uac-db-common/src/main/java/cn/loveapp/uac/db/common/entity/UserSettings.java)
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java)

**Section sources**
- [UserCenterServiceImpl.java](file://uac-service/src/main/java/cn/loveapp/uac/service/export/UserCenterServiceImpl.java#L192-L464)

## 事务处理与输入验证

### 事务处理
系统在服务层（`UserCenterServiceImpl`）对每个核心操作进行事务管理：
- `batchSettingUpdate`：整个批量更新操作在一个事务中执行。
- `userSettingCopy`：设置复制和标签复制分别在独立的逻辑块中处理，异常时整体回滚。
- `batchSettingGet`：查询操作无需事务，但保证数据读取的一致性视图。

### 输入验证
系统采用多层次输入验证机制：
1. **注解验证**：使用 `@NotNull`、`@NotEmpty`、`@NotBlank` 等JSR-303注解进行基础验证。
2. **业务逻辑验证**：在服务层检查参数的业务合理性（如用户是否存在、平台是否支持）。
3. **空值检查**：对关键字段进行空值校验，防止NPE。
4. **集合验证**：确保用户列表和设置列表非空。

```mermaid
flowchart TD
A([请求进入]) --> B["@Validated 注解验证"]
B --> C{"验证通过?"}
C --> |否| D["返回参数错误"]
C --> |是| E["服务层业务验证"]
E --> F{"验证通过?"}
F --> |否| G["返回业务错误"]
F --> |是| H["执行业务逻辑"]
H --> I["返回成功响应"]
D --> J([结束])
G --> J
I --> J
```

**Diagram sources**
- [UserSettingCopyRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/UserSettingCopyRequest.java#L1-L85)
- [BatchSettingUpdateRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchSettingUpdateRequest.java#L1-L24)
- [UserController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java#L1-L108)

**Section sources**
- [UserSettingCopyRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/UserSettingCopyRequest.java#L1-L85)
- [BatchSettingUpdateRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/BatchSettingUpdateRequest.java#L1-L24)
- [UserController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/UserController.java#L1-L108)

## 使用场景与最佳实践

### 常见使用场景
1. **新用户初始化**：复制标杆用户的设置到新注册用户。
2. **配置批量迁移**：在系统升级或平台迁移时，批量更新用户配置。
3. **数据同步**：定期同步用户设置到其他系统或缓存。
4. **批量查询分析**：为运营分析批量获取用户配置数据。

### 最佳实践
- **批量操作大小**：建议单次批量操作不超过100个用户或1000个设置项，避免超时。
- **错误重试**：对网络或数据库异常实现指数退避重试机制。
- **幂等性设计**：确保复制和更新操作的幂等性，避免重复执行导致数据异常。
- **监控告警**：对核心接口添加监控，及时发现性能瓶颈和错误率上升。

## 错误处理与性能优化

### 错误处理建议
1. **参数错误**：检查请求体是否符合规范，必填字段是否缺失。
2. **服务器错误**：查看日志中的异常堆栈，定位数据库或业务逻辑问题。
3. **用户不存在**：确认用户ID、平台ID、应用名称的组合是否正确。
4. **权限问题**：确保调用方有操作目标用户的权限。

### 性能优化提示
1. **批量操作优化**：合并小批量请求，减少数据库交互次数。
2. **缓存策略**：对频繁读取的设置项使用Redis缓存，设置合理过期时间。
3. **索引优化**：确保 `userId+platformId+appName+settingKey` 有复合索引。
4. **异步处理**：对于非实时性要求的操作，可采用消息队列异步处理。