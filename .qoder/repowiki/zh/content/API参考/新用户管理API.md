# 新用户管理API

<cite>
**本文档引用文件**  
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java)
- [GetPullDataProgressRequest.java](file://uac-newuser-api/src/main/java/cn/loveapp/uac/newuser/dto/request/GetPullDataProgressRequest.java)
- [GetSaveDataTotalResultRequest.java](file://uac-newuser-api/src/main/java/cn/loveapp/uac/newuser/dto/request/GetSaveDataTotalResultRequest.java)
- [GetPullDataProgressResponse.java](file://uac-newuser-api/src/main/java/cn/loveapp/uac/newuser/dto/response/GetPullDataProgressResponse.java)
- [GetSaveDataTotalResultResponse.java](file://uac-newuser-api/src/main/java/cn/loveapp/uac/newuser/dto/response/GetSaveDataTotalResultResponse.java)
</cite>

## 目录
1. [简介](#简介)
2. [核心接口说明](#核心接口说明)
3. [请求参数详解](#请求参数详解)
4. [响应数据结构](#响应数据结构)
5. [接口在新用户开通流程中的作用](#接口在新用户开通流程中的作用)
6. [与后台定时任务的协同机制](#与后台定时任务的协同机制)
7. [接口调用时序图](#接口调用时序图)
8. [安全验证与错误处理机制](#安全验证与错误处理机制)
9. [总结](#总结)

## 简介
本文档详细描述了新用户管理API中的两个核心接口：**获取数据拉取进度**（`GetPullDataProgressRequest`）和**获取数据保存结果**（`GetSaveDataTotalResultRequest`）。这些接口服务于新用户开通流程，用于实时查询后台数据同步任务的执行状态，包括进度百分比、任务状态等关键指标。文档结合`NewuserController`实现，说明其安全校验、业务逻辑及与后台定时任务的协作机制。

## 核心接口说明
新用户管理API提供了两个关键查询接口，用于监控用户数据初始化过程的状态：

- **获取数据拉取进度接口**：通过`/uac/newuser/saveDataCourse`端点，查询当前登录用户的数据拉取和保存进度。
- **准备开通/关闭用户接口**：通过`/uac/newuser/prepare`端点，触发用户开通或关闭流程。

这些接口是新用户从授权到完全可用这一流程中的重要监控手段。

**接口来源**
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java#L30-L78)

## 请求参数详解
### 获取数据拉取进度请求（GetPullDataProgressRequest）
该接口通过HTTP GET请求调用，无需显式请求体，其参数隐含在用户的会话（Session）中。

- **sellerId**：商家ID，标识目标用户。
- **businessId**：业务ID，标识用户所属的业务线（如平台类型）。
- **platform**：平台标识，如淘宝、拼多多等。
- **serviceName**：服务名称，标识具体开通的服务。

这些信息通过`HttpServletRequest`中的会话上下文自动提取，由`SessionValidateUtil`进行校验。

### 准备开通用户请求（Prepare Request）
该接口通过HTTP POST请求调用，接收`SaveDataDTO`对象作为请求体。

- **sellerId** (String, 必填)：商家ID。
- **businessId** (String, 必填)：业务ID。
- **platform** (String, 可选)：平台ID，若为空则使用默认值。
- **serviceName** (String, 可选)：服务名称。
- **closeSaveData** (Boolean)：是否关闭数据保存功能。

**请求参数来源**
- [GetPullDataProgressRequest.java](file://uac-newuser-api/src/main/java/cn/loveapp/uac/newuser/dto/request/GetPullDataProgressRequest.java)
- [SaveDataDTO.java](file://uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/dto/SaveDataDTO.java)

## 响应数据结构
### 获取数据拉取进度响应（GetPullDataProgressResponse）
响应体为`SaveDataCourseDTO`对象，包含以下关键字段：

- **progress** (Integer)：当前已拉取并保存的数据量。
- **total** (Integer)：预计需要拉取的总数据量。
- **status** (String)：任务状态，例如 "RUNNING", "SUCCESS", "FAILED"。
- **progressPercent** (Double)：计算得出的进度百分比（`progress / total * 100`），用于前端展示。

当`progress`或`total`为`null`时，表示任务尚未开始或状态未知，接口返回错误。

### 准备开通用户响应
返回标准的`CommonApiResponse`对象：
- **code** (Integer)：HTTP状态码或自定义错误码。
- **message** (String)：描述性信息，如“提交成功”或“参数异常”。
- **body** (Boolean)：操作结果布尔值。

**响应数据来源**
- [GetPullDataProgressResponse.java](file://uac-newuser-api/src/main/java/cn/loveapp/uac/newuser/dto/response/GetPullDataProgressResponse.java)
- [SaveDataCourseDTO.java](file://uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/dto/SaveDataCourseDTO.java)

## 接口在新用户开通流程中的作用
这两个接口是新用户开通流程的**监控与控制入口**。

1.  **触发阶段**：当ISV（独立软件开发商）调用`/prepare`接口并传入`closeSaveData=false`时，系统将用户标记为“待开通”。
2.  **执行阶段**：后台的定时任务（如`ScanWaitOpenUser`）会扫描所有“待开通”状态的用户，并启动数据拉取和保存流程。
3.  **监控阶段**：前端或ISV通过调用`/saveDataCourse`接口，轮询获取当前用户的`progress`和`total`，从而计算出`progressPercent`，向用户展示一个实时的进度条。
4.  **完成阶段**：当后台任务完成所有数据的拉取和保存后，`progress`将等于`total`，`status`变为“SUCCESS”，前端可据此提示用户开通成功。

**流程作用来源**
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java#L30-L78)
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java)

## 与后台定时任务的协同机制
新用户管理API与`uac-newuser-scheduler`模块中的定时任务紧密协作，形成“**请求-调度-执行-查询**”的闭环。

- **API层（NewuserController）**：接收外部请求，进行安全校验，并将用户状态置为“待开通”。
- **调度层（Scheduler）**：`ScanWaitOpenUser`任务定期扫描数据库或Redis，找出所有状态为“待开通”的用户记录。
- **执行层（Business Service）**：对于每个待开通用户，调度任务会调用`UserSaveDataBusinessHandleService`的具体实现（如`ItemUserSaveDataBusinessHandleServiceImpl`），执行跨平台的数据拉取（Pull）和本地化保存（Save）。
- **状态层（Redis/DB）**：在执行过程中，业务服务会实时更新该用户的`progress`（已处理数量）和`total`（总数量）到缓存（如Redis）中。
- **查询层（API）**：`saveDataCourse`接口从缓存中读取最新的`progress`和`total`，组装成响应返回给客户端。

这种设计实现了**异步解耦**，避免了用户请求长时间阻塞，同时提供了实时的进度反馈。

**协同机制来源**
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java)
- [ScanWaitOpenUser.java](file://uac-newusers/uac-newuser-scheduler/src/main/java/cn/loveapp/uac/newuser/scheduler/task/open/ScanWaitOpenUser.java)
- [UserSaveDataBusinessHandleService.java](file://uac-newuser-common/src/main/java/cn/loveapp/uac/newuser/common/business/UserSaveDataBusinessHandleService.java)

## 接口调用时序图
以下时序图展示了从客户端发起进度查询请求到获取响应的完整流程。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Controller as "NewuserController"
participant SessionUtil as "SessionValidateUtil"
participant BusinessService as "UserSaveDataBusinessHandleService"
participant Cache as "Redis/缓存"
Client->>Controller : GET /uac/newuser/saveDataCourse
Controller->>SessionUtil : checkSessionInfo(request)
SessionUtil-->>Controller : 返回UserInfoDTO
alt 会话校验失败
Controller-->>Client : 返回401错误
return
end
Controller->>BusinessService : getPullDataProgress(businessId, userInfo)
BusinessService->>Cache : 读取 progress 和 total
Cache-->>BusinessService : 返回进度数据
BusinessService-->>Controller : 返回SaveDataCourseDTO
Controller->>Controller : 验证数据有效性
alt 数据无效
Controller-->>Client : 返回500错误
return
end
Controller-->>Client : 返回200及进度信息
```

**时序图来源**
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java#L30-L55)

## 安全验证与错误处理机制
`NewuserController`实现了严格的安全验证和健壮的错误处理。

### 安全验证
- **会话校验**：`saveDataCourse`接口依赖`SessionValidateUtil.checkSessionInfo()`方法。该方法从`HttpServletRequest`中解析并验证用户会话，确保只有经过身份验证的用户才能查询其数据进度。
- **参数校验**：`prepare`接口对`SaveDataDTO`中的`sellerId`和`businessId`进行非空校验，防止无效请求进入业务层。

### 错误处理
- **会话错误**：如果`checkSessionInfo`返回失败，控制器会立即返回`CommonApiResponse.failed()`，包含错误码和消息。
- **业务逻辑错误**：在`saveDataCourse`中，如果从服务层获取的`progress`或`total`为`null`，会记录日志并返回“获取进度失败”的通用错误。
- **数据一致性保护**：代码中包含一个保护性逻辑：如果`total`小于`progress`（理论上不应发生），会将`total`强制设置为`progress`的值，防止前端计算出超过100%的进度，保证了用户体验的合理性。

**安全与错误处理来源**
- [NewuserController.java](file://uac-newusers/uac-newuser-service/src/main/java/cn/loveapp/uac/newuser/service/controller/NewuserController.java#L60-L75)

## 总结
新用户管理API通过`/saveDataCourse`和`/prepare`等接口，为新用户开通流程提供了关键的监控能力。它通过与后台`ScanWaitOpenUser`等定时任务的协同工作，实现了异步数据初始化，并通过查询接口暴露进度信息。其设计注重安全性，通过会话校验保护数据访问，并通过完善的错误处理机制保障了服务的稳定性。开发者在集成时，应理解其异步特性，通过轮询`saveDataCourse`接口来获取最终的开通结果。