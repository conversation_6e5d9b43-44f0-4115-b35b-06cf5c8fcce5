# 回调接口规范

<cite>
**本文档中引用的文件**  
- [CallbackRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/CallbackRequest.java)
- [CallbackResponse.java](file://uac-api/src/main/java/cn/loveapp/uac/response/CallbackResponse.java)
- [CallbackController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/CallbackController.java)
- [CallbackPlatformHandleService.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/platform/biz/CallbackPlatformHandleService.java)
- [AuthBo.java](file://uac-common/src/main/java/cn/loveapp/uac/common/bo/AuthBo.java)
</cite>

## 目录
1. [简介](#简介)
2. [核心数据结构](#核心数据结构)
3. [RESTful接口设计](#restful接口设计)
4. [安全性实现](#安全性实现)
5. [请求与响应示例](#请求与响应示例)
6. [错误处理策略](#错误处理策略)

## 简介
本规范文档详细描述了用户中心服务中的授权回调接口设计与实现。该接口用于接收第三方平台（如淘宝、拼多多等）在用户完成授权后返回的授权码，并完成后续的令牌获取与用户信息同步流程。文档重点说明了 `CallbackRequest` 和 `CallbackResponse` 的数据结构定义、`CallbackController` 的接口设计、安全性保障机制以及典型使用场景。

## 核心数据结构

### CallbackRequest 请求数据结构
`CallbackRequest` 类定义了授权回调接口的请求参数，包含以下字段：

- **code**：授权码，由第三方平台生成并传递，用于换取访问令牌（access token），不能为空。
- **platformId**：平台标识，表示用户授权的电商平台类型（如 TAO 表示淘宝），需通过 `@CheckPlatformHasExist` 注解验证其有效性。
- **app**：应用名称，表示请求来源的应用模块（如 trade 表示交易系统），需通过 `@CheckAppHasExist` 注解验证其存在性。

该类位于 `uac-api` 模块中，作为跨服务调用的标准请求对象。

**Section sources**
- [CallbackRequest.java](file://uac-api/src/main/java/cn/loveapp/uac/request/CallbackRequest.java#L1-L22)

### CallbackResponse 响应数据结构
`CallbackResponse` 类定义了授权回调接口的响应数据，包含以下字段：

- **sellerNick**：卖家昵称，授权成功后从第三方平台获取的用户标识。
- **accessToken**：访问令牌，用于后续调用第三方平台 API 的凭证（未加密前的原始值）。

此外，该类提供了静态工厂方法 `of(sellerNick, accessToken)` 用于构建响应实例，并通过 `isSuccess()` 方法判断授权是否成功（两个字段均非空且非空字符串）。

**Section sources**
- [CallbackResponse.java](file://uac-api/src/main/java/cn/loveapp/uac/response/CallbackResponse.java#L1-L34)

## RESTful接口设计

### 接口定义
`CallbackController` 提供了 `/uac/callback/authCallback` 接口用于处理授权回调请求。

- **HTTP 方法**：支持 `GET` 和 `POST` 方法，兼容不同平台的回调方式。
- **URL 路径**：`/uac/callback/authCallback`
- **请求格式**：表单参数或查询参数形式传递 `code`、`platformId` 和 `app`。
- **响应格式**：标准 JSON 响应，封装在 `CommonApiResponse<CallbackResponse>` 中，遵循统一的 API 响应结构。

### 控制器逻辑
控制器通过 `@Validated` 对 `CallbackRequest` 进行参数校验，调用 `CallbackPlatformHandleService` 的 `authCallback` 方法处理业务逻辑。若返回的 `CallbackResponse` 成功，则返回 200 OK 及响应数据；否则返回 500 服务器错误。

```mermaid
sequenceDiagram
participant Client as "第三方平台"
participant Controller as "CallbackController"
participant Service as "CallbackPlatformHandleService"
Client->>Controller : GET/POST /uac/callback/authCallback
Controller->>Controller : 参数校验(@Validated)
Controller->>Service : authCallback(AuthBo, platformId, app)
Service-->>Controller : CallbackResponse
Controller->>Controller : isSuccess()判断
alt 授权成功
Controller-->>Client : 200 OK + sellerNick, accessToken
else 授权失败
Controller-->>Client : 500 Error
end
```

**Diagram sources**
- [CallbackController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/CallbackController.java#L26-L43)
- [CallbackPlatformHandleService.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/platform/biz/CallbackPlatformHandleService.java#L1-L21)

**Section sources**
- [CallbackController.java](file://uac-service/src/main/java/cn/loveapp/uac/service/controller/CallbackController.java#L1-L43)

## 安全性实现

### 参数校验机制
接口通过注解驱动的方式实现参数校验：
- `@NotBlank` 确保 `code` 字段非空。
- `@CheckPlatformHasExist` 验证 `platformId` 是否为系统支持的有效平台。
- `@CheckAppHasExist` 验证 `app` 是否为注册的应用模块。

### 业务逻辑封装
`CallbackController` 本身不处理具体平台逻辑，而是将请求委托给 `CallbackPlatformHandleService` 接口。该接口由各平台的具体实现类（如 `TaoCallbackPlatformHandleServiceImpl`）提供，确保了扩展性和职责分离。

### 错误隔离
即使业务处理失败（如令牌获取失败），接口仍返回 200 状态码以防止第三方平台重复回调，真正的错误通过响应体中的 `code` 字段（如 500）体现，避免因 HTTP 错误码导致的重试风暴。

## 请求与响应示例

### 成功请求示例
```
GET /uac/callback/authCallback?code=auth_code_123&platformId=TAO&app=trade
```

### 成功响应示例
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "sellerNick": "淘宝卖家A",
    "accessToken": "access_token_456"
  }
}
```

### 失败响应示例（参数校验失败）
```json
{
  "code": 400,
  "message": "Bad Request",
  "data": null
}
```

### 失败响应示例（业务处理失败）
```json
{
  "code": 500,
  "message": "Internal Server Error",
  "data": null
}
```

## 错误处理策略

### 参数校验失败
当 `code` 为空或 `platformId`/`app` 无效时，Spring 框架自动返回 400 错误，无需业务代码干预。

### 网络或服务异常
若在调用第三方平台接口时发生网络超时或服务不可用，`CallbackPlatformHandleService` 实现类应捕获异常并返回包含错误信息的 `CallbackResponse`，控制器据此返回 500 错误。

### 空值处理
`CallbackResponse` 的 `isSuccess()` 方法明确要求 `sellerNick` 和 `accessToken` 均不能为 `null` 或空字符串，确保只有完整有效的授权结果才被视为成功。

### 单元测试覆盖
通过 `CallbackControllerTest` 对各种场景（如 `code` 为空、业务成功、业务失败）进行了充分的单元测试，确保接口行为符合预期。

**Section sources**
- [CallbackControllerTest.java](file://uac-service/src/test/java/cn/loveapp/uac/service/controller/CallbackControllerTest.java#L34-L101)