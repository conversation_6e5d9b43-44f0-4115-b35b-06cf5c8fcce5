# 多平台认证实现

<cite>
**本文档引用文件**  
- [PddAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java)
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java)
- [PDDTradeERPAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/pdd/PDDTradeERPAppConfig.java)
- [TaoBaoTradeERPAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/taobao/TaoBaoTradeERPAppConfig.java)
- [PddRefreshTokenCallbackResult.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/domain/PddRefreshTokenCallbackResult.java)
- [TaobaoRefreshTokenCallbackResult.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/domain/TaobaoRefreshTokenCallbackResult.java)
- [AppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/AppConfig.java)
- [RefreshTokenCallbackResult.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/domain/RefreshTokenCallbackResult.java)
</cite>

## 目录
1. [引言](#引言)
2. [核心组件分析](#核心组件分析)
3. [平台认证流程共性与差异](#平台认证流程共性与差异)
4. [平台特定配置类详解](#平台特定配置类详解)
5. [API调用重试机制与异常处理](#api调用重试机制与异常处理)
6. [性能优化建议](#性能优化建议)
7. [响应格式与错误码处理示例](#响应格式与错误码处理示例)
8. [结论](#结论)

## 引言
本文档详细分析了拼多多（PDD）与淘宝（Tao）平台认证服务的实现机制，重点对比 `PddAuthServiceImpl` 与 `TaoAuthServiceImpl` 的实现差异。文档涵盖授权码交换 access_token、用户信息获取、token 刷新等核心流程，解析平台特定配置类的作用，并提供重试机制、异常处理及性能优化策略。

## 核心组件分析

### 认证服务实现类结构
系统采用继承架构，`PddAuthServiceImpl` 和 `TaoAuthServiceImpl` 均继承自 `BaseAuthServiceImpl`，实现 `AuthService` 接口，形成统一的认证服务抽象。

```mermaid
classDiagram
class AuthService {
<<interface>>
+getCallbackResultByCode(String, String, String) RefreshTokenCallbackResult
+refreshToken(UserInfoBo, String, String, String) RefreshTokenCallbackResult
+getRetryCount() Integer
+getPlatformId() String
}
class BaseAuthServiceImpl {
-Map~String, AppConfig~ appConfigMap
-DistributeConfig distributeConfig
+getCallbackResultByCode(String, String, Map, Class, String) T
+reGetAccessTokenWithRefreshToken(String, String, Map, Class, String) T
+getActualConfig(String) AppConfig
+convertUserRedisEntity2UserInfoBo(UserInfoBo, UserRedisEntity, String, String) void
}
class PddAuthServiceImpl {
+getCallbackResultByCode(String, String, String) RefreshTokenCallbackResult
+refreshToken(UserInfoBo, String, String, String) RefreshTokenCallbackResult
+getPlatformId() String
}
class TaoAuthServiceImpl {
+getCallbackResultByCode(String, String, String) RefreshTokenCallbackResult
+refreshToken(UserInfoBo, String, String, String) RefreshTokenCallbackResult
+decryptToken(String, String, String) String
+encryptToken(String, String, String) String
+convertUserRedisEntity2UserInfoBo(UserInfoBo, UserRedisEntity, String, String) void
+getPlatformId() String
}
AuthService <|-- BaseAuthServiceImpl
BaseAuthServiceImpl <|-- PddAuthServiceImpl
BaseAuthServiceImpl <|-- TaoAuthServiceImpl
```

**图示来源**
- [PddAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java)
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java)

**本节来源**
- [PddAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java#L26-L129)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java#L31-L249)
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java#L34-L288)

### 认证结果响应结构
`RefreshTokenCallbackResult` 是认证流程的核心数据结构，各平台通过继承实现差异化字段映射。

```mermaid
classDiagram
class RefreshTokenCallbackResult {
+String decryptAccessToken
+String decryptRefreshToken
+String accessToken
+Long expiresIn
+String refreshToken
+String[] scope
+String sellerNick
+String sellerId
+String subSellerNick
+String subSellerId
+LocalDateTime authDeadLine
}
class PddRefreshTokenCallbackResult {
+String accessToken
+Long expiresIn
+String refreshToken
+String[] scope
+String sellerNick
+String sellerId
+isSuccess() boolean
}
class TaobaoRefreshTokenCallbackResult {
+String accessToken
+Long expiresIn
+String tokenType
+String refreshToken
+Long w1ExpiresIn
+String sellerNick
+String sellerId
+String subSellerNick
+String subSellerId
+String errorDescription
+String error
+getUrlDecodeSellerNick() String
+getUrlDecodeSubSellerNick() String
+isSuccess() boolean
}
RefreshTokenCallbackResult <|-- PddRefreshTokenCallbackResult
RefreshTokenCallbackResult <|-- TaobaoRefreshTokenCallbackResult
```

**图示来源**
- [PddRefreshTokenCallbackResult.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/domain/PddRefreshTokenCallbackResult.java)
- [TaobaoRefreshTokenCallbackResult.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/domain/TaobaoRefreshTokenCallbackResult.java)
- [RefreshTokenCallbackResult.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/domain/RefreshTokenCallbackResult.java)

**本节来源**
- [PddRefreshTokenCallbackResult.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/domain/PddRefreshTokenCallbackResult.java#L12-L44)
- [TaobaoRefreshTokenCallbackResult.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/domain/TaobaoRefreshTokenCallbackResult.java#L12-L72)

## 平台认证流程共性与差异

### 共性流程
两个平台均遵循标准 OAuth 2.0 授权码模式，包含以下共性步骤：
1. 通过授权码（code）调用 `getCallbackResultByCode` 获取 access_token
2. 使用 refresh_token 调用 `refreshToken` 方法刷新凭证
3. 均继承 `BaseAuthServiceImpl` 的网络请求与重试逻辑
4. 均对 token 进行加密存储（除特定 appName 外）
5. 均返回 `RefreshTokenCallbackResult` 结构化结果

### 实现差异

| 特性 | 拼多多 (PDD) | 淘宝 (Tao) |
|------|-------------|-----------|
| **HTTP 客户端** | 使用 `HttpUtil.sendJsonPost` | 使用 `NetworkUtil.http` |
| **请求头** | 显式设置 Content-type 等头信息 | 可选传入 headers |
| **Token 解密** | 无特殊处理 | 实现 `decryptToken` 方法，支持 URL 解码 |
| **Token 加密** | 无特殊处理 | 实现 `encryptToken` 方法，支持 APP_ITEM 不加密 |
| **用户信息转换** | 使用父类 `convertUserRedisEntity2UserInfoBo` | 重写该方法，支持多 APP 类型（TRADE, ITEM, SHOP_HELPER） |
| **授权到期时间** | 直接使用 `expiresIn` 计算 | 使用 `w1ExpiresIn` 并归零到当日 0 点 |
| **错误判断** | 仅检查 `accessToken` 是否为空 | 检查 `error` 和 `errorDescription` 字段 |

**本节来源**
- [PddAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java#L26-L129)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java#L31-L249)
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java#L34-L288)

## 平台特定配置类详解

### 配置类继承结构
所有平台配置类均继承自 `AppConfig`，通过 `@ConfigurationProperties` 绑定配置项。

```mermaid
classDiagram
class AppConfig {
+String appkey
+String appSecret
+String sessionkey
+String refreshTokenUrl
+String authCodeTokenUrl
+String redirectUrl
}
class PDDTradeERPAppConfig {
}
class TaoBaoTradeERPAppConfig {
}
AppConfig <|-- PDDTradeERPAppConfig
AppConfig <|-- TaoBaoTradeERPAppConfig
```

**图示来源**
- [AppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/AppConfig.java)
- [PDDTradeERPAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/pdd/PDDTradeERPAppConfig.java)
- [TaoBaoTradeERPAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/taobao/TaoBaoTradeERPAppConfig.java)

### 配置项说明

#### 基础配置项 (AppConfig)
- **appkey**: 平台应用标识
- **appSecret**: 应用密钥
- **sessionkey**: 会话密钥（用于 token 加解密）
- **refreshTokenUrl**: 刷新 token 的 API 地址
- **authCodeTokenUrl**: 授权码换 token 的 API 地址
- **redirectUrl**: 授权回调地址

#### PDDTradeERPAppConfig
- 配置前缀：`uac.pdd.tradeerp.app`
- 用于拼多多 ERP 场景的认证配置
- 在 `PddAuthServiceImpl` 构造函数中注入

#### TaoBaoTradeERPAppConfig
- 配置前缀：`uac.taobao.tradeerp.app`
- 用于淘宝 ERP 场景的认证配置
- 在 `TaoAuthServiceImpl` 构造函数中注入

**本节来源**
- [PDDTradeERPAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/pdd/PDDTradeERPAppConfig.java#L14-L18)
- [TaoBaoTradeERPAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/taobao/TaoBaoTradeERPAppConfig.java#L14-L20)
- [AppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/AppConfig.java#L1-L19)

## API调用重试机制与异常处理

### 重试机制
- 重试次数由 `uac.network.retry.count` 配置项控制，默认为 5 次
- 两个实现类均通过 `@Value("${uac.network.retry.count:5}")` 注入
- 在 `getCallbackResultByCode` 方法中实现 while 循环重试
- 每次重试后记录日志，包含剩余重试次数

### 异常处理策略
| 平台 | 网络异常 | 业务异常 | 日志记录 |
|------|---------|---------|---------|
| PDD | `IOException` | `UserNeedAuthException` | 记录请求/响应、错误信息 |
| 淘宝 | `Exception` | `UserNeedAuthException` | 记录请求/响应、错误信息 |

- PDD 使用 `HttpUtil.sendJsonPost`，捕获 `IOException`
- 淘宝使用 `NetworkUtil.http`，捕获通用 `Exception`
- 均在 `finally` 块中记录响应日志
- 空结果或失败时返回 `null`，由上层处理

**本节来源**
- [PddAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/PddAuthServiceImpl.java#L26-L129)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java#L31-L249)
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java#L34-L288)

## 性能优化建议

1. **减少反射调用**：`JSONObject.parseObject` 在高频调用场景下可考虑缓存解析器
2. **连接池优化**：`HttpUtil` 和 `NetworkUtil` 应配置合理的连接池大小与超时时间
3. **异步刷新**：token 刷新可结合 `uac-job` 模块的 `RefreshAccessTokenTask` 异步执行
4. **缓存配置对象**：`getActualConfig` 方法可增加本地缓存避免频繁 Map 查找
5. **批量处理**：对于多店铺认证，可实现批量接口减少网络开销
6. **监控埋点**：在关键方法（如 `getCallbackResultByCode`）添加耗时监控

## 响应格式与错误码处理示例

### 错误码处理差异
```java
// PDD 错误判断
public boolean isSuccess() {
    return StringUtils.isNotBlank(accessToken);
}

// 淘宝错误判断
public boolean isSuccess() {
    return StringUtils.isBlank(errorDescription) || 
           StringUtils.isBlank(error) || 
           StringUtils.isNotBlank(accessToken);
}
```

### 特殊字符处理
淘宝实现中包含对 `\r`、`\n` 等特殊字符的过滤，防止解密失败：
```java
token = token.replaceAll("\\\\r", "");
token = token.replaceAll("\\\\n", "");
token = token.replaceAll("\\n", "");
token = token.replaceAll("\\r", "");
```

### 用户信息转换示例
淘宝根据 `appName` 区分不同业务线的 Redis 字段映射：
- `APP_TRADE`: 使用 `trade_access_token_m` 等字段
- `APP_ITEM`: 使用 `item_access_token_mp` 等字段
- `APP_SHOP_HELPER`: 使用 `shophelper_access_token_mp` 等字段

**本节来源**
- [PddRefreshTokenCallbackResult.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/domain/PddRefreshTokenCallbackResult.java#L35-L44)
- [TaobaoRefreshTokenCallbackResult.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/domain/TaobaoRefreshTokenCallbackResult.java#L65-L72)
- [TaoAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/TaoAuthServiceImpl.java#L150-L249)

## 结论
`PddAuthServiceImpl` 与 `TaoAuthServiceImpl` 在继承统一基类的基础上，实现了平台特定的认证逻辑。PDD 实现更简洁，依赖标准 JSON POST；淘宝实现更复杂，包含 URL 解码、多 APP 类型支持等特性。两者均具备完善的重试与异常处理机制，通过配置类实现灵活的参数管理。建议在扩展新平台时参考此模式，保持架构一致性。