# 抖店认证实现

<cite>
**本文档引用文件**  
- [DoudianAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/DoudianAuthServiceImpl.java)
- [DoudianAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/doudian/DoudianAppConfig.java)
- [AppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/AppConfig.java)
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java)
</cite>

## 目录
1. [项目结构](#项目结构)  
2. [核心组件](#核心组件)  
3. [架构概述](#架构概述)  
4. [详细组件分析](#详细组件分析)  
5. [依赖分析](#依赖分析)  

## 项目结构

本项目采用模块化设计，主要模块包括 `uac-api`、`uac-common`、`uac-db-common`、`uac-service` 等。其中，抖店认证功能的核心实现位于 `uac-common` 模块下的 `cn.loveapp.uac.common.platform.api.impl` 包中。

```mermaid
graph TD
subgraph "uac-common"
DoudianAppConfig[DoudianAppConfig]
DoudianAuthServiceImpl[DoudianAuthServiceImpl]
BaseAuthServiceImpl[BaseAuthServiceImpl]
AuthService[AuthService]
end
subgraph "uac-db-common"
UserRedisEntity[UserRedisEntity]
end
DoudianAuthServiceImpl --> DoudianAppConfig
DoudianAuthServiceImpl --> BaseAuthServiceImpl
DoudianAuthServiceImpl --> AuthService
BaseAuthServiceImpl --> UserRedisEntity
```

**图示来源**  
- [DoudianAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/doudian/DoudianAppConfig.java)
- [DoudianAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/DoudianAuthServiceImpl.java)
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java)

**章节来源**  
- [DoudianAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/doudian/DoudianAppConfig.java)
- [DoudianAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/DoudianAuthServiceImpl.java)

## 核心组件

`DoudianAuthServiceImpl` 是抖店平台认证的核心服务类，继承自 `BaseAuthServiceImpl`，实现了 `AuthService` 接口。该类负责处理抖店 OAuth 流程中的授权码获取、access_token 刷新及用户信息获取等操作。

**章节来源**  
- [DoudianAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/DoudianAuthServiceImpl.java)

## 架构概述

抖店认证流程基于 OAuth 2.0 协议，主要包含以下步骤：
1. 用户授权后，平台返回授权码（code）。
2. 使用授权码换取 access_token 和 refresh_token。
3. 定期使用 refresh_token 刷新 access_token，确保长期有效。

```mermaid
sequenceDiagram
participant 用户
participant 前端
participant 后端
participant 抖店平台
用户->>前端 : 点击授权
前端->>抖店平台 : 跳转至授权URL
抖店平台->>用户 : 授权确认
用户->>抖店平台 : 确认授权
抖店平台->>前端 : 返回code
前端->>后端 : 发送code
后端->>抖店平台 : 使用code换取token
抖店平台->>后端 : 返回access_token和refresh_token
后端->>后端 : 加密存储token
后端->>前端 : 返回认证结果
```

**图示来源**  
- [DoudianAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/DoudianAuthServiceImpl.java)
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java)

## 详细组件分析

### DoudianAuthServiceImpl 分析

`DoudianAuthServiceImpl` 类通过 `DoudianSDKService` 与抖音开放平台进行交互，完成 token 的刷新操作。其核心方法为 `refreshToken`，该方法接收 `UserInfoBo` 对象和 `refreshToken` 字符串，调用 SDK 接口刷新 token，并将结果封装为 `RefreshTokenCallbackResult` 返回。

#### 类图
```mermaid
classDiagram
class DoudianAuthServiceImpl {
+Integer retryCount
+DoudianSDKService doudianSDKService
+DoudianAuthServiceImpl(DoudianAppConfig, DistributeConfig)
+Integer getRetryCount()
+RefreshTokenCallbackResult getCallbackResultByCode(String, String, String)
+RefreshTokenCallbackResult refreshToken(UserInfoBo, String, String, String)
+String getPlatformId()
}
class BaseAuthServiceImpl {
+Map~String, AppConfig~ appConfigMap
+DistributeConfig distributeConfig
+String encryptToken(String, String, String)
+String decryptToken(String, String, String)
+<T> T getCallbackResultByCode(String, String, Map~String, String~, Class~T~, String)
+<T> T reGetAccessTokenWithRefreshToken(String, String, Map~String, String~, Class~T~, String)
}
class DoudianAppConfig {
+String appkey
+String appSecret
+String sessionkey
+String refreshTokenUrl
+String authCodeTokenUrl
+String redirectUrl
}
DoudianAuthServiceImpl --|> BaseAuthServiceImpl
DoudianAuthServiceImpl --> DoudianAppConfig
```

**图示来源**  
- [DoudianAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/DoudianAuthServiceImpl.java)
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java)
- [DoudianAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/doudian/DoudianAppConfig.java)

#### 配置项说明

`DoudianAppConfig` 继承自 `AppConfig`，包含以下关键配置项：

| 配置项 | 功能说明 | 设置建议 |
|--------|--------|--------|
| appkey | 应用唯一标识 | 从抖音开放平台获取 |
| appSecret | 应用密钥 | 严格保密，不得泄露 |
| sessionkey | 用于加密解密的密钥 | 定期更换以增强安全性 |
| refreshTokenUrl | 刷新 token 的接口地址 | 根据抖音开放平台文档配置 |
| authCodeTokenUrl | 获取 token 的接口地址 | 根据抖音开放平台文档配置 |
| redirectUrl | 授权回调地址 | 必须与平台注册的回调地址一致 |

**章节来源**  
- [DoudianAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/doudian/DoudianAppConfig.java)
- [AppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/AppConfig.java)

### 错误处理策略

在 `refreshToken` 方法中，系统会对 SDK 返回结果进行校验。若返回结果为空、失败或数据为空，则记录错误日志并返回 null。同时，捕获异常并在日志中记录详细信息，便于后续排查问题。

```mermaid
flowchart TD
Start([开始]) --> CheckResponse[检查响应结果]
CheckResponse --> ResponseValid{"响应有效?"}
ResponseValid --> |否| LogError[记录错误日志]
ResponseValid --> |是| ProcessData[处理数据]
LogError --> ReturnNull[返回null]
ProcessData --> EncryptToken[加密Token]
EncryptToken --> SetResult[设置回调结果]
SetResult --> ReturnResult[返回结果]
ReturnNull --> End([结束])
ReturnResult --> End
```

**图示来源**  
- [DoudianAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/DoudianAuthServiceImpl.java)

**章节来源**  
- [DoudianAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/DoudianAuthServiceImpl.java)

## 依赖分析

`DoudianAuthServiceImpl` 依赖于 `DoudianAppConfig` 提供的配置信息，以及 `BaseAuthServiceImpl` 提供的基础认证功能。同时，通过 `DoudianSDKService` 与抖音开放平台进行通信。

```mermaid
graph TD
DoudianAuthServiceImpl --> DoudianAppConfig
DoudianAuthServiceImpl --> BaseAuthServiceImpl
DoudianAuthServiceImpl --> DoudianSDKService
BaseAuthServiceImpl --> AppConfig
BaseAuthServiceImpl --> UserRedisEntity
```

**图示来源**  
- [DoudianAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/DoudianAuthServiceImpl.java)
- [DoudianAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/doudian/DoudianAppConfig.java)
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java)

**章节来源**  
- [DoudianAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/DoudianAuthServiceImpl.java)
- [DoudianAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/doudian/DoudianAppConfig.java)
- [BaseAuthServiceImpl.java](file://uac-common/src/main/java/cn/loveapp/uac/common/platform/api/impl/BaseAuthServiceImpl.java)