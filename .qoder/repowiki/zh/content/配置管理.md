# 配置管理

<cite>
**本文档引用的文件**  
- [CacheTimeoutConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/CacheTimeoutConfig.java)
- [RocketMQAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/RocketMQAppConfig.java)
- [ArticleCodeConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/app/ArticleCodeConfig.java)
- [SwitchCacheKeyConstant.java](file://uac-common/src/main/java/cn/loveapp/uac/common/constant/SwitchCacheKeyConstant.java)
- [VersionGrayHandler.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/VersionGrayHandler.java)
- [VersionGrayConfig.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/config/VersionGrayConfig.java)
- [TaoBaoTradeAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/taobao/TaoBaoTradeAppConfig.java)
- [AppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/AppConfig.java)
- [application-dev.properties](file://uac-service/src/main/resources/application-dev.properties)
- [application-prod.properties](file://uac-service/src/main/resources/application-prod.properties)
</cite>

## 目录
1. [配置管理体系概述](#配置管理体系概述)
2. [关键配置项清单](#关键配置项清单)
3. [配置加载机制与优先级](#配置加载机制与优先级)
4. [灰度发布机制（VersionGrayHandler）](#灰度发布机制versiongrayhandler)
5. [功能开关实现（SwitchCacheKeyConstant）](#功能开关实现switchcachekeyconstant)
6. [配置项详细说明](#配置项详细说明)

## 配置管理体系概述

本项目采用基于Spring Boot的外部化配置机制，结合Apollo配置中心实现多环境（dev、pretest、prod）的统一管理。配置文件通过`application-{profile}.properties`进行环境隔离，并通过`@Value`、`@ConfigurationProperties`等注解注入到具体配置类中。所有敏感配置（如AppKey、Secret）均通过Apollo进行动态管理，支持运行时热更新。

**配置管理核心组件：**
- **Apollo配置中心**：集中管理所有环境的配置项。
- **Spring Profile**：通过`spring.profiles.active`激活对应环境配置。
- **@Configuration类**：封装各模块配置，提供类型安全的访问接口。

**Section sources**
- [VersionGrayConfig.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/config/VersionGrayConfig.java#L1-L45)
- [AppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/AppConfig.java#L1-L20)

## 关键配置项清单

以下为项目中关键配置项的分类清单：

### 数据库与Redis连接
- 数据库连接池配置（未在当前上下文中体现，通常位于`application.properties`）
- Redis连接地址与超时配置

### RocketMQ配置
- `rocketmq.default.app.config.namesrvAddr`：RocketMQ NameServer地址

### 电商平台AppKey/Secret
- 淘宝交易：`uac.taobao.trade.app.appkey`, `uac.taobao.trade.app.appSecret`
- 其他平台类似，均继承自`AppConfig`

### 定时任务调度表达式
- 未在当前上下文中体现，通常位于`@Scheduled`注解或配置类中

### 缓存超时时间
- 用户设置缓存超时：`uac.redis.usersettings.timeout`（默认604800秒，即7天）

### 灰度发布配置
- 灰度开关：`uac.version.gray.enable`
- 灰度用户列表：`uac.version.gray.users`
- 灰度平台列表：`uac.version.gray.platforms`
- 灰度服务地址：`uac.version.gray.serviceHost`

### 功能开关
- 缓存键开关：`hasNickIp`（定义在`SwitchCacheKeyConstant`中）

**Section sources**
- [RocketMQAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/RocketMQAppConfig.java#L1-L19)
- [TaoBaoTradeAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/taobao/TaoBaoTradeAppConfig.java#L1-L21)
- [CacheTimeoutConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/CacheTimeoutConfig.java#L1-L23)
- [SwitchCacheKeyConstant.java](file://uac-common/src/main/java/cn/loveapp/uac/common/constant/SwitchCacheKeyConstant.java#L1-L12)

## 配置加载机制与优先级

配置加载遵循Spring Boot的外部化配置优先级规则，具体顺序如下：

1. **默认配置**：在`@Value`注解中通过`:`指定默认值，如`${uac.redis.usersettings.timeout:604800}`。
2. **Apollo远程配置**：通过Apollo配置中心动态加载，优先级高于本地文件。
3. **本地属性文件**：`application-{profile}.properties`文件中的配置。
4. **主配置文件**：`application.properties`作为全局默认配置。

配置类通过`@ConfigurationProperties(prefix = "xxx")`绑定前缀配置，确保类型安全和结构清晰。

```mermaid
flowchart TD
A["配置请求"] --> B{是否存在环境变量?}
B --> |是| C["使用环境变量值"]
B --> |否| D{Apollo配置中心是否有值?}
D --> |是| E["使用Apollo配置"]
D --> |否| F{application-{profile}.properties中是否有值?}
F --> |是| G["使用Profile配置"]
F --> |否| H{application.properties中是否有值?}
H --> |是| I["使用主配置"]
H --> |否| J{@Value中是否有默认值?}
J --> |是| K["使用默认值"]
J --> |否| L["抛出异常"]
```

**Diagram sources**
- [CacheTimeoutConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/CacheTimeoutConfig.java#L1-L23)
- [RocketMQAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/RocketMQAppConfig.java#L1-L19)

**Section sources**
- [CacheTimeoutConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/CacheTimeoutConfig.java#L1-L23)

## 灰度发布机制（VersionGrayHandler）

灰度发布通过`VersionGrayHandler`实现，基于用户、平台或应用维度进行流量分流。

### 实现原理
1. **拦截器注册**：`VersionGrayHandler`实现`HandlerInterceptor`和`RequestBodyAdvice`，在请求处理前后进行拦截。
2. **条件判断**：从请求参数或请求体中提取`sellerNick`、`platformId`、`appName`，与配置中的灰度名单比对。
3. **异常触发**：若匹配灰度条件，抛出`VersionGrayHitException`。
4. **流量转发**：通过`@ExceptionHandler`捕获异常，使用`RoutingDelegateUtils`将请求重定向至灰度服务地址。

### 配置项
- `uac.version.gray.enable`：是否启用灰度。
- `uac.version.gray.users`：灰度用户Nick列表。
- `uac.version.gray.platforms`：灰度平台ID列表（支持`platformId_appName`组合）。
- `uac.version.gray.serviceHost`：灰度服务主机地址。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Handler as "VersionGrayHandler"
participant Utils as "RoutingDelegateUtils"
participant GrayServer as "灰度服务"
Client->>Handler : 发送请求(sellerNick, platformId)
Handler->>Handler : preHandle() 或 afterBodyRead()
Handler->>Handler : checkGrayConditions()
alt 在灰度名单中
Handler->>Handler : 抛出 VersionGrayHitException
Handler->>Utils : @ExceptionHandler 处理异常
Utils->>GrayServer : redirect() 转发请求
GrayServer-->>Client : 返回响应
else 不在灰度名单
Handler-->>Client : 继续正常流程
end
```

**Diagram sources**
- [VersionGrayHandler.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/VersionGrayHandler.java#L1-L157)
- [VersionGrayConfig.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/config/VersionGrayConfig.java#L1-L45)

**Section sources**
- [VersionGrayHandler.java](file://uac-service/src/main/java/cn/loveapp/uac/service/web/VersionGrayHandler.java#L1-L157)
- [VersionGrayConfig.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/config/VersionGrayConfig.java#L1-L45)

## 功能开关实现（SwitchCacheKeyConstant）

功能开关通过常量类`SwitchCacheKeyConstant`定义缓存键名，用于控制特定功能的开启或关闭。

### 当前定义
- `HAS_NICK_IP`：用于控制是否启用基于卖家Nick和IP的限制功能。

### 使用方式
该常量通常作为Redis缓存的Key前缀，结合业务逻辑判断是否执行特定功能。例如：
```java
if (redisTemplate.hasKey(SwitchCacheKeyConstant.HAS_NICK_IP + ":" + sellerNick)) {
    // 执行限制逻辑
}
```

此类开关便于通过缓存动态开启/关闭功能，无需重启服务。

**Section sources**
- [SwitchCacheKeyConstant.java](file://uac-common/src/main/java/cn/loveapp/uac/common/constant/SwitchCacheKeyConstant.java#L1-L12)

## 配置项详细说明

| 配置项 | 默认值 | 作用范围 | 修改影响 |
|-------|-------|---------|---------|
| `uac.redis.usersettings.timeout` | 604800 | 用户设置缓存 | 调整缓存过期时间，影响数据一致性与性能 |
| `uac.version.gray.enable` | false | 灰度发布控制 | 开启后按规则分流请求至灰度服务 |
| `uac.version.gray.users` | 无 | 灰度用户列表 | 添加用户Nick可使其请求进入灰度流程 |
| `uac.version.gray.platforms` | 无 | 灰度平台列表 | 添加平台ID可使该平台所有请求进入灰度 |
| `uac.version.gray.serviceHost` | 无 | 灰度服务地址 | 必须配置有效地址，否则灰度请求失败 |
| `rocketmq.default.app.config.namesrvAddr` | 无 | RocketMQ连接 | 影响消息队列的连接与通信 |
| `uac.taobao.trade.app.appkey` | 无 | 淘宝交易应用 | 用于API调用身份认证，错误将导致调用失败 |
| `uac.taobao.trade.app.appSecret` | 无 | 淘宝交易应用 | 用于签名生成，泄露有安全风险 |

**Section sources**
- [CacheTimeoutConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/redis/CacheTimeoutConfig.java#L1-L23)
- [VersionGrayConfig.java](file://uac-service-common/src/main/java/cn/loveapp/uac/service/config/VersionGrayConfig.java#L1-L45)
- [RocketMQAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/rocketmq/RocketMQAppConfig.java#L1-L19)
- [TaoBaoTradeAppConfig.java](file://uac-common/src/main/java/cn/loveapp/uac/common/config/taobao/TaoBaoTradeAppConfig.java#L1-L21)